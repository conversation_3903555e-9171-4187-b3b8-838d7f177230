name: Integration tests
on:
  push:
    branches:
      - "main"
      - "release-*"
  pull_request:
    branches:
      - "main"
      - "release-*"
env:
  GOPRIVATE: "github.com/akuityio"
  GOWORK: "off"

# https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions#concurrency
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build-image:
    name: Build and Push Docker images
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write # needed for signing the images with GitHub OIDC Token
    services:
      registry:
        image: registry:2
        ports:
          - 5000:5000
    env:
      AKUITY_PLATFORM_REPO: us-docker.pkg.dev/akuity/akp/akuity-platform
    timeout-minutes: 45
    outputs:
      akuity-platform-image: ${{ steps.push-image.outputs.akuity-platform-image }}
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        name: Checkout
        with:
          # fetch-depth: 0 needed for `git rev-list --count` to work properly
          fetch-depth: 0
      # The following is necessary because `go list` (used in our makefile) does not seem to
      # use git credentials the same way as `git ls-remote`, and secrets.GITHUB_TOKEN is not
      # accepted by GitHub for username/password auth
      - name: Setup Private Git Repo Access
        run: /usr/bin/git config --global url."https://${{ secrets.AKUITYBOT_PAT }}:<EMAIL>/".insteadOf "https://github.com/"
      - name: Install Cosign
        uses: sigstore/cosign-installer@398d4b0eeef1380460a10c8013a76f728fb906ac # v3.9.1
      # Crane is a tool for interacting with remote images and registries.
      # We use it to retrieve the image digest of container manifest for signing.
      - name: Install Crane
        uses: imjasonh/setup-crane@31b88efe9de28ae0ffa220711af4b60be9435f6e # v0.4
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@e468171a9de216ec08956ac3ada2f0791b6bd435 # v3.11.1
        with:
          driver-opts: network=host
      - name: Login to Google Artifact Registry
        if: github.event_name != 'pull_request'
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        with:
          registry: us-docker.pkg.dev
          username: ${{ secrets.ARTIFACT_REGISTRY_USERNAME }}
          password: ${{ secrets.ARTIFACT_REGISTRY_PASSWORD }}
      - name: Cache Docker layers
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-
      - name: Setup Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: 1.24.4
          check-latest: true
          cache: false
      - name: Cache Go Modules
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-
      - name: Build distroless base image
        run: ./hack/distroless/build-base.sh
      - name: Build & Push Images
        run: make image
        id: push-image
        env:
          PUSH_IMAGE: ${{ github.event_name != 'pull_request' && github.ref_name == 'main' }}
          PUSH_LATEST: ${{ github.event_name != 'pull_request' && github.ref_name == 'main' }}
          IMAGE_REPO: ${{ env.AKUITY_PLATFORM_REPO }}
          GH_TOKEN: ${{ secrets.AKUITYBOT_PAT }}
          HUBSPOT_HUB_ID: ${{ secrets.HUBSPOT_HUB_ID }}
          BASE_IMAGE: localhost:5000/akp-distroless-base:latest
          SENTRY_TOKEN: ${{ github.ref_name == 'main' && secrets.SENTRY_TOKEN || '' }}
      - name: Echo image built
        if: ${{ github.event_name != 'pull_request' && github.ref_name == 'main' }}
        run: echo "Image built - [${{ steps.push-image.outputs.akuity-platform-image }}]"
      - name: Git digest of container images
        if: ${{ github.event_name != 'pull_request' && github.ref_name == 'main' }}
        run: |
          set -xo pipefail
          echo "AKUITY_PLATFORM_DIGEST=$(crane digest ${{ steps.push-image.outputs.akuity-platform-image }})" >> $GITHUB_ENV
      - name: Sign akuity-platform images
        if: ${{ github.event_name != 'pull_request' && github.ref_name == 'main' }}
        run: |
          cosign sign \
          -a "repo=${{ github.repository }}" \
          -a "workflow=${{ github.workflow }}" \
          -a "sha=${{ github.sha }}" \
          --yes \
          ${AKUITY_PLATFORM_REPO}@${{ env.AKUITY_PLATFORM_DIGEST }}

  build-cli:
    runs-on: ubuntu-latest
    if: github.event_name != 'pull_request'
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          # fetch-depth: 0 needed for `git rev-list --count` to work properly
          fetch-depth: 0
      - name: Setup Private Git Repo Access
        run: /usr/bin/git config --global url."https://${{ secrets.AKUITYBOT_PAT }}:<EMAIL>/".insteadOf "https://github.com/"
      - name: Setup Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: 1.24.4
          check-latest: true
          cache: false
      - name: Cache Go Modules
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-
      - name: Build CLI Binaries
        run: make release-cli
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@b47578312673ae6fa5b5096b330d9fbac3d116df # v4.2.1
        with:
          role-to-assume: arn:aws:iam::541216676946:role/github-actions
          aws-region: us-west-2
      - name: Push binaries
        run: |
          aws s3 sync "./dist/akuity-cli" "s3://akuity-release-tst/akuity-cli"

  copy-helm-chart:
    runs-on: ubuntu-latest
    timeout-minutes: 3
    if: github.event_name == 'push' && github.ref_name == 'main'
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          path: akuity-platform
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          repository: akuityio/akuity-platform-deploy
          token: ${{ secrets.AKUITYBOT_PAT }}
          path: akuity-platform-deploy
      - run: |
          rm -rf ./akuity-platform-deploy/base/charts/akuity-platform
          cp -rf ./akuity-platform/charts/akuity-platform ./akuity-platform-deploy/base/charts/akuity-platform
      - name: copy-helm-chart
        working-directory: ./akuity-platform-deploy
        run: |
          if [ -n "$(git status -s)" ]; then
            echo "Helm Chart updates:"
            git diff
            git config --global user.name "Akuity Bot"
            git config --global user.email "<EMAIL>"
            git add --all
            git commit -m "Helm chart update from '${{ github.repository }}:${{ github.sha }}'"
            git push
          else
            echo "There are no Helm Chart updates"
          fi

  changes:
    runs-on: ubuntu-latest
    timeout-minutes: 3
    outputs:
      backend: ${{ steps.filter.outputs.backend }}
      frontend: ${{ steps.filter.outputs.frontend }}
      helm: ${{ steps.filter.outputs.helm }}
    steps:
      # git isn't installed in the default image
      # https://github.com/actions/runner/pull/3056
      - run: sudo apt-get update && sudo apt install -y --no-install-recommends git
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: dorny/paths-filter@de90cc6fb38fc0963ad72b210f1f284cd68cea36 # v3.0.2
        name: changed-files
        id: filter
        with:
          list-files: "csv"
          filters: |
            backend:
              - '!(portal/ui/**)'
            frontend:
              - 'portal/ui/**'
              - 'aims/ui/**'
              - Dockerfile
            helm:
              - charts/akuity-platform/**

  build-ui:
    name: Build, test & lint UI code
    timeout-minutes: 15
    needs: changes
    if: ${{ needs.changes.outputs.backend == 'true' || needs.changes.outputs.frontend == 'true' || github.event_name == 'push' }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Cache pnpm modules
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: ~/.pnpm-store
          key: ${{ runner.os }}-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-
      - uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda # v4.1.0
        with:
          version: 9.3.0
          run_install: |
            - cwd: portal/ui/
      - name: Setup Git
        env:
          GITHUB_TOKEN: ${{ secrets.AKUITYBOT_PAT }}
        run: |
          /usr/bin/git config --global url."https://${GITHUB_TOKEN}:<EMAIL>/".insteadOf "https://github.com/"
      - name: Run tests
        env:
          NODE_ENV: test
        run: |
          pnpm run test
        working-directory: portal/ui/
      - name: Build UI code
        run: |
          VERSION=v99999 make ui
        env:
          NODE_ENV: production
          NODE_ONLINE_ENV: online
          HOST_ARCH: amd64
      - name: Run ESLint
        run: pnpm run lint
        working-directory: portal/ui/
      - name: Save coverage results
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: portal-ui
          path: portal/ui/build
      - name: Save coverage results
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: js-coverage-results
          path: portal/ui/coverage

  build-aims-ui:
    name: Build, test & lint AIMS UI code
    timeout-minutes: 10
    needs: changes
    if: ${{ needs.changes.outputs.frontend == 'true' || github.event_name == 'push' }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Cache pnpm modules
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: ~/.pnpm-store
          key: ${{ runner.os }}-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-
      - uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda # v4.1.0
        with:
          version: 9.3.0
          run_install: |
            - cwd: aims/ui/
      - uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda # v4.1.0
        with:
          version: 9.3.0
          run_install: |
            - cwd: portal/ui/
      - name: Setup Git
        env:
          GITHUB_TOKEN: ${{ secrets.AKUITYBOT_PAT }}
        run: |
          /usr/bin/git config --global url."https://${GITHUB_TOKEN}:<EMAIL>/".insteadOf "https://github.com/"
      - name: Build UI code
        run: |
          make aims-ui
        env:
          NODE_ENV: production
          NODE_ONLINE_ENV: online
          HOST_ARCH: amd64
      - name: Run ESLint
        run: pnpm run lint
        working-directory: aims/ui/
      - name: Save coverage results
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: js-coverage-results
          path: aims/ui/coverage

  lint-backend:
    name: Lint backend code
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: changes
    if: ${{ needs.changes.outputs.backend == 'true' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup private git repo access
        env:
          GITHUB_TOKEN: ${{ secrets.AKUITYBOT_PAT }}
        run: |
          /usr/bin/git config --global url."https://${GITHUB_TOKEN}:<EMAIL>/".insteadOf "https://github.com/"
      # https://github.com/bufbuild/buf-setup-action
      # https://github.com/bufbuild/buf/releases
      - uses: bufbuild/buf-setup-action@a47c93e0b1648d5651a065437926377d060baa99 # v1.50.0
        with:
          version: 1.41.0
          github_token: ${{ github.token }}
      - name: Determine if 'skip-breaking-protobuf' label is present
        id: check-label
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
        if: github.event_name == 'pull_request'
        with:
          script: |
            const labels = context.payload.pull_request.labels.map(label => label.name);
            return labels.includes('skip-breaking-protobuf') ? 'true' : 'false';
          result-encoding: string
      - name: Lint protobuf
        uses: bufbuild/buf-lint-action@06f9dd823d873146471cfaaf108a993fe00e5325 # v1.1.1
      - name: Check breaking protobuf changes
        if: steps.check-label.outputs.result != 'true'
        uses: bufbuild/buf-breaking-action@c57b3d842a5c3f3b454756ef65305a50a587c5ba # v1.1.4
        with:
          against: https://github.com/akuityio/akuity-platform.git#branch=main
      - name: Check protobuf format
        run: buf format --exit-code
      - uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: 1.24.4
          check-latest: true
          cache: false
      - name: Lint go code
        uses: golangci/golangci-lint-action@4afd733a84b1f43292c63897423277bb7f4313a9 # v8.0.0
        with:
          version: v2.1.6
          skip-cache: true
          args: --timeout 10m

  build-backend:
    name: Run unit tests and build binaries
    runs-on: ubuntu-latest-4-cores
    needs: changes
    if: ${{ needs.changes.outputs.backend == 'true' || needs.changes.outputs.frontend == 'true' }}
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: 1.24.4
          check-latest: true
          cache: false
      - uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda # v4.1.0
        with:
          version: 9.3.0
          run_install: |
            - cwd: portal/ui/
      - uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda # v4.1.0
        with:
          version: 9.3.0
          run_install: |
            - cwd: aims/ui/
      # https://github.com/actions/cache/blob/main/examples.md#go---modules
      - uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
            ~/.cache/ms-playwright
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-
      - name: Download Go dependencies
        env:
          GITHUB_TOKEN: ${{ secrets.AKUITYBOT_PAT }}
        run: |
          /usr/bin/git config --global url."https://${GITHUB_TOKEN}:<EMAIL>/".insteadOf "https://github.com/"
          go mod download
      - uses: bufbuild/buf-setup-action@a47c93e0b1648d5651a065437926377d060baa99 # v1.50.0
        with:
          version: 1.41.0
          github_token: ${{ github.token }}
      - name: Install protoc
        timeout-minutes: 15
        run: |
          sudo apt-get update
          sudo apt-get install -y protobuf-compiler
      - name: Login to Docker Hub
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        with:
          username: akuitybot
          password: ${{ secrets.DOCKERHUB_AKUITYBOT_RO_TOKEN }}
      - name: Run Generate
        id: run-generate
        run: |
          set -euo pipefail
          go mod tidy
          go mod verify
          make tools-update generate-in-container
          echo "line_count=$(wc -l < models/sql/schema.sql)" >> $GITHUB_OUTPUT
          cd models && make install-toolchain generate LIQUIBASE_IMAGE=quay.io/akuity/liquibase:4.29
      - name: Check nothing has changed
        run: |
          new_line_count=$(wc -l < models/sql/schema.sql)
          echo "new line count is $new_line_count"
          if [ "${{ steps.run-generate.outputs.line_count }}" -ne "$new_line_count" ]; then
            echo "detected new lines in schema.sql, it had ${{ steps.run-generate.outputs.line_count }} lines but has $new_line_count after make generate in models directory."
            git diff --exit-code -- . > /tmp/codegen.patch
            else
            echo "same number of lines in the diff"
            if ! git diff --exit-code -- . ':!models/sql/schema.sql' > /tmp/codegen.patch; then
              echo "Changes detected in generated code. Please consult the diff-patch artifact from this build."
              exit 1
            fi
          fi
          echo "done checking if something has changed"
      - name: Run unit tests
        run: make test
      - name: Build Binaries
        run: |
          VERSION=v99999 make akuity-platform
          go test -cover -c -o dist/e2e.test ./test/e2e
          ls ./test/functional | xargs -I {} go test -cover -c -o dist/functional-{}.test ./test/functional/{}
      - uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: backend-binaries
          path: dist
      - name: Upload Diff
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        if: failure()
        with:
          name: diff-patch
          path: /tmp/codegen.patch

  e2e-tests:
    name: Run functional & e2e tests
    runs-on: ubuntu-latest-8-cores
    timeout-minutes: 40
    needs: [changes, build-backend, build-ui]
    if: ${{ needs.changes.outputs.backend == 'true' || needs.changes.outputs.frontend == 'true' }}
    strategy:
      fail-fast: false
      matrix:
        type: [functional, e2e]
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          name: portal-ui
          path: portal/ui/build

      - uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          name: backend-binaries
          path: dist

      - name: Setup K3S
        env:
          DOCKER_USERNAME: ${{ secrets.ARTIFACT_REGISTRY_USERNAME }}
          DOCKER_PASSWORD: ${{ secrets.ARTIFACT_REGISTRY_PASSWORD }}
          K3S_VERSION: v1.31.3-k3s1
        run: |
          curl -s https://raw.githubusercontent.com/k3d-io/k3d/main/install.sh | bash
          cat >/tmp/registries.yaml <<EOL
          configs:
            us-docker.pkg.dev:
              auth:
                username: $DOCKER_USERNAME
                password: $DOCKER_PASSWORD
          EOL
          k3d cluster create --image rancher/k3s:$K3S_VERSION --registry-config /tmp/registries.yaml --volume /tmp/db:/mnt/data

      - name: Environment info
        run: |
          set  -x
          date
          kubectl version
          kubectl cluster-info
          kubectl config get-contexts
          git log --pretty=format:'%h - <%an> %d %s (%cr)' --abbrev-commit --date=relative -3
      - name: Setup Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: 1.24.4
          check-latest: true
          cache: false

      - name: Setup Tests Environment
        timeout-minutes: 5
        env:
          GITHUB_TOKEN: ${{ secrets.AKUITYBOT_PAT }}
          DOCKER_USERNAME: ${{ secrets.ARTIFACT_REGISTRY_USERNAME }}
          DOCKER_PASSWORD: ${{ secrets.ARTIFACT_REGISTRY_PASSWORD }}
          PGPASSWORD: dbpassword
          LOGS_DIR: /tmp/logs
        run: |
          set -x
          sudo apt-get install --yes postgresql-client
          sudo K14SIO_INSTALL_BIN_DIR=/usr/bin GH_TOKEN=$GITHUB_TOKEN ./hack/download-ytt-ci.sh 0.50.0
          go install github.com/mattn/goreman@latest

          # https://www.telepresence.io/docs/latest/quick-start/?os=gnu-linux
          curl -fL https://app.getambassador.io/download/tel2oss/releases/download/v2.16.1/telepresence-linux-amd64 -o /usr/local/bin/telepresence
          chmod a+x /usr/local/bin/telepresence
          telepresence helm install
          until telepresence connect; do sleep 1; echo "telepresence connect .."; done

          make dev-setup dev-env DEV_DB_SCHEMA=upgrade LIQUIBASE_IMAGE=quay.io/akuity/liquibase:4.29
          until pg_isready --host postgres.akuity-platform --user postgres; do sleep 1; echo waiting for db hosts; done
          VERSION=v99999 make validate-dev-env

          mkdir -p $LOGS_DIR
          export AKP_BINARY=dist/akuity-platform
          chmod +x ./dist/*
          goreman start portal-server 2>&1 >> $LOGS_DIR/portal-server.log &
          until curl -f http://localhost:9090/api/healthz; do sleep 1; echo waiting for portal server; done
      - name: E2E & functional tests
        timeout-minutes: 20
        env:
          VIDEO_DIR: /tmp/videos
          LOGS_DIR: /tmp/logs
          GITHUB_USERNAME: akuitybot
          GITHUB_TOKEN: ${{ secrets.AKUITYBOT_PAT }}
          DOCKER_USERNAME: ${{ secrets.ARTIFACT_REGISTRY_USERNAME }}
          DOCKER_PASSWORD: ${{ secrets.ARTIFACT_REGISTRY_PASSWORD }}
          AKP_BINARY: ./dist/akuity-platform
          KARGO_INSTANCE_VERSION: latest
        run: |
          mkdir -p $LOGS_DIR
          if [ ${{ matrix.type }} == 'functional' ]; then
            for binary in ./dist/functional-*.test; do
              "$binary" -test.v -test.parallel 1 -test.coverprofile=coverage.out
            done
          else
            goreman start platform-controller 2>&1 >> $LOGS_DIR/platform-controller.log &
            kubectl get secret/akuity-pullsecrets -n akuity-platform -o jsonpath="{.data.\.dockerconfigjson}" | base64 --decode > /tmp/akuity-pullsecrets.json
            AKUITY_PULLSECRETS=/tmp/akuity-pullsecrets.json ./dist/e2e.test -test.coverprofile=coverage.out -test.count=1 -test.timeout 20m -test.v
          fi

      - name: Debug Failure
        if: failure()
        run: |
          set -x
          kubectl get pods --all-namespaces
          kubectl get secrets -A
          ls /tmp/db
          ./hack/log-dump.sh
          set +x
      - name: Upload Platform logs
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        if: failure()
        with:
          name: e2e-cli-platform-logs
          path: /tmp/logs
          if-no-files-found: error
      - name: Upload E2E Videos
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        if: failure()
        with:
          name: e2e-cli-ui-videos
          path: /tmp/videos/*
      - name: save coverage results
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: go-coverage-results-${{ matrix.type }}
          path: coverage.out

  run-security-scans:
    name: Run security scans
    runs-on: ubuntu-latest
    timeout-minutes: 15
    if: ${{ always() }}
    needs: [e2e-tests, build-ui]
    steps:
      - name: Checkout code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          # Disabling shallow clone is recommended for improving relevancy of reporting
          fetch-depth: 0
      - name: Download coverage results
        uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        if: ${{ needs.changes.outputs.backend == 'true' || needs.changes.outputs.frontend == 'true' }}
        with:
          name: go-coverage-results-e2e
      - name: Download coverage results
        uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        if: ${{ needs.changes.outputs.backend == 'true' || needs.changes.outputs.frontend == 'true' }}
        with:
          name: go-coverage-results-functional
      - name: Download JavaScript coverage results
        uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        if: ${{ needs.changes.outputs.frontend == 'true' || github.event_name == 'push' }}
        with:
          name: js-coverage-results
          path: portal/ui/coverage
      - name: SonarCloud Scan
        uses: SonarSource/sonarqube-scan-action@2500896589ef8f7247069a56136f8dc177c27ccf # v5.2.0
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          GITHUB_TOKEN: ${{ secrets.AKUITYBOT_PAT }}

  helm-docs-check:
    name: Verify Helm Documentation Generation
    runs-on: ubuntu-latest
    timeout-minutes: 5
    if: ${{ needs.changes.outputs.helm == 'true' }}
    needs: changes
    steps:
      - name: Checkout code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Install readme-generator-for-helm
        run: npm install -g @bitnami/readme-generator-for-helm
      - name: Generate self-hosted docs
        run: readme-generator --values ${PWD}/charts/akuity-platform/values.yaml --config ${PWD}/hack/selfhosted-docs/readme-generator-config.json --readme ${PWD}/charts/akuity-platform/README.md
