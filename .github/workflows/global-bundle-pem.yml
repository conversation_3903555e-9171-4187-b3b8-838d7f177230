name: Regenerate Global Bundle PEM
on:
  workflow_dispatch: # manually trigger the workflow
  schedule:
    - cron: '0 0 * * 0' # every sunday at midnight

jobs:
  regenerate-global-bundle-pem:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Platform Repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Regenerate Global Bundle PEM
        run: |
          make generate-global-bundle
      - name: Check if there are any changes
        id: check-changes
        run: |
          if ! git diff --exit-code -- .; then
            echo "changes=true" >> $GITHUB_OUTPUT
          else
            echo "changes=false" >> $GITHUB_OUTPUT
          fi
      - name: Create Pull Request
        uses: peter-evans/create-pull-request@271a8d0340265f705b14b6d32b9829c1cb33d45e # v7.0.8
        if: steps.check-changes.outputs.changes == 'true'
        with:
          commit-message: "chore: regenerate global bundle pem"
          title: "chore: regenerate global bundle pem"
          body: "Regenerate global bundle pem"
          branch: akuity-bot/regenerate-global-bundle-pem
          delete-branch: true
          token: ${{ secrets.AKUITYBOT_PAT }}
          author: Aku<PERSON> Bot <<EMAIL>>
