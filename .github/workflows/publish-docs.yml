name: Publish docs
on:
  repository_dispatch:
    types: [trigger-publish-docs]
  workflow_dispatch: # manually trigger the workflow

jobs:
  publish-selfhosted-docs:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Platform Repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          path: akuity-platform
      - name: Checkout Docs Repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          repository: akuityio/docs.akuity.io
          token: ${{ secrets.AKUITYBOT_PAT }}
          path: docs
      - name: Install readme-generator-for-helm
        run: npm install -g @bitnami/readme-generator-for-helm
      - name: Login to Google Artifact Registry
        if: github.event_name != 'pull_request'
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        with:
          registry: us-docker.pkg.dev
          username: ${{ secrets.ARTIFACT_REGISTRY_USERNAME }}
          password: ${{ secrets.ARTIFACT_REGISTRY_PASSWORD }}
      - name: Setup private git repo access
        env:
          GITHUB_TOKEN: ${{ secrets.AKUITYBOT_PAT }}
        run: |
          /usr/bin/git config --global url."https://${GITHUB_TOKEN}:<EMAIL>/".insteadOf "https://github.com/"
      - name: Generate self-hosted docs
        working-directory: ./akuity-platform
        env:
          GITHUB_TOKEN: ${{ secrets.AKUITYBOT_PAT }}
          GH_TOKEN: ${{ secrets.AKUITYBOT_PAT }}
        run: |
          bash ./hack/selfhosted-docs/selfhosted-docs.sh changelog ../docs/docs/60-changelog/30-selfhosted
          bash ./hack/selfhosted-docs/selfhosted-docs.sh chart-docs ../docs/docs/100-self-hosted/31-helm-values-reference.md
      - name: Create PR
        working-directory: ./docs
        env:
          GITHUB_TOKEN: ${{ secrets.AKUITYBOT_PAT }}
        run: |
          git config --global user.name "Akuity Bot"
          git config --global user.email "<EMAIL>"
          git checkout -b akuity-bot/self-hosted-docs-update
          git add .
          git commit -m "Update self-hosted docs"
          git push origin akuity-bot/self-hosted-docs-update --force
          gh pr create --title "Update self-hosted docs" --body "Update self-hosted docs"

  publish-selfhosted-images-list:
    runs-on: ubuntu-latest-4-cores
    timeout-minutes: 15
    steps:
      - name: Checkout code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Setup K3S
        env:
          DOCKER_USERNAME: ${{ secrets.ARTIFACT_REGISTRY_USERNAME }}
          DOCKER_PASSWORD: ${{ secrets.ARTIFACT_REGISTRY_PASSWORD }}
          K3S_VERSION: v1.31.3-k3s1
        run: |
          curl -s https://raw.githubusercontent.com/k3d-io/k3d/main/install.sh | bash
          cat >/tmp/registries.yaml <<EOL
          configs:
            us-docker.pkg.dev:
              auth:
                username: $DOCKER_USERNAME
                password: $DOCKER_PASSWORD
          EOL
          k3d cluster create --image rancher/k3s:$K3S_VERSION --registry-config /tmp/registries.yaml

      - name: Environment info
        run: |
          set  -x
          date
          kubectl version
          kubectl cluster-info
          kubectl config get-contexts
          git log --pretty=format:'%h - <%an> %d %s (%cr)' --abbrev-commit --date=relative -3

      - name: Setup Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: 1.24.4
          check-latest: true
          cache: false

      - name: Parse go.mod to get agent short commit
        id: parse_agent
        run: |
          line=$(grep 'github.com/akuityio/agent' go.mod)
          echo "Found line: $line"
          if echo "$line" | grep -qE -- '-[a-f0-9]{12,}$'; then
            short_sha=$(echo "$line" | sed -E 's/.*-([a-f0-9]{12,})$/\1/')
          else
            # Use the version part (e.g., "v0.5.54")
            short_sha=$(echo "$line" | awk '{print $2}')
          fi
          echo "agent_commit=$short_sha" >> "$GITHUB_OUTPUT"
          echo "Extracted commit: $short_sha"

      - name: Setup private git repo access
        env:
          GITHUB_TOKEN: ${{ secrets.AKUITYBOT_PAT }}
        run: |
          /usr/bin/git config --global url."https://${GITHUB_TOKEN}:<EMAIL>/".insteadOf "https://github.com/"

      - name: Clone Agent Repo
        env:
          GITHUB_TOKEN: ${{ secrets.AKUITYBOT_PAT }}
        run: |
          git clone "https://bot:$<EMAIL>/akuityio/agent.git" ../agent

      - name: Clone Docs Repo
        env:
          GITHUB_TOKEN: ${{ secrets.AKUITYBOT_PAT }}
        run: |
          git clone "https://bot:$<EMAIL>/akuityio/docs.akuity.io.git" ../docs.akuity.io

      - name: Resolve full SHA
        id: resolve_sha
        run: |
          cd ../agent
          git fetch --all --tags
          full_sha=$(git rev-parse ${{ steps.parse_agent.outputs.agent_commit }})
          echo "full_sha=$full_sha" >> $GITHUB_OUTPUT
          git checkout $full_sha

      - name: Generate Manifests
        env:
          DOCKER_USERNAME: ${{ secrets.ARTIFACT_REGISTRY_USERNAME }}
          DOCKER_PASSWORD: ${{ secrets.ARTIFACT_REGISTRY_PASSWORD }}
          GITHUB_TOKEN: ${{ secrets.AKUITYBOT_PAT }}
        run: |
          set -x
          sudo K14SIO_INSTALL_BIN_DIR=/usr/bin GH_TOKEN=$GITHUB_TOKEN ./hack/download-ytt-ci.sh 0.50.0
          make dev-setup
          cd ../agent
          make install-toolchain
          make codegen
          sudo snap install yq

          cd ../akuity-platform
          versions=$(go run ./hack/selfhosted-docs/main.go --export-argocd-versions)
          # Convert comma-separated list into an array:
          IFS=',' read -r -a versions_array <<< "$versions"

          cd ../agent
          for v in "${versions_array[@]}"; do
            echo "Generating manifests for ArgoCD version: $v"
            yq eval ".argo_cd.version = \"${v}\"" -i examples/dev/cplane/values.yaml
            yq eval ".k3s.postgres.ro_hostname = \"postgres\"" -i examples/dev/cplane/values.yaml
            go run cmd/agentctl/main.go argocd tenant manifests --data-value-file ./examples/dev/cplane/values.yaml > argocd-manifests-"$v".yaml
          done

          cd ../akuity-platform
          kargo_versions=$(go run ./hack/selfhosted-docs/main.go --export-kargo-versions)
          IFS=',' read -r -a kargo_versions_array <<< "$kargo_versions"

          cd ../agent
          for v in "${kargo_versions_array[@]}"; do
            echo "Generating manifests for Kargo version: $v"
            yq eval ".kargo.version = \"${v}\"" -i examples/dev/kargo/cplane/values.yaml
            go run cmd/agentctl/main.go kargo tenant manifests --data-value-file ./examples/dev/kargo/cplane/values.yaml > kargo-manifests-"$v".yaml
          done

      - name: Generate images list
        env:
          GITHUB_TOKEN: ${{ secrets.AKUITYBOT_PAT }}
        run: |
          # Get the latest release version from AKP
          latest_release=$(curl -s -H "Authorization: Bearer $GITHUB_TOKEN" https://api.github.com/repos/akuityio/akuity-platform/releases/latest | jq -r .tag_name)
          go run ./hack/selfhosted-docs/main.go --akp-version "$latest_release" ../agent/argocd-manifests-*.yaml ../agent/kargo-manifests-*.yaml
          ls -la

      - name: Create PR
        env:
          GITHUB_TOKEN: ${{ secrets.AKUITYBOT_PAT }}
        run: |
          cd ../docs.akuity.io
          git config --global user.name "Akuity Bot"
          git config --global user.email "<EMAIL>"
          git checkout -b akuity-bot/self-hosted-images-list-update
          mv ../akuity-platform/30-images-list.md ./docs/100-self-hosted/30-images-list.md
          git add .
          git commit -m "Update self-hosted images list"
          git push origin akuity-bot/self-hosted-images-list-update --force
          gh pr create --title "Update self-hosted images list" --body "Update self-hosted images list"
