# Documentation: https://github.com/BirthdayResearch/oss-governance-bot
name: Governance

on:
  pull_request_target:
    types: [ synchronize, opened, labeled, unlabeled ]
  issues:
    types: [ opened, labeled, unlabeled ]
  issue_comment:
    types: [ created ]
    
# PR updates can happen in quick succession leading to this
# workflow being trigger a number of times. This limits it
# to one concurrent run per PR.
concurrency:
  group: '${{ github.workflow }}-${{ github.event.pull_request.number || github.event.issue.number}}'
  cancel-in-progress: true

jobs:
  governance:
    name: Governance
    runs-on: self-hosted
    steps:
      - uses: BirthdayResearch/oss-governance-bot@3abd2d1fd2376ba9990fbc795e7a4c54254e9c61 # v4.0.0
