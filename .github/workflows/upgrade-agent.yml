name: Upgrade Agent
on:
  workflow_dispatch:
    inputs:
      message:
        description: One liner to use in commit message
        required: true
        type: string

jobs:
  upgrade-agent:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Setup Private Git Repo Access
        run: |
          /usr/bin/git config --global url."https://${{ secrets.AKUITYBOT_PAT }}:<EMAIL>/".insteadOf "https://github.com/"
          go env -w GOPRIVATE=github.com/akuityio/agent
      - name: Setup Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: 1.24.4
          check-latest: true
          cache: false
      - name: Cache Go Modules
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-
      - name: Upgrade Agent
        env:
          GH_TOKEN: ${{ secrets.AKUITYBOT_PAT }}
        run: |
          go get github.com/akuityio/agent@$(gh api "repos/akuityio/agent/commits/master" --template "{{.sha}}")
          go mod tidy
      - name: Create Pull Request
        uses: peter-evans/create-pull-request@271a8d0340265f705b14b6d32b9829c1cb33d45e # v7.0.8
        with:
          commit-message: "chore: upgrade agent: ${{ inputs.message }}"
          title: "chore: upgrade agent: ${{ inputs.message }}"
          body: "Upgrade agent: ${{ inputs.message }}"
          branch: akuity-bot/upgrade-agent
          delete-branch: true
          token: ${{ secrets.AKUITYBOT_PAT }}
          author: Akuity Bot <<EMAIL>>
