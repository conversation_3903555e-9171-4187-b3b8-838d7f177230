pretext: Triggered via {{eventName}} by {{actor}} {{or action "action"}} on "{{ref}}" (`{{diffRef}}`)

text: |
  {{workflow}} run `#{{runNumber}}` is a <{{workflowRunUrl}}|*{{jobStatus}}*> (<https://github.com/akuityio/akuity-platform/actions/workflows/canary-test.yml|Test>, <https://github.com/akuityio/akuity-platform/actions/workflows/canary-stage.yml|Stage>, <https://github.com/akuityio/akuity-platform/actions/workflows/canary-prod.yml|Prod> runs)
  {{#if payload.commits}}
  Commits:
  {{#each payload.commits}}
  <{{this.url}}|`{{truncate this.id 8}}`> - {{this.message}}
  {{/each}}
  {{/if}}

footer: >-
  <{{repositoryUrl}}|{{repositoryName}}> {{workflow}} <{{workflowRunUrl}}|#{{runNumber}}>

fallback: |-
  [GitHub] {{workflow}} #{{runNumber}} {{jobName}} is {{jobStatus}}

colors:
  success: '#5DADE2'
  failure: '#884EA0'
  cancelled: '#A569BD'
  default: '#7D3C98'

icons:
  success: ':white_check_mark:'
  failure: ':grimacing:'
  cancelled: ':x:'
  skipped: ':heavy_minus_sign:'
  default: ':interrobang:'
