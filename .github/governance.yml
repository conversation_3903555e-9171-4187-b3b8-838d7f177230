# Documentation: https://github.com/BirthdayResearch/oss-governance-bot
version: v1

issue:
  labels:
    - prefix: kind
      list:
        - feat
        - bug
        - customer
      multiple: true
      needs: true

    - prefix: priority
      list:
        - urgent
        - high
        - normal
        - low
      multiple: false
      needs: true

    - prefix: area
      list:
        # generic areas
        - chore
        - devx
        - docs
        - perf
        - regression
        - security
        - tech-debt
        - test
        - usability
        # product specific areas
        - agent
        - billing
        - cd
        - cli
        - events
        - infra
        - marketing
        - models
        - platform
        - portal
        - portal-ui
        - rollouts
        - self-hosted
        - workflows
      multiple: true
      needs: true
