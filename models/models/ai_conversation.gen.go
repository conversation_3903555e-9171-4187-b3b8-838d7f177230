// Code generated by SQLBoiler 4.16.2 (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package models

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// AiConversation is an object representing the database table.
type AiConversation struct {
	ID                  string      `boil:"id" json:"id" toml:"id" yaml:"id"`
	OrganizationID      string      `boil:"organization_id" json:"organization_id" toml:"organization_id" yaml:"organization_id"`
	UserID              string      `boil:"user_id" json:"user_id" toml:"user_id" yaml:"user_id"`
	CreationTimestamp   time.Time   `boil:"creation_timestamp" json:"creation_timestamp" toml:"creation_timestamp" yaml:"creation_timestamp"`
	LastUpdateTimestamp time.Time   `boil:"last_update_timestamp" json:"last_update_timestamp" toml:"last_update_timestamp" yaml:"last_update_timestamp"`
	Title               null.String `boil:"title" json:"title,omitempty" toml:"title" yaml:"title,omitempty"`
	Messages            null.JSON   `boil:"messages" json:"messages,omitempty" toml:"messages" yaml:"messages,omitempty"`
	InstanceID          null.String `boil:"instance_id" json:"instance_id,omitempty" toml:"instance_id" yaml:"instance_id,omitempty"`
	Contexts            null.JSON   `boil:"contexts" json:"contexts,omitempty" toml:"contexts" yaml:"contexts,omitempty"`
	Metadata            null.JSON   `boil:"metadata" json:"metadata,omitempty" toml:"metadata" yaml:"metadata,omitempty"`
	KargoInstanceID     null.String `boil:"kargo_instance_id" json:"kargo_instance_id,omitempty" toml:"kargo_instance_id" yaml:"kargo_instance_id,omitempty"`
	Public              null.Bool   `boil:"public" json:"public,omitempty" toml:"public" yaml:"public,omitempty"`
	Feedbacks           null.JSON   `boil:"feedbacks" json:"feedbacks,omitempty" toml:"feedbacks" yaml:"feedbacks,omitempty"`
	Runbooks            null.JSON   `boil:"runbooks" json:"runbooks,omitempty" toml:"runbooks" yaml:"runbooks,omitempty"`

	R *aiConversationR `boil:"-" json:"-" toml:"-" yaml:"-"`
	L aiConversationL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var AiConversationColumns = struct {
	ID                  string
	OrganizationID      string
	UserID              string
	CreationTimestamp   string
	LastUpdateTimestamp string
	Title               string
	Messages            string
	InstanceID          string
	Contexts            string
	Metadata            string
	KargoInstanceID     string
	Public              string
	Feedbacks           string
	Runbooks            string
}{
	ID:                  "id",
	OrganizationID:      "organization_id",
	UserID:              "user_id",
	CreationTimestamp:   "creation_timestamp",
	LastUpdateTimestamp: "last_update_timestamp",
	Title:               "title",
	Messages:            "messages",
	InstanceID:          "instance_id",
	Contexts:            "contexts",
	Metadata:            "metadata",
	KargoInstanceID:     "kargo_instance_id",
	Public:              "public",
	Feedbacks:           "feedbacks",
	Runbooks:            "runbooks",
}

var AiConversationTableColumns = struct {
	ID                  string
	OrganizationID      string
	UserID              string
	CreationTimestamp   string
	LastUpdateTimestamp string
	Title               string
	Messages            string
	InstanceID          string
	Contexts            string
	Metadata            string
	KargoInstanceID     string
	Public              string
	Feedbacks           string
	Runbooks            string
}{
	ID:                  "ai_conversation.id",
	OrganizationID:      "ai_conversation.organization_id",
	UserID:              "ai_conversation.user_id",
	CreationTimestamp:   "ai_conversation.creation_timestamp",
	LastUpdateTimestamp: "ai_conversation.last_update_timestamp",
	Title:               "ai_conversation.title",
	Messages:            "ai_conversation.messages",
	InstanceID:          "ai_conversation.instance_id",
	Contexts:            "ai_conversation.contexts",
	Metadata:            "ai_conversation.metadata",
	KargoInstanceID:     "ai_conversation.kargo_instance_id",
	Public:              "ai_conversation.public",
	Feedbacks:           "ai_conversation.feedbacks",
	Runbooks:            "ai_conversation.runbooks",
}

// Generated where

type whereHelpertime_Time struct{ field string }

func (w whereHelpertime_Time) EQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.EQ, x)
}
func (w whereHelpertime_Time) NEQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelpertime_Time) LT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertime_Time) LTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertime_Time) GT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertime_Time) GTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

var AiConversationWhere = struct {
	ID                  whereHelperstring
	OrganizationID      whereHelperstring
	UserID              whereHelperstring
	CreationTimestamp   whereHelpertime_Time
	LastUpdateTimestamp whereHelpertime_Time
	Title               whereHelpernull_String
	Messages            whereHelpernull_JSON
	InstanceID          whereHelpernull_String
	Contexts            whereHelpernull_JSON
	Metadata            whereHelpernull_JSON
	KargoInstanceID     whereHelpernull_String
	Public              whereHelpernull_Bool
	Feedbacks           whereHelpernull_JSON
	Runbooks            whereHelpernull_JSON
}{
	ID:                  whereHelperstring{field: "\"ai_conversation\".\"id\""},
	OrganizationID:      whereHelperstring{field: "\"ai_conversation\".\"organization_id\""},
	UserID:              whereHelperstring{field: "\"ai_conversation\".\"user_id\""},
	CreationTimestamp:   whereHelpertime_Time{field: "\"ai_conversation\".\"creation_timestamp\""},
	LastUpdateTimestamp: whereHelpertime_Time{field: "\"ai_conversation\".\"last_update_timestamp\""},
	Title:               whereHelpernull_String{field: "\"ai_conversation\".\"title\""},
	Messages:            whereHelpernull_JSON{field: "\"ai_conversation\".\"messages\""},
	InstanceID:          whereHelpernull_String{field: "\"ai_conversation\".\"instance_id\""},
	Contexts:            whereHelpernull_JSON{field: "\"ai_conversation\".\"contexts\""},
	Metadata:            whereHelpernull_JSON{field: "\"ai_conversation\".\"metadata\""},
	KargoInstanceID:     whereHelpernull_String{field: "\"ai_conversation\".\"kargo_instance_id\""},
	Public:              whereHelpernull_Bool{field: "\"ai_conversation\".\"public\""},
	Feedbacks:           whereHelpernull_JSON{field: "\"ai_conversation\".\"feedbacks\""},
	Runbooks:            whereHelpernull_JSON{field: "\"ai_conversation\".\"runbooks\""},
}

// AiConversationRels is where relationship names are stored.
var AiConversationRels = struct {
	Instance      string
	KargoInstance string
	Organization  string
}{
	Instance:      "Instance",
	KargoInstance: "KargoInstance",
	Organization:  "Organization",
}

// aiConversationR is where relationships are stored.
type aiConversationR struct {
	Instance      *ArgoCDInstance `boil:"Instance" json:"Instance" toml:"Instance" yaml:"Instance"`
	KargoInstance *KargoInstance  `boil:"KargoInstance" json:"KargoInstance" toml:"KargoInstance" yaml:"KargoInstance"`
	Organization  *Organization   `boil:"Organization" json:"Organization" toml:"Organization" yaml:"Organization"`
}

// NewStruct creates a new relationship struct
func (*aiConversationR) NewStruct() *aiConversationR {
	return &aiConversationR{}
}

func (r *aiConversationR) GetInstance() *ArgoCDInstance {
	if r == nil {
		return nil
	}
	return r.Instance
}

func (r *aiConversationR) GetKargoInstance() *KargoInstance {
	if r == nil {
		return nil
	}
	return r.KargoInstance
}

func (r *aiConversationR) GetOrganization() *Organization {
	if r == nil {
		return nil
	}
	return r.Organization
}

// aiConversationL is where Load methods for each relationship are stored.
type aiConversationL struct{}

var (
	aiConversationAllColumns            = []string{"id", "organization_id", "user_id", "creation_timestamp", "last_update_timestamp", "title", "messages", "instance_id", "contexts", "metadata", "kargo_instance_id", "public", "feedbacks", "runbooks"}
	aiConversationColumnsWithoutDefault = []string{"id", "organization_id", "user_id"}
	aiConversationColumnsWithDefault    = []string{"creation_timestamp", "last_update_timestamp", "title", "messages", "instance_id", "contexts", "metadata", "kargo_instance_id", "public", "feedbacks", "runbooks"}
	aiConversationPrimaryKeyColumns     = []string{"id"}
	aiConversationGeneratedColumns      = []string{}
)

type (
	// AiConversationSlice is an alias for a slice of pointers to AiConversation.
	// This should almost always be used instead of []AiConversation.
	AiConversationSlice []*AiConversation
	// AiConversationHook is the signature for custom AiConversation hook methods
	AiConversationHook func(context.Context, boil.ContextExecutor, *AiConversation) error

	aiConversationQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	aiConversationType                 = reflect.TypeOf(&AiConversation{})
	aiConversationMapping              = queries.MakeStructMapping(aiConversationType)
	aiConversationPrimaryKeyMapping, _ = queries.BindMapping(aiConversationType, aiConversationMapping, aiConversationPrimaryKeyColumns)
	aiConversationInsertCacheMut       sync.RWMutex
	aiConversationInsertCache          = make(map[string]insertCache)
	aiConversationUpdateCacheMut       sync.RWMutex
	aiConversationUpdateCache          = make(map[string]updateCache)
	aiConversationUpsertCacheMut       sync.RWMutex
	aiConversationUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var aiConversationAfterSelectMu sync.Mutex
var aiConversationAfterSelectHooks []AiConversationHook

var aiConversationBeforeInsertMu sync.Mutex
var aiConversationBeforeInsertHooks []AiConversationHook
var aiConversationAfterInsertMu sync.Mutex
var aiConversationAfterInsertHooks []AiConversationHook

var aiConversationBeforeUpdateMu sync.Mutex
var aiConversationBeforeUpdateHooks []AiConversationHook
var aiConversationAfterUpdateMu sync.Mutex
var aiConversationAfterUpdateHooks []AiConversationHook

var aiConversationBeforeDeleteMu sync.Mutex
var aiConversationBeforeDeleteHooks []AiConversationHook
var aiConversationAfterDeleteMu sync.Mutex
var aiConversationAfterDeleteHooks []AiConversationHook

var aiConversationBeforeUpsertMu sync.Mutex
var aiConversationBeforeUpsertHooks []AiConversationHook
var aiConversationAfterUpsertMu sync.Mutex
var aiConversationAfterUpsertHooks []AiConversationHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *AiConversation) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range aiConversationAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *AiConversation) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range aiConversationBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *AiConversation) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range aiConversationAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *AiConversation) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range aiConversationBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *AiConversation) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range aiConversationAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *AiConversation) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range aiConversationBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *AiConversation) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range aiConversationAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *AiConversation) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range aiConversationBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *AiConversation) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range aiConversationAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddAiConversationHook registers your hook function for all future operations.
func AddAiConversationHook(hookPoint boil.HookPoint, aiConversationHook AiConversationHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		aiConversationAfterSelectMu.Lock()
		aiConversationAfterSelectHooks = append(aiConversationAfterSelectHooks, aiConversationHook)
		aiConversationAfterSelectMu.Unlock()
	case boil.BeforeInsertHook:
		aiConversationBeforeInsertMu.Lock()
		aiConversationBeforeInsertHooks = append(aiConversationBeforeInsertHooks, aiConversationHook)
		aiConversationBeforeInsertMu.Unlock()
	case boil.AfterInsertHook:
		aiConversationAfterInsertMu.Lock()
		aiConversationAfterInsertHooks = append(aiConversationAfterInsertHooks, aiConversationHook)
		aiConversationAfterInsertMu.Unlock()
	case boil.BeforeUpdateHook:
		aiConversationBeforeUpdateMu.Lock()
		aiConversationBeforeUpdateHooks = append(aiConversationBeforeUpdateHooks, aiConversationHook)
		aiConversationBeforeUpdateMu.Unlock()
	case boil.AfterUpdateHook:
		aiConversationAfterUpdateMu.Lock()
		aiConversationAfterUpdateHooks = append(aiConversationAfterUpdateHooks, aiConversationHook)
		aiConversationAfterUpdateMu.Unlock()
	case boil.BeforeDeleteHook:
		aiConversationBeforeDeleteMu.Lock()
		aiConversationBeforeDeleteHooks = append(aiConversationBeforeDeleteHooks, aiConversationHook)
		aiConversationBeforeDeleteMu.Unlock()
	case boil.AfterDeleteHook:
		aiConversationAfterDeleteMu.Lock()
		aiConversationAfterDeleteHooks = append(aiConversationAfterDeleteHooks, aiConversationHook)
		aiConversationAfterDeleteMu.Unlock()
	case boil.BeforeUpsertHook:
		aiConversationBeforeUpsertMu.Lock()
		aiConversationBeforeUpsertHooks = append(aiConversationBeforeUpsertHooks, aiConversationHook)
		aiConversationBeforeUpsertMu.Unlock()
	case boil.AfterUpsertHook:
		aiConversationAfterUpsertMu.Lock()
		aiConversationAfterUpsertHooks = append(aiConversationAfterUpsertHooks, aiConversationHook)
		aiConversationAfterUpsertMu.Unlock()
	}
}

// One returns a single aiConversation record from the query.
func (q aiConversationQuery) One(ctx context.Context, exec boil.ContextExecutor) (*AiConversation, error) {
	o := &AiConversation{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "models: failed to execute a one query for ai_conversation")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all AiConversation records from the query.
func (q aiConversationQuery) All(ctx context.Context, exec boil.ContextExecutor) (AiConversationSlice, error) {
	var o []*AiConversation

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "models: failed to assign all query results to AiConversation slice")
	}

	if len(aiConversationAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all AiConversation records in the query.
func (q aiConversationQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "models: failed to count ai_conversation rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q aiConversationQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "models: failed to check if ai_conversation exists")
	}

	return count > 0, nil
}

// Instance pointed to by the foreign key.
func (o *AiConversation) Instance(mods ...qm.QueryMod) argoCDInstanceQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.InstanceID),
	}

	queryMods = append(queryMods, mods...)

	return ArgoCDInstances(queryMods...)
}

// KargoInstance pointed to by the foreign key.
func (o *AiConversation) KargoInstance(mods ...qm.QueryMod) kargoInstanceQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.KargoInstanceID),
	}

	queryMods = append(queryMods, mods...)

	return KargoInstances(queryMods...)
}

// Organization pointed to by the foreign key.
func (o *AiConversation) Organization(mods ...qm.QueryMod) organizationQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.OrganizationID),
	}

	queryMods = append(queryMods, mods...)

	return Organizations(queryMods...)
}

// LoadInstance allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (aiConversationL) LoadInstance(ctx context.Context, e boil.ContextExecutor, singular bool, maybeAiConversation interface{}, mods queries.Applicator) error {
	var slice []*AiConversation
	var object *AiConversation

	if singular {
		var ok bool
		object, ok = maybeAiConversation.(*AiConversation)
		if !ok {
			object = new(AiConversation)
			ok = queries.SetFromEmbeddedStruct(&object, &maybeAiConversation)
			if !ok {
				return errors.New(fmt.Sprintf("failed to set %T from embedded struct %T", object, maybeAiConversation))
			}
		}
	} else {
		s, ok := maybeAiConversation.(*[]*AiConversation)
		if ok {
			slice = *s
		} else {
			ok = queries.SetFromEmbeddedStruct(&slice, maybeAiConversation)
			if !ok {
				return errors.New(fmt.Sprintf("failed to set %T from embedded struct %T", slice, maybeAiConversation))
			}
		}
	}

	args := make(map[interface{}]struct{})
	if singular {
		if object.R == nil {
			object.R = &aiConversationR{}
		}
		if !queries.IsNil(object.InstanceID) {
			args[object.InstanceID] = struct{}{}
		}

	} else {
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &aiConversationR{}
			}

			if !queries.IsNil(obj.InstanceID) {
				args[obj.InstanceID] = struct{}{}
			}

		}
	}

	if len(args) == 0 {
		return nil
	}

	argsSlice := make([]interface{}, len(args))
	i := 0
	for arg := range args {
		argsSlice[i] = arg
		i++
	}

	query := NewQuery(
		qm.From(`argo_cd_instance`),
		qm.WhereIn(`argo_cd_instance.id in ?`, argsSlice...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load ArgoCDInstance")
	}

	var resultSlice []*ArgoCDInstance
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice ArgoCDInstance")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for argo_cd_instance")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for argo_cd_instance")
	}

	if len(argoCDInstanceAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.Instance = foreign
		if foreign.R == nil {
			foreign.R = &argoCDInstanceR{}
		}
		foreign.R.InstanceAiConversations = append(foreign.R.InstanceAiConversations, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if queries.Equal(local.InstanceID, foreign.ID) {
				local.R.Instance = foreign
				if foreign.R == nil {
					foreign.R = &argoCDInstanceR{}
				}
				foreign.R.InstanceAiConversations = append(foreign.R.InstanceAiConversations, local)
				break
			}
		}
	}

	return nil
}

// LoadKargoInstance allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (aiConversationL) LoadKargoInstance(ctx context.Context, e boil.ContextExecutor, singular bool, maybeAiConversation interface{}, mods queries.Applicator) error {
	var slice []*AiConversation
	var object *AiConversation

	if singular {
		var ok bool
		object, ok = maybeAiConversation.(*AiConversation)
		if !ok {
			object = new(AiConversation)
			ok = queries.SetFromEmbeddedStruct(&object, &maybeAiConversation)
			if !ok {
				return errors.New(fmt.Sprintf("failed to set %T from embedded struct %T", object, maybeAiConversation))
			}
		}
	} else {
		s, ok := maybeAiConversation.(*[]*AiConversation)
		if ok {
			slice = *s
		} else {
			ok = queries.SetFromEmbeddedStruct(&slice, maybeAiConversation)
			if !ok {
				return errors.New(fmt.Sprintf("failed to set %T from embedded struct %T", slice, maybeAiConversation))
			}
		}
	}

	args := make(map[interface{}]struct{})
	if singular {
		if object.R == nil {
			object.R = &aiConversationR{}
		}
		if !queries.IsNil(object.KargoInstanceID) {
			args[object.KargoInstanceID] = struct{}{}
		}

	} else {
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &aiConversationR{}
			}

			if !queries.IsNil(obj.KargoInstanceID) {
				args[obj.KargoInstanceID] = struct{}{}
			}

		}
	}

	if len(args) == 0 {
		return nil
	}

	argsSlice := make([]interface{}, len(args))
	i := 0
	for arg := range args {
		argsSlice[i] = arg
		i++
	}

	query := NewQuery(
		qm.From(`kargo_instance`),
		qm.WhereIn(`kargo_instance.id in ?`, argsSlice...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load KargoInstance")
	}

	var resultSlice []*KargoInstance
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice KargoInstance")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for kargo_instance")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for kargo_instance")
	}

	if len(kargoInstanceAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.KargoInstance = foreign
		if foreign.R == nil {
			foreign.R = &kargoInstanceR{}
		}
		foreign.R.AiConversations = append(foreign.R.AiConversations, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if queries.Equal(local.KargoInstanceID, foreign.ID) {
				local.R.KargoInstance = foreign
				if foreign.R == nil {
					foreign.R = &kargoInstanceR{}
				}
				foreign.R.AiConversations = append(foreign.R.AiConversations, local)
				break
			}
		}
	}

	return nil
}

// LoadOrganization allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (aiConversationL) LoadOrganization(ctx context.Context, e boil.ContextExecutor, singular bool, maybeAiConversation interface{}, mods queries.Applicator) error {
	var slice []*AiConversation
	var object *AiConversation

	if singular {
		var ok bool
		object, ok = maybeAiConversation.(*AiConversation)
		if !ok {
			object = new(AiConversation)
			ok = queries.SetFromEmbeddedStruct(&object, &maybeAiConversation)
			if !ok {
				return errors.New(fmt.Sprintf("failed to set %T from embedded struct %T", object, maybeAiConversation))
			}
		}
	} else {
		s, ok := maybeAiConversation.(*[]*AiConversation)
		if ok {
			slice = *s
		} else {
			ok = queries.SetFromEmbeddedStruct(&slice, maybeAiConversation)
			if !ok {
				return errors.New(fmt.Sprintf("failed to set %T from embedded struct %T", slice, maybeAiConversation))
			}
		}
	}

	args := make(map[interface{}]struct{})
	if singular {
		if object.R == nil {
			object.R = &aiConversationR{}
		}
		args[object.OrganizationID] = struct{}{}

	} else {
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &aiConversationR{}
			}

			args[obj.OrganizationID] = struct{}{}

		}
	}

	if len(args) == 0 {
		return nil
	}

	argsSlice := make([]interface{}, len(args))
	i := 0
	for arg := range args {
		argsSlice[i] = arg
		i++
	}

	query := NewQuery(
		qm.From(`organization`),
		qm.WhereIn(`organization.id in ?`, argsSlice...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Organization")
	}

	var resultSlice []*Organization
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Organization")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for organization")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for organization")
	}

	if len(organizationAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.Organization = foreign
		if foreign.R == nil {
			foreign.R = &organizationR{}
		}
		foreign.R.AiConversations = append(foreign.R.AiConversations, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.OrganizationID == foreign.ID {
				local.R.Organization = foreign
				if foreign.R == nil {
					foreign.R = &organizationR{}
				}
				foreign.R.AiConversations = append(foreign.R.AiConversations, local)
				break
			}
		}
	}

	return nil
}

// SetInstance of the aiConversation to the related item.
// Sets o.R.Instance to related.
// Adds o to related.R.InstanceAiConversations.
func (o *AiConversation) SetInstance(ctx context.Context, exec boil.ContextExecutor, insert bool, related *ArgoCDInstance) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"ai_conversation\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"instance_id"}),
		strmangle.WhereClause("\"", "\"", 2, aiConversationPrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	queries.Assign(&o.InstanceID, related.ID)
	if o.R == nil {
		o.R = &aiConversationR{
			Instance: related,
		}
	} else {
		o.R.Instance = related
	}

	if related.R == nil {
		related.R = &argoCDInstanceR{
			InstanceAiConversations: AiConversationSlice{o},
		}
	} else {
		related.R.InstanceAiConversations = append(related.R.InstanceAiConversations, o)
	}

	return nil
}

// RemoveInstance relationship.
// Sets o.R.Instance to nil.
// Removes o from all passed in related items' relationships struct.
func (o *AiConversation) RemoveInstance(ctx context.Context, exec boil.ContextExecutor, related *ArgoCDInstance) error {
	var err error

	queries.SetScanner(&o.InstanceID, nil)
	if _, err = o.Update(ctx, exec, boil.Whitelist("instance_id")); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	if o.R != nil {
		o.R.Instance = nil
	}
	if related == nil || related.R == nil {
		return nil
	}

	for i, ri := range related.R.InstanceAiConversations {
		if queries.Equal(o.InstanceID, ri.InstanceID) {
			continue
		}

		ln := len(related.R.InstanceAiConversations)
		if ln > 1 && i < ln-1 {
			related.R.InstanceAiConversations[i] = related.R.InstanceAiConversations[ln-1]
		}
		related.R.InstanceAiConversations = related.R.InstanceAiConversations[:ln-1]
		break
	}
	return nil
}

// SetKargoInstance of the aiConversation to the related item.
// Sets o.R.KargoInstance to related.
// Adds o to related.R.AiConversations.
func (o *AiConversation) SetKargoInstance(ctx context.Context, exec boil.ContextExecutor, insert bool, related *KargoInstance) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"ai_conversation\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"kargo_instance_id"}),
		strmangle.WhereClause("\"", "\"", 2, aiConversationPrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	queries.Assign(&o.KargoInstanceID, related.ID)
	if o.R == nil {
		o.R = &aiConversationR{
			KargoInstance: related,
		}
	} else {
		o.R.KargoInstance = related
	}

	if related.R == nil {
		related.R = &kargoInstanceR{
			AiConversations: AiConversationSlice{o},
		}
	} else {
		related.R.AiConversations = append(related.R.AiConversations, o)
	}

	return nil
}

// RemoveKargoInstance relationship.
// Sets o.R.KargoInstance to nil.
// Removes o from all passed in related items' relationships struct.
func (o *AiConversation) RemoveKargoInstance(ctx context.Context, exec boil.ContextExecutor, related *KargoInstance) error {
	var err error

	queries.SetScanner(&o.KargoInstanceID, nil)
	if _, err = o.Update(ctx, exec, boil.Whitelist("kargo_instance_id")); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	if o.R != nil {
		o.R.KargoInstance = nil
	}
	if related == nil || related.R == nil {
		return nil
	}

	for i, ri := range related.R.AiConversations {
		if queries.Equal(o.KargoInstanceID, ri.KargoInstanceID) {
			continue
		}

		ln := len(related.R.AiConversations)
		if ln > 1 && i < ln-1 {
			related.R.AiConversations[i] = related.R.AiConversations[ln-1]
		}
		related.R.AiConversations = related.R.AiConversations[:ln-1]
		break
	}
	return nil
}

// SetOrganization of the aiConversation to the related item.
// Sets o.R.Organization to related.
// Adds o to related.R.AiConversations.
func (o *AiConversation) SetOrganization(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Organization) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"ai_conversation\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"organization_id"}),
		strmangle.WhereClause("\"", "\"", 2, aiConversationPrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.OrganizationID = related.ID
	if o.R == nil {
		o.R = &aiConversationR{
			Organization: related,
		}
	} else {
		o.R.Organization = related
	}

	if related.R == nil {
		related.R = &organizationR{
			AiConversations: AiConversationSlice{o},
		}
	} else {
		related.R.AiConversations = append(related.R.AiConversations, o)
	}

	return nil
}

// AiConversations retrieves all the records using an executor.
func AiConversations(mods ...qm.QueryMod) aiConversationQuery {
	mods = append(mods, qm.From("\"ai_conversation\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"ai_conversation\".*"})
	}

	return aiConversationQuery{q}
}

// FindAiConversation retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindAiConversation(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*AiConversation, error) {
	aiConversationObj := &AiConversation{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"ai_conversation\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, aiConversationObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "models: unable to select from ai_conversation")
	}

	if err = aiConversationObj.doAfterSelectHooks(ctx, exec); err != nil {
		return aiConversationObj, err
	}

	return aiConversationObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *AiConversation) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("models: no ai_conversation provided for insertion")
	}

	var err error

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(aiConversationColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	aiConversationInsertCacheMut.RLock()
	cache, cached := aiConversationInsertCache[key]
	aiConversationInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			aiConversationAllColumns,
			aiConversationColumnsWithDefault,
			aiConversationColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(aiConversationType, aiConversationMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(aiConversationType, aiConversationMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"ai_conversation\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"ai_conversation\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "models: unable to insert into ai_conversation")
	}

	if !cached {
		aiConversationInsertCacheMut.Lock()
		aiConversationInsertCache[key] = cache
		aiConversationInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the AiConversation.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *AiConversation) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	aiConversationUpdateCacheMut.RLock()
	cache, cached := aiConversationUpdateCache[key]
	aiConversationUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			aiConversationAllColumns,
			aiConversationPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("models: unable to update ai_conversation, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"ai_conversation\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, aiConversationPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(aiConversationType, aiConversationMapping, append(wl, aiConversationPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "models: unable to update ai_conversation row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "models: failed to get rows affected by update for ai_conversation")
	}

	if !cached {
		aiConversationUpdateCacheMut.Lock()
		aiConversationUpdateCache[key] = cache
		aiConversationUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q aiConversationQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "models: unable to update all for ai_conversation")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "models: unable to retrieve rows affected for ai_conversation")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o AiConversationSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("models: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), aiConversationPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"ai_conversation\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, aiConversationPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "models: unable to update all in aiConversation slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "models: unable to retrieve rows affected all in update all aiConversation")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *AiConversation) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns, opts ...UpsertOptionFunc) error {
	if o == nil {
		return errors.New("models: no ai_conversation provided for upsert")
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(aiConversationColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	aiConversationUpsertCacheMut.RLock()
	cache, cached := aiConversationUpsertCache[key]
	aiConversationUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, _ := insertColumns.InsertColumnSet(
			aiConversationAllColumns,
			aiConversationColumnsWithDefault,
			aiConversationColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			aiConversationAllColumns,
			aiConversationPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("models: unable to upsert ai_conversation, could not build update column list")
		}

		ret := strmangle.SetComplement(aiConversationAllColumns, strmangle.SetIntersect(insert, update))

		conflict := conflictColumns
		if len(conflict) == 0 && updateOnConflict && len(update) != 0 {
			if len(aiConversationPrimaryKeyColumns) == 0 {
				return errors.New("models: unable to upsert ai_conversation, could not build conflict column list")
			}

			conflict = make([]string, len(aiConversationPrimaryKeyColumns))
			copy(conflict, aiConversationPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"ai_conversation\"", updateOnConflict, ret, update, conflict, insert, opts...)

		cache.valueMapping, err = queries.BindMapping(aiConversationType, aiConversationMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(aiConversationType, aiConversationMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "models: unable to upsert ai_conversation")
	}

	if !cached {
		aiConversationUpsertCacheMut.Lock()
		aiConversationUpsertCache[key] = cache
		aiConversationUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single AiConversation record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *AiConversation) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("models: no AiConversation provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), aiConversationPrimaryKeyMapping)
	sql := "DELETE FROM \"ai_conversation\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "models: unable to delete from ai_conversation")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "models: failed to get rows affected by delete for ai_conversation")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q aiConversationQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("models: no aiConversationQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "models: unable to delete all from ai_conversation")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "models: failed to get rows affected by deleteall for ai_conversation")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o AiConversationSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(aiConversationBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), aiConversationPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"ai_conversation\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, aiConversationPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "models: unable to delete all from aiConversation slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "models: failed to get rows affected by deleteall for ai_conversation")
	}

	if len(aiConversationAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *AiConversation) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindAiConversation(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *AiConversationSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := AiConversationSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), aiConversationPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"ai_conversation\".* FROM \"ai_conversation\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, aiConversationPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "models: unable to reload all in AiConversationSlice")
	}

	*o = slice

	return nil
}

// AiConversationExists checks if the AiConversation row exists.
func AiConversationExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"ai_conversation\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "models: unable to check if ai_conversation exists")
	}

	return exists, nil
}

// Exists checks if the AiConversation row exists.
func (o *AiConversation) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	return AiConversationExists(ctx, exec, o.ID)
}
