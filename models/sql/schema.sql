-- Lock Database
UPDATE public.databasechangeloglock SET LOCKED = TRUE, LOCKEDBY = 'admin', LOCKGRANTED = NOW() WHERE ID = 1 AND LOCKED = FALSE;

-- Create Database Change Log Table
CREATE TABLE public.databasechangelog (ID VARCHAR(255) NOT NULL, AUTHOR VARCHAR(255) NOT NULL, FILENAME VARCHAR(255) NOT NULL, DATEEXECUTED TIMESTAMP WITHOUT TIME ZONE NOT NULL, ORDEREXECUTED INTEGER NOT NULL, EXECTYPE VARCHAR(10) NOT NULL, MD5SUM VARCHAR(35), DESCRIPTION VARCHAR(255), COMMENTS VARCHAR(255), TAG VARCHAR(255), LIQUIBASE VARCHAR(20), CONTEXTS VARCHAR(255), LABELS VARCHAR(255), DEPLOYMENT_ID VARCHAR(10));

-- *********************************************************************
-- Update Database Script
-- *********************************************************************
-- Change Log: changelog-root.yaml
-- Ran at:now
-- Against: postgres@****************************************
-- Liquibase version: 4.29.1
-- *********************************************************************

-- Changeset changelog-root.yaml::1::remington
CREATE TABLE public.organization (id VARCHAR(50) NOT NULL, name VARCHAR(50) NOT NULL, "memberCount" INTEGER, max_instances INTEGER NOT NULL, max_clusters INTEGER NOT NULL, CONSTRAINT organization_pkey PRIMARY KEY (id), UNIQUE (name));

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('1', 'remington', 'changelog-root.yaml', NOW(), 1, '9:c7b602e0b4ed92aee797f4784e84bcc9', 'createTable tableName=organization', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::2::remington
CREATE TABLE public.akuity_user (id VARCHAR(50) NOT NULL, email VARCHAR(254) NOT NULL, max_instances INTEGER NOT NULL, max_clusters INTEGER NOT NULL, CONSTRAINT akuity_user_pkey PRIMARY KEY (id), UNIQUE (email));

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('2', 'remington', 'changelog-root.yaml', NOW(), 2, '9:df0a2319a996e9b3e79cb7b75e744bff', 'createTable tableName=akuity_user', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::3::remington
CREATE TABLE public.argo_cd_instance (id VARCHAR(22) NOT NULL, name VARCHAR(50) NOT NULL, user_owner VARCHAR(50), organization_owner VARCHAR(50), generation INTEGER NOT NULL, status_observed_generation INTEGER, status_shard_id VARCHAR(50), status_hostname VARCHAR(255), status_health JSONB, status_conditions JSONB, deletion_timestamp TIMESTAMP WITH TIME ZONE, favorite BOOLEAN, CONSTRAINT argo_cd_instance_pkey PRIMARY KEY (id), CONSTRAINT fk_argo_cd_instance_organization_owner FOREIGN KEY (organization_owner) REFERENCES public.organization(id), CONSTRAINT fk_argo_cd_instance_user_owner FOREIGN KEY (user_owner) REFERENCES public.akuity_user(id));

CREATE UNIQUE INDEX argo_cd_instance_name_user_owner ON argo_cd_instance (user_owner, name)
WHERE argo_cd_instance.organization_owner IS NULL;

CREATE UNIQUE INDEX argo_cd_instance_name_organization_owner ON argo_cd_instance (organization_owner, name)
WHERE argo_cd_instance.user_owner IS NULL;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('3', 'remington', 'changelog-root.yaml', NOW(), 3, '9:18911ea00d04fd1776ef5be8a0f7d713', 'createTable tableName=argo_cd_instance; sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::4::remington
ALTER TABLE argo_cd_instance ADD CONSTRAINT argo_cd_instance_one_owner
CHECK ((user_owner IS NULL AND organization_owner IS NOT NULL)
OR (user_owner IS NOT NULL AND organization_owner IS NULL));

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('4', 'remington', 'changelog-root.yaml', NOW(), 4, '9:365b3f7a051929fed3c798213b5e9eee', 'sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::5::remington
CREATE TABLE public.instance_role (instance_id VARCHAR(50), user_id VARCHAR(50), role VARCHAR(50) NOT NULL, CONSTRAINT fk_instance_role_user_id FOREIGN KEY (user_id) REFERENCES public.akuity_user(id), CONSTRAINT fk_instance_role_instance_id FOREIGN KEY (instance_id) REFERENCES public.argo_cd_instance(id));

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('5', 'remington', 'changelog-root.yaml', NOW(), 5, '9:7964cb9335801cc80768e621286aa977', 'createTable tableName=instance_role', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::6::remington
CREATE TABLE public.organization_user (id VARCHAR(22) NOT NULL, user_id VARCHAR(50) NOT NULL, organization_id VARCHAR(50) NOT NULL, organization_role VARCHAR(50) NOT NULL, CONSTRAINT organization_user_pkey PRIMARY KEY (id, user_id, organization_id), CONSTRAINT fk_organization_user_user_id FOREIGN KEY (user_id) REFERENCES public.akuity_user(id), CONSTRAINT fk_organization_user_organization_id FOREIGN KEY (organization_id) REFERENCES public.organization(id));

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('6', 'remington', 'changelog-root.yaml', NOW(), 6, '9:b9557249a5b14344158e7c69f1e6aad8', 'createTable tableName=organization_user', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::7::remington
CREATE TABLE public.organization_invite (id VARCHAR(50) NOT NULL, expiration_time TIMESTAMP WITH TIME ZONE NOT NULL, invitee_email VARCHAR(254) NOT NULL, organization_id VARCHAR(50) NOT NULL, organization_role VARCHAR(50) NOT NULL, invited_by_email VARCHAR(254) NOT NULL, active BOOLEAN, CONSTRAINT organization_invite_pkey PRIMARY KEY (id), CONSTRAINT fk_organization_invite_organization_id FOREIGN KEY (organization_id) REFERENCES public.organization(id));

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('7', 'remington', 'changelog-root.yaml', NOW(), 7, '9:8618e849d578c4e368eda3e69a8653ad', 'createTable tableName=organization_invite', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::8::remington
CREATE TABLE public.argo_cd_cluster (id VARCHAR(50) NOT NULL, instance_id VARCHAR(50) NOT NULL, name VARCHAR(50) NOT NULL, sequence_id INTEGER NOT NULL, private_spec TEXT, status_manifests TEXT, status_conditions JSONB, status_agent_state JSONB, deletion_timestamp TIMESTAMP WITH TIME ZONE, CONSTRAINT argo_cd_cluster_pkey PRIMARY KEY (id), CONSTRAINT fk_argo_cd_cluster_instance_id FOREIGN KEY (instance_id) REFERENCES public.argo_cd_instance(id));

ALTER TABLE public.argo_cd_cluster ADD CONSTRAINT unique_cluster_instance UNIQUE (instance_id, name);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('8', 'remington', 'changelog-root.yaml', NOW(), 8, '9:37aab7aff60e130703b6242338c6cb16', 'createTable tableName=argo_cd_cluster; addUniqueConstraint constraintName=unique_cluster_instance, tableName=argo_cd_cluster', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::9::terrytangyuan
ALTER TABLE public.argo_cd_cluster ADD CONSTRAINT unique_cluster_seq_id UNIQUE (instance_id, sequence_id);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('9', 'terrytangyuan', 'changelog-root.yaml', NOW(), 9, '9:30780d1db9f8c92a16ee9ddc34ebab7e', 'addUniqueConstraint constraintName=unique_cluster_seq_id, tableName=argo_cd_cluster', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::10::terrytangyuan
CREATE OR REPLACE FUNCTION notify_argo_cd_instance_inserted() RETURNS TRIGGER AS $$

BEGIN
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'user_owner', NEW.user_owner::text,
                    'organization_owner', NEW.organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'added'::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance ON argo_cd_instance;
CREATE TRIGGER on_inserted_argo_cd_instance
AFTER INSERT ON argo_cd_instance
FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_inserted();

CREATE OR REPLACE FUNCTION notify_argo_cd_instance_updated() RETURNS TRIGGER AS $$
DECLARE
    updated_non_status_columns numeric;
BEGIN

    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value AND (n.key LIKE 'deletion_timestamp' OR n.key LIKE 'generation');


    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'user_owner', OLD.user_owner::text,
                    'organization_owner', OLD.organization_owner::text,
                    'spec_changed', (updated_non_status_columns > 0)::boolean,
                    'type', 'modified'::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_updated_argo_cd_instance ON argo_cd_instance;
CREATE TRIGGER on_updated_argo_cd_instance
    AFTER UPDATE ON argo_cd_instance
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_updated();

CREATE OR REPLACE FUNCTION notify_argo_cd_instance_deleted() RETURNS TRIGGER AS $$

BEGIN
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', OLD.id::text,
                    'user_owner', OLD.user_owner::text,
                    'organization_owner', OLD.organization_owner::text,
                    'spec_changed', false::boolean,
                    'type', 'deleted'::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_deleted_argo_cd_instance ON argo_cd_instance;
CREATE TRIGGER on_deleted_argo_cd_instance
    AFTER DELETE ON argo_cd_instance
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_deleted();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('10', 'terrytangyuan', 'changelog-root.yaml', NOW(), 10, '9:fc1ecaa4ca01577cb399807a40cc8aab', 'sqlFile path=argo_cd_instance_triggers.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::11::terrytangyuan
CREATE OR REPLACE FUNCTION notify_argo_cd_cluster_inserted() RETURNS TRIGGER AS $$

BEGIN
    PERFORM pg_notify(
            'argo_cd_cluster_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'instance_id', NEW.instance_id::text,
                    'spec_changed', true::boolean,
                    'type', 'added'::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_cluster ON argo_cd_cluster;
CREATE TRIGGER on_inserted_argo_cd_cluster
    AFTER INSERT ON argo_cd_cluster
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_cluster_inserted();

CREATE OR REPLACE FUNCTION notify_argo_cd_cluster_updated() RETURNS TRIGGER AS $$
DECLARE
    updated_non_status_columns numeric;
BEGIN

    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value AND n.key NOT LIKE 'status_%';

    PERFORM pg_notify(
            'argo_cd_cluster_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'instance_id', NEW.instance_id::text,
                    'spec_changed', (updated_non_status_columns > 0)::boolean,
                    'type', 'modified'::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_updated_argo_cd_cluster ON argo_cd_cluster;
CREATE TRIGGER on_updated_argo_cd_cluster
    AFTER UPDATE ON argo_cd_cluster
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_cluster_updated();

CREATE OR REPLACE FUNCTION notify_argo_cd_cluster_deleted() RETURNS TRIGGER AS $$

BEGIN
    PERFORM pg_notify(
            'argo_cd_cluster_updated',
            json_build_object(
                    'id', OLD.id::text,
                    'instance_id', OLD.instance_id::text,
                    'spec_changed', false::boolean,
                    'type', 'deleted'::text
                )::text
        );

RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_deleted_argo_cd_cluster ON argo_cd_cluster;
CREATE TRIGGER on_deleted_argo_cd_cluster
    AFTER DELETE ON argo_cd_cluster
    FOR EACH ROW
    EXECUTE PROCEDURE notify_argo_cd_cluster_deleted();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('11', 'terrytangyuan', 'changelog-root.yaml', NOW(), 11, '9:b19a5e6bfc11c2cc72b6655bdc1ceedc', 'sqlFile path=argo_cd_cluster_triggers.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::12::terrytangyuan
CREATE TABLE public.argo_cd_instance_config (instance_id VARCHAR(50) NOT NULL, version VARCHAR(50), subdomain VARCHAR(50) NOT NULL, argocd_cm JSONB, argocd_secret TEXT, argocd_rbac_cm JSONB, spec JSONB, private_spec TEXT, CONSTRAINT argo_cd_instance_config_pkey PRIMARY KEY (instance_id), CONSTRAINT fk_argo_cd_instance_config_instance_id FOREIGN KEY (instance_id) REFERENCES public.argo_cd_instance(id), UNIQUE (subdomain));

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('12', 'terrytangyuan', 'changelog-root.yaml', NOW(), 12, '9:4857c0345faef0685a3da16fd0385e92', 'createTable tableName=argo_cd_instance_config', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::13::alexander
CREATE OR REPLACE FUNCTION notify_argo_cd_instance_config_changed() RETURNS TRIGGER AS $$
DECLARE
    user_owner VARCHAR;
    organization_owner VARCHAR;
BEGIN
    SELECT INTO user_owner, organization_owner argo_cd_instance.user_owner, argo_cd_instance.organization_owner
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.instance_id::text,
                    'user_owner', user_owner::text,
                    'organization_owner', organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'modified'::text
                )::text
        );
    UPDATE argo_cd_instance SET generation = generation + 1 WHERE id = NEW.instance_id;

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_inserted_argo_cd_instance_config
    AFTER INSERT ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();


DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_updated_argo_cd_instance_config
    AFTER UPDATE ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('13', 'alexander', 'changelog-root.yaml', NOW(), 13, '9:3d99ec222fb7a8d5a6c7204459e8390c', 'sqlFile path=argo_cd_instance_config_triggers.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::14::gdsoumya
ALTER TABLE argo_cd_cluster ADD CONSTRAINT seq_id_check CHECK (sequence_id >= 0 AND sequence_id <= 4811);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('14', 'gdsoumya', 'changelog-root.yaml', NOW(), 14, '9:d777f1cf410cf2efa2279e4388df3f34', 'sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::15::gdsoumya
ALTER TABLE public.organization ADD creation_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL;

ALTER TABLE public.akuity_user ADD creation_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL;

ALTER TABLE public.argo_cd_instance ADD creation_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL;

ALTER TABLE public.argo_cd_cluster ADD creation_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('15', 'gdsoumya', 'changelog-root.yaml', NOW(), 15, '9:e28bc72fa034541810aaac8465755679', 'addColumn tableName=organization; addColumn tableName=akuity_user; addColumn tableName=argo_cd_instance; addColumn tableName=argo_cd_cluster', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::16::gdsoumya
ALTER TABLE public.akuity_user ADD max_orgs INTEGER DEFAULT 5 NOT NULL;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('16', 'gdsoumya', 'changelog-root.yaml', NOW(), 16, '9:c51482ca8242161737c2e747d0c5913b', 'addColumn tableName=akuity_user', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::17::gdsoumya
ALTER TABLE public.argo_cd_cluster ADD generation INTEGER DEFAULT 1 NOT NULL;

ALTER TABLE public.argo_cd_cluster ADD status_observed_generation INTEGER;

CREATE OR REPLACE FUNCTION notify_argo_cd_instance_config_changed() RETURNS TRIGGER AS $$
DECLARE
    user_owner VARCHAR;
    organization_owner VARCHAR;
BEGIN
    SELECT INTO user_owner, organization_owner argo_cd_instance.user_owner, argo_cd_instance.organization_owner
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.instance_id::text,
                    'user_owner', user_owner::text,
                    'organization_owner', organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'modified'::text
                )::text
        );
    UPDATE argo_cd_instance SET generation = generation + 1 WHERE id = NEW.instance_id;
    IF TG_OP = 'UPDATE' and NEW.version != OLD.version THEN
        UPDATE argo_cd_cluster SET generation = generation + 1 WHERE instance_id = NEW.instance_id;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_inserted_argo_cd_instance_config
    AFTER INSERT ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();


DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_updated_argo_cd_instance_config
    AFTER UPDATE ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('17', 'gdsoumya', 'changelog-root.yaml', NOW(), 17, '9:c66c25f98416b67d0d0044bcd9fd623b', 'addColumn tableName=argo_cd_cluster; sqlFile path=argo_cd_instance_config_triggers_v2.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::18::sunghoonkang
CREATE TABLE public.api_key (id VARCHAR(16) NOT NULL, secret_hash VARCHAR(64) NOT NULL, secret_salt VARCHAR(32) NOT NULL, description VARCHAR(255), organization VARCHAR(50), role JSONB, creation_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL, expiration_timestamp TIMESTAMP WITH TIME ZONE, CONSTRAINT api_key_pkey PRIMARY KEY (id), CONSTRAINT fk_akuity_api_key_organization FOREIGN KEY (organization) REFERENCES public.organization(id));

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('18', 'sunghoonkang', 'changelog-root.yaml', NOW(), 18, '9:4b5b550ef45317129f218a06da63f77b', 'createTable tableName=api_key', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::19::alexander
ALTER TABLE public.argo_cd_cluster ADD spec JSONB;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('19', 'alexander', 'changelog-root.yaml', NOW(), 19, '9:61b0128f7ba2940f0ba2a7d20e337e0e', 'addColumn tableName=argo_cd_cluster', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::20::gdsoumya
CREATE OR REPLACE FUNCTION bump_gen_argo_cd_cluster_updated() RETURNS TRIGGER AS $$
DECLARE
updated_non_status_columns numeric;
BEGIN
    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value AND n.key NOT LIKE 'status_%';

    IF updated_non_status_columns > 0 THEN
        NEW.generation = OLD.generation + 1;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS bump_gen_on_updated_argo_cd_cluster ON argo_cd_cluster;
CREATE TRIGGER bump_gen_on_updated_argo_cd_cluster
    BEFORE UPDATE ON argo_cd_cluster
    FOR EACH ROW
    EXECUTE PROCEDURE bump_gen_argo_cd_cluster_updated();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('20', 'gdsoumya', 'changelog-root.yaml', NOW(), 20, '9:f23cf93d05881f8d22ddf8b0ee420b04', 'sqlFile path=argo_cd_cluster_triggers_gen_bump.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::21::pavel
ALTER TABLE public.argo_cd_instance_config ADD argocd_notifications_cm JSONB;

ALTER TABLE public.argo_cd_instance_config ADD argocd_notifications_secret TEXT;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('21', 'pavel', 'changelog-root.yaml', NOW(), 21, '9:2252d364d1689d5b46322859e84b33d2', 'addColumn tableName=argo_cd_instance_config', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::22::sunghoonkang
ALTER TABLE public.api_key RENAME COLUMN role TO permissions;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('22', 'sunghoonkang', 'changelog-root.yaml', NOW(), 22, '9:7a88b7cf003ecdf1e0fb31790508a2e2', 'renameColumn newColumnName=permissions, oldColumnName=role, tableName=api_key', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::23::remington
ALTER TABLE public.argo_cd_instance ADD status_info JSONB;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('23', 'remington', 'changelog-root.yaml', NOW(), 23, '9:dad99b66aebeb179d5b7227681e50258', 'addColumn tableName=argo_cd_instance', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::24::alexander
ALTER TABLE public.argo_cd_instance DROP COLUMN favorite;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('24', 'alexander', 'changelog-root.yaml', NOW(), 24, '9:2bcd368fecca8138e476295cd2a3aac5', 'dropColumn columnName=favorite, tableName=argo_cd_instance', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::25::alexander
CREATE TABLE public.argo_cd_sync_operation (id VARCHAR(50) NOT NULL, instance_id VARCHAR(50) NOT NULL, application_name VARCHAR(50) NOT NULL, start_time TIMESTAMP WITH TIME ZONE NOT NULL, end_time TIMESTAMP WITH TIME ZONE NOT NULL, result_phase VARCHAR(50) NOT NULL, result_message VARCHAR(500) NOT NULL, details JSONB, CONSTRAINT argo_cd_sync_operation_pkey PRIMARY KEY (id), CONSTRAINT fk_instance_role_instance_id FOREIGN KEY (instance_id) REFERENCES public.argo_cd_instance(id));

ALTER TABLE public.argo_cd_instance ADD status_recent_processed_event_id INTEGER DEFAULT 0 NOT NULL;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('25', 'alexander', 'changelog-root.yaml', NOW(), 25, '9:6d85fccf184faea5de53a512dc8a4371', 'createTable tableName=argo_cd_sync_operation; addColumn tableName=argo_cd_instance', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::26::gdsoumya
CREATE TABLE public.audit_log (id VARCHAR(50) NOT NULL, instance_id VARCHAR(50), organization_id VARCHAR(50), timestamp TIMESTAMP WITH TIME ZONE NOT NULL, action VARCHAR(50) NOT NULL, object JSONB NOT NULL, details JSONB, actor JSONB NOT NULL, CONSTRAINT audit_log_pkey PRIMARY KEY (id), CONSTRAINT fk_audit_log_organization_id FOREIGN KEY (organization_id) REFERENCES public.organization(id));

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('26', 'gdsoumya', 'changelog-root.yaml', NOW(), 26, '9:a3c68160309a5142f7266dabc8a4b34d', 'createTable tableName=audit_log', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::27::jiachengxu
ALTER TABLE public.akuity_user ADD user_info JSONB;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('27', 'jiachengxu', 'changelog-root.yaml', NOW(), 27, '9:1be45f69cf2e5c495176fa3db710b8eb', 'addColumn tableName=akuity_user', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::28::pavel
ALTER TABLE public.akuity_user ADD max_applications INTEGER DEFAULT 20 NOT NULL;

ALTER TABLE public.organization ADD max_applications INTEGER DEFAULT 20 NOT NULL;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('28', 'pavel', 'changelog-root.yaml', NOW(), 28, '9:30954b2718cbf72f8bbd350c246e60cc', 'addColumn tableName=akuity_user; addColumn tableName=organization', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::29::gdsoumya
ALTER TABLE public.argo_cd_cluster ADD namespace VARCHAR(63) DEFAULT 'akuity' NOT NULL;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('29', 'gdsoumya', 'changelog-root.yaml', NOW(), 29, '9:3d6459fd72693ca398e2de3f2e950a1c', 'addColumn tableName=argo_cd_cluster', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::30::jiachengxu
SELECT 1;

ALTER TABLE public.akuity_user ALTER COLUMN email TYPE VARCHAR(254) USING (email::VARCHAR(254));

ALTER TABLE public.organization_invite ALTER COLUMN invitee_email TYPE VARCHAR(254) USING (invitee_email::VARCHAR(254));

ALTER TABLE public.organization_invite ALTER COLUMN invited_by_email TYPE VARCHAR(254) USING (invited_by_email::VARCHAR(254));

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('30', 'jiachengxu', 'changelog-root.yaml', NOW(), 30, '9:9801893f79999439b0da143d7c67b559', 'sql; modifyDataType columnName=email, tableName=akuity_user; modifyDataType columnName=invitee_email, tableName=organization_invite; modifyDataType columnName=invited_by_email, tableName=organization_invite', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::31::gdsoumya
ALTER TABLE public.argo_cd_cluster ADD auto_upgrade_disabled BOOLEAN DEFAULT FALSE NOT NULL;

CREATE OR REPLACE FUNCTION bump_gen_argo_cd_cluster_updated() RETURNS TRIGGER AS $$
DECLARE
updated_non_status_columns numeric;
BEGIN
    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value AND n.key NOT LIKE 'status_%' AND n.key != 'auto_upgrade_disabled' ;

    IF updated_non_status_columns > 0 OR (NEW.auto_upgrade_disabled != OLD.auto_upgrade_disabled AND NEW.auto_upgrade_disabled=false) THEN
            NEW.generation = OLD.generation + 1;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS bump_gen_on_updated_argo_cd_cluster ON argo_cd_cluster;
CREATE TRIGGER bump_gen_on_updated_argo_cd_cluster
    BEFORE UPDATE ON argo_cd_cluster
    FOR EACH ROW
    EXECUTE PROCEDURE bump_gen_argo_cd_cluster_updated();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('31', 'gdsoumya', 'changelog-root.yaml', NOW(), 31, '9:5580d024379342d967bd01095a2f5b76', 'addColumn tableName=argo_cd_cluster; sqlFile path=argo_cd_cluster_triggers_gen_bump_v2.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::32::alexmt
ALTER TABLE public.argo_cd_instance_config ADD internal_spec JSONB;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('32', 'alexmt', 'changelog-root.yaml', NOW(), 32, '9:23454f65ce3ae30a0b3fcb82e10e02f5', 'addColumn tableName=argo_cd_instance_config', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::33::gdsoumya
ALTER TABLE public.argo_cd_cluster ADD namespace_scoped BOOLEAN DEFAULT FALSE NOT NULL;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('33', 'gdsoumya', 'changelog-root.yaml', NOW(), 33, '9:6b86cbd3a0410035ffc3747630cb3c5c', 'addColumn tableName=argo_cd_cluster', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::34::alexmt
ALTER TABLE public.akuity_user ADD ui_preferences JSONB;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('34', 'alexmt', 'changelog-root.yaml', NOW(), 34, '9:3dfa3b98aa607c0c50e813c3c84e6911', 'addColumn tableName=akuity_user', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::35::Marvin9
ALTER TABLE public.argo_cd_instance ADD description VARCHAR(255);

ALTER TABLE public.argo_cd_cluster ADD description VARCHAR(255);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('35', 'Marvin9', 'changelog-root.yaml', NOW(), 35, '9:7a83e27b2fdfd8c8aa041e9ba7cd2148', 'addColumn tableName=argo_cd_instance; addColumn tableName=argo_cd_cluster', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::36::alex
ALTER TABLE public.akuity_user ALTER COLUMN max_applications DROP DEFAULT;

ALTER TABLE public.organization ALTER COLUMN max_applications DROP DEFAULT;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('36', 'alex', 'changelog-root.yaml', NOW(), 36, '9:6011a867a889351c1e3b592cb7b559d2', 'dropDefaultValue columnName=max_applications, tableName=akuity_user; dropDefaultValue columnName=max_applications, tableName=organization', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::37::pavel
ALTER TABLE public.audit_log DROP COLUMN instance_id;

CREATE INDEX audit_log_action ON audit_log (action);

CREATE INDEX audit_log_timestamp ON audit_log (timestamp);

CREATE INDEX audit_log_organization_id ON audit_log(organization_id);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('37', 'pavel', 'changelog-root.yaml', NOW(), 37, '9:870b05f3edaeec28c36831fd16b31bd1', 'dropColumn columnName=instance_id, tableName=audit_log; sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::38::gdsoumya
CREATE TABLE public.billing (id VARCHAR(50) NOT NULL, customer_id VARCHAR(100) NOT NULL, billing_email VARCHAR(254) NOT NULL, organization_id VARCHAR(50) NOT NULL, billing_authority VARCHAR(10) NOT NULL, creation_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL, generation INTEGER DEFAULT 1 NOT NULL, CONSTRAINT billing_pkey PRIMARY KEY (id), CONSTRAINT fk_audit_log_organization_id FOREIGN KEY (organization_id) REFERENCES public.organization(id), UNIQUE (customer_id), UNIQUE (billing_email), UNIQUE (organization_id));

ALTER TABLE public.organization ADD org_status JSONB;

CREATE OR REPLACE FUNCTION notify_billing_updated() RETURNS TRIGGER AS $$
DECLARE
    updated_non_status_columns numeric;
BEGIN

    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value;


    PERFORM pg_notify(
            'billing_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'spec_changed', (updated_non_status_columns > 0)::boolean,
                    'type', 'modified'::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_updated_billing ON billing;
CREATE TRIGGER on_updated_billing
    AFTER UPDATE ON billing
    FOR EACH ROW
EXECUTE PROCEDURE notify_billing_updated();

/*
Update all existing org status columns with this default value
sets all current subscriptions to trial type and the default expiry to december 1st 00:00:00 GMT
*/
UPDATE organization SET org_status = '{"expiry_time": 1669852800, "trial": true}' WHERE 1=1;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('38', 'gdsoumya', 'changelog-root.yaml', NOW(), 38, '9:7502183ae2f8ac43debce6db12d53e5b', 'createTable tableName=billing; addColumn tableName=organization; sqlFile path=billing_triggers.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::39::alexander
ALTER TABLE public.organization DROP COLUMN "memberCount";

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('39', 'alexander', 'changelog-root.yaml', NOW(), 39, '9:2f74e174ab0240a990e340f4b4c2fcab', 'dropColumn columnName=memberCount, tableName=organization', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::40::remington
ALTER TABLE public.billing ADD billing_metadata TEXT;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('40', 'remington', 'changelog-root.yaml', NOW(), 40, '9:0a4b45444fae2e541d17e77073a147cd', 'addColumn tableName=billing', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::41::evgeny
ALTER TABLE public.akuity_user ADD user_info_private TEXT;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('41', 'evgeny', 'changelog-root.yaml', NOW(), 41, '9:a130a910571b61a5f7af6d38686b8548', 'addColumn tableName=akuity_user', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::42::alexmt
CREATE OR REPLACE FUNCTION notify_argo_cd_instance_inserted() RETURNS TRIGGER AS $$

BEGIN
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'organization_owner', NEW.organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'added'::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance ON argo_cd_instance;
CREATE TRIGGER on_inserted_argo_cd_instance
AFTER INSERT ON argo_cd_instance
FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_inserted();

CREATE OR REPLACE FUNCTION notify_argo_cd_instance_updated() RETURNS TRIGGER AS $$
DECLARE
    updated_non_status_columns numeric;
BEGIN

    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value AND (n.key LIKE 'deletion_timestamp' OR n.key LIKE 'generation');


    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'organization_owner', OLD.organization_owner::text,
                    'spec_changed', (updated_non_status_columns > 0)::boolean,
                    'type', 'modified'::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_updated_argo_cd_instance ON argo_cd_instance;
CREATE TRIGGER on_updated_argo_cd_instance
    AFTER UPDATE ON argo_cd_instance
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_updated();

CREATE OR REPLACE FUNCTION notify_argo_cd_instance_deleted() RETURNS TRIGGER AS $$

BEGIN
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', OLD.id::text,
                    'organization_owner', OLD.organization_owner::text,
                    'spec_changed', false::boolean,
                    'type', 'deleted'::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_deleted_argo_cd_instance ON argo_cd_instance;
CREATE TRIGGER on_deleted_argo_cd_instance
    AFTER DELETE ON argo_cd_instance
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_deleted();

CREATE OR REPLACE FUNCTION notify_argo_cd_instance_config_changed() RETURNS TRIGGER AS $$
DECLARE
    organization_owner VARCHAR;
BEGIN
    SELECT INTO organization_owner argo_cd_instance.organization_owner
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'modified'::text
                )::text
        );
    UPDATE argo_cd_instance SET generation = generation + 1 WHERE id = NEW.instance_id;
    IF TG_OP = 'UPDATE' and NEW.version != OLD.version THEN
        UPDATE argo_cd_cluster SET generation = generation + 1 WHERE instance_id = NEW.instance_id;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_inserted_argo_cd_instance_config
    AFTER INSERT ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();


DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_updated_argo_cd_instance_config
    AFTER UPDATE ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();

ALTER TABLE public.argo_cd_instance DROP COLUMN user_owner;

ALTER TABLE public.akuity_user DROP COLUMN max_instances;

ALTER TABLE public.akuity_user DROP COLUMN max_clusters;

ALTER TABLE public.akuity_user DROP COLUMN max_applications;

ALTER TABLE public.argo_cd_instance ALTER COLUMN  organization_owner SET NOT NULL;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('42', 'alexmt', 'changelog-root.yaml', NOW(), 42, '9:bbb8375bbb9de6ef5406474bdc57157e', 'sqlFile path=argo_cd_instance_triggers_v2.sql; sqlFile path=argo_cd_instance_config_triggers_v3.sql; dropColumn columnName=user_owner, tableName=argo_cd_instance; dropColumn columnName=max_instances, tableName=akuity_user; dropColumn columnName=ma...', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::43::evgeny
CREATE INDEX argo_cd_sync_operation_instance_id ON argo_cd_sync_operation (instance_id);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('43', 'evgeny', 'changelog-root.yaml', NOW(), 43, '9:356f5d020b02a33b1109c910c54db038', 'sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::44::pavel
ALTER TABLE public.argo_cd_instance_config ADD argocd_image_updater_enable BOOLEAN;

ALTER TABLE public.argo_cd_instance_config ADD argocd_image_updater_cm JSONB;

ALTER TABLE public.argo_cd_instance_config ADD argocd_image_updater_ssh_cm JSONB;

ALTER TABLE public.argo_cd_instance_config ADD argocd_image_updater_secret TEXT;

CREATE OR REPLACE FUNCTION notify_argo_cd_instance_config_changed() RETURNS TRIGGER AS $$
DECLARE
    organization_owner VARCHAR;
BEGIN
    SELECT INTO organization_owner argo_cd_instance.organization_owner
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'modified'::text
                )::text
        );
    UPDATE argo_cd_instance SET generation = generation + 1 WHERE id = NEW.instance_id;
    IF TG_OP = 'UPDATE' and (NEW.version != OLD.version or NEW.argocd_image_updater_enable != OLD.argocd_image_updater_enable) THEN
        UPDATE argo_cd_cluster SET generation = generation + 1 WHERE instance_id = NEW.instance_id;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_inserted_argo_cd_instance_config
    AFTER INSERT ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();


DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_updated_argo_cd_instance_config
    AFTER UPDATE ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('44', 'pavel', 'changelog-root.yaml', NOW(), 44, '9:4abfc6d98938bc08051fd9be25fbf6b1', 'addColumn tableName=argo_cd_instance_config; sqlFile path=argo_cd_instance_config_triggers_v4.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::45::evgeny
DROP INDEX argo_cd_sync_operation_instance_id;

ALTER TABLE public.akuity_user ADD last_activity_timestamp TIMESTAMP WITH TIME ZONE;

ALTER TABLE public.akuity_user ADD last_location VARCHAR(64);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('45', 'evgeny', 'changelog-root.yaml', NOW(), 45, '9:ddb48e38c1012527815b6d1735ebd928', 'sql; addColumn tableName=akuity_user', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::46::pavel
CREATE OR REPLACE FUNCTION notify_argo_cd_instance_config_changed() RETURNS TRIGGER AS $$
DECLARE
    organization_owner VARCHAR;
BEGIN
    SELECT INTO organization_owner argo_cd_instance.organization_owner
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'modified'::text
                )::text
        );
    UPDATE argo_cd_instance SET generation = generation + 1 WHERE id = NEW.instance_id;
    IF TG_OP = 'UPDATE' and (NEW.version != OLD.version or NEW.argocd_image_updater_enable != OLD.argocd_image_updater_enable or (coalesce(NEW.spec->>'repo_server_delegate', '') != coalesce(OLD.spec->>'repo_server_delegate', ''))) THEN
        UPDATE argo_cd_cluster SET generation = generation + 1 WHERE instance_id = NEW.instance_id;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_inserted_argo_cd_instance_config
    AFTER INSERT ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();


DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_updated_argo_cd_instance_config
    AFTER UPDATE ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('46', 'pavel', 'changelog-root.yaml', NOW(), 46, '9:d3cdb66c4f9d806fd4b2bd83be3def97', 'sqlFile path=argo_cd_instance_config_triggers_v5.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::47::remington
ALTER TABLE public.billing ALTER COLUMN  billing_email DROP NOT NULL;

ALTER TABLE public.billing ADD manual BOOLEAN DEFAULT FALSE;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('47', 'remington', 'changelog-root.yaml', NOW(), 47, '9:f9f4cd40a07d9261d69fd388b49946e4', 'dropNotNullConstraint columnName=billing_email, tableName=billing; addColumn tableName=billing', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::48::alex
CREATE OR REPLACE FUNCTION notify_argo_cd_instance_config_changed() RETURNS TRIGGER AS $$
DECLARE
    organization_owner VARCHAR;
BEGIN
    SELECT INTO organization_owner argo_cd_instance.organization_owner
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'modified'::text
                )::text
        );
    UPDATE argo_cd_instance SET generation = generation + 1 WHERE id = NEW.instance_id;
    -- upgrade all clusters if:
    IF TG_OP = 'UPDATE' and (
            -- instance argocd version changed
                NEW.version != OLD.version or
            -- image updater enabled/disabled
                NEW.argocd_image_updater_enable != OLD.argocd_image_updater_enable or
            -- git delegate enabled/disabled
                (coalesce(NEW.spec ->> 'repo_server_delegate', '') !=
                 coalesce(OLD.spec ->> 'repo_server_delegate', '')) or
            -- git delegate in managed cluster is enabled and image updater/declarative management is changed
                (coalesce(NEW.spec -> 'repo_server_delegate' ->> 'managed_cluster', '') != '' and (
                    NEW.spec->>'declarative_management_enabled' != OLD.spec ->> 'declarative_management_enabled' or
                    NEW.argocd_image_updater_enable != OLD.argocd_image_updater_enable)
                )
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1 WHERE instance_id = NEW.instance_id;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_inserted_argo_cd_instance_config
    AFTER INSERT ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();


DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_updated_argo_cd_instance_config
    AFTER UPDATE ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('48', 'alex', 'changelog-root.yaml', NOW(), 48, '9:acc0bc7443ed96f3a809409c8ea4ca91', 'sqlFile path=argo_cd_instance_config_triggers_v6.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::49::sunghoonkang
CREATE TABLE public.organization_sso_realm (id VARCHAR(50) NOT NULL, organization_id VARCHAR(50) NOT NULL, domain VARCHAR(255) NOT NULL, CONSTRAINT organization_sso_realm_pkey PRIMARY KEY (id), CONSTRAINT fk_organization_sso_realm_organization_id FOREIGN KEY (organization_id) REFERENCES public.organization(id), UNIQUE (domain));

ALTER TABLE public.organization ADD sso_connection_strategy VARCHAR(64);

ALTER TABLE public.organization ADD sso_auto_add_member BOOLEAN;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('49', 'sunghoonkang', 'changelog-root.yaml', NOW(), 49, '9:6bebde43cad180c6c63519297a14de62', 'createTable tableName=organization_sso_realm; addColumn tableName=organization', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::50::remington
ALTER TABLE public.billing ADD inactive BOOLEAN DEFAULT FALSE;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('50', 'remington', 'changelog-root.yaml', NOW(), 50, '9:cf18abae8c0d51a8f47b9b5250cf600d', 'addColumn tableName=billing', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::51::sunghoonkang
ALTER TABLE public.organization ADD feature_gates JSONB;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('51', 'sunghoonkang', 'changelog-root.yaml', NOW(), 51, '9:8c21601fc071da0b88a9975c8ba2896a', 'addColumn tableName=organization', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::52::remington
ALTER TABLE public.argo_cd_instance_config ADD argocd_appset_secret TEXT;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('52', 'remington', 'changelog-root.yaml', NOW(), 52, '9:41021acd04a2c5c08f1dc120d4c3ebc0', 'addColumn tableName=argo_cd_instance_config', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::53::alex
CREATE OR REPLACE FUNCTION notify_argo_cd_instance_config_changed() RETURNS TRIGGER AS $$
DECLARE
    organization_owner VARCHAR;
BEGIN
    SELECT INTO organization_owner argo_cd_instance.organization_owner
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'modified'::text
                )::text
        );
    UPDATE argo_cd_instance SET generation = generation + 1 WHERE id = NEW.instance_id;
    -- upgrade all clusters to latest version if:
    IF TG_OP = 'UPDATE' and (
            -- instance argocd version changed
                NEW.version != OLD.version or
            -- image updater enabled/disabled
                NEW.argocd_image_updater_enable != OLD.argocd_image_updater_enable or
            -- git delegate enabled/disabled
                (coalesce(NEW.spec ->> 'repo_server_delegate', '') !=
                 coalesce(OLD.spec ->> 'repo_server_delegate', '')) or
            -- git delegate in managed cluster is enabled and image updater/declarative management is changed
                (coalesce(NEW.spec -> 'repo_server_delegate' ->> 'managed_cluster', '') != '' and (
                    NEW.spec->>'declarative_management_enabled' != OLD.spec ->> 'declarative_management_enabled' or
                    NEW.argocd_image_updater_enable != OLD.argocd_image_updater_enable)
                )
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1, spec=jsonb_set(spec::jsonb, '{targetVersion}', 'null') WHERE instance_id = NEW.instance_id;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_inserted_argo_cd_instance_config
    AFTER INSERT ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();


DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_updated_argo_cd_instance_config
    AFTER UPDATE ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('53', 'alex', 'changelog-root.yaml', NOW(), 53, '9:f51c0b1c46223027d24591893c6d4927', 'sqlFile path=argo_cd_instance_config_triggers_v7.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::54::evgeny
ALTER TABLE public.argo_cd_instance ADD status_recent_processed_event_info JSONB;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('54', 'evgeny', 'changelog-root.yaml', NOW(), 54, '9:f16de9754cc0e6349174612f8cc965e4', 'addColumn tableName=argo_cd_instance', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::55::evgeny
ALTER TABLE public.argo_cd_instance DROP COLUMN status_recent_processed_event_id;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('55', 'evgeny', 'changelog-root.yaml', NOW(), 55, '9:d6caaf87d3cbc184a69a9a1b7f08994e', 'dropColumn tableName=argo_cd_instance', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::56::pavel
ALTER TABLE public.argo_cd_cluster ADD status_info JSONB;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('56', 'pavel', 'changelog-root.yaml', NOW(), 56, '9:eb762aecd5cc2d68657ba05699e4ed54', 'addColumn tableName=argo_cd_cluster', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::56::gdsoumya
CREATE OR REPLACE FUNCTION notify_argo_cd_instance_config_changed() RETURNS TRIGGER AS $$
DECLARE
    organization_owner VARCHAR;
BEGIN
    SELECT INTO organization_owner argo_cd_instance.organization_owner
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'modified'::text
                )::text
        );
    UPDATE argo_cd_instance SET generation = generation + 1 WHERE id = NEW.instance_id;
    -- upgrade all clusters to latest version if:
    IF TG_OP = 'UPDATE' and (
            -- instance argocd version changed
                NEW.version != OLD.version or
            -- image updater enabled/disabled
                NEW.argocd_image_updater_enable != OLD.argocd_image_updater_enable or
            -- git delegate enabled/disabled
                (coalesce(NEW.spec ->> 'repo_server_delegate', '') !=
                 coalesce(OLD.spec ->> 'repo_server_delegate', '')) or
            -- declarative management enabled/disabled
                NEW.spec->>'declarative_management_enabled' != OLD.spec ->> 'declarative_management_enabled' or
            -- image updater spec changed
                (coalesce(NEW.spec ->> 'image_updater_delegate', '') !=
                 coalesce(OLD.spec ->> 'image_updater_delegate', '')) or
            -- appset spec changed
                (coalesce(NEW.spec ->> 'app_set_delegate', '') !=
                 coalesce(OLD.spec ->> 'app_set_delegate', ''))
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1, spec=jsonb_set(spec::jsonb, '{targetVersion}', 'null') WHERE instance_id = NEW.instance_id;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_inserted_argo_cd_instance_config
    AFTER INSERT ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();


DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_updated_argo_cd_instance_config
    AFTER UPDATE ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();


/*
Update all existing instance specs with image updater config to match delegate config
as currently that's the expected behavior
*/
UPDATE argo_cd_instance_config SET spec = jsonb_set(spec, '{image_updater_delegate}', spec -> 'repo_server_delegate') WHERE coalesce(spec ->> 'repo_server_delegate', '') != '';
UPDATE argo_cd_instance_config SET spec = jsonb_set(spec, '{app_set_delegate}', spec -> 'repo_server_delegate') WHERE coalesce(spec ->> 'repo_server_delegate', '') != '';

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('56', 'gdsoumya', 'changelog-root.yaml', NOW(), 57, '9:60feaee90f255c720cb4a6d6d4fd4d51', 'sqlFile path=argo_cd_instance_config_triggers_v8.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::56::alex
CREATE OR REPLACE FUNCTION notify_argo_cd_instance_inserted() RETURNS TRIGGER AS $$

BEGIN
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'organization_owner', NEW.organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'added'::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance ON argo_cd_instance;
CREATE TRIGGER on_inserted_argo_cd_instance
AFTER INSERT ON argo_cd_instance
FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_inserted();

CREATE OR REPLACE FUNCTION notify_argo_cd_instance_updated() RETURNS TRIGGER AS $$
DECLARE
    updated_non_status_columns numeric;
BEGIN

    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value AND (n.key LIKE 'deletion_timestamp' OR n.key LIKE 'generation' OR n.key LIKE 'observed_generation');


    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'organization_owner', OLD.organization_owner::text,
                    'spec_changed', (updated_non_status_columns > 0)::boolean,
                    'type', 'modified'::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_updated_argo_cd_instance ON argo_cd_instance;
CREATE TRIGGER on_updated_argo_cd_instance
    AFTER UPDATE ON argo_cd_instance
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_updated();

CREATE OR REPLACE FUNCTION notify_argo_cd_instance_deleted() RETURNS TRIGGER AS $$

BEGIN
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', OLD.id::text,
                    'organization_owner', OLD.organization_owner::text,
                    'spec_changed', false::boolean,
                    'type', 'deleted'::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_deleted_argo_cd_instance ON argo_cd_instance;
CREATE TRIGGER on_deleted_argo_cd_instance
    AFTER DELETE ON argo_cd_instance
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_deleted();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('56', 'alex', 'changelog-root.yaml', NOW(), 58, '9:5eb719986c19176b8ac1a1b76e6e433a', 'sqlFile path=argo_cd_instance_triggers_v3.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::57::alex
CREATE OR REPLACE FUNCTION notify_argo_cd_instance_inserted() RETURNS TRIGGER AS $$

BEGIN
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'organization_owner', NEW.organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'added'::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance ON argo_cd_instance;
CREATE TRIGGER on_inserted_argo_cd_instance
AFTER INSERT ON argo_cd_instance
FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_inserted();

CREATE OR REPLACE FUNCTION notify_argo_cd_instance_updated() RETURNS TRIGGER AS $$
DECLARE
    updated_non_status_columns numeric;
BEGIN

    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value AND (n.key LIKE 'deletion_timestamp' OR n.key LIKE 'generation');


    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'organization_owner', OLD.organization_owner::text,
                    'spec_changed', (updated_non_status_columns > 0)::boolean,
                    'type', 'modified'::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_updated_argo_cd_instance ON argo_cd_instance;
CREATE TRIGGER on_updated_argo_cd_instance
    AFTER UPDATE ON argo_cd_instance
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_updated();

CREATE OR REPLACE FUNCTION notify_argo_cd_instance_deleted() RETURNS TRIGGER AS $$

BEGIN
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', OLD.id::text,
                    'organization_owner', OLD.organization_owner::text,
                    'spec_changed', false::boolean,
                    'type', 'deleted'::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_deleted_argo_cd_instance ON argo_cd_instance;
CREATE TRIGGER on_deleted_argo_cd_instance
    AFTER DELETE ON argo_cd_instance
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_deleted();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('57', 'alex', 'changelog-root.yaml', NOW(), 59, '9:3f5dac043c3673ba03aefc022185fff2', 'sqlFile path=argo_cd_instance_triggers_v4.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::58::goldin
ALTER TABLE public.argo_cd_sync_operation ALTER COLUMN application_name TYPE VARCHAR(253) USING (application_name::VARCHAR(253));

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('58', 'goldin', 'changelog-root.yaml', NOW(), 60, '9:f271251bf289b60c3ab218e841327cd9', 'modifyDataType columnName=application_name, tableName=argo_cd_sync_operation', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::59::goldin
ALTER TABLE public.akuity_user DROP COLUMN user_info;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('59', 'goldin', 'changelog-root.yaml', NOW(), 61, '9:84e7d557e6ee5bdee30d082fcd2949e3', 'dropColumn columnName=user_info, tableName=akuity_user', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::60::jiachengxu
ALTER TABLE public.argo_cd_instance_config ADD argocd_config_management_plugins JSONB;

CREATE OR REPLACE FUNCTION notify_argo_cd_instance_config_changed() RETURNS TRIGGER AS $$
DECLARE
    organization_owner VARCHAR;
BEGIN
    SELECT INTO organization_owner argo_cd_instance.organization_owner
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'modified'::text
                )::text
        );
    UPDATE argo_cd_instance SET generation = generation + 1 WHERE id = NEW.instance_id;
    -- upgrade all clusters to latest version if:
    IF TG_OP = 'UPDATE' and (
            -- instance argocd version changed
                NEW.version != OLD.version or
            -- image updater enabled/disabled
                NEW.argocd_image_updater_enable != OLD.argocd_image_updater_enable or
            -- git delegate enabled/disabled
                (coalesce(NEW.spec ->> 'repo_server_delegate', '') !=
                 coalesce(OLD.spec ->> 'repo_server_delegate', '')) or
            -- declarative management enabled/disabled
                NEW.spec->>'declarative_management_enabled' != OLD.spec ->> 'declarative_management_enabled' or
            -- image updater spec changed
                (coalesce(NEW.spec ->> 'image_updater_delegate', '') !=
                 coalesce(OLD.spec ->> 'image_updater_delegate', '')) or
            -- appset spec changed
                (coalesce(NEW.spec ->> 'app_set_delegate', '') !=
                 coalesce(OLD.spec ->> 'app_set_delegate', '')) or
            -- config management plugins spec changed
                NEW.argocd_config_management_plugins != OLD.argocd_config_management_plugins
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1, spec=jsonb_set(spec::jsonb, '{targetVersion}', 'null') WHERE instance_id = NEW.instance_id;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_inserted_argo_cd_instance_config
    AFTER INSERT ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();


DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_updated_argo_cd_instance_config
    AFTER UPDATE ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();


/*
Update all existing instance specs with image updater config to match delegate config
as currently that's the expected behavior
*/
UPDATE argo_cd_instance_config SET spec = jsonb_set(spec, '{image_updater_delegate}', spec -> 'repo_server_delegate') WHERE coalesce(spec ->> 'repo_server_delegate', '') != '';
UPDATE argo_cd_instance_config SET spec = jsonb_set(spec, '{app_set_delegate}', spec -> 'repo_server_delegate') WHERE coalesce(spec ->> 'repo_server_delegate', '') != '';

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('60', 'jiachengxu', 'changelog-root.yaml', NOW(), 62, '9:e0c549a74c554616fc92b838e37ae8c1', 'addColumn tableName=argo_cd_instance_config; sqlFile path=argo_cd_instance_config_triggers_v9.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::61::gdsoumya
ALTER TABLE public.argo_cd_cluster ADD status_observed_cred_rotation INTEGER;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('61', 'gdsoumya', 'changelog-root.yaml', NOW(), 63, '9:ac0b8448f471dac464f78fb3ebee9719', 'addColumn tableName=argo_cd_cluster', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::62::Marvin9
ALTER TABLE public.akuity_user ADD internal_metadata JSONB;

UPDATE akuity_user SET internal_metadata = '{ "added_in_free_trial_list": true }';

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('62', 'Marvin9', 'changelog-root.yaml', NOW(), 64, '9:de1c22bd2fcf0f51d94806216763ca32', 'addColumn tableName=akuity_user; sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::63::jiachengxu
CREATE OR REPLACE FUNCTION notify_argo_cd_instance_config_changed() RETURNS TRIGGER AS $$
DECLARE
    organization_owner VARCHAR;
BEGIN
    SELECT INTO organization_owner argo_cd_instance.organization_owner
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'modified'::text
                )::text
        );
    UPDATE argo_cd_instance SET generation = generation + 1 WHERE id = NEW.instance_id;
    -- upgrade all clusters to latest version if:
    IF TG_OP = 'UPDATE' and (
            -- instance argocd version changed
                NEW.version != OLD.version or
            -- image updater enabled/disabled
                NEW.argocd_image_updater_enable != OLD.argocd_image_updater_enable or
            -- git delegate enabled/disabled
                (coalesce(NEW.spec ->> 'repo_server_delegate', '') !=
                 coalesce(OLD.spec ->> 'repo_server_delegate', '')) or
            -- declarative management enabled/disabled
                NEW.spec->>'declarative_management_enabled' != OLD.spec ->> 'declarative_management_enabled' or
            -- image updater spec changed
                (coalesce(NEW.spec ->> 'image_updater_delegate', '') !=
                 coalesce(OLD.spec ->> 'image_updater_delegate', '')) or
            -- appset spec changed
                (coalesce(NEW.spec ->> 'app_set_delegate', '') !=
                 coalesce(OLD.spec ->> 'app_set_delegate', '')) or
            -- config management plugins spec changed
                (coalesce(NEW.argocd_config_management_plugins::jsonb, '{}'::jsonb) !=
                coalesce(OLD.argocd_config_management_plugins::jsonb, '{}'::jsonb))
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1, spec=jsonb_set(spec::jsonb, '{targetVersion}', 'null') WHERE instance_id = NEW.instance_id;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_inserted_argo_cd_instance_config
    AFTER INSERT ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();


DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_updated_argo_cd_instance_config
    AFTER UPDATE ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();


/*
Update all existing instance specs with image updater config to match delegate config
as currently that's the expected behavior
*/
UPDATE argo_cd_instance_config SET spec = jsonb_set(spec, '{image_updater_delegate}', spec -> 'repo_server_delegate') WHERE coalesce(spec ->> 'repo_server_delegate', '') != '';
UPDATE argo_cd_instance_config SET spec = jsonb_set(spec, '{app_set_delegate}', spec -> 'repo_server_delegate') WHERE coalesce(spec ->> 'repo_server_delegate', '') != '';

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('63', 'jiachengxu', 'changelog-root.yaml', NOW(), 65, '9:6852dec6fb3caeefdff827daaf842ac0', 'sqlFile path=argo_cd_instance_config_triggers_v10.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::64::gdsoumya
-- This file deletes the existing cluster trigger first and then updates all
-- existing ""/null cluster target versions to reported versions and then
-- recreates the same old func

DROP TRIGGER IF EXISTS bump_gen_on_updated_argo_cd_cluster ON argo_cd_cluster;

UPDATE argo_cd_cluster
SET
    spec = jsonb_set(spec, '{targetVersion}', to_jsonb(regexp_replace(status_agent_state ->> 'version', 'v([0-9]*)\.([0-9]*)\.([0-9]*)', '\1.\2.\3')::text))
WHERE coalesce(status_agent_state ->> 'version', '') != '' AND coalesce(spec ->> 'targetVersion', '') = '';

CREATE TRIGGER bump_gen_on_updated_argo_cd_cluster
    BEFORE UPDATE ON argo_cd_cluster
    FOR EACH ROW
    EXECUTE PROCEDURE bump_gen_argo_cd_cluster_updated();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('64', 'gdsoumya', 'changelog-root.yaml', NOW(), 66, '9:11255142af3439e88b90075235daed66', 'sqlFile path=argo_cd_cluster_fix_target_version.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::65::bikramnehra
CREATE OR REPLACE FUNCTION notify_argo_cd_instance_config_changed() RETURNS TRIGGER AS $$
DECLARE
    organization_owner VARCHAR;
BEGIN
    SELECT INTO organization_owner argo_cd_instance.organization_owner
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'modified'::text
                )::text
        );
    UPDATE argo_cd_instance SET generation = generation + 1 WHERE id = NEW.instance_id;
    -- upgrade all clusters to latest version if:
    IF TG_OP = 'UPDATE' and (
            -- instance argocd version changed
                NEW.version != OLD.version or
            -- image updater enabled/disabled
                NEW.argocd_image_updater_enable != OLD.argocd_image_updater_enable or
            -- git delegate enabled/disabled
                (coalesce(NEW.spec ->> 'repo_server_delegate', '') !=
                 coalesce(OLD.spec ->> 'repo_server_delegate', '')) or
            -- declarative management enabled/disabled
                NEW.spec->>'declarative_management_enabled' != OLD.spec ->> 'declarative_management_enabled' or
            -- image updater spec changed
                (coalesce(NEW.spec ->> 'image_updater_delegate', '') !=
                 coalesce(OLD.spec ->> 'image_updater_delegate', '')) or
            -- appset spec changed
                (coalesce(NEW.spec ->> 'app_set_delegate', '') !=
                 coalesce(OLD.spec ->> 'app_set_delegate', '')) or
            -- config management plugins spec changed
                (coalesce(NEW.argocd_config_management_plugins::jsonb, '{}'::jsonb) !=
                coalesce(OLD.argocd_config_management_plugins::jsonb, '{}'::jsonb))
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1, spec=jsonb_set(spec::jsonb, '{targetVersion}', 'null') WHERE instance_id = NEW.instance_id;
    END IF;
    -- upgrade generation for resource inclusion/exclusion changes:
    IF TG_OP = 'UPDATE' and (
            -- resource inclusions changed
                (coalesce(NEW.argocd_cm ->> 'resource.inclusions', '') !=
                 coalesce(OLD.argocd_cm ->> 'resource.inclusions', '')) or
            -- resource exclusions changed
                (coalesce(NEW.argocd_cm ->> 'resource.exclusions', '') !=
                 coalesce(OLD.argocd_cm ->> 'resource.exclusions', ''))
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1 WHERE instance_id = NEW.instance_id;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_inserted_argo_cd_instance_config
    AFTER INSERT ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();


DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_updated_argo_cd_instance_config
    AFTER UPDATE ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();


/*
Update all existing instance specs with image updater config to match delegate config
as currently that's the expected behavior
*/
UPDATE argo_cd_instance_config SET spec = jsonb_set(spec, '{image_updater_delegate}', spec -> 'repo_server_delegate') WHERE coalesce(spec ->> 'repo_server_delegate', '') != '';
UPDATE argo_cd_instance_config SET spec = jsonb_set(spec, '{app_set_delegate}', spec -> 'repo_server_delegate') WHERE coalesce(spec ->> 'repo_server_delegate', '') != '';

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('65', 'bikramnehra', 'changelog-root.yaml', NOW(), 67, '9:0eb4fc74b76779c50da42f606ceb80c3', 'sqlFile path=argo_cd_instance_config_triggers_v11.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::66::aborilov
ALTER TABLE public.argo_cd_instance ADD shard VARCHAR(255);

CREATE OR REPLACE FUNCTION notify_argo_cd_instance_config_changed() RETURNS TRIGGER AS $$
DECLARE
    organization_owner VARCHAR;
    shard VARCHAR;
BEGIN
    SELECT INTO organization_owner argo_cd_instance.organization_owner
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    SELECT INTO shard argo_cd_instance.shard
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'modified'::text,
                    'shard', shard::text
                )::text
        );
    UPDATE argo_cd_instance SET generation = generation + 1 WHERE id = NEW.instance_id;
    -- upgrade all clusters to latest version if:
    IF TG_OP = 'UPDATE' and (
            -- instance argocd version changed
                NEW.version != OLD.version or
            -- image updater enabled/disabled
                NEW.argocd_image_updater_enable != OLD.argocd_image_updater_enable or
            -- git delegate enabled/disabled
                (coalesce(NEW.spec ->> 'repo_server_delegate', '') !=
                 coalesce(OLD.spec ->> 'repo_server_delegate', '')) or
            -- declarative management enabled/disabled
                NEW.spec->>'declarative_management_enabled' != OLD.spec ->> 'declarative_management_enabled' or
            -- image updater spec changed
                (coalesce(NEW.spec ->> 'image_updater_delegate', '') !=
                 coalesce(OLD.spec ->> 'image_updater_delegate', '')) or
            -- appset spec changed
                (coalesce(NEW.spec ->> 'app_set_delegate', '') !=
                 coalesce(OLD.spec ->> 'app_set_delegate', '')) or
            -- config management plugins spec changed
                (coalesce(NEW.argocd_config_management_plugins::jsonb, '{}'::jsonb) !=
                coalesce(OLD.argocd_config_management_plugins::jsonb, '{}'::jsonb))
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1, spec=jsonb_set(spec::jsonb, '{targetVersion}', 'null') WHERE instance_id = NEW.instance_id;
    END IF;
    -- upgrade generation for resource inclusion/exclusion changes:
    IF TG_OP = 'UPDATE' and (
            -- resource inclusions changed
                (coalesce(NEW.argocd_cm ->> 'resource.inclusions', '') !=
                 coalesce(OLD.argocd_cm ->> 'resource.inclusions', '')) or
            -- resource exclusions changed
                (coalesce(NEW.argocd_cm ->> 'resource.exclusions', '') !=
                 coalesce(OLD.argocd_cm ->> 'resource.exclusions', ''))
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1 WHERE instance_id = NEW.instance_id;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_inserted_argo_cd_instance_config
    AFTER INSERT ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();


DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_updated_argo_cd_instance_config
    AFTER UPDATE ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();

CREATE OR REPLACE FUNCTION notify_argo_cd_instance_inserted() RETURNS TRIGGER AS $$

BEGIN
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'organization_owner', NEW.organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'added'::text,
                    'shard', NEW.shard::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance ON argo_cd_instance;
CREATE TRIGGER on_inserted_argo_cd_instance
AFTER INSERT ON argo_cd_instance
FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_inserted();

CREATE OR REPLACE FUNCTION notify_argo_cd_instance_updated() RETURNS TRIGGER AS $$
DECLARE
    updated_non_status_columns numeric;
BEGIN

    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value AND (n.key LIKE 'deletion_timestamp' OR n.key LIKE 'generation');


    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'organization_owner', OLD.organization_owner::text,
                    'spec_changed', (updated_non_status_columns > 0)::boolean,
                    'type', 'modified'::text,
                    'shard', NEW.shard::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_updated_argo_cd_instance ON argo_cd_instance;
CREATE TRIGGER on_updated_argo_cd_instance
    AFTER UPDATE ON argo_cd_instance
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_updated();

CREATE OR REPLACE FUNCTION notify_argo_cd_instance_deleted() RETURNS TRIGGER AS $$

BEGIN
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', OLD.id::text,
                    'organization_owner', OLD.organization_owner::text,
                    'spec_changed', false::boolean,
                    'type', 'deleted'::text,
                    'shard', OLD.shard::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_deleted_argo_cd_instance ON argo_cd_instance;
CREATE TRIGGER on_deleted_argo_cd_instance
    AFTER DELETE ON argo_cd_instance
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_deleted();

CREATE OR REPLACE FUNCTION notify_argo_cd_cluster_inserted() RETURNS TRIGGER AS $$
DECLARE
    shard VARCHAR;
BEGIN
    SELECT INTO shard argo_cd_instance.shard
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'argo_cd_cluster_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'instance_id', NEW.instance_id::text,
                    'spec_changed', true::boolean,
                    'type', 'added'::text,
                    'shard', shard::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_cluster ON argo_cd_cluster;
CREATE TRIGGER on_inserted_argo_cd_cluster
    AFTER INSERT ON argo_cd_cluster
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_cluster_inserted();

CREATE OR REPLACE FUNCTION notify_argo_cd_cluster_updated() RETURNS TRIGGER AS $$
DECLARE
    updated_non_status_columns numeric;
    shard VARCHAR;
BEGIN

    SELECT INTO shard argo_cd_instance.shard
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value AND n.key NOT LIKE 'status_%';

    PERFORM pg_notify(
            'argo_cd_cluster_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'instance_id', NEW.instance_id::text,
                    'spec_changed', (updated_non_status_columns > 0)::boolean,
                    'type', 'modified'::text,
                    'shard', shard::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_updated_argo_cd_cluster ON argo_cd_cluster;
CREATE TRIGGER on_updated_argo_cd_cluster
    AFTER UPDATE ON argo_cd_cluster
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_cluster_updated();

CREATE OR REPLACE FUNCTION notify_argo_cd_cluster_deleted() RETURNS TRIGGER AS $$
DECLARE
    shard VARCHAR;

BEGIN
    SELECT INTO shard argo_cd_instance.shard
    FROM argo_cd_instance WHERE id = OLD.instance_id;
    PERFORM pg_notify(
            'argo_cd_cluster_updated',
            json_build_object(
                    'id', OLD.id::text,
                    'instance_id', OLD.instance_id::text,
                    'spec_changed', false::boolean,
                    'type', 'deleted'::text,
                    'shard', shard::text
                )::text
        );

RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_deleted_argo_cd_cluster ON argo_cd_cluster;
CREATE TRIGGER on_deleted_argo_cd_cluster
    AFTER DELETE ON argo_cd_cluster
    FOR EACH ROW
    EXECUTE PROCEDURE notify_argo_cd_cluster_deleted();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('66', 'aborilov', 'changelog-root.yaml', NOW(), 68, '9:194b5a3e275381cc8ed2dd43d8a88617', 'addColumn tableName=argo_cd_instance; sqlFile path=argo_cd_instance_config_triggers_v12.sql; sqlFile path=argo_cd_instance_triggers_v5.sql; sqlFile path=argo_cd_cluster_triggers_v2.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::67::aborilov
UPDATE public.argo_cd_instance SET shard = '' WHERE shard IS NULL;

ALTER TABLE public.argo_cd_instance ALTER COLUMN  shard SET NOT NULL;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('67', 'aborilov', 'changelog-root.yaml', NOW(), 69, '9:7f9d1df0b2752a5a2de5aaaefb19cf85', 'addNotNullConstraint columnName=shard, constraintName=shard_not_null, tableName=argo_cd_instance', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::68::evgeny
ALTER TABLE public.akuity_user DROP COLUMN internal_metadata;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('68', 'evgeny', 'changelog-root.yaml', NOW(), 70, '9:f9bf69d46d889669a6e3ea23158b0f05', 'dropColumn columnName=internal_metadata, tableName=akuity_user', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::69::alexander
ALTER TABLE public.argo_cd_instance ALTER COLUMN  shard SET DEFAULT '';

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('69', 'alexander', 'changelog-root.yaml', NOW(), 71, '9:1b11eaa7d7a90dddcfd732f60cedc85b', 'addDefaultValue columnName=shard, tableName=argo_cd_instance', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::70::alex/gdsoumya
CREATE TABLE public.kargo_instance (id VARCHAR(50) NOT NULL, name VARCHAR(50) NOT NULL, organization_owner VARCHAR(50), generation INTEGER NOT NULL, status_observed_generation INTEGER, status_hostname VARCHAR(255), status_health JSONB, status_conditions JSONB, deletion_timestamp TIMESTAMP WITH TIME ZONE, creation_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL, status_info JSONB, CONSTRAINT kargo_instance_pkey PRIMARY KEY (id), CONSTRAINT fk_argo_cd_instance_organization_owner FOREIGN KEY (organization_owner) REFERENCES public.organization(id), UNIQUE (name));

CREATE TABLE public.kargo_instance_config (instance_id VARCHAR(50) NOT NULL, version VARCHAR(50), spec JSONB, private_spec TEXT, internal_spec JSONB, CONSTRAINT kargo_instance_config_pkey PRIMARY KEY (instance_id), CONSTRAINT fk_kargo_instance_config_instance_id FOREIGN KEY (instance_id) REFERENCES public.kargo_instance(id));

CREATE TABLE public.kargo_cluster (id VARCHAR(50) NOT NULL, instance_id VARCHAR(50) NOT NULL, name VARCHAR(50) NOT NULL, description VARCHAR(255), private_spec TEXT, status_manifests TEXT, status_conditions JSONB, status_agent_state JSONB, deletion_timestamp TIMESTAMP WITH TIME ZONE, creation_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL, generation INTEGER NOT NULL, status_observed_generation INTEGER, spec JSONB, namespace VARCHAR(63) DEFAULT 'akuity' NOT NULL, auto_upgrade_disabled BOOLEAN DEFAULT FALSE NOT NULL, status_observed_cred_rotation INTEGER, CONSTRAINT kargo_cluster_pkey PRIMARY KEY (id), CONSTRAINT fk_kargo_cluster_instance_id FOREIGN KEY (instance_id) REFERENCES public.kargo_instance(id));

CREATE OR REPLACE FUNCTION notify_kargo_instance_config_changed() RETURNS TRIGGER AS $$
DECLARE
    organization_owner VARCHAR;
BEGIN
    SELECT INTO organization_owner kargo_instance.organization_owner
    FROM kargo_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'kargo_instance_updated',
            json_build_object(
                    'id', NEW.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'modified'::text
                )::text
        );
    UPDATE kargo_instance SET generation = generation + 1 WHERE id = NEW.instance_id;
    -- upgrade all clusters to latest version if:
    IF TG_OP = 'UPDATE' and (
            -- instance kargo version changed
                NEW.version != OLD.version
        )THEN
        UPDATE kargo_cluster SET generation = generation + 1, spec=jsonb_set(spec::jsonb, '{targetVersion}', 'null') WHERE instance_id = NEW.instance_id;
    END IF;

    IF TG_OP = 'UPDATE' and (
           -- default shard cluster changed
                (coalesce(NEW.spec ->> 'default_shard_cluster', '') !=
                 coalesce(OLD.spec ->> 'default_shard_cluster', ''))
        )THEN
    UPDATE kargo_cluster SET generation = generation + 1 WHERE instance_id = NEW.instance_id;
    END IF;

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_kargo_instance_config ON kargo_instance_config;
CREATE TRIGGER on_inserted_kargo_instance_config
    AFTER INSERT ON kargo_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_kargo_instance_config_changed();


DROP TRIGGER IF EXISTS on_updated_kargo_instance_config ON kargo_instance_config;
CREATE TRIGGER on_updated_kargo_instance_config
    AFTER UPDATE ON kargo_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_kargo_instance_config_changed();

CREATE OR REPLACE FUNCTION notify_kargo_instance_inserted() RETURNS TRIGGER AS $$

BEGIN
    PERFORM pg_notify(
            'kargo_instance_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'organization_owner', NEW.organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'added'::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_kargo_instance ON kargo_instance;
CREATE TRIGGER on_inserted_kargo_instance
AFTER INSERT ON kargo_instance
FOR EACH ROW
EXECUTE PROCEDURE notify_kargo_instance_inserted();

CREATE OR REPLACE FUNCTION notify_kargo_instance_updated() RETURNS TRIGGER AS $$
DECLARE
    updated_non_status_columns numeric;
BEGIN

    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value AND (n.key LIKE 'deletion_timestamp' OR n.key LIKE 'generation');


    PERFORM pg_notify(
            'kargo_instance_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'organization_owner', OLD.organization_owner::text,
                    'spec_changed', (updated_non_status_columns > 0)::boolean,
                    'type', 'modified'::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_updated_kargo_instance ON kargo_instance;
CREATE TRIGGER on_updated_kargo_instance
    AFTER UPDATE ON kargo_instance
    FOR EACH ROW
EXECUTE PROCEDURE notify_kargo_instance_updated();

CREATE OR REPLACE FUNCTION notify_kargo_instance_deleted() RETURNS TRIGGER AS $$

BEGIN
    PERFORM pg_notify(
            'kargo_instance_updated',
            json_build_object(
                    'id', OLD.id::text,
                    'organization_owner', OLD.organization_owner::text,
                    'spec_changed', false::boolean,
                    'type', 'deleted'::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_deleted_kargo_instance ON kargo_instance;
CREATE TRIGGER on_deleted_kargo_instance
    AFTER DELETE ON kargo_instance
    FOR EACH ROW
EXECUTE PROCEDURE notify_kargo_instance_deleted();

CREATE OR REPLACE FUNCTION notify_kargo_cluster_inserted() RETURNS TRIGGER AS $$

BEGIN
    PERFORM pg_notify(
            'kargo_cluster_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'instance_id', NEW.instance_id::text,
                    'spec_changed', true::boolean,
                    'type', 'added'::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_kargo_cluster ON kargo_cluster;
CREATE TRIGGER on_inserted_kargo_cluster
    AFTER INSERT ON kargo_cluster
    FOR EACH ROW
EXECUTE PROCEDURE notify_kargo_cluster_inserted();

CREATE OR REPLACE FUNCTION notify_kargo_cluster_updated() RETURNS TRIGGER AS $$
DECLARE
    updated_non_status_columns numeric;
BEGIN

    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value AND n.key NOT LIKE 'status_%';

    PERFORM pg_notify(
            'kargo_cluster_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'instance_id', NEW.instance_id::text,
                    'spec_changed', (updated_non_status_columns > 0)::boolean,
                    'type', 'modified'::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_updated_kargo_cluster ON kargo_cluster;
CREATE TRIGGER on_updated_kargo_cluster
    AFTER UPDATE ON kargo_cluster
    FOR EACH ROW
EXECUTE PROCEDURE notify_kargo_cluster_updated();

CREATE OR REPLACE FUNCTION notify_kargo_cluster_deleted() RETURNS TRIGGER AS $$

BEGIN
    PERFORM pg_notify(
            'kargo_cluster_updated',
            json_build_object(
                    'id', OLD.id::text,
                    'instance_id', OLD.instance_id::text,
                    'spec_changed', false::boolean,
                    'type', 'deleted'::text
                )::text
        );

RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_deleted_kargo_cluster ON kargo_cluster;
CREATE TRIGGER on_deleted_kargo_cluster
    AFTER DELETE ON kargo_cluster
    FOR EACH ROW
    EXECUTE PROCEDURE notify_kargo_cluster_deleted();

CREATE OR REPLACE FUNCTION bump_gen_kargo_cluster_updated() RETURNS TRIGGER AS $$
DECLARE
updated_non_status_columns numeric;
BEGIN
    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value AND n.key NOT LIKE 'status_%' AND n.key != 'auto_upgrade_disabled' ;

    IF updated_non_status_columns > 0 OR (NEW.auto_upgrade_disabled != OLD.auto_upgrade_disabled AND NEW.auto_upgrade_disabled=false) THEN
            NEW.generation = OLD.generation + 1;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS bump_gen_on_updated_kargo_cluster ON kargo_cluster;
CREATE TRIGGER bump_gen_on_updated_kargo_cluster
    BEFORE UPDATE ON kargo_cluster
    FOR EACH ROW
    EXECUTE PROCEDURE bump_gen_kargo_cluster_updated();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('70', 'alex/gdsoumya', 'changelog-root.yaml', NOW(), 72, '9:cd83ffb98e3a53c35a903d64416f247b', 'createTable tableName=kargo_instance; createTable tableName=kargo_instance_config; createTable tableName=kargo_cluster; sqlFile path=kargo_instance_config_triggers.sql; sqlFile path=kargo_instance_triggers.sql; sqlFile path=kargo_cluster_triggers....', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::71::gdsoumya
CREATE OR REPLACE FUNCTION notify_argo_cd_instance_config_changed() RETURNS TRIGGER AS $$
DECLARE
    organization_owner VARCHAR;
    shard VARCHAR;
BEGIN
    SELECT INTO organization_owner argo_cd_instance.organization_owner
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    SELECT INTO shard argo_cd_instance.shard
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'modified'::text,
                    'shard', shard::text
                )::text
        );
    UPDATE argo_cd_instance SET generation = generation + 1 WHERE id = NEW.instance_id;
    -- upgrade all clusters to latest version if:
    IF TG_OP = 'UPDATE' and (
            -- instance argocd version changed
                NEW.version != OLD.version or
            -- image updater enabled/disabled
                NEW.argocd_image_updater_enable != OLD.argocd_image_updater_enable or
            -- git delegate enabled/disabled
                (coalesce(NEW.spec ->> 'repo_server_delegate', '') !=
                 coalesce(OLD.spec ->> 'repo_server_delegate', '')) or
            -- declarative management enabled/disabled
                NEW.spec->>'declarative_management_enabled' != OLD.spec ->> 'declarative_management_enabled' or
            -- image updater spec changed
                (coalesce(NEW.spec ->> 'image_updater_delegate', '') !=
                 coalesce(OLD.spec ->> 'image_updater_delegate', '')) or
            -- appset spec changed
                (coalesce(NEW.spec ->> 'app_set_delegate', '') !=
                 coalesce(OLD.spec ->> 'app_set_delegate', '')) or
            -- config management plugins spec changed
                (coalesce(NEW.argocd_config_management_plugins::jsonb, '{}'::jsonb) !=
                coalesce(OLD.argocd_config_management_plugins::jsonb, '{}'::jsonb))
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1, spec=jsonb_set(spec::jsonb, '{targetVersion}', 'null') WHERE instance_id = NEW.instance_id;
    END IF;
    -- upgrade generation for resource inclusion/exclusion changes:
    IF TG_OP = 'UPDATE' and (
            -- resource inclusions changed
                (coalesce(NEW.argocd_cm ->> 'resource.inclusions', '') !=
                 coalesce(OLD.argocd_cm ->> 'resource.inclusions', '')) or
            -- resource exclusions changed
                (coalesce(NEW.argocd_cm ->> 'resource.exclusions', '') !=
                 coalesce(OLD.argocd_cm ->> 'resource.exclusions', '')) or
             -- declarative management app set policy changed when appset delegate is set
                (coalesce(NEW.spec->> 'appset_policy', '') !=
                coalesce(OLD.spec->> 'appset_policy', '') and
                coalesce(NEW.spec ->> 'app_set_delegate', '') != '')
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1 WHERE instance_id = NEW.instance_id;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_inserted_argo_cd_instance_config
    AFTER INSERT ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();


DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_updated_argo_cd_instance_config
    AFTER UPDATE ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('71', 'gdsoumya', 'changelog-root.yaml', NOW(), 73, '9:035e1d7dd34d797347206cef1a0a3e9e', 'sqlFile path=argo_cd_instance_config_triggers_v13.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::72::aborilov
DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;

UPDATE "argo_cd_instance_config"
SET "internal_spec" = 
  CASE 
    WHEN "internal_spec" IS NULL THEN '{"k3s_image": "quay.io/akuity/rancher/k3s:v1.24.12-k3s1"}'
    ELSE jsonb_set("internal_spec", '{k3s_image}', '"quay.io/akuity/rancher/k3s:v1.24.12-k3s1"')
  END
WHERE COALESCE(internal_spec ->> 'k3s_image', '') != 'quay.io/akuity/rancher/k3s:v1.24.12-k3s1';

CREATE TRIGGER on_updated_argo_cd_instance_config
    AFTER UPDATE ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('72', 'aborilov', 'changelog-root.yaml', NOW(), 74, '9:8319de6dfba2d787594af19d0a428bd9', 'sqlFile path=argo_cd_instance_k3s_version_upgrade.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::73::remington
ALTER TABLE public.organization ADD oidc_map JSONB;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('73', 'remington', 'changelog-root.yaml', NOW(), 75, '9:1cf23a849df44f23ff6ef9362199125f', 'addColumn tableName=organization', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::74::evgeny
CREATE TABLE public.audit_log_archive (id VARCHAR(50) NOT NULL, organization_id VARCHAR(50) NOT NULL, start_date TIMESTAMP WITH TIME ZONE NOT NULL, end_date TIMESTAMP WITH TIME ZONE NOT NULL, start_record VARCHAR(50) NOT NULL, end_record VARCHAR(50) NOT NULL, records INTEGER NOT NULL, days INTEGER NOT NULL, path VARCHAR(255) NOT NULL, checksum VARCHAR(44) NOT NULL, creation_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL, CONSTRAINT audit_log_archive_pkey PRIMARY KEY (id), CONSTRAINT fk_audit_log_archives_start_record FOREIGN KEY (start_record) REFERENCES public.audit_log(id), CONSTRAINT fk_audit_log_archives_end_record FOREIGN KEY (end_record) REFERENCES public.audit_log(id), CONSTRAINT fk_audit_log_archives_organization_id FOREIGN KEY (organization_id) REFERENCES public.organization(id));

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('74', 'evgeny', 'changelog-root.yaml', NOW(), 76, '9:eee1fbe125d8c5356dc1c9464fee41c6', 'createTable tableName=audit_log_archive', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::75::gdsoumya
CREATE OR REPLACE FUNCTION notify_argo_cd_instance_config_changed() RETURNS TRIGGER AS $$
DECLARE
    organization_owner VARCHAR;
    shard VARCHAR;
BEGIN
    SELECT INTO organization_owner argo_cd_instance.organization_owner
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    SELECT INTO shard argo_cd_instance.shard
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'modified'::text,
                    'shard', shard::text
                )::text
        );
    UPDATE argo_cd_instance SET generation = generation + 1 WHERE id = NEW.instance_id;
    -- upgrade all clusters to latest version if:
    IF TG_OP = 'UPDATE' and (
            -- instance argocd version changed
                NEW.version != OLD.version or
            -- image updater enabled/disabled
                NEW.argocd_image_updater_enable != OLD.argocd_image_updater_enable or
            -- git delegate enabled/disabled
                (coalesce(NEW.spec ->> 'repo_server_delegate', '') !=
                 coalesce(OLD.spec ->> 'repo_server_delegate', '')) or
            -- image updater spec changed
                (coalesce(NEW.spec ->> 'image_updater_delegate', '') !=
                 coalesce(OLD.spec ->> 'image_updater_delegate', '')) or
            -- appset spec changed
                (coalesce(NEW.spec ->> 'app_set_delegate', '') !=
                 coalesce(OLD.spec ->> 'app_set_delegate', '')) or
            -- config management plugins spec changed
                (coalesce(NEW.argocd_config_management_plugins::jsonb, '{}'::jsonb) !=
                coalesce(OLD.argocd_config_management_plugins::jsonb, '{}'::jsonb))
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1, spec=jsonb_set(spec::jsonb, '{targetVersion}', 'null') WHERE instance_id = NEW.instance_id;
    END IF;
    -- upgrade generation for resource inclusion/exclusion changes:
    IF TG_OP = 'UPDATE' and (
            -- resource inclusions changed
                (coalesce(NEW.argocd_cm ->> 'resource.inclusions', '') !=
                 coalesce(OLD.argocd_cm ->> 'resource.inclusions', '')) or
            -- resource exclusions changed
                (coalesce(NEW.argocd_cm ->> 'resource.exclusions', '') !=
                 coalesce(OLD.argocd_cm ->> 'resource.exclusions', '')) or
            -- appset enabled/disabled
                 (coalesce(NEW.spec ->> 'appset_disabled', 'false') !=
                 coalesce(OLD.spec ->> 'appset_disabled', 'false')) or
             -- declarative management app set policy changed when appset delegate is set
                (coalesce(NEW.spec->> 'appset_policy', '') !=
                coalesce(OLD.spec->> 'appset_policy', '') and
                coalesce(NEW.spec ->> 'app_set_delegate', '') != '')
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1 WHERE instance_id = NEW.instance_id;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_inserted_argo_cd_instance_config
    AFTER INSERT ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();


DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_updated_argo_cd_instance_config
    AFTER UPDATE ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('75', 'gdsoumya', 'changelog-root.yaml', NOW(), 77, '9:028f5ee847ff4f2482ebee8b7ebe9391', 'sqlFile path=argo_cd_instance_config_triggers_v14.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::76::evgeny
CREATE UNIQUE INDEX audit_log_archive_path ON audit_log_archive (path);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('76', 'evgeny', 'changelog-root.yaml', NOW(), 78, '9:33c7707259b4eb63d66ccd668e9395f4', 'sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::77::bikramnehra
CREATE OR REPLACE FUNCTION notify_argo_cd_instance_config_changed() RETURNS TRIGGER AS $$
DECLARE
    organization_owner VARCHAR;
    shard VARCHAR;
BEGIN
    SELECT INTO organization_owner argo_cd_instance.organization_owner
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    SELECT INTO shard argo_cd_instance.shard
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'modified'::text,
                    'shard', shard::text
                )::text
        );
    UPDATE argo_cd_instance SET generation = generation + 1 WHERE id = NEW.instance_id;
    -- upgrade all clusters to latest version if:
    IF TG_OP = 'UPDATE' and (
            -- instance argocd version changed
                NEW.version != OLD.version or
            -- image updater enabled/disabled
                NEW.argocd_image_updater_enable != OLD.argocd_image_updater_enable or
            -- git delegate enabled/disabled
                (coalesce(NEW.spec ->> 'repo_server_delegate', '') !=
                 coalesce(OLD.spec ->> 'repo_server_delegate', '')) or
            -- image updater spec changed
                (coalesce(NEW.spec ->> 'image_updater_delegate', '') !=
                 coalesce(OLD.spec ->> 'image_updater_delegate', '')) or
            -- appset spec changed
                (coalesce(NEW.spec ->> 'app_set_delegate', '') !=
                 coalesce(OLD.spec ->> 'app_set_delegate', '')) or
            -- agent permissions rules spec changed
                (coalesce(NEW.spec ->> 'agent_permissions_rules', '') !=
                 coalesce(OLD.spec ->> 'agent_permissions_rules', '')) or
            -- config management plugins spec changed
                (coalesce(NEW.argocd_config_management_plugins::jsonb, '{}'::jsonb) !=
                coalesce(OLD.argocd_config_management_plugins::jsonb, '{}'::jsonb))
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1, spec=jsonb_set(spec::jsonb, '{targetVersion}', 'null') WHERE instance_id = NEW.instance_id;
    END IF;
    -- upgrade generation for resource inclusion/exclusion changes:
    IF TG_OP = 'UPDATE' and (
            -- resource inclusions changed
                (coalesce(NEW.argocd_cm ->> 'resource.inclusions', '') !=
                 coalesce(OLD.argocd_cm ->> 'resource.inclusions', '')) or
            -- resource exclusions changed
                (coalesce(NEW.argocd_cm ->> 'resource.exclusions', '') !=
                 coalesce(OLD.argocd_cm ->> 'resource.exclusions', '')) or
            -- appset enabled/disabled
                 (coalesce(NEW.spec ->> 'appset_disabled', 'false') !=
                 coalesce(OLD.spec ->> 'appset_disabled', 'false')) or
             -- declarative management app set policy changed when appset delegate is set
                (coalesce(NEW.spec->> 'appset_policy', '') !=
                coalesce(OLD.spec->> 'appset_policy', '') and
                coalesce(NEW.spec ->> 'app_set_delegate', '') != '')
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1 WHERE instance_id = NEW.instance_id;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_inserted_argo_cd_instance_config
    AFTER INSERT ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();


DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_updated_argo_cd_instance_config
    AFTER UPDATE ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('77', 'bikramnehra', 'changelog-root.yaml', NOW(), 79, '9:87f42cc9f8b3ca05500024335e95db8d', 'sqlFile path=argo_cd_instance_config_triggers_v15.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::78::gdsoumya
DROP TABLE public.kargo_cluster CASCADE;

DROP TABLE public.kargo_instance_config CASCADE;

DROP TABLE public.kargo_instance CASCADE;

CREATE TABLE public.kargo_instance (id VARCHAR(50) NOT NULL, name VARCHAR(50) NOT NULL, shard VARCHAR(255) DEFAULT '' NOT NULL, organization_owner VARCHAR(50), generation INTEGER NOT NULL, status_observed_generation INTEGER, status_hostname VARCHAR(255), status_health JSONB, status_conditions JSONB, deletion_timestamp TIMESTAMP WITH TIME ZONE, creation_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL, status_info JSONB, description VARCHAR(255), CONSTRAINT kargo_instance_pkey PRIMARY KEY (id), CONSTRAINT fk_argo_cd_instance_organization_owner FOREIGN KEY (organization_owner) REFERENCES public.organization(id), UNIQUE (name));

CREATE TABLE public.kargo_instance_config (instance_id VARCHAR(50) NOT NULL, version VARCHAR(50), spec JSONB, private_spec TEXT, internal_spec JSONB, controller_cm JSONB, webhook_cm JSONB, oidc_config JSONB, api_secret TEXT, api_cm JSONB, CONSTRAINT kargo_instance_config_pkey PRIMARY KEY (instance_id), CONSTRAINT fk_kargo_instance_config_instance_id FOREIGN KEY (instance_id) REFERENCES public.kargo_instance(id));

CREATE TABLE public.kargo_agent (id VARCHAR(50) NOT NULL, instance_id VARCHAR(50) NOT NULL, remote_argocd_instance_id VARCHAR(50), name VARCHAR(50) NOT NULL, description VARCHAR(255), private_spec TEXT, status_manifests TEXT, status_conditions JSONB, status_agent_state JSONB, deletion_timestamp TIMESTAMP WITH TIME ZONE, creation_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL, generation INTEGER NOT NULL, status_observed_generation INTEGER, spec JSONB, namespace VARCHAR(63) DEFAULT 'akuity' NOT NULL, auto_upgrade_disabled BOOLEAN DEFAULT FALSE NOT NULL, status_observed_cred_rotation INTEGER, CONSTRAINT kargo_agent_pkey PRIMARY KEY (id), CONSTRAINT fk_kargo_agent_instance_id FOREIGN KEY (instance_id) REFERENCES public.kargo_instance(id), CONSTRAINT fk_remote_argocd_instance_id FOREIGN KEY (remote_argocd_instance_id) REFERENCES public.argo_cd_instance(id));

CREATE OR REPLACE FUNCTION notify_kargo_instance_config_changed() RETURNS TRIGGER AS $$
DECLARE
    shard VARCHAR;
    organization_owner VARCHAR;
BEGIN
    SELECT INTO organization_owner kargo_instance.organization_owner
    FROM kargo_instance WHERE id = NEW.instance_id;

    SELECT INTO shard kargo_instance.shard
    FROM kargo_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'kargo_instance_updated',
            json_build_object(
                    'id', NEW.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'modified'::text,
                    'shard', shard::text
                )::text
        );
    UPDATE kargo_instance SET generation = generation + 1 WHERE id = NEW.instance_id;
    -- upgrade all agents to latest version if:
    IF TG_OP = 'UPDATE' and (
            -- instance kargo version changed
                NEW.version != OLD.version
        )THEN
        UPDATE kargo_agent SET generation = generation + 1, spec=jsonb_set(spec::jsonb, '{targetVersion}', 'null') WHERE instance_id = NEW.instance_id;
    END IF;

    IF TG_OP = 'UPDATE' and (
           -- default shard agent changed
                (coalesce(NEW.spec ->> 'default_shard_agent', '') !=
                 coalesce(OLD.spec ->> 'default_shard_agent', ''))
        )THEN
    UPDATE kargo_agent SET generation = generation + 1 WHERE instance_id = NEW.instance_id;
    END IF;

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_kargo_instance_config ON kargo_instance_config;
CREATE TRIGGER on_inserted_kargo_instance_config
    AFTER INSERT ON kargo_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_kargo_instance_config_changed();


DROP TRIGGER IF EXISTS on_updated_kargo_instance_config ON kargo_instance_config;
CREATE TRIGGER on_updated_kargo_instance_config
    AFTER UPDATE ON kargo_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_kargo_instance_config_changed();

CREATE OR REPLACE FUNCTION notify_kargo_instance_inserted() RETURNS TRIGGER AS $$
DECLARE
    shard VARCHAR;
BEGIN
    PERFORM pg_notify(
            'kargo_instance_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'organization_owner', NEW.organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'added'::text,
                    'shard', NEW.shard::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_kargo_instance ON kargo_instance;
CREATE TRIGGER on_inserted_kargo_instance
AFTER INSERT ON kargo_instance
FOR EACH ROW
EXECUTE PROCEDURE notify_kargo_instance_inserted();

CREATE OR REPLACE FUNCTION notify_kargo_instance_updated() RETURNS TRIGGER AS $$
DECLARE
    shard VARCHAR;
    updated_non_status_columns numeric;
BEGIN

    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value AND (n.key LIKE 'deletion_timestamp' OR n.key LIKE 'generation');


    PERFORM pg_notify(
            'kargo_instance_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'organization_owner', OLD.organization_owner::text,
                    'spec_changed', (updated_non_status_columns > 0)::boolean,
                    'type', 'modified'::text,
                    'shard', NEW.shard::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_updated_kargo_instance ON kargo_instance;
CREATE TRIGGER on_updated_kargo_instance
    AFTER UPDATE ON kargo_instance
    FOR EACH ROW
EXECUTE PROCEDURE notify_kargo_instance_updated();

CREATE OR REPLACE FUNCTION notify_kargo_instance_deleted() RETURNS TRIGGER AS $$
DECLARE
    shard VARCHAR;
BEGIN
    PERFORM pg_notify(
            'kargo_instance_updated',
            json_build_object(
                    'id', OLD.id::text,
                    'organization_owner', OLD.organization_owner::text,
                    'spec_changed', false::boolean,
                    'type', 'deleted'::text,
                    'shard', OLD.shard::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_deleted_kargo_instance ON kargo_instance;
CREATE TRIGGER on_deleted_kargo_instance
    AFTER DELETE ON kargo_instance
    FOR EACH ROW
EXECUTE PROCEDURE notify_kargo_instance_deleted();

CREATE OR REPLACE FUNCTION notify_kargo_agent_inserted() RETURNS TRIGGER AS $$
DECLARE
    shard VARCHAR;
BEGIN
    SELECT INTO shard kargo_instance.shard
    FROM kargo_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'kargo_agent_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'instance_id', NEW.instance_id::text,
                    'spec_changed', true::boolean,
                    'type', 'added'::text,
                    'shard', shard::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_kargo_agent ON kargo_agent;
CREATE TRIGGER on_inserted_kargo_agent
    AFTER INSERT ON kargo_agent
    FOR EACH ROW
EXECUTE PROCEDURE notify_kargo_agent_inserted();

CREATE OR REPLACE FUNCTION notify_kargo_agent_updated() RETURNS TRIGGER AS $$
DECLARE
    shard VARCHAR;
    updated_non_status_columns numeric;
BEGIN

    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value AND n.key NOT LIKE 'status_%';

    SELECT INTO shard kargo_instance.shard
    FROM kargo_instance WHERE id = NEW.instance_id;

    PERFORM pg_notify(
            'kargo_agent_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'instance_id', NEW.instance_id::text,
                    'spec_changed', (updated_non_status_columns > 0)::boolean,
                    'type', 'modified'::text,
                    'shard', shard::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_updated_kargo_agent ON kargo_agent;
CREATE TRIGGER on_updated_kargo_agent
    AFTER UPDATE ON kargo_agent
    FOR EACH ROW
EXECUTE PROCEDURE notify_kargo_agent_updated();

CREATE OR REPLACE FUNCTION notify_kargo_agent_deleted() RETURNS TRIGGER AS $$
DECLARE
    shard VARCHAR;
BEGIN
    SELECT INTO shard kargo_instance.shard
    FROM kargo_instance WHERE id = OLD.instance_id;
    PERFORM pg_notify(
            'kargo_agent_updated',
            json_build_object(
                    'id', OLD.id::text,
                    'instance_id', OLD.instance_id::text,
                    'spec_changed', false::boolean,
                    'type', 'deleted'::text,
                    'shard', shard::text
                )::text
        );

RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_deleted_kargo_agent ON kargo_agent;
CREATE TRIGGER on_deleted_kargo_agent
    AFTER DELETE ON kargo_agent
    FOR EACH ROW
    EXECUTE PROCEDURE notify_kargo_agent_deleted();

CREATE OR REPLACE FUNCTION bump_gen_kargo_agent_updated() RETURNS TRIGGER AS $$
DECLARE
updated_non_status_columns numeric;
BEGIN
    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value AND n.key NOT LIKE 'status_%' AND n.key != 'auto_upgrade_disabled' ;

    IF updated_non_status_columns > 0 OR (NEW.auto_upgrade_disabled != OLD.auto_upgrade_disabled AND NEW.auto_upgrade_disabled=false) THEN
            NEW.generation = OLD.generation + 1;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS bump_gen_on_updated_kargo_agent ON kargo_agent;
CREATE TRIGGER bump_gen_on_updated_kargo_agent
    BEFORE UPDATE ON kargo_agent
    FOR EACH ROW
    EXECUTE PROCEDURE bump_gen_kargo_agent_updated();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('78', 'gdsoumya', 'changelog-root.yaml', NOW(), 80, '9:169ec86a67dfc2160a22a3671083a3b2', 'dropTable tableName=kargo_cluster; dropTable tableName=kargo_instance_config; dropTable tableName=kargo_instance; createTable tableName=kargo_instance; createTable tableName=kargo_instance_config; createTable tableName=kargo_agent; sqlFile path=ka...', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::79::goldin
ALTER TABLE public.audit_log ADD is_deleted BOOLEAN DEFAULT FALSE NOT NULL;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('79', 'goldin', 'changelog-root.yaml', NOW(), 81, '9:c7f4cfcf2b5d49ff24153bbbd80adf93', 'addColumn tableName=audit_log', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::80::sunghoonkang
CREATE TABLE public.organization_plan (id VARCHAR(50) NOT NULL, name VARCHAR(50) NOT NULL, default_plan BOOLEAN DEFAULT FALSE, features JSONB, quota JSONB, CONSTRAINT organization_plan_pkey PRIMARY KEY (id));

CREATE UNIQUE INDEX organization_plan_default_plan ON organization_plan (default_plan)
WHERE organization_plan.default_plan = true;

ALTER TABLE public.organization ADD plan VARCHAR(50);

ALTER TABLE public.organization ADD CONSTRAINT fk_organization_plan FOREIGN KEY (plan) REFERENCES public.organization_plan (id);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('80', 'sunghoonkang', 'changelog-root.yaml', NOW(), 82, '9:d769bfffcbe59b895f4e351606854e81', 'createTable tableName=organization_plan; sql; addColumn tableName=organization', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::81::jiachengxu
CREATE TABLE public.team (id VARCHAR(50) NOT NULL, organization_id VARCHAR(50) NOT NULL, name VARCHAR(50) NOT NULL, creation_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL, description VARCHAR(255), CONSTRAINT team_pkey PRIMARY KEY (id), CONSTRAINT fk_team_organization_id FOREIGN KEY (organization_id) REFERENCES public.organization(id));

CREATE TABLE public.team_user (id VARCHAR(50) NOT NULL, team_id VARCHAR(50) NOT NULL, user_id VARCHAR(50) NOT NULL, CONSTRAINT team_user_pkey PRIMARY KEY (id), CONSTRAINT fk_team_user_user_id FOREIGN KEY (user_id) REFERENCES public.akuity_user(id), CONSTRAINT fk_team_user_team_id FOREIGN KEY (team_id) REFERENCES public.team(id));

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('81', 'jiachengxu', 'changelog-root.yaml', NOW(), 83, '9:f89dda07b8b25147766c8f30b7e25a81', 'createTable tableName=team; createTable tableName=team_user', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::82::gdsoumya
ALTER TABLE public.kargo_instance_config ADD subdomain VARCHAR(50);

ALTER TABLE public.kargo_instance_config ADD UNIQUE (subdomain);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('82', 'gdsoumya', 'changelog-root.yaml', NOW(), 84, '9:785dbbaec479ea95179ae7a0d3f7cd1e', 'addColumn tableName=kargo_instance_config', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::83::jiachengxu
ALTER TABLE public.team_user ADD organization_id VARCHAR(50) NOT NULL;

ALTER TABLE public.team_user ADD CONSTRAINT fk_team_organization_id FOREIGN KEY (organization_id) REFERENCES public.organization (id);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('83', 'jiachengxu', 'changelog-root.yaml', NOW(), 85, '9:ae6098a419ee48240958d049f35800d1', 'addColumn tableName=team_user', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::84::pavel
CREATE TABLE public.custom_role (id VARCHAR(50) NOT NULL, name VARCHAR(50) NOT NULL, description VARCHAR(255), policy TEXT, organization_id VARCHAR(50) NOT NULL, CONSTRAINT custom_role_pkey PRIMARY KEY (id, organization_id), CONSTRAINT fk_organization_user_organization_id FOREIGN KEY (organization_id) REFERENCES public.organization(id));

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('84', 'pavel', 'changelog-root.yaml', NOW(), 86, '9:102d71c17adf633fa5759e3c59f25c52', 'createTable tableName=custom_role', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::85::remington
ALTER TABLE public.organization DROP CONSTRAINT fk_organization_plan;

DO $$ DECLARE constraint_name varchar;
BEGIN
  SELECT tc.CONSTRAINT_NAME into strict constraint_name
    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
    WHERE CONSTRAINT_TYPE = 'PRIMARY KEY'
      AND TABLE_NAME = 'organization_plan' AND TABLE_SCHEMA = 'public';
    EXECUTE 'alter table public.organization_plan drop constraint "' || constraint_name || '"';
END $$;

ALTER TABLE public.organization_plan ADD PRIMARY KEY (name);

ALTER TABLE public.organization_plan ADD CONSTRAINT unique_organization_plan_name UNIQUE (name);

ALTER TABLE public.organization_plan ALTER COLUMN  id DROP NOT NULL;

ALTER TABLE public.organization ADD CONSTRAINT fk_organization_plan FOREIGN KEY (plan) REFERENCES public.organization_plan (name);

ALTER TABLE public.organization_plan ADD billing_authority VARCHAR(50) NOT NULL;

ALTER TABLE public.organization_plan ADD product_id VARCHAR(50) NOT NULL;

ALTER TABLE public.organization_plan ADD display_name VARCHAR(50) NOT NULL;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('85', 'remington', 'changelog-root.yaml', NOW(), 87, '9:fe35083a8716b0a556234097fe66c8a0', 'dropForeignKeyConstraint baseTableName=organization, constraintName=fk_organization_plan; dropPrimaryKey tableName=organization_plan; addPrimaryKey tableName=organization_plan; addUniqueConstraint constraintName=unique_organization_plan_name, tabl...', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::86::remington
CREATE TABLE public.addon_plan (included_with_plan VARCHAR(50), included_quantity INTEGER DEFAULT 0, name VARCHAR(50) NOT NULL, product_id VARCHAR(50) NOT NULL, min_quantity INTEGER DEFAULT 0 NOT NULL, max_quantity INTEGER DEFAULT 100 NOT NULL, description VARCHAR(255), quota JSONB, display_name VARCHAR(50) NOT NULL, CONSTRAINT addon_plan_pkey PRIMARY KEY (name), CONSTRAINT fk_plan_addon_included_with_plan FOREIGN KEY (included_with_plan) REFERENCES public.organization_plan(name));

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('86', 'remington', 'changelog-root.yaml', NOW(), 88, '9:3bfc60e124fe4a300fbaecf329375f46', 'createTable tableName=addon_plan', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::87::pavel
ALTER TABLE public.audit_log ADD count INTEGER DEFAULT 1 NOT NULL;

ALTER TABLE public.audit_log ADD is_aggregated BOOLEAN DEFAULT FALSE NOT NULL;

ALTER TABLE public.audit_log ADD last_occurred_timestamp TIMESTAMP WITH TIME ZONE;

CREATE INDEX audit_log_timestamp_is_aggregated ON audit_log (timestamp, is_aggregated);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('87', 'pavel', 'changelog-root.yaml', NOW(), 89, '9:7999ac44178f47b4a13c1110611c5444', 'addColumn tableName=audit_log; sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::88::gdsoumya
ALTER TABLE public.organization ADD max_kargo_projects INTEGER DEFAULT 0 NOT NULL;

ALTER TABLE public.organization ADD max_kargo_instances INTEGER DEFAULT 0 NOT NULL;

ALTER TABLE public.organization ADD max_kargo_agents INTEGER DEFAULT 0 NOT NULL;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('88', 'gdsoumya', 'changelog-root.yaml', NOW(), 90, '9:a5615f941fdb040c81f2936f6f35e5db', 'addColumn tableName=organization', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::89::jiachengxu
ALTER TABLE public.argo_cd_cluster ADD readonly_settings_changed_generation INTEGER;

CREATE OR REPLACE FUNCTION bump_gen_argo_cd_cluster_updated() RETURNS TRIGGER AS $$
DECLARE
updated_non_status_columns numeric;
BEGIN
    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value AND n.key NOT LIKE 'status_%' AND n.key != 'auto_upgrade_disabled' ;

    IF updated_non_status_columns > 0 OR (NEW.auto_upgrade_disabled != OLD.auto_upgrade_disabled AND NEW.auto_upgrade_disabled=false) THEN
            NEW.generation = OLD.generation + 1;
    END IF;
    -- read-only spec changed
    IF NEW.namespace != OLD.namespace OR
       NEW.namespace_scoped != OLD.namespace_scoped OR
       (coalesce(NEW.spec ->> 'appReplication', 'false') != coalesce(OLD.spec ->> 'appReplication', 'false')) THEN
            NEW.readonly_settings_changed_generation = NEW.generation;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS bump_gen_on_updated_argo_cd_cluster ON argo_cd_cluster;
CREATE TRIGGER bump_gen_on_updated_argo_cd_cluster
    BEFORE UPDATE ON argo_cd_cluster
    FOR EACH ROW
    EXECUTE PROCEDURE bump_gen_argo_cd_cluster_updated();

CREATE OR REPLACE FUNCTION notify_argo_cd_instance_config_changed() RETURNS TRIGGER AS $$
DECLARE
    organization_owner VARCHAR;
    shard VARCHAR;
BEGIN
    SELECT INTO organization_owner argo_cd_instance.organization_owner
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    SELECT INTO shard argo_cd_instance.shard
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'modified'::text,
                    'shard', shard::text
                )::text
        );
    UPDATE argo_cd_instance SET generation = generation + 1 WHERE id = NEW.instance_id;
    -- upgrade all clusters to latest version if:
    IF TG_OP = 'UPDATE' and (
            -- instance argocd version changed
                NEW.version != OLD.version or
            -- image updater enabled/disabled
                NEW.argocd_image_updater_enable != OLD.argocd_image_updater_enable or
            -- git delegate enabled/disabled
                (coalesce(NEW.spec ->> 'repo_server_delegate', '') !=
                 coalesce(OLD.spec ->> 'repo_server_delegate', '')) or
            -- image updater spec changed
                (coalesce(NEW.spec ->> 'image_updater_delegate', '') !=
                 coalesce(OLD.spec ->> 'image_updater_delegate', '')) or
            -- appset spec changed
                (coalesce(NEW.spec ->> 'app_set_delegate', '') !=
                 coalesce(OLD.spec ->> 'app_set_delegate', '')) or
            -- agent permissions rules spec changed
                (coalesce(NEW.spec ->> 'agent_permissions_rules', '') !=
                 coalesce(OLD.spec ->> 'agent_permissions_rules', '')) or
            -- config management plugins spec changed
                (coalesce(NEW.argocd_config_management_plugins::jsonb, '{}'::jsonb) !=
                coalesce(OLD.argocd_config_management_plugins::jsonb, '{}'::jsonb))
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1, spec=jsonb_set(spec::jsonb, '{targetVersion}', 'null') WHERE instance_id = NEW.instance_id;
    END IF;
    -- upgrade generation for resource inclusion/exclusion changes:
    IF TG_OP = 'UPDATE' and (
            -- resource inclusions changed
                (coalesce(NEW.argocd_cm ->> 'resource.inclusions', '') !=
                 coalesce(OLD.argocd_cm ->> 'resource.inclusions', '')) or
            -- resource exclusions changed
                (coalesce(NEW.argocd_cm ->> 'resource.exclusions', '') !=
                 coalesce(OLD.argocd_cm ->> 'resource.exclusions', '')) or
            -- appset enabled/disabled
                 (coalesce(NEW.spec ->> 'appset_disabled', 'false') !=
                 coalesce(OLD.spec ->> 'appset_disabled', 'false')) or
             -- declarative management app set policy changed when appset delegate is set
                (coalesce(NEW.spec->> 'appset_policy', '') !=
                coalesce(OLD.spec->> 'appset_policy', '') and
                coalesce(NEW.spec ->> 'app_set_delegate', '') != '')
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1 WHERE instance_id = NEW.instance_id;
    END IF;
    -- upgrade read-only spec changes:
    IF TG_OP = 'UPDATE' and (
            -- read-only spec changed
                (coalesce(NEW.spec ->> 'agent_permissions_rules', '') !=
                 coalesce(OLD.spec ->> 'agent_permissions_rules', ''))
        )THEN
        UPDATE argo_cd_cluster SET readonly_settings_changed_generation = generation WHERE instance_id = NEW.instance_id;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_inserted_argo_cd_instance_config
    AFTER INSERT ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();


DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_updated_argo_cd_instance_config
    AFTER UPDATE ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();

ALTER TABLE public.kargo_agent ADD readonly_settings_changed_generation INTEGER;

CREATE OR REPLACE FUNCTION bump_gen_kargo_agent_updated() RETURNS TRIGGER AS $$
DECLARE
updated_non_status_columns numeric;
BEGIN
    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value AND n.key NOT LIKE 'status_%' AND n.key != 'auto_upgrade_disabled' ;

    IF updated_non_status_columns > 0 OR (NEW.auto_upgrade_disabled != OLD.auto_upgrade_disabled AND NEW.auto_upgrade_disabled=false) THEN
            NEW.generation = OLD.generation + 1;
    END IF;
    -- read-only spec changed
    IF NEW.namespace != OLD.namespace THEN
            NEW.readonly_settings_changed_generation = NEW.generation;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS bump_gen_on_updated_kargo_agent ON kargo_agent;
CREATE TRIGGER bump_gen_on_updated_kargo_agent
    BEFORE UPDATE ON kargo_agent
    FOR EACH ROW
    EXECUTE PROCEDURE bump_gen_kargo_agent_updated();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('89', 'jiachengxu', 'changelog-root.yaml', NOW(), 91, '9:0239f73872418814fdea2a26d12c9dfe', 'addColumn tableName=argo_cd_cluster; sqlFile path=argo_cd_cluster_triggers_gen_bump_v3.sql; sqlFile path=argo_cd_instance_config_triggers_v16.sql; addColumn tableName=kargo_agent; sqlFile path=kargo_agent_triggers_gen_bump_v2.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::90::sunghoonkang
CREATE TABLE public.workspace (id VARCHAR(50) NOT NULL, organization_id VARCHAR(50) NOT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255), creation_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL, CONSTRAINT workspace_pkey PRIMARY KEY (id), CONSTRAINT fk_workspace_organization_id FOREIGN KEY (organization_id) REFERENCES public.organization(id));

CREATE TABLE public.workspace_member (id VARCHAR(50) NOT NULL, workspace_id VARCHAR(50) NOT NULL, user_id VARCHAR(50), team_id VARCHAR(50), role VARCHAR(50) NOT NULL, CONSTRAINT workspace_member_pkey PRIMARY KEY (id), CONSTRAINT fk_workspace_member_user_id FOREIGN KEY (user_id) REFERENCES public.akuity_user(id), CONSTRAINT fk_workspace_member_workspace_id FOREIGN KEY (workspace_id) REFERENCES public.workspace(id), CONSTRAINT fk_workspace_member_team_id FOREIGN KEY (team_id) REFERENCES public.team(id));

ALTER TABLE workspace_member ADD CONSTRAINT workspace_member_reference
CHECK ((user_id IS NOT NULL AND team_id IS NULL)
OR (team_id IS NOT NULL AND user_id IS NULL));

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('90', 'sunghoonkang', 'changelog-root.yaml', NOW(), 92, '9:fc1d38af2acf4be6ef2d1fec057751dd', 'createTable tableName=workspace; createTable tableName=workspace_member; sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::91::jiachengxu
ALTER TABLE public.argo_cd_instance ADD workspace_id VARCHAR(50);

ALTER TABLE public.argo_cd_instance ADD CONSTRAINT fk_argo_cd_instance_workspace_id FOREIGN KEY (workspace_id) REFERENCES public.workspace (id);

ALTER TABLE public.kargo_instance ADD workspace_id VARCHAR(50);

ALTER TABLE public.kargo_instance ADD CONSTRAINT fk_kargo_instance_workspace_id FOREIGN KEY (workspace_id) REFERENCES public.workspace (id);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('91', 'jiachengxu', 'changelog-root.yaml', NOW(), 93, '9:edb17fd9aceacdce10e6ba3bdc53f11c', 'addColumn tableName=argo_cd_instance; addColumn tableName=kargo_instance', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::92::gdsoumya
CREATE OR REPLACE FUNCTION notify_kargo_instance_config_changed() RETURNS TRIGGER AS $$
DECLARE
    shard VARCHAR;
    organization_owner VARCHAR;
BEGIN
    SELECT INTO organization_owner kargo_instance.organization_owner
    FROM kargo_instance WHERE id = NEW.instance_id;

    SELECT INTO shard kargo_instance.shard
    FROM kargo_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'kargo_instance_updated',
            json_build_object(
                    'id', NEW.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'modified'::text,
                    'shard', shard::text
                )::text
        );
    UPDATE kargo_instance SET generation = generation + 1 WHERE id = NEW.instance_id;
    -- upgrade all agents to latest version if:
    IF TG_OP = 'UPDATE' and (
            -- instance kargo version changed
                NEW.version != OLD.version
        )THEN
        UPDATE kargo_agent SET generation = generation + 1, spec=jsonb_set(spec::jsonb, '{targetVersion}', 'null') WHERE instance_id = NEW.instance_id;
    END IF;

    IF TG_OP = 'UPDATE' and (
           -- default shard agent changed
                (coalesce(NEW.spec ->> 'default_shard_agent', '') !=
                 coalesce(OLD.spec ->> 'default_shard_agent', '')) or
            -- global credential ns has changed
                (coalesce(NEW.spec ->> 'global_credentials_ns', '[]') !=
                 coalesce(OLD.spec ->> 'global_credentials_ns', '[]')) or
             -- controller cm field has changed
                (coalesce(NEW.controller_cm::jsonb, '{}'::jsonb) !=
                 coalesce(OLD.controller_cm::jsonb, '{}'::jsonb))
        )THEN
    UPDATE kargo_agent SET generation = generation + 1 WHERE instance_id = NEW.instance_id;
    END IF;

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_kargo_instance_config ON kargo_instance_config;
CREATE TRIGGER on_inserted_kargo_instance_config
    AFTER INSERT ON kargo_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_kargo_instance_config_changed();


DROP TRIGGER IF EXISTS on_updated_kargo_instance_config ON kargo_instance_config;
CREATE TRIGGER on_updated_kargo_instance_config
    AFTER UPDATE ON kargo_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_kargo_instance_config_changed();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('92', 'gdsoumya', 'changelog-root.yaml', NOW(), 94, '9:af20e284e626f5421887a738203312a6', 'sqlFile path=kargo_instance_config_triggers_v3.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::93::remington
ALTER TABLE public.argo_cd_instance ADD backup JSONB;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('93', 'remington', 'changelog-root.yaml', NOW(), 95, '9:31d7d1196ba3e11478ab3366675a28c0', 'addColumn tableName=argo_cd_instance', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::94::aborilov
CREATE OR REPLACE FUNCTION notify_argo_cd_cluster_inserted() RETURNS TRIGGER AS $$
DECLARE
    shard VARCHAR;
    organization_owner VARCHAR;
BEGIN
    SELECT INTO shard, organization_owner  argo_cd_instance.shard, argo_cd_instance.organization_owner
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'argo_cd_cluster_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'instance_id', NEW.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'name', NEW.name::text,
                    'spec_changed', true::boolean,
                    'type', 'added'::text,
                    'shard', shard::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_cluster ON argo_cd_cluster;
CREATE TRIGGER on_inserted_argo_cd_cluster
    AFTER INSERT ON argo_cd_cluster
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_cluster_inserted();

CREATE OR REPLACE FUNCTION notify_argo_cd_cluster_updated() RETURNS TRIGGER AS $$
DECLARE
    updated_non_status_columns numeric;
    shard VARCHAR;
    organization_owner VARCHAR;
BEGIN

    SELECT INTO shard, organization_owner  argo_cd_instance.shard, argo_cd_instance.organization_owner
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value AND n.key NOT LIKE 'status_%';

    PERFORM pg_notify(
            'argo_cd_cluster_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'instance_id', NEW.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'name', NEW.name::text,
                    'spec_changed', (updated_non_status_columns > 0)::boolean,
                    'type', 'modified'::text,
                    'shard', shard::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_updated_argo_cd_cluster ON argo_cd_cluster;
CREATE TRIGGER on_updated_argo_cd_cluster
    AFTER UPDATE ON argo_cd_cluster
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_cluster_updated();

CREATE OR REPLACE FUNCTION notify_argo_cd_cluster_deleted() RETURNS TRIGGER AS $$
DECLARE
    shard VARCHAR;
    organization_owner VARCHAR;

BEGIN
    SELECT INTO shard, organization_owner  argo_cd_instance.shard, argo_cd_instance.organization_owner
    FROM argo_cd_instance WHERE id = OLD.instance_id;
    PERFORM pg_notify(
            'argo_cd_cluster_updated',
            json_build_object(
                    'id', OLD.id::text,
                    'instance_id', OLD.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'name', OLD.name::text,
                    'spec_changed', false::boolean,
                    'type', 'deleted'::text,
                    'shard', shard::text
                )::text
        );

RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_deleted_argo_cd_cluster ON argo_cd_cluster;
CREATE TRIGGER on_deleted_argo_cd_cluster
    AFTER DELETE ON argo_cd_cluster
    FOR EACH ROW
    EXECUTE PROCEDURE notify_argo_cd_cluster_deleted();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('94', 'aborilov', 'changelog-root.yaml', NOW(), 96, '9:89b2c94ca54ae3151e36c315adf34d96', 'sqlFile path=argo_cd_cluster_triggers_v3.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::95::gdsoumya
ALTER TABLE public.kargo_instance_config ADD miscellaneous_secrets TEXT;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('95', 'gdsoumya', 'changelog-root.yaml', NOW(), 97, '9:44977ee356212c3b6ca5ae0e57661293', 'addColumn tableName=kargo_instance_config', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::96::alexmt
CREATE OR REPLACE FUNCTION notify_argo_cd_instance_config_changed() RETURNS TRIGGER AS $$
DECLARE
    organization_owner VARCHAR;
    shard VARCHAR;
BEGIN
    SELECT INTO organization_owner argo_cd_instance.organization_owner
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    SELECT INTO shard argo_cd_instance.shard
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'modified'::text,
                    'shard', shard::text
                )::text
        );
    UPDATE argo_cd_instance SET generation = generation + 1 WHERE id = NEW.instance_id;
    -- upgrade all clusters to latest version if:
    IF TG_OP = 'UPDATE' and (
            -- instance argocd version changed
                NEW.version != OLD.version or
            -- image updater enabled/disabled
                NEW.argocd_image_updater_enable != OLD.argocd_image_updater_enable or
            -- git delegate enabled/disabled
                (coalesce(NEW.spec ->> 'repo_server_delegate', '') !=
                 coalesce(OLD.spec ->> 'repo_server_delegate', '')) or
            -- image updater spec changed
                (coalesce(NEW.spec ->> 'image_updater_delegate', '') !=
                 coalesce(OLD.spec ->> 'image_updater_delegate', '')) or
            -- appset spec changed
                (coalesce(NEW.spec ->> 'app_set_delegate', '') !=
                 coalesce(OLD.spec ->> 'app_set_delegate', '')) or
            -- agent permissions rules spec changed
                (coalesce(NEW.spec ->> 'agent_permissions_rules', '') !=
                 coalesce(OLD.spec ->> 'agent_permissions_rules', '')) or
            -- config management plugins spec changed
                (coalesce(NEW.argocd_config_management_plugins::jsonb, '{}'::jsonb) !=
                coalesce(OLD.argocd_config_management_plugins::jsonb, '{}'::jsonb))
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1, spec=jsonb_set(spec::jsonb, '{targetVersion}', 'null') WHERE instance_id = NEW.instance_id;
    END IF;
    -- upgrade generation for resource inclusion/exclusion changes:
    IF TG_OP = 'UPDATE' and (
            -- resource inclusions changed
                (coalesce(NEW.argocd_cm ->> 'resource.inclusions', '') !=
                 coalesce(OLD.argocd_cm ->> 'resource.inclusions', '')) or
            -- resource exclusions changed
                (coalesce(NEW.argocd_cm ->> 'resource.exclusions', '') !=
                 coalesce(OLD.argocd_cm ->> 'resource.exclusions', '')) or
            -- appset enabled/disabled
                 (coalesce(NEW.spec ->> 'appset_disabled', 'false') !=
                 coalesce(OLD.spec ->> 'appset_disabled', 'false')) or
             -- declarative management app set policy changed when appset delegate is set
                (coalesce(NEW.spec->> 'appset_policy', '') !=
                coalesce(OLD.spec->> 'appset_policy', '') and
                coalesce(NEW.spec ->> 'app_set_delegate', '') != '')
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1 WHERE instance_id = NEW.instance_id;
    END IF;
    -- upgrade read-only spec changes:
    IF TG_OP = 'UPDATE' and (
            -- read-only spec changed
                (coalesce(NEW.spec ->> 'agent_permissions_rules', '') !=
                 coalesce(OLD.spec ->> 'agent_permissions_rules', ''))
        )THEN
        UPDATE argo_cd_cluster SET readonly_settings_changed_generation = generation WHERE instance_id = NEW.instance_id and spec->>'directClusterSpec' is null;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_inserted_argo_cd_instance_config
    AFTER INSERT ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();


DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_updated_argo_cd_instance_config
    AFTER UPDATE ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('96', 'alexmt', 'changelog-root.yaml', NOW(), 98, '9:941ef4fa42d89ddf2d082e5d625f94be', 'sqlFile path=argo_cd_instance_config_triggers_v17.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::97::gdsoumya
ALTER TABLE public.kargo_instance ADD status_recent_processed_event_info JSONB;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('97', 'gdsoumya', 'changelog-root.yaml', NOW(), 99, '9:de4a405880548b00c5419ea6b25cb5d9', 'addColumn tableName=kargo_instance', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::98::sunghoonkang
ALTER TABLE public.akuity_user ADD last_password_reset_timestamp TIMESTAMP WITH TIME ZONE;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('98', 'sunghoonkang', 'changelog-root.yaml', NOW(), 100, '9:3d5a0f775be61ba0ff94260b1434612c', 'addColumn tableName=akuity_user', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::99::gdsoumya
update
    audit_log
set details = jsonb_set(details,'{patch}',to_jsonb(to_json((((details->'patch'#>>'{}')::jsonb) - 'status_manifests')::text)), false)
where
    ((details->'patch'#>>'{}')::json)->>'status_manifests' is not null;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('99', 'gdsoumya', 'changelog-root.yaml', NOW(), 101, '9:79b0aded9274d88c0799a33d934952a2', 'sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::100::pavel
ALTER TABLE public.argo_cd_sync_operation ADD count INTEGER DEFAULT 1 NOT NULL;

ALTER TABLE public.argo_cd_sync_operation ADD is_aggregated BOOLEAN DEFAULT FALSE NOT NULL;

ALTER TABLE public.argo_cd_sync_operation ADD last_occurred_timestamp TIMESTAMP WITH TIME ZONE;

ALTER TABLE public.argo_cd_sync_operation ADD duration INTEGER;

CREATE INDEX argo_cd_sync_operation_start_time_is_aggregated ON argo_cd_sync_operation (start_time, is_aggregated);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('100', 'pavel', 'changelog-root.yaml', NOW(), 102, '9:d5a3e332895d79d2f33e688687c53425', 'addColumn tableName=argo_cd_sync_operation; sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::101::gdsoumya
CREATE TABLE public.kargo_promotions (id VARCHAR(50) NOT NULL, instance_id VARCHAR(50) NOT NULL, project_name VARCHAR(253) NOT NULL, stage_name VARCHAR(253) NOT NULL, promotion_name VARCHAR(253) NOT NULL, start_time TIMESTAMP WITH TIME ZONE NOT NULL, end_time TIMESTAMP WITH TIME ZONE NOT NULL, result_phase VARCHAR(50) NOT NULL, result_message VARCHAR(500) NOT NULL, details JSONB, CONSTRAINT kargo_promotions_pkey PRIMARY KEY (id), CONSTRAINT fk_kargo_role_instance_id FOREIGN KEY (instance_id) REFERENCES public.kargo_instance(id));

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('101', 'gdsoumya', 'changelog-root.yaml', NOW(), 103, '9:e192fc6afa47d6166e5169fd3f8164cb', 'createTable tableName=kargo_promotions', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::102::hanxiaop
ALTER TABLE public.workspace ADD CONSTRAINT unique_organization_workspace_name UNIQUE (organization_id, name);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('102', 'hanxiaop', 'changelog-root.yaml', NOW(), 104, '9:11dfea2f4379f64ea01f8f5d9ed7a579', 'addUniqueConstraint constraintName=unique_organization_workspace_name, tableName=workspace', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::103::jiachengxu
ALTER TABLE public.akuity_user ADD notification_settings JSONB;

CREATE TABLE public.event (id VARCHAR(50) NOT NULL, creation_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL, event_type VARCHAR(50), organization_id VARCHAR(50), metadata JSONB, CONSTRAINT event_pkey PRIMARY KEY (id), CONSTRAINT fk_event_organization_id FOREIGN KEY (organization_id) REFERENCES public.organization(id));

CREATE TABLE public.notification (id VARCHAR(50) NOT NULL, creation_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL, event_id VARCHAR(50) NOT NULL, target_id VARCHAR(50), delivery_method VARCHAR(50) NOT NULL, status_is_delivered BOOLEAN DEFAULT FALSE NOT NULL, CONSTRAINT notification_pkey PRIMARY KEY (id), CONSTRAINT fk_notification_event_id FOREIGN KEY (event_id) REFERENCES public.event(id) ON DELETE CASCADE);

CREATE OR REPLACE FUNCTION notify_notification_inserted() RETURNS TRIGGER AS $$

BEGIN
    PERFORM pg_notify(
            'notification_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'target_id', NEW.target_id::text,
                    'type', 'added'::text,
                    'delivery_method', NEW.delivery_method::text
                )::text
        );

RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_notification ON notification;
CREATE TRIGGER on_inserted_notification
    AFTER INSERT ON notification
    FOR EACH ROW
EXECUTE PROCEDURE notify_notification_inserted();

CREATE INDEX notification_target_id ON notification (target_id);

CREATE INDEX notification_delivery_method ON notification (delivery_method);

CREATE INDEX notification_status_is_delivered ON notification (status_is_delivered);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('103', 'jiachengxu', 'changelog-root.yaml', NOW(), 105, '9:72047041a1db3311ab114c5469dd847a', 'addColumn tableName=akuity_user; createTable tableName=event; createTable tableName=notification; sqlFile path=notification_triggers.sql; sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::104::Marvin9
CREATE TABLE public.internal_audit (id VARCHAR(50) NOT NULL, timestamp TIMESTAMP WITH TIME ZONE NOT NULL, action VARCHAR(50) NOT NULL, object JSONB NOT NULL, details JSONB, actor JSONB NOT NULL, CONSTRAINT internal_audit_pkey PRIMARY KEY (id));

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('104', 'Marvin9', 'changelog-root.yaml', NOW(), 106, '9:a8fa1640353a5434cfae103480662d3c', 'createTable tableName=internal_audit', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::105::jiachengxu
ALTER TABLE public.workspace ADD is_default BOOLEAN DEFAULT FALSE NOT NULL;

ALTER TABLE public.workspace_member ADD organization_id VARCHAR(50) NOT NULL;

ALTER TABLE public.workspace_member ADD CONSTRAINT fk_workspace_member_organization_id FOREIGN KEY (organization_id) REFERENCES public.organization (id);

CREATE OR REPLACE FUNCTION notify_argo_cd_instance_config_changed() RETURNS TRIGGER AS $$
DECLARE
    organization_owner VARCHAR;
    shard VARCHAR;
    workspace_id VARCHAR;
BEGIN
    SELECT INTO organization_owner argo_cd_instance.organization_owner
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    SELECT INTO shard argo_cd_instance.shard
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    SELECT INTO workspace_id argo_cd_instance.workspace_id
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'modified'::text,
                    'shard', shard::text,
                    'workspace_id', workspace_id::text
                )::text
        );
    UPDATE argo_cd_instance SET generation = generation + 1 WHERE id = NEW.instance_id;
    -- upgrade all clusters to latest version if:
    IF TG_OP = 'UPDATE' and (
            -- instance argocd version changed
                NEW.version != OLD.version or
            -- image updater enabled/disabled
                NEW.argocd_image_updater_enable != OLD.argocd_image_updater_enable or
            -- git delegate enabled/disabled
                (coalesce(NEW.spec ->> 'repo_server_delegate', '') !=
                 coalesce(OLD.spec ->> 'repo_server_delegate', '')) or
            -- image updater spec changed
                (coalesce(NEW.spec ->> 'image_updater_delegate', '') !=
                 coalesce(OLD.spec ->> 'image_updater_delegate', '')) or
            -- appset spec changed
                (coalesce(NEW.spec ->> 'app_set_delegate', '') !=
                 coalesce(OLD.spec ->> 'app_set_delegate', '')) or
            -- agent permissions rules spec changed
                (coalesce(NEW.spec ->> 'agent_permissions_rules', '') !=
                 coalesce(OLD.spec ->> 'agent_permissions_rules', '')) or
            -- config management plugins spec changed
                (coalesce(NEW.argocd_config_management_plugins::jsonb, '{}'::jsonb) !=
                coalesce(OLD.argocd_config_management_plugins::jsonb, '{}'::jsonb))
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1, spec=jsonb_set(spec::jsonb, '{targetVersion}', 'null') WHERE instance_id = NEW.instance_id;
    END IF;
    -- upgrade generation for resource inclusion/exclusion changes:
    IF TG_OP = 'UPDATE' and (
            -- resource inclusions changed
                (coalesce(NEW.argocd_cm ->> 'resource.inclusions', '') !=
                 coalesce(OLD.argocd_cm ->> 'resource.inclusions', '')) or
            -- resource exclusions changed
                (coalesce(NEW.argocd_cm ->> 'resource.exclusions', '') !=
                 coalesce(OLD.argocd_cm ->> 'resource.exclusions', '')) or
            -- appset enabled/disabled
                 (coalesce(NEW.spec ->> 'appset_disabled', 'false') !=
                 coalesce(OLD.spec ->> 'appset_disabled', 'false')) or
             -- declarative management app set policy changed when appset delegate is set
                (coalesce(NEW.spec->> 'appset_policy', '') !=
                coalesce(OLD.spec->> 'appset_policy', '') and
                coalesce(NEW.spec ->> 'app_set_delegate', '') != '')
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1 WHERE instance_id = NEW.instance_id;
    END IF;
    -- upgrade read-only spec changes:
    IF TG_OP = 'UPDATE' and (
            -- read-only spec changed
                (coalesce(NEW.spec ->> 'agent_permissions_rules', '') !=
                 coalesce(OLD.spec ->> 'agent_permissions_rules', ''))
        )THEN
        UPDATE argo_cd_cluster SET readonly_settings_changed_generation = generation WHERE instance_id = NEW.instance_id and spec->>'directClusterSpec' is null;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_inserted_argo_cd_instance_config
    AFTER INSERT ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();


DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_updated_argo_cd_instance_config
    AFTER UPDATE ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();

CREATE OR REPLACE FUNCTION notify_argo_cd_instance_inserted() RETURNS TRIGGER AS $$

BEGIN
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'organization_owner', NEW.organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'added'::text,
                    'shard', NEW.shard::text,
                    'workspace_id', NEW.workspace_id::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance ON argo_cd_instance;
CREATE TRIGGER on_inserted_argo_cd_instance
AFTER INSERT ON argo_cd_instance
FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_inserted();

CREATE OR REPLACE FUNCTION notify_argo_cd_instance_updated() RETURNS TRIGGER AS $$
DECLARE
    updated_non_status_columns numeric;
BEGIN

    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value AND (n.key LIKE 'deletion_timestamp' OR n.key LIKE 'generation');


    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'organization_owner', OLD.organization_owner::text,
                    'spec_changed', (updated_non_status_columns > 0)::boolean,
                    'type', 'modified'::text,
                    'shard', NEW.shard::text,
                    'workspace_id', NEW.workspace_id::text
                )::text
        );

    -- TODO(jiachengxu): Send events based on the workspace_id change.

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_updated_argo_cd_instance ON argo_cd_instance;
CREATE TRIGGER on_updated_argo_cd_instance
    AFTER UPDATE ON argo_cd_instance
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_updated();

CREATE OR REPLACE FUNCTION notify_argo_cd_instance_deleted() RETURNS TRIGGER AS $$

BEGIN
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', OLD.id::text,
                    'organization_owner', OLD.organization_owner::text,
                    'spec_changed', false::boolean,
                    'type', 'deleted'::text,
                    'shard', OLD.shard::text,
                    'workspace_id', OLD.workspace_id::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_deleted_argo_cd_instance ON argo_cd_instance;
CREATE TRIGGER on_deleted_argo_cd_instance
    AFTER DELETE ON argo_cd_instance
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_deleted();

CREATE OR REPLACE FUNCTION notify_kargo_instance_config_changed() RETURNS TRIGGER AS $$
DECLARE
    shard VARCHAR;
    organization_owner VARCHAR;
    workspace_id VARCHAR;
BEGIN
    SELECT INTO organization_owner kargo_instance.organization_owner
    FROM kargo_instance WHERE id = NEW.instance_id;

    SELECT INTO shard kargo_instance.shard
    FROM kargo_instance WHERE id = NEW.instance_id;
    SELECT INTO workspace_id kargo_instance.workspace_id
    FROM kargo_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'kargo_instance_updated',
            json_build_object(
                    'id', NEW.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'modified'::text,
                    'shard', shard::text,
                    'workspace_id', workspace_id::text
                )::text
        );
    UPDATE kargo_instance SET generation = generation + 1 WHERE id = NEW.instance_id;
    -- upgrade all agents to latest version if:
    IF TG_OP = 'UPDATE' and (
            -- instance kargo version changed
                NEW.version != OLD.version
        )THEN
        UPDATE kargo_agent SET generation = generation + 1, spec=jsonb_set(spec::jsonb, '{targetVersion}', 'null') WHERE instance_id = NEW.instance_id;
    END IF;

    IF TG_OP = 'UPDATE' and (
           -- default shard agent changed
                (coalesce(NEW.spec ->> 'default_shard_agent', '') !=
                 coalesce(OLD.spec ->> 'default_shard_agent', '')) or
            -- global credential ns has changed
                (coalesce(NEW.spec ->> 'global_credentials_ns', '[]') !=
                 coalesce(OLD.spec ->> 'global_credentials_ns', '[]')) or
             -- controller cm field has changed
                (coalesce(NEW.controller_cm::jsonb, '{}'::jsonb) !=
                 coalesce(OLD.controller_cm::jsonb, '{}'::jsonb))
        )THEN
    UPDATE kargo_agent SET generation = generation + 1 WHERE instance_id = NEW.instance_id;
    END IF;

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_kargo_instance_config ON kargo_instance_config;
CREATE TRIGGER on_inserted_kargo_instance_config
    AFTER INSERT ON kargo_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_kargo_instance_config_changed();


DROP TRIGGER IF EXISTS on_updated_kargo_instance_config ON kargo_instance_config;
CREATE TRIGGER on_updated_kargo_instance_config
    AFTER UPDATE ON kargo_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_kargo_instance_config_changed();

CREATE OR REPLACE FUNCTION notify_kargo_instance_inserted() RETURNS TRIGGER AS $$
DECLARE
    shard VARCHAR;
BEGIN
    PERFORM pg_notify(
            'kargo_instance_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'organization_owner', NEW.organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'added'::text,
                    'shard', NEW.shard::text,
                    'workspace_id', NEW.workspace_id::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_kargo_instance ON kargo_instance;
CREATE TRIGGER on_inserted_kargo_instance
AFTER INSERT ON kargo_instance
FOR EACH ROW
EXECUTE PROCEDURE notify_kargo_instance_inserted();

CREATE OR REPLACE FUNCTION notify_kargo_instance_updated() RETURNS TRIGGER AS $$
DECLARE
    shard VARCHAR;
    updated_non_status_columns numeric;
BEGIN

    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value AND (n.key LIKE 'deletion_timestamp' OR n.key LIKE 'generation');


    PERFORM pg_notify(
            'kargo_instance_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'organization_owner', OLD.organization_owner::text,
                    'spec_changed', (updated_non_status_columns > 0)::boolean,
                    'type', 'modified'::text,
                    'shard', NEW.shard::text,
                    'workspace_id', NEW.workspace_id::text
                )::text
        );

    -- TODO(jiachengxu): Send events based on the workspace_id change.

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_updated_kargo_instance ON kargo_instance;
CREATE TRIGGER on_updated_kargo_instance
    AFTER UPDATE ON kargo_instance
    FOR EACH ROW
EXECUTE PROCEDURE notify_kargo_instance_updated();

CREATE OR REPLACE FUNCTION notify_kargo_instance_deleted() RETURNS TRIGGER AS $$
DECLARE
    shard VARCHAR;
BEGIN
    PERFORM pg_notify(
            'kargo_instance_updated',
            json_build_object(
                    'id', OLD.id::text,
                    'organization_owner', OLD.organization_owner::text,
                    'spec_changed', false::boolean,
                    'type', 'deleted'::text,
                    'shard', OLD.shard::text,
                    'workspace_id', OLD.workspace_id::text
                )::text
        );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_deleted_kargo_instance ON kargo_instance;
CREATE TRIGGER on_deleted_kargo_instance
    AFTER DELETE ON kargo_instance
    FOR EACH ROW
EXECUTE PROCEDURE notify_kargo_instance_deleted();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('105', 'jiachengxu', 'changelog-root.yaml', NOW(), 107, '9:1f5f6593098247b81d548adf48468bae', 'addColumn tableName=workspace; addColumn tableName=workspace_member; sqlFile path=argo_cd_instance_config_triggers_v18.sql; sqlFile path=argo_cd_instance_triggers_v6.sql; sqlFile path=kargo_instance_config_triggers_v4.sql; sqlFile path=kargo_insta...', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::106::jiachengxu
ALTER TABLE public.api_key ADD workspace_id VARCHAR(50);

ALTER TABLE public.api_key ADD CONSTRAINT fk_api_key_workspace_id FOREIGN KEY (workspace_id) REFERENCES public.workspace (id);

ALTER TABLE public.workspace_member ADD permissions JSONB;

/*
This is the migration script to workspace. It does the following things:
- Create default workspace for the organization that does not have a default workspace
- Add organization users to the default workspace, organization member will be workspace member, and organization admin and owner will be workspace admin
- Change organization admin to organization member since we don't have organization admin anymore
- Add ArgoCD and Kargo instances to the default workspace
- Add necessary workspace role to API keys
*/

-- Create default workspace for the organization which does not have a default workspace
INSERT INTO workspace (id, organization_id, name, creation_timestamp, is_default)
SELECT
    gen_random_uuid(),
    organization.id,
    'default',
    now(),
    true
FROM organization
WHERE NOT EXISTS (
    SELECT 1
    FROM workspace
    WHERE organization_id = organization.id AND is_default = true
);

-- Add organization users to the default workspace by inserting into workspace_member table
-- organization member will be workspace member, and organization admin and owner will be workspace admin
INSERT INTO workspace_member (id, workspace_id, user_id, role, organization_id)
SELECT
    gen_random_uuid(),
    workspace.id,
    akuity_user.id,
    CASE
        WHEN organization_user.organization_role = 'admin' OR organization_user.organization_role = 'owner' THEN 'admin'
        ELSE 'member'
    END,
    organization_user.organization_id
FROM organization_user
JOIN akuity_user ON akuity_user.id = organization_user.user_id
JOIN workspace ON workspace.organization_id = organization_user.organization_id AND workspace.is_default = true
WHERE NOT EXISTS (
    SELECT 1
    FROM workspace_member
    WHERE workspace_id = workspace.id AND user_id = akuity_user.id
);

UPDATE workspace_member
SET permissions = CASE
    WHEN role = 'admin' THEN '{"actions": null, "roles": ["workspace/admin"], "custom_roles": null}'::jsonb
    WHEN role = 'member' THEN '{"actions": null, "roles": ["workspace/member"], "custom_roles": null}'::jsonb
    ELSE '{"actions": null, "roles": [], "custom_roles": null}'::jsonb
END;


-- Change organization admin to organization member since we don't have organization admin anymore
UPDATE organization_user
SET organization_role = 'member'
WHERE organization_role = 'admin';

-- Add ArgoCD and Kargo instances to the default workspace by setting workspace_id to the default workspace id in argo_cd_instance and kargo_instance table
UPDATE argo_cd_instance
SET workspace_id = (
    SELECT id
    FROM workspace
    WHERE organization_id = argo_cd_instance.organization_owner AND is_default = true
)
WHERE workspace_id IS NULL;
UPDATE kargo_instance
SET workspace_id = (
    SELECT id
    FROM workspace
    WHERE organization_id = kargo_instance.organization_owner AND is_default = true
)
WHERE workspace_id IS NULL;

-- Add workspace role to API keys, check the permissions column of the akp_key table
-- If the API key contains only roles and the API key is organization admin, change organization admin to organization member and add workspace admin
-- If the API key contains only roles and the API key is organization member, add workspace member
-- If the API key contains both roles and custom roles, change organization admin to organization member
-- For other cases, keep the roles as it is
UPDATE api_key
SET permissions = jsonb_set(
        permissions,
        '{roles}',
        CASE
            WHEN permissions->'custom_roles' = 'null' AND (permissions-> 'roles' @> '"organization/admin"' AND NOT permissions->'roles' @> '"workspace/admin"') THEN
                (permissions->'roles')::jsonb - 'organization/admin' || '"organization/member"' || '"workspace/admin"'
            WHEN permissions->'custom_roles' = 'null' AND (permissions-> 'roles' @> '"organization/member"' AND NOT (permissions->'roles' @> '"workspace/member"' OR permissions->'roles' @> '"workspace/admin"')) THEN
                permissions->'roles' || '"workspace/member"'
            WHEN permissions->'custom_roles' != 'null' AND permissions-> 'roles' @> '"organization/admin"' THEN
                (permissions->'roles')::jsonb - 'organization/admin' || '"organization/member"'
            ELSE
                permissions->'roles'
        END
              )
WHERE permissions->'roles' @> '"organization/member"' OR permissions->'roles' @> '"organization/admin"';

-- Add workspace_id to the API key if the API key is organization admin or organization member and does not have custom roles
UPDATE api_key
SET workspace_id = (
    SELECT id
    FROM workspace
    WHERE organization_id = api_key.organization AND is_default = true
)
WHERE permissions->'custom_roles' = 'null' AND (permissions->'roles' @> '"organization/member"' OR permissions->'roles' @> '"organization/admin"');

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('106', 'jiachengxu', 'changelog-root.yaml', NOW(), 108, '9:4393f6194f52675f0f76543a48e7ea83', 'addColumn tableName=api_key; addColumn tableName=workspace_member; sqlFile path=workspace-migration.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::107::hanxiaop
CREATE TABLE public.argo_cd_cluster_k8s_object (id VARCHAR(50) NOT NULL, cluster_id VARCHAR(50) NOT NULL, organization_id VARCHAR(50) NOT NULL, instance_id VARCHAR(50) NOT NULL, name VARCHAR(255) NOT NULL, namespace VARCHAR(255), "group" VARCHAR(255), version VARCHAR(255), kind VARCHAR(255), argocd_application_info JSONB, columns JSONB, creation_timestamp TIMESTAMP WITH TIME ZONE NOT NULL, CONSTRAINT argo_cd_cluster_k8s_object_pkey PRIMARY KEY (id), CONSTRAINT fk_argo_cd_cluster_k8s_object_organization_id FOREIGN KEY (organization_id) REFERENCES public.organization(id), CONSTRAINT fk_argo_cd_cluster_k8s_object_instance_id FOREIGN KEY (instance_id) REFERENCES public.argo_cd_instance(id), CONSTRAINT fk_argo_cd_cluster_id FOREIGN KEY (cluster_id) REFERENCES public.argo_cd_cluster(id) ON DELETE CASCADE);

CREATE INDEX idx_k8s_object_org_instance_kind ON public.argo_cd_cluster_k8s_object(organization_id, instance_id, "group", kind);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('107', 'hanxiaop', 'changelog-root.yaml', NOW(), 109, '9:eeff36d8e4f98f0dc7804416d9dcd882', 'createTable tableName=argo_cd_cluster_k8s_object; createIndex indexName=idx_k8s_object_org_instance_kind, tableName=argo_cd_cluster_k8s_object', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::108::pavel
ALTER TABLE public.organization ADD max_workspaces INTEGER DEFAULT 0 NOT NULL;

UPDATE ORGANIZATION SET max_kargo_agents=3 WHERE max_kargo_agents=0;

UPDATE ORGANIZATION SET max_kargo_projects=5 WHERE max_kargo_projects=0;

UPDATE ORGANIZATION SET max_kargo_instances=1 WHERE max_kargo_instances=0;

UPDATE ORGANIZATION SET max_workspaces=1;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('108', 'pavel', 'changelog-root.yaml', NOW(), 110, '9:3c7f7a5f6ea52e823ed28434b53de2eb', 'addColumn tableName=organization; sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::109::pavel
-- Add workspace role to API keys, check the permissions column of the akp_key table
-- If the API key contains only roles and the API key is organization admin, change organization admin to organization member and add workspace admin
-- If the API key contains only roles and the API key is organization member, add workspace member
-- If the API key contains both roles and custom roles, change organization admin to organization member
-- For other cases, keep the roles as it is
UPDATE api_key
SET permissions = jsonb_set(
        permissions,
        '{roles}',
        CASE
            WHEN COALESCE(permissions->'custom_roles', 'null') = 'null' AND (permissions-> 'roles' @> '"organization/admin"' AND NOT permissions->'roles' @> '"workspace/admin"') THEN
                (permissions->'roles')::jsonb - 'organization/admin' || '"organization/member"' || '"workspace/admin"'
            WHEN COALESCE(permissions->'custom_roles', 'null') = 'null' AND (permissions-> 'roles' @> '"organization/member"' AND NOT (permissions->'roles' @> '"workspace/member"' OR permissions->'roles' @> '"workspace/admin"')) THEN
                permissions->'roles' || '"workspace/member"'
            WHEN COALESCE(permissions->'custom_roles', 'null') != 'null' AND permissions-> 'roles' @> '"organization/admin"' THEN
                (permissions->'roles')::jsonb - 'organization/admin' || '"organization/member"'
            ELSE
                permissions->'roles'
        END
              )
WHERE permissions->'roles' @> '"organization/member"' OR permissions->'roles' @> '"organization/admin"';

-- Add workspace_id to the API key if the API key is organization admin or organization member and does not have custom roles
UPDATE api_key
SET workspace_id = (
    SELECT id
    FROM workspace
    WHERE organization_id = api_key.organization AND is_default = true
)
WHERE COALESCE(permissions->'custom_roles', 'null') = 'null' AND (permissions->'roles' @> '"organization/member"' OR permissions->'roles' @> '"organization/admin"');

-- set feature_gates to empty struct for old orgs
UPDATE organization SET feature_gates='{}' WHERE feature_gates IS NULL;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('109', 'pavel', 'changelog-root.yaml', NOW(), 111, '9:4ac1479ffeef3756e2b6186f04d2336b', 'sqlFile path=workspace-migration_v2.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::110::gdsoumya
ALTER TABLE public.event ADD status_processed BOOLEAN DEFAULT FALSE NOT NULL;

ALTER TABLE public.notification ADD status_metadata JSONB;

ALTER TABLE public.notification ADD generation INTEGER DEFAULT 0 NOT NULL;

ALTER TABLE public.notification ADD status_failed_delivery BOOLEAN DEFAULT FALSE NOT NULL;

CREATE OR REPLACE FUNCTION notify_notification_inserted() RETURNS TRIGGER AS $$
BEGIN
        PERFORM pg_notify(
                'notification_updated',
                json_build_object(
                        'id', NEW.id::text,
                        'target_id', NEW.target_id::text,
                        'type', 'inserted'::text,
                        'delivery_method', NEW.delivery_method::text,
                        'delivered', NEW.status_is_delivered::boolean
                    )::text
            );
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_notification ON notification;
CREATE TRIGGER on_inserted_notification
    AFTER INSERT ON notification
    FOR EACH ROW
EXECUTE PROCEDURE notify_notification_inserted();

CREATE OR REPLACE FUNCTION notify_notification_updated() RETURNS TRIGGER AS $$
DECLARE
updated_non_status_columns numeric;
BEGIN
SELECT COUNT(n.key)
INTO updated_non_status_columns
FROM jsonb_each(to_jsonb(OLD)) o
         JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
WHERE n.value IS DISTINCT FROM o.value AND n.key NOT LIKE 'status_%' ;

IF updated_non_status_columns > 0 AND NEW.status_is_delivered = FALSE AND NEW.status_failed_delivery = FALSE THEN
        PERFORM pg_notify(
                'notification_updated',
                json_build_object(
                        'id', NEW.id::text,
                        'target_id', NEW.target_id::text,
                        'type', 'modified'::text,
                        'delivery_method', NEW.delivery_method::text,
                        'delivered', NEW.status_is_delivered::boolean,
                        'failed_delivery', NEW.status_failed_delivery::boolean
                    )::text
            );
END IF;
RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_updated_notification ON notification;
CREATE TRIGGER on_updated_notification
    AFTER UPDATE ON notification
    FOR EACH ROW
    EXECUTE PROCEDURE notify_notification_updated();

CREATE OR REPLACE FUNCTION notify_event_changed() RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status_processed = FALSE THEN
        PERFORM pg_notify(
                'event_updated',
                json_build_object(
                        'id', NEW.id::text,
                        'event_type', NEW.event_type::text,
                        'type', 'modified'::text,
                        'processed', NEW.status_processed::boolean
                    )::text
            );
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_event ON event;
CREATE TRIGGER on_inserted_event
    AFTER INSERT ON event
    FOR EACH ROW
EXECUTE PROCEDURE notify_event_changed();

DROP TRIGGER IF EXISTS on_updated_event ON event;
CREATE TRIGGER on_updated_event
    AFTER UPDATE ON event
    FOR EACH ROW
EXECUTE PROCEDURE notify_event_changed();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('110', 'gdsoumya', 'changelog-root.yaml', NOW(), 112, '9:f5b9e685b6b7c6d8d11209f3bba0cf9e', 'addColumn tableName=event; addColumn tableName=notification; sqlFile path=notification_triggers_v2.sql; sqlFile path=event_triggers.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::111::yiwei
ALTER TABLE public.argo_cd_cluster ADD status_k8s_info JSONB;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('111', 'yiwei', 'changelog-root.yaml', NOW(), 113, '9:e90d4597573dc61008ede0ab812c6493', 'addColumn tableName=argo_cd_cluster', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::112::sunghoonkang
CREATE TABLE public.organization_notification_config (id VARCHAR(50) NOT NULL, organization_id VARCHAR(50) NOT NULL, delivery_method VARCHAR(50) NOT NULL, events JSONB, config JSONB, CONSTRAINT organization_notification_config_pkey PRIMARY KEY (id), CONSTRAINT fk_organization_webhook_config_organization_id FOREIGN KEY (organization_id) REFERENCES public.organization(id));

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('112', 'sunghoonkang', 'changelog-root.yaml', NOW(), 114, '9:8fe9494183f406fd0338e034a3ae6010', 'createTable tableName=organization_notification_config', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::113::yiwei
ALTER TABLE public.argo_cd_cluster_k8s_object ADD owner_id VARCHAR(50);

ALTER TABLE public.argo_cd_cluster_k8s_object ADD CONSTRAINT fk_argo_cd_cluster_k8s_object_id FOREIGN KEY (owner_id) REFERENCES public.argo_cd_cluster_k8s_object (id) ON DELETE CASCADE;

ALTER TABLE public.argo_cd_cluster_k8s_object ADD children_count INTEGER DEFAULT 0 NOT NULL;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('113', 'yiwei', 'changelog-root.yaml', NOW(), 115, '9:58702cadd776a3e5c5838e8bf080437c', 'addColumn tableName=argo_cd_cluster_k8s_object', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::114::yiwei
ALTER TABLE public.argo_cd_cluster_k8s_object DROP CONSTRAINT fk_argo_cd_cluster_k8s_object_id;

CREATE INDEX idx_argo_cd_cluster_k8s_object_owner_id ON public.argo_cd_cluster_k8s_object(owner_id);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('114', 'yiwei', 'changelog-root.yaml', NOW(), 116, '9:96952d7ae3851a85befc2184226b256e', 'dropForeignKeyConstraint baseTableName=argo_cd_cluster_k8s_object, constraintName=fk_argo_cd_cluster_k8s_object_id; createIndex indexName=idx_argo_cd_cluster_k8s_object_owner_id, tableName=argo_cd_cluster_k8s_object', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::115::sunghoonkang
ALTER TABLE public.organization_notification_config ADD creation_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('115', 'sunghoonkang', 'changelog-root.yaml', NOW(), 117, '9:ed2a83da6817812fb4a094431feacc6d', 'addColumn tableName=organization_notification_config', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::116::sunghoonkang
ALTER TABLE public.organization_notification_config ADD name VARCHAR(50) DEFAULT '' NOT NULL;

ALTER TABLE public.organization_notification_config ADD CONSTRAINT unique_organization_notification_config_name_by_delivery_method UNIQUE (organization_id, delivery_method, name);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('116', 'sunghoonkang', 'changelog-root.yaml', NOW(), 118, '9:dec9f1cc5aa8608f4aeace2d38a01894', 'addColumn tableName=organization_notification_config; addUniqueConstraint constraintName=unique_organization_notification_config_name_by_delivery_method, tableName=organization_notification_config', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::117::sunghoonkang
ALTER TABLE public.notification ADD last_delivery_timestamp TIMESTAMP WITH TIME ZONE;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('117', 'sunghoonkang', 'changelog-root.yaml', NOW(), 119, '9:f6fd15ec885e5cdf61df3552da0fbc30', 'addColumn tableName=notification', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::118::pavel
ALTER TABLE public.argo_cd_instance_config ADD fqdn VARCHAR(253);

ALTER TABLE public.argo_cd_instance_config ADD UNIQUE (fqdn);

UPDATE argo_cd_instance_config
SET fqdn = COALESCE(NULLIF(spec->>'fqdn', ''), NULL);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('118', 'pavel', 'changelog-root.yaml', NOW(), 120, '9:3a514ef454a0887d81955c9a07461e6c', 'addColumn tableName=argo_cd_instance_config; sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::119::jiachengxu
CREATE OR REPLACE FUNCTION notify_argo_cd_instance_config_changed() RETURNS TRIGGER AS $$
DECLARE
    organization_owner VARCHAR;
    shard VARCHAR;
    workspace_id VARCHAR;
BEGIN
    SELECT INTO organization_owner argo_cd_instance.organization_owner
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    SELECT INTO shard argo_cd_instance.shard
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    SELECT INTO workspace_id argo_cd_instance.workspace_id
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'modified'::text,
                    'shard', shard::text,
                    'workspace_id', workspace_id::text
                )::text
        );
    UPDATE argo_cd_instance SET generation = generation + 1 WHERE id = NEW.instance_id;
    -- upgrade all clusters to latest version if:
    IF TG_OP = 'UPDATE' and (
            -- instance argocd version changed
                NEW.version != OLD.version or
            -- image updater enabled/disabled
                NEW.argocd_image_updater_enable != OLD.argocd_image_updater_enable or
            -- git delegate enabled/disabled
                (coalesce(NEW.spec ->> 'repo_server_delegate', '') !=
                 coalesce(OLD.spec ->> 'repo_server_delegate', '')) or
            -- image updater spec changed
                (coalesce(NEW.spec ->> 'image_updater_delegate', '') !=
                 coalesce(OLD.spec ->> 'image_updater_delegate', '')) or
            -- appset spec changed
                (coalesce(NEW.spec ->> 'app_set_delegate', '') !=
                 coalesce(OLD.spec ->> 'app_set_delegate', '')) or
            -- agent permissions rules spec changed
                (coalesce(NEW.spec ->> 'agent_permissions_rules', '') !=
                 coalesce(OLD.spec ->> 'agent_permissions_rules', '')) or
            -- config management plugins spec changed
                (coalesce(NEW.argocd_config_management_plugins::jsonb, '{}'::jsonb) !=
                coalesce(OLD.argocd_config_management_plugins::jsonb, '{}'::jsonb)) or
            -- multi-cluster kubernetes dashboard changed
                (coalesce(NEW.spec ->> 'multi_cluster_k8s_dashboard_enabled', 'false') !=
                 coalesce(OLD.spec ->> 'multi_cluster_k8s_dashboard_enabled', 'false'))
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1, spec=jsonb_set(spec::jsonb, '{targetVersion}', 'null') WHERE instance_id = NEW.instance_id;
    END IF;
    -- upgrade generation for resource inclusion/exclusion changes:
    IF TG_OP = 'UPDATE' and (
            -- resource inclusions changed
                (coalesce(NEW.argocd_cm ->> 'resource.inclusions', '') !=
                 coalesce(OLD.argocd_cm ->> 'resource.inclusions', '')) or
            -- resource exclusions changed
                (coalesce(NEW.argocd_cm ->> 'resource.exclusions', '') !=
                 coalesce(OLD.argocd_cm ->> 'resource.exclusions', '')) or
            -- appset enabled/disabled
                 (coalesce(NEW.spec ->> 'appset_disabled', 'false') !=
                 coalesce(OLD.spec ->> 'appset_disabled', 'false')) or
             -- declarative management app set policy changed when appset delegate is set
                (coalesce(NEW.spec->> 'appset_policy', '') !=
                coalesce(OLD.spec->> 'appset_policy', '') and
                coalesce(NEW.spec ->> 'app_set_delegate', '') != '')
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1 WHERE instance_id = NEW.instance_id;
    END IF;
    -- upgrade read-only spec changes:
    IF TG_OP = 'UPDATE' and (
            -- read-only spec changed
                (coalesce(NEW.spec ->> 'agent_permissions_rules', '') !=
                 coalesce(OLD.spec ->> 'agent_permissions_rules', ''))
        )THEN
        UPDATE argo_cd_cluster SET readonly_settings_changed_generation = generation WHERE instance_id = NEW.instance_id and spec->>'directClusterSpec' is null;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_inserted_argo_cd_instance_config
    AFTER INSERT ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();


DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_updated_argo_cd_instance_config
    AFTER UPDATE ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('119', 'jiachengxu', 'changelog-root.yaml', NOW(), 121, '9:01a249c657f930b70a1cf9fa1b8ddf03', 'sqlFile path=argo_cd_instance_config_triggers_v19.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::120::gdsoumya
ALTER TABLE public.kargo_instance DROP CONSTRAINT kargo_instance_name_key;

CREATE UNIQUE INDEX kargo_instance_name_organization_owner ON kargo_instance (organization_owner, name);

ALTER TABLE public.kargo_agent ADD CONSTRAINT unique_agent_instance UNIQUE (instance_id, name);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('120', 'gdsoumya', 'changelog-root.yaml', NOW(), 122, '9:2821376ec0a65f382a14f0f35c91b702', 'dropUniqueConstraint constraintName=kargo_instance_name_key, tableName=kargo_instance; sql; addUniqueConstraint constraintName=unique_agent_instance, tableName=kargo_agent', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::121::gdsoumya
CREATE OR REPLACE FUNCTION notify_notification_inserted() RETURNS TRIGGER AS $$
BEGIN
        PERFORM pg_notify(
                'notification_updated',
                json_build_object(
                        'id', NEW.id::text,
                        'target_id', NEW.target_id::text,
                        'type', 'added'::text,
                        'delivery_method', NEW.delivery_method::text,
                        'delivered', NEW.status_is_delivered::boolean
                    )::text
            );
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_notification ON notification;
CREATE TRIGGER on_inserted_notification
    AFTER INSERT ON notification
    FOR EACH ROW
EXECUTE PROCEDURE notify_notification_inserted();

CREATE OR REPLACE FUNCTION notify_notification_updated() RETURNS TRIGGER AS $$
DECLARE
updated_non_status_columns numeric;
BEGIN
SELECT COUNT(n.key)
INTO updated_non_status_columns
FROM jsonb_each(to_jsonb(OLD)) o
         JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
WHERE n.value IS DISTINCT FROM o.value AND n.key NOT LIKE 'status_%' ;

IF updated_non_status_columns > 0 AND NEW.status_is_delivered = FALSE AND NEW.status_failed_delivery = FALSE THEN
        PERFORM pg_notify(
                'notification_updated',
                json_build_object(
                        'id', NEW.id::text,
                        'target_id', NEW.target_id::text,
                        'type', 'modified'::text,
                        'delivery_method', NEW.delivery_method::text,
                        'delivered', NEW.status_is_delivered::boolean,
                        'failed_delivery', NEW.status_failed_delivery::boolean
                    )::text
            );
END IF;
RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_updated_notification ON notification;
CREATE TRIGGER on_updated_notification
    AFTER UPDATE ON notification
    FOR EACH ROW
    EXECUTE PROCEDURE notify_notification_updated();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('121', 'gdsoumya', 'changelog-root.yaml', NOW(), 123, '9:4d39a6f73c626a028bb28094e50147c9', 'sqlFile path=notification_triggers_v3.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::122::jiachengxu
CREATE INDEX idx_argo_cd_cluster_k8s_object_container_image_tag_2 ON argo_cd_cluster_k8s_object ((columns->>'image'), (columns->>'tag'))
WHERE "group" = 'dashboard.akuity.io' AND kind = 'Container' AND columns->>'image' IS NOT NULL AND columns->>'tag' IS NOT NULL;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('122', 'jiachengxu', 'changelog-root.yaml', NOW(), 124, '9:d18e1afca66960d67d52600a7f943dc9', 'sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::123::pavel
ALTER TABLE public.custom_role ADD workspace_id VARCHAR(50);

ALTER TABLE public.custom_role ADD CONSTRAINT fk_custom_role_workspace_id FOREIGN KEY (workspace_id) REFERENCES public.workspace (id);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('123', 'pavel', 'changelog-root.yaml', NOW(), 125, '9:2e36ee0569ec11f36a80b29af7312ee4', 'addColumn tableName=custom_role', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::124::pavel
ALTER TABLE public.kargo_instance_config ADD fqdn VARCHAR(253);

ALTER TABLE public.kargo_instance_config ADD UNIQUE (fqdn);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('124', 'pavel', 'changelog-root.yaml', NOW(), 126, '9:b67bcd032897d8797de2e880c1241e71', 'addColumn tableName=kargo_instance_config', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::125::remington
ALTER TABLE public.organization ADD oidc_team_map JSONB;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('125', 'remington', 'changelog-root.yaml', NOW(), 127, '9:2749e4f4b655d5a2fdc340fb93c92c58', 'addColumn tableName=organization', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::126::pavel
ALTER TABLE public.argo_cd_cluster ADD internal_spec JSONB;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('126', 'pavel', 'changelog-root.yaml', NOW(), 128, '9:97302f9f0f30421568821738a338cdce', 'addColumn tableName=argo_cd_cluster', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::127::pavel
SELECT 1;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('127', 'pavel', 'changelog-root.yaml', NOW(), 129, '9:0a7ed49c904abc7110aa09324b49f106', 'sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::128::jiachengxu
DO $$ DECLARE constraint_name varchar;
BEGIN
  SELECT tc.CONSTRAINT_NAME into strict constraint_name
    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
    WHERE CONSTRAINT_TYPE = 'PRIMARY KEY'
      AND TABLE_NAME = 'argo_cd_cluster_k8s_object' AND TABLE_SCHEMA = 'public';
    EXECUTE 'alter table public.argo_cd_cluster_k8s_object drop constraint "' || constraint_name || '"';
END $$;

ALTER TABLE public.argo_cd_cluster_k8s_object ADD PRIMARY KEY (id, cluster_id);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('128', 'jiachengxu', 'changelog-root.yaml', NOW(), 130, '9:3160de7e18b06f4c6eca29515ab6403b', 'dropPrimaryKey tableName=argo_cd_cluster_k8s_object; addPrimaryKey tableName=argo_cd_cluster_k8s_object', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::129::hanxiaop
DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;

UPDATE argo_cd_instance_config
SET spec = jsonb_set(spec, '{image_updater_version}', '"v0.12.2"', true)
WHERE argocd_image_updater_enable = true AND (spec->>'image_updater_version' IS NULL OR spec->>'image_updater_version' = '');

CREATE OR REPLACE FUNCTION notify_argo_cd_instance_config_changed() RETURNS TRIGGER AS $$
DECLARE
    organization_owner VARCHAR;
    shard VARCHAR;
    workspace_id VARCHAR;
BEGIN
    SELECT INTO organization_owner argo_cd_instance.organization_owner
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    SELECT INTO shard argo_cd_instance.shard
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    SELECT INTO workspace_id argo_cd_instance.workspace_id
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'modified'::text,
                    'shard', shard::text,
                    'workspace_id', workspace_id::text
                )::text
        );
    UPDATE argo_cd_instance SET generation = generation + 1 WHERE id = NEW.instance_id;
    -- upgrade all clusters to latest version if:
    IF TG_OP = 'UPDATE' and (
            -- instance argocd version changed
                NEW.version != OLD.version or
            -- image updater enabled/disabled
                NEW.argocd_image_updater_enable != OLD.argocd_image_updater_enable or
            -- git delegate enabled/disabled
                (coalesce(NEW.spec ->> 'repo_server_delegate', '') !=
                 coalesce(OLD.spec ->> 'repo_server_delegate', '')) or
            -- image updater spec changed
                (coalesce(NEW.spec ->> 'image_updater_delegate', '') !=
                 coalesce(OLD.spec ->> 'image_updater_delegate', '')) or
            -- appset spec changed
                (coalesce(NEW.spec ->> 'app_set_delegate', '') !=
                 coalesce(OLD.spec ->> 'app_set_delegate', '')) or
            -- agent permissions rules spec changed
                (coalesce(NEW.spec ->> 'agent_permissions_rules', '') !=
                 coalesce(OLD.spec ->> 'agent_permissions_rules', '')) or
            -- config management plugins spec changed
                (coalesce(NEW.argocd_config_management_plugins::jsonb, '{}'::jsonb) !=
                coalesce(OLD.argocd_config_management_plugins::jsonb, '{}'::jsonb)) or
            -- multi-cluster kubernetes dashboard changed
                (coalesce(NEW.spec ->> 'multi_cluster_k8s_dashboard_enabled', 'false') !=
                 coalesce(OLD.spec ->> 'multi_cluster_k8s_dashboard_enabled', 'false')) or
            -- image updater version changed
                (coalesce(NEW.spec ->> 'image_updater_version', '') !=
                 coalesce(OLD.spec ->> 'image_updater_version', ''))
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1, spec=jsonb_set(spec::jsonb, '{targetVersion}', 'null') WHERE instance_id = NEW.instance_id;
    END IF;
    -- upgrade generation for resource inclusion/exclusion changes:
    IF TG_OP = 'UPDATE' and (
            -- resource inclusions changed
                (coalesce(NEW.argocd_cm ->> 'resource.inclusions', '') !=
                 coalesce(OLD.argocd_cm ->> 'resource.inclusions', '')) or
            -- resource exclusions changed
                (coalesce(NEW.argocd_cm ->> 'resource.exclusions', '') !=
                 coalesce(OLD.argocd_cm ->> 'resource.exclusions', '')) or
            -- appset enabled/disabled
                 (coalesce(NEW.spec ->> 'appset_disabled', 'false') !=
                 coalesce(OLD.spec ->> 'appset_disabled', 'false')) or
             -- declarative management app set policy changed when appset delegate is set
                (coalesce(NEW.spec->> 'appset_policy', '') !=
                coalesce(OLD.spec->> 'appset_policy', '') and
                coalesce(NEW.spec ->> 'app_set_delegate', '') != '')
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1 WHERE instance_id = NEW.instance_id;
    END IF;
    -- upgrade read-only spec changes:
    IF TG_OP = 'UPDATE' and (
            -- read-only spec changed
                (coalesce(NEW.spec ->> 'agent_permissions_rules', '') !=
                 coalesce(OLD.spec ->> 'agent_permissions_rules', ''))
        )THEN
        UPDATE argo_cd_cluster SET readonly_settings_changed_generation = generation WHERE instance_id = NEW.instance_id and spec->>'directClusterSpec' is null;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_inserted_argo_cd_instance_config
    AFTER INSERT ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();


DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_updated_argo_cd_instance_config
    AFTER UPDATE ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('129', 'hanxiaop', 'changelog-root.yaml', NOW(), 131, '9:7d05a00b7ce2c06e99915c78b5c49a24', 'sql; sqlFile path=argo_cd_instance_config_triggers_v20.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::130::gdsoumya
ALTER TABLE public.organization ADD max_kargo_stages INTEGER DEFAULT 0 NOT NULL;

UPDATE organization SET max_kargo_stages = 20;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('130', 'gdsoumya', 'changelog-root.yaml', NOW(), 132, '9:1529ad65e976d7b0828eea8c46065621', 'addColumn tableName=organization; sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::131::hanxiaop
CREATE TABLE public.argo_cd_cluster_k8s_event (id VARCHAR(50) NOT NULL, organization_id VARCHAR(50) NOT NULL, instance_id VARCHAR(50) NOT NULL, cluster_id VARCHAR(50) NOT NULL, "group" VARCHAR(255), kind VARCHAR(255), namespace VARCHAR(255), name VARCHAR(255), info JSONB, timestamp TIMESTAMP WITH TIME ZONE NOT NULL, last_timestamp TIMESTAMP WITH TIME ZONE, severity VARCHAR(255), reason VARCHAR(255), count INTEGER, CONSTRAINT argo_cd_cluster_k8s_event_pkey PRIMARY KEY (id), CONSTRAINT fk_argo_cd_cluster_k8s_event_organization_id FOREIGN KEY (organization_id) REFERENCES public.organization(id) ON DELETE CASCADE, CONSTRAINT fk_argo_cd_cluster_k8s_event_instance_id FOREIGN KEY (instance_id) REFERENCES public.argo_cd_instance(id) ON DELETE CASCADE, CONSTRAINT fk_argo_cd_cluster_k8s_event_cluster_id FOREIGN KEY (cluster_id) REFERENCES public.argo_cd_cluster(id) ON DELETE CASCADE);

CREATE INDEX idx_argo_cd_cluster_k8s_event_org_instance_group_kind ON public.argo_cd_cluster_k8s_event(organization_id, instance_id, "group", kind);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('131', 'hanxiaop', 'changelog-root.yaml', NOW(), 133, '9:6fc0118ce2499d1924c8df9ea90ddea8', 'createTable tableName=argo_cd_cluster_k8s_event; createIndex indexName=idx_argo_cd_cluster_k8s_event_org_instance_group_kind, tableName=argo_cd_cluster_k8s_event', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::132::jiachengxu
ALTER TABLE public.argo_cd_cluster_k8s_object ADD deletion_timestamp TIMESTAMP WITH TIME ZONE;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('132', 'jiachengxu', 'changelog-root.yaml', NOW(), 134, '9:97058cd518853119711d624b381493dc', 'addColumn tableName=argo_cd_cluster_k8s_object', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::133::gdsoumya
CREATE TABLE public.addon_repo (id VARCHAR(50) NOT NULL, organization_id VARCHAR(50) NOT NULL, instance_id VARCHAR(50) NOT NULL, spec JSONB, generation INTEGER NOT NULL, status_processed_generation INTEGER, deletion_timestamp TIMESTAMP WITH TIME ZONE, status_info JSONB, CONSTRAINT addon_repo_pkey PRIMARY KEY (id), CONSTRAINT fk_addon_repo_instance_id FOREIGN KEY (instance_id) REFERENCES public.argo_cd_instance(id), CONSTRAINT fk_addon_repo_organization_id FOREIGN KEY (organization_id) REFERENCES public.organization(id));

CREATE TABLE public.addons (id VARCHAR(50) NOT NULL, organization_id VARCHAR(50) NOT NULL, instance_id VARCHAR(50) NOT NULL, repo_id VARCHAR(50) NOT NULL, spec JSONB, status_operation JSONB, generation INTEGER NOT NULL, status_processed_generation INTEGER, deletion_timestamp TIMESTAMP WITH TIME ZONE, status_info JSONB, CONSTRAINT addons_pkey PRIMARY KEY (id), CONSTRAINT fk_addons_organization_id FOREIGN KEY (organization_id) REFERENCES public.organization(id), CONSTRAINT fk_addons_instance_id FOREIGN KEY (instance_id) REFERENCES public.argo_cd_instance(id), CONSTRAINT fk_addons_repo_id FOREIGN KEY (repo_id) REFERENCES public.addon_repo(id));

CREATE TABLE public.cluster_addons (id VARCHAR(50) NOT NULL, organization_id VARCHAR(50) NOT NULL, instance_id VARCHAR(50) NOT NULL, cluster_name VARCHAR(50) NOT NULL, addon_id VARCHAR(50) NOT NULL, spec JSONB, status_operation JSONB, generation INTEGER NOT NULL, status_processed_generation INTEGER, deletion_timestamp TIMESTAMP WITH TIME ZONE, status_info JSONB, CONSTRAINT cluster_addons_pkey PRIMARY KEY (id), CONSTRAINT fk_addons_id FOREIGN KEY (addon_id) REFERENCES public.addons(id), CONSTRAINT fk_addons_instance_id FOREIGN KEY (instance_id) REFERENCES public.argo_cd_instance(id), CONSTRAINT fk_addons_organization_id FOREIGN KEY (organization_id) REFERENCES public.organization(id));

CREATE UNIQUE INDEX addon_id_cluster_name ON cluster_addons (addon_id, cluster_name);

CREATE OR REPLACE FUNCTION notify_addon_repo_inserted() RETURNS TRIGGER AS $$
DECLARE
shard VARCHAR;
BEGIN
SELECT INTO shard  argo_cd_instance.shard
FROM argo_cd_instance WHERE id = NEW.instance_id;
PERFORM pg_notify(
            'addon_repo_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'instance_id', NEW.instance_id::text,
                    'organization_owner', NEW.organization_id::text,
                    'spec_changed', true::boolean,
                    'type', 'added'::text,
                    'shard', shard::text
                )::text
        );

RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_addon_repo ON addon_repo;
CREATE TRIGGER on_inserted_addon_repo
    AFTER INSERT ON addon_repo
    FOR EACH ROW
    EXECUTE PROCEDURE notify_addon_repo_inserted();

CREATE OR REPLACE FUNCTION notify_addon_repo_updated() RETURNS TRIGGER AS $$
DECLARE
updated_non_status_columns numeric;
shard VARCHAR;
BEGIN
SELECT INTO shard  argo_cd_instance.shard
FROM argo_cd_instance WHERE id = NEW.instance_id;
SELECT COUNT(n.key)
INTO updated_non_status_columns
FROM jsonb_each(to_jsonb(OLD)) o
         JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
WHERE n.value IS DISTINCT FROM o.value AND n.key NOT LIKE 'status_%';

PERFORM pg_notify(
            'addon_repo_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'instance_id', NEW.instance_id::text,
                    'organization_owner', NEW.organization_id::text,
                    'spec_changed', (updated_non_status_columns > 0)::boolean,
                    'type', 'modified'::text,
                    'shard', shard::text
                )::text
        );
RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_updated_addon_repo ON addon_repo;
CREATE TRIGGER on_updated_addon_repo
    AFTER UPDATE ON addon_repo
    FOR EACH ROW
    EXECUTE PROCEDURE notify_addon_repo_updated();

CREATE OR REPLACE FUNCTION notify_addon_repo_deleted() RETURNS TRIGGER AS $$
DECLARE
shard VARCHAR;
BEGIN
SELECT INTO shard  argo_cd_instance.shard
FROM argo_cd_instance WHERE id = NEW.instance_id;
PERFORM pg_notify(
            'addon_repo_updated',
            json_build_object(
                    'id', OLD.id::text,
                    'instance_id', OLD.instance_id::text,
                    'organization_owner', OLD.organization_id::text,
                    'spec_changed', false::boolean,
                    'type', 'deleted'::text,
                    'shard', shard::text
                )::text
        );

RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_deleted_addon_repo ON addon_repo;
CREATE TRIGGER on_deleted_addon_repo
    AFTER DELETE ON addon_repo
    FOR EACH ROW
    EXECUTE PROCEDURE notify_addon_repo_deleted();

CREATE OR REPLACE FUNCTION notify_addons_inserted() RETURNS TRIGGER AS $$
DECLARE
    shard VARCHAR;
BEGIN
    SELECT INTO shard  argo_cd_instance.shard
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
                'addons_updated',
                json_build_object(
                        'id', NEW.id::text,
                        'instance_id', NEW.instance_id::text,
                        'organization_owner', NEW.organization_id::text,
                        'spec_changed', true::boolean,
                        'type', 'added'::text,
                        'shard', shard::text
                    )::text
            );
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_addons ON addons;
CREATE TRIGGER on_inserted_addons
    AFTER INSERT ON addons
    FOR EACH ROW
    EXECUTE PROCEDURE notify_addons_inserted();

CREATE OR REPLACE FUNCTION notify_addons_updated() RETURNS TRIGGER AS $$
DECLARE
    updated_non_status_columns numeric;
    shard VARCHAR;
BEGIN
    SELECT INTO shard  argo_cd_instance.shard
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value AND n.key NOT LIKE 'status_%';

    PERFORM pg_notify(
                'addons_updated',
                json_build_object(
                        'id', NEW.id::text,
                        'instance_id', NEW.instance_id::text,
                        'organization_owner', NEW.organization_id::text,
                        'spec_changed', (updated_non_status_columns > 0)::boolean,
                        'type', 'modified'::text,
                        'shard', shard::text
                    )::text
            );
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_updated_addons ON addons;
CREATE TRIGGER on_updated_addons
    AFTER UPDATE ON addons
    FOR EACH ROW
    EXECUTE PROCEDURE notify_addons_updated();

CREATE OR REPLACE FUNCTION notify_addons_deleted() RETURNS TRIGGER AS $$
DECLARE
shard VARCHAR;
BEGIN
SELECT INTO shard  argo_cd_instance.shard
FROM argo_cd_instance WHERE id = NEW.instance_id;
PERFORM pg_notify(
            'addons_updated',
            json_build_object(
                    'id', OLD.id::text,
                    'instance_id', OLD.instance_id::text,
                    'organization_owner', OLD.organization_id::text,
                    'spec_changed', false::boolean,
                    'type', 'deleted'::text,
                    'shard', shard::text
                )::text
        );

RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_deleted_addons ON addons;
CREATE TRIGGER on_deleted_addons
    AFTER DELETE ON addons
    FOR EACH ROW
    EXECUTE PROCEDURE notify_addons_deleted();

CREATE OR REPLACE FUNCTION gen_bump_addons_updated() RETURNS TRIGGER AS $$
DECLARE
    updated_non_status_columns numeric;
BEGIN
    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value AND n.key NOT LIKE 'status_%';

    IF updated_non_status_columns > 0 THEN
        NEW.generation = OLD.generation + 1;
    ELSEIF NEW.status_operation IS NOT NULL and NEW.status_operation != '{}' and  (OLD.status_operation is NULL or NEW.status_operation != OLD.status_operation) THEN
        NEW.generation = OLD.generation + 1;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS gen_bump_addons_updated ON addons;
CREATE TRIGGER gen_bump_addons_updated
    BEFORE UPDATE ON addons
    FOR EACH ROW
    EXECUTE PROCEDURE gen_bump_addons_updated();

CREATE OR REPLACE FUNCTION notify_cluster_addons_inserted() RETURNS TRIGGER AS $$
DECLARE
    shard VARCHAR;
BEGIN
    SELECT INTO shard  argo_cd_instance.shard
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
                'cluster_addons_updated',
                json_build_object(
                        'id', NEW.id::text,
                        'instance_id', NEW.instance_id::text,
                        'organization_owner', NEW.organization_id::text,
                        'spec_changed', true::boolean,
                        'type', 'added'::text,
                        'shard', shard::text
                    )::text
            );
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_cluster_addons ON cluster_addons;
CREATE TRIGGER on_inserted_cluster_addons
    AFTER INSERT ON cluster_addons
    FOR EACH ROW
    EXECUTE PROCEDURE notify_cluster_addons_inserted();

CREATE OR REPLACE FUNCTION notify_cluster_addons_updated() RETURNS TRIGGER AS $$
DECLARE
    updated_non_status_columns numeric;
    shard VARCHAR;
BEGIN
    SELECT INTO shard  argo_cd_instance.shard
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value AND n.key NOT LIKE 'status_%';

    PERFORM pg_notify(
                'cluster_addons_updated',
                json_build_object(
                        'id', NEW.id::text,
                        'instance_id', NEW.instance_id::text,
                        'organization_owner', NEW.organization_id::text,
                        'spec_changed', (updated_non_status_columns > 0)::boolean,
                        'type', 'modified'::text,
                        'shard', shard::text
                    )::text
            );
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_updated_cluster_addons ON cluster_addons;
CREATE TRIGGER on_updated_cluster_addons
    AFTER UPDATE ON cluster_addons
    FOR EACH ROW
    EXECUTE PROCEDURE notify_cluster_addons_updated();

CREATE OR REPLACE FUNCTION notify_cluster_addons_deleted() RETURNS TRIGGER AS $$
DECLARE
shard VARCHAR;
BEGIN
SELECT INTO shard  argo_cd_instance.shard
FROM argo_cd_instance WHERE id = NEW.instance_id;
PERFORM pg_notify(
            'cluster_addons_updated',
            json_build_object(
                    'id', OLD.id::text,
                    'instance_id', OLD.instance_id::text,
                    'organization_owner', OLD.organization_id::text,
                    'spec_changed', false::boolean,
                    'type', 'deleted'::text,
                    'shard', shard::text
                )::text
        );

RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_deleted_cluster_addons ON cluster_addons;
CREATE TRIGGER on_deleted_cluster_addons
    AFTER DELETE ON cluster_addons
    FOR EACH ROW
    EXECUTE PROCEDURE notify_cluster_addons_deleted();

CREATE OR REPLACE FUNCTION gen_bump_cluster_addons_updated() RETURNS TRIGGER AS $$
DECLARE
    updated_non_status_columns numeric;
BEGIN
    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value AND n.key NOT LIKE 'status_%';

    IF updated_non_status_columns > 0 THEN
        NEW.generation = OLD.generation + 1;
    ELSEIF NEW.status_operation IS NOT NULL and NEW.status_operation != '{}' and  (OLD.status_operation is NULL or NEW.status_operation != OLD.status_operation) THEN
        NEW.generation = OLD.generation + 1;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS gen_bump_cluster_addons_updated ON cluster_addons;
CREATE TRIGGER gen_bump_cluster_addons_updated
    BEFORE UPDATE ON cluster_addons
    FOR EACH ROW
    EXECUTE PROCEDURE gen_bump_cluster_addons_updated();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('133', 'gdsoumya', 'changelog-root.yaml', NOW(), 135, '9:614efe5626ca113918aacdab5decdd8c', 'createTable tableName=addon_repo; createTable tableName=addons; createTable tableName=cluster_addons; sql; sqlFile path=addon_repo_trigger_v1.sql; sqlFile path=addons_trigger_v1.sql; sqlFile path=addons_trigger_gen_bump_v1.sql; sqlFile path=cluste...', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::134::gdsoumya
ALTER TABLE public.addons ADD status_source_update JSONB;

CREATE OR REPLACE FUNCTION gen_bump_addons_updated() RETURNS TRIGGER AS $$
DECLARE
    updated_non_status_columns numeric;
BEGIN
    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value AND n.key NOT LIKE 'status_%';

    IF updated_non_status_columns > 0 THEN
        NEW.generation = OLD.generation + 1;
    ELSEIF NEW.status_operation IS NOT NULL and NEW.status_operation != '{}' and  (OLD.status_operation is NULL or NEW.status_operation != OLD.status_operation) THEN
        NEW.generation = OLD.generation + 1;
    ELSEIF NEW.status_source_update IS NOT NULL and NEW.status_source_update != '{}' and  (OLD.status_source_update is NULL or NEW.status_source_update != OLD.status_operation) THEN
        NEW.generation = OLD.generation + 1;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS gen_bump_addons_updated ON addons;
CREATE TRIGGER gen_bump_addons_updated
    BEFORE UPDATE ON addons
    FOR EACH ROW
    EXECUTE PROCEDURE gen_bump_addons_updated();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('134', 'gdsoumya', 'changelog-root.yaml', NOW(), 136, '9:b2685969e89af9538fe666ccc254ab28', 'addColumn tableName=addons; sqlFile path=addons_trigger_gen_bump_v2.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::135::jiachengxu
CREATE TABLE public.organization_kubevision_usage (id VARCHAR(50) NOT NULL, timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL, organization_id VARCHAR(50) NOT NULL, instance_count INTEGER DEFAULT 0 NOT NULL, cluster_count INTEGER DEFAULT 0 NOT NULL, node_count INTEGER DEFAULT 0 NOT NULL, pod_count INTEGER DEFAULT 0 NOT NULL, container_count INTEGER DEFAULT 0 NOT NULL, api_resource_count INTEGER DEFAULT 0 NOT NULL, object_count INTEGER DEFAULT 0 NOT NULL, CONSTRAINT organization_kubevision_usage_pkey PRIMARY KEY (id), CONSTRAINT fk_organization_kubevision_usage_organization_id FOREIGN KEY (organization_id) REFERENCES public.organization(id) ON DELETE CASCADE);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('135', 'jiachengxu', 'changelog-root.yaml', NOW(), 137, '9:381a016faeb9918990dadb6528aa04ac', 'createTable tableName=organization_kubevision_usage', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::136::yiwei
CREATE OR REPLACE FUNCTION notify_argo_cd_instance_config_changed() RETURNS TRIGGER AS $$
DECLARE
    organization_owner VARCHAR;
    shard VARCHAR;
    workspace_id VARCHAR;
BEGIN
    SELECT INTO organization_owner argo_cd_instance.organization_owner
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    SELECT INTO shard argo_cd_instance.shard
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    SELECT INTO workspace_id argo_cd_instance.workspace_id
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'modified'::text,
                    'shard', shard::text,
                    'workspace_id', workspace_id::text
                )::text
        );
    UPDATE argo_cd_instance SET generation = generation + 1 WHERE id = NEW.instance_id;
    -- upgrade all clusters to latest version if:
    IF TG_OP = 'UPDATE' and (
            -- instance argocd version changed
                NEW.version != OLD.version or
            -- image updater enabled/disabled
                NEW.argocd_image_updater_enable != OLD.argocd_image_updater_enable or
            -- git delegate enabled/disabled
                (coalesce(NEW.spec ->> 'repo_server_delegate', '') !=
                 coalesce(OLD.spec ->> 'repo_server_delegate', '')) or
            -- image updater spec changed
                (coalesce(NEW.spec ->> 'image_updater_delegate', '') !=
                 coalesce(OLD.spec ->> 'image_updater_delegate', '')) or
            -- appset spec changed
                (coalesce(NEW.spec ->> 'app_set_delegate', '') !=
                 coalesce(OLD.spec ->> 'app_set_delegate', '')) or
            -- agent permissions rules spec changed
                (coalesce(NEW.spec ->> 'agent_permissions_rules', '') !=
                 coalesce(OLD.spec ->> 'agent_permissions_rules', '')) or
            -- config management plugins spec changed
                (coalesce(NEW.argocd_config_management_plugins::jsonb, '{}'::jsonb) !=
                coalesce(OLD.argocd_config_management_plugins::jsonb, '{}'::jsonb)) or
            -- multi-cluster kubernetes dashboard changed
                (coalesce(NEW.spec ->> 'multi_cluster_k8s_dashboard_enabled', 'false') !=
                 coalesce(OLD.spec ->> 'multi_cluster_k8s_dashboard_enabled', 'false')) or
            -- kubevision config changed
                (coalesce(NEW.spec ->> 'kube_vision_config', '') !=
                 coalesce(OLD.spec ->> 'kube_vision_config', '')) or
            -- image updater version changed
                (coalesce(NEW.spec ->> 'image_updater_version', '') !=
                 coalesce(OLD.spec ->> 'image_updater_version', ''))
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1, spec=jsonb_set(spec::jsonb, '{targetVersion}', 'null') WHERE instance_id = NEW.instance_id;
    END IF;
    -- upgrade generation for resource inclusion/exclusion changes:
    IF TG_OP = 'UPDATE' and (
            -- resource inclusions changed
                (coalesce(NEW.argocd_cm ->> 'resource.inclusions', '') !=
                 coalesce(OLD.argocd_cm ->> 'resource.inclusions', '')) or
            -- resource exclusions changed
                (coalesce(NEW.argocd_cm ->> 'resource.exclusions', '') !=
                 coalesce(OLD.argocd_cm ->> 'resource.exclusions', '')) or
            -- appset enabled/disabled
                 (coalesce(NEW.spec ->> 'appset_disabled', 'false') !=
                 coalesce(OLD.spec ->> 'appset_disabled', 'false')) or
             -- declarative management app set policy changed when appset delegate is set
                (coalesce(NEW.spec->> 'appset_policy', '') !=
                coalesce(OLD.spec->> 'appset_policy', '') and
                coalesce(NEW.spec ->> 'app_set_delegate', '') != '')
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1 WHERE instance_id = NEW.instance_id;
    END IF;
    -- upgrade read-only spec changes:
    IF TG_OP = 'UPDATE' and (
            -- read-only spec changed
                (coalesce(NEW.spec ->> 'agent_permissions_rules', '') !=
                 coalesce(OLD.spec ->> 'agent_permissions_rules', ''))
        )THEN
        UPDATE argo_cd_cluster SET readonly_settings_changed_generation = generation WHERE instance_id = NEW.instance_id and spec->>'directClusterSpec' is null;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_inserted_argo_cd_instance_config
    AFTER INSERT ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();


DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_updated_argo_cd_instance_config
    AFTER UPDATE ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('136', 'yiwei', 'changelog-root.yaml', NOW(), 138, '9:50f4bf3abb06d22b89ecaa2ff26c3f30', 'sqlFile path=argo_cd_instance_config_triggers_v21.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::137::hanxiaop
CREATE INDEX idx_k8s_object_org_instance_namespace ON public.argo_cd_cluster_k8s_object(organization_id, instance_id, cluster_id, namespace);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('137', 'hanxiaop', 'changelog-root.yaml', NOW(), 139, '9:87ef21c295993864a67588020d19fcf2', 'createIndex indexName=idx_k8s_object_org_instance_namespace, tableName=argo_cd_cluster_k8s_object', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::138::gdsoumya
CREATE OR REPLACE FUNCTION notify_addons_inserted() RETURNS TRIGGER AS $$
DECLARE
    shard VARCHAR;
BEGIN
    SELECT INTO shard  argo_cd_instance.shard
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
                'addons_updated',
                json_build_object(
                        'id', NEW.id::text,
                        'instance_id', NEW.instance_id::text,
                        'organization_owner', NEW.organization_id::text,
                        'spec_changed', true::boolean,
                        'type', 'added'::text,
                        'shard', shard::text
                    )::text
            );
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_addons ON addons;
CREATE TRIGGER on_inserted_addons
    AFTER INSERT ON addons
    FOR EACH ROW
    EXECUTE PROCEDURE notify_addons_inserted();

CREATE OR REPLACE FUNCTION notify_addons_updated() RETURNS TRIGGER AS $$
DECLARE
    updated_non_status_columns numeric;
    shard VARCHAR;
BEGIN
    SELECT INTO shard  argo_cd_instance.shard
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value AND n.key NOT LIKE 'status_%';

    PERFORM pg_notify(
                'addons_updated',
                json_build_object(
                        'id', NEW.id::text,
                        'instance_id', NEW.instance_id::text,
                        'organization_owner', NEW.organization_id::text,
                        'spec_changed', (updated_non_status_columns > 0)::boolean,
                        'type', 'modified'::text,
                        'shard', shard::text
                    )::text
            );

    IF (
        -- if helmSourcePaths changed reconcile repo
        (coalesce(NEW.spec ->> 'helmValues', '') !=
         coalesce(OLD.spec ->> 'helmValues', ''))
    )THEN
        UPDATE addon_repo SET generation = generation + 1 WHERE id = NEW.repo_id;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_updated_addons ON addons;
CREATE TRIGGER on_updated_addons
    AFTER UPDATE ON addons
    FOR EACH ROW
    EXECUTE PROCEDURE notify_addons_updated();

CREATE OR REPLACE FUNCTION notify_addons_deleted() RETURNS TRIGGER AS $$
DECLARE
shard VARCHAR;
BEGIN
SELECT INTO shard  argo_cd_instance.shard
FROM argo_cd_instance WHERE id = NEW.instance_id;
PERFORM pg_notify(
            'addons_updated',
            json_build_object(
                    'id', OLD.id::text,
                    'instance_id', OLD.instance_id::text,
                    'organization_owner', OLD.organization_id::text,
                    'spec_changed', false::boolean,
                    'type', 'deleted'::text,
                    'shard', shard::text
                )::text
        );

RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_deleted_addons ON addons;
CREATE TRIGGER on_deleted_addons
    AFTER DELETE ON addons
    FOR EACH ROW
    EXECUTE PROCEDURE notify_addons_deleted();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('138', 'gdsoumya', 'changelog-root.yaml', NOW(), 140, '9:586a52bb8b110f748e1d63256ccb8f33', 'sqlFile path=addons_trigger_v2.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::139::anubhav06
CREATE TABLE public.blacklisted_tokens (id VARCHAR(50) NOT NULL, token TEXT NOT NULL, akuity_user_id VARCHAR(255) NOT NULL, expiration_timestamp TIMESTAMP WITH TIME ZONE NOT NULL, CONSTRAINT blacklisted_tokens_pkey PRIMARY KEY (id), CONSTRAINT fk_akuity_user_id FOREIGN KEY (akuity_user_id) REFERENCES public.akuity_user(id));

CREATE INDEX idx_blacklisted_tokens_token ON public.blacklisted_tokens(token);

ALTER TABLE public.akuity_user ADD user_info_public JSONB;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('139', 'anubhav06', 'changelog-root.yaml', NOW(), 141, '9:606b987506fca1021cf5b49ade1fa3f5', 'createTable tableName=blacklisted_tokens; createIndex indexName=idx_blacklisted_tokens_token, tableName=blacklisted_tokens; addColumn tableName=akuity_user', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::140::pavel
ALTER TABLE public.organization ADD verified_domains JSONB;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('140', 'pavel', 'changelog-root.yaml', NOW(), 142, '9:0bce4257a6d6d03b074074c4e2e614e9', 'addColumn tableName=organization', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::141::pavel
WITH aggregated_domains AS (
  SELECT
    organization_id,
    jsonb_agg(jsonb_build_object('domain', domain, 'verified', true)) AS new_domains
  FROM organization_sso_realm
  GROUP BY organization_id
)
UPDATE organization
SET verified_domains = (
  SELECT jsonb_agg(DISTINCT elem)
  FROM jsonb_array_elements(COALESCE(verified_domains, '[]'::jsonb) || a.new_domains) elem
)
FROM aggregated_domains a
WHERE organization.id = a.organization_id;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('141', 'pavel', 'changelog-root.yaml', NOW(), 143, '9:131d56cf61e83318bc463cc719176764', 'sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::142::alex
CREATE OR REPLACE FUNCTION notify_argo_cd_instance_config_changed() RETURNS TRIGGER AS $$
DECLARE
    organization_owner VARCHAR;
    shard VARCHAR;
    workspace_id VARCHAR;
BEGIN
    SELECT INTO organization_owner argo_cd_instance.organization_owner
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    SELECT INTO shard argo_cd_instance.shard
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    SELECT INTO workspace_id argo_cd_instance.workspace_id
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'modified'::text,
                    'shard', shard::text,
                    'workspace_id', workspace_id::text
                )::text
        );
    UPDATE argo_cd_instance SET generation = generation + 1 WHERE id = NEW.instance_id;
    -- upgrade all clusters to latest version if:
    IF TG_OP = 'UPDATE' and (
            -- instance argocd version changed
                NEW.version != OLD.version or
            -- image updater enabled/disabled
                NEW.argocd_image_updater_enable != OLD.argocd_image_updater_enable or
            -- git delegate enabled/disabled
                (coalesce(NEW.spec ->> 'repo_server_delegate', '') !=
                 coalesce(OLD.spec ->> 'repo_server_delegate', '')) or
            -- image updater spec changed
                (coalesce(NEW.spec ->> 'image_updater_delegate', '') !=
                 coalesce(OLD.spec ->> 'image_updater_delegate', '')) or
            -- appset spec changed
                (coalesce(NEW.spec ->> 'app_set_delegate', '') !=
                 coalesce(OLD.spec ->> 'app_set_delegate', '')) or
            -- agent permissions rules spec changed
                (coalesce(NEW.spec ->> 'agent_permissions_rules', '') !=
                 coalesce(OLD.spec ->> 'agent_permissions_rules', '')) or
            -- config management plugins spec changed
                (coalesce(NEW.argocd_config_management_plugins::jsonb, '{}'::jsonb) !=
                coalesce(OLD.argocd_config_management_plugins::jsonb, '{}'::jsonb)) or
            -- multi-cluster kubernetes dashboard changed
                (coalesce(NEW.spec ->> 'multi_cluster_k8s_dashboard_enabled', 'false') !=
                 coalesce(OLD.spec ->> 'multi_cluster_k8s_dashboard_enabled', 'false')) or
            -- kubevision config changed
                (coalesce(NEW.spec ->> 'kube_vision_config', '') !=
                 coalesce(OLD.spec ->> 'kube_vision_config', '')) or
            -- image updater version changed
                (coalesce(NEW.spec ->> 'image_updater_version', '') !=
                 coalesce(OLD.spec ->> 'image_updater_version', '')) or
            -- app in any namespace changed
                (coalesce((NEW.spec -> 'app_in_any_namespace' ->> 'enabled')::bool, false) !=
                 coalesce((OLD.spec -> 'app_in_any_namespace' ->> 'enabled')::bool, false))
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1, spec=jsonb_set(spec::jsonb, '{targetVersion}', 'null') WHERE instance_id = NEW.instance_id;
    END IF;
    -- upgrade generation for resource inclusion/exclusion changes:
    IF TG_OP = 'UPDATE' and (
            -- resource inclusions changed
                (coalesce(NEW.argocd_cm ->> 'resource.inclusions', '') !=
                 coalesce(OLD.argocd_cm ->> 'resource.inclusions', '')) or
            -- resource exclusions changed
                (coalesce(NEW.argocd_cm ->> 'resource.exclusions', '') !=
                 coalesce(OLD.argocd_cm ->> 'resource.exclusions', '')) or
            -- appset enabled/disabled
                 (coalesce(NEW.spec ->> 'appset_disabled', 'false') !=
                 coalesce(OLD.spec ->> 'appset_disabled', 'false')) or
             -- declarative management app set policy changed when appset delegate is set
                (coalesce(NEW.spec->> 'appset_policy', '') !=
                coalesce(OLD.spec->> 'appset_policy', '') and
                coalesce(NEW.spec ->> 'app_set_delegate', '') != '')
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1 WHERE instance_id = NEW.instance_id;
    END IF;
    -- upgrade read-only spec changes:
    IF TG_OP = 'UPDATE' and (
            -- read-only spec changed
                (coalesce(NEW.spec ->> 'agent_permissions_rules', '') !=
                 coalesce(OLD.spec ->> 'agent_permissions_rules', ''))
        )THEN
        UPDATE argo_cd_cluster SET readonly_settings_changed_generation = generation WHERE instance_id = NEW.instance_id and spec->>'directClusterSpec' is null;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_inserted_argo_cd_instance_config
    AFTER INSERT ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();


DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_updated_argo_cd_instance_config
    AFTER UPDATE ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('142', 'alex', 'changelog-root.yaml', NOW(), 144, '9:ab327ca27df260e4fd34c0bed9a14ebe', 'sqlFile path=argo_cd_instance_config_triggers_v22.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::143::alex
ALTER TABLE public.argo_cd_instance_config ADD basepath VARCHAR(253) DEFAULT '' NOT NULL;

ALTER TABLE public.argo_cd_instance_config DROP CONSTRAINT argo_cd_instance_config_fqdn_key;

ALTER TABLE public.argo_cd_instance_config ADD CONSTRAINT argo_cd_instance_config_fqdn_basepath_key UNIQUE (fqdn, basepath);

ALTER TABLE public.argo_cd_instance_config DROP CONSTRAINT argo_cd_instance_config_subdomain_key;

ALTER TABLE public.argo_cd_instance_config ADD CONSTRAINT argo_cd_instance_config_subdomain_basepath_key UNIQUE (subdomain, basepath);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('143', 'alex', 'changelog-root.yaml', NOW(), 145, '9:99328889b0d97b39ef9617d9deee00aa', 'addColumn tableName=argo_cd_instance_config; dropUniqueConstraint constraintName=argo_cd_instance_config_fqdn_key, tableName=argo_cd_instance_config; addUniqueConstraint constraintName=argo_cd_instance_config_fqdn_basepath_key, tableName=argo_cd_i...', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::144::jiachengxu
CREATE TABLE public.ai_conversation (id VARCHAR(50) NOT NULL, organization_id VARCHAR(50) NOT NULL, user_id VARCHAR(50) NOT NULL, creation_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL, last_update_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL, title VARCHAR(255), messages JSONB, CONSTRAINT ai_conversation_pkey PRIMARY KEY (id), CONSTRAINT fk_ai_conversation_organization_id FOREIGN KEY (organization_id) REFERENCES public.organization(id));

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('144', 'jiachengxu', 'changelog-root.yaml', NOW(), 146, '9:46cd2e5cd58c93c2efdd65742425e9de', 'createTable tableName=ai_conversation', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::145::pavel
ALTER TABLE public.organization_invite ADD info JSONB;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('145', 'pavel', 'changelog-root.yaml', NOW(), 147, '9:938f86cd9fc2a8913972dc82b52bc472', 'addColumn tableName=organization_invite', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::146::yiwei
ALTER TABLE public.ai_conversation ADD instance_id VARCHAR(50);

ALTER TABLE public.ai_conversation ADD CONSTRAINT fk_ai_conversation_instance_id FOREIGN KEY (instance_id) REFERENCES public.argo_cd_instance (id);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('146', 'yiwei', 'changelog-root.yaml', NOW(), 148, '9:55265e76ba85fd31e0b81ef8a172bef7', 'addColumn tableName=ai_conversation', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::147::gdsoumya
CREATE TABLE public.addon_marketplace_installs (id VARCHAR(50) NOT NULL, organization_id VARCHAR(50) NOT NULL, instance_id VARCHAR(50) NOT NULL, config JSONB, generation INTEGER NOT NULL, status_processed_generation INTEGER, status_info JSONB, CONSTRAINT addon_marketplace_installs_pkey PRIMARY KEY (id), CONSTRAINT fk_addon_mkt_instance_id FOREIGN KEY (instance_id) REFERENCES public.argo_cd_instance(id), CONSTRAINT fk_addon_mkt_organization_id FOREIGN KEY (organization_id) REFERENCES public.organization(id));

CREATE OR REPLACE FUNCTION notify_addon_marketplace_install_inserted() RETURNS TRIGGER AS $$
DECLARE
shard VARCHAR;
BEGIN
SELECT INTO shard  argo_cd_instance.shard
FROM argo_cd_instance WHERE id = NEW.instance_id;
PERFORM pg_notify(
            'addon_marketplace_install_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'instance_id', NEW.instance_id::text,
                    'organization_owner', NEW.organization_id::text,
                    'spec_changed', true::boolean,
                    'type', 'added'::text,
                    'shard', shard::text
                )::text
        );

RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_addon_marketplace_install ON addon_marketplace_installs;
CREATE TRIGGER on_inserted_addon_marketplace_install
    AFTER INSERT ON addon_marketplace_installs
    FOR EACH ROW
    EXECUTE PROCEDURE notify_addon_marketplace_install_inserted();

CREATE OR REPLACE FUNCTION notify_addon_marketplace_install_updated() RETURNS TRIGGER AS $$
DECLARE
updated_non_status_columns numeric;
shard VARCHAR;
BEGIN
SELECT INTO shard  argo_cd_instance.shard
FROM argo_cd_instance WHERE id = NEW.instance_id;
SELECT COUNT(n.key)
INTO updated_non_status_columns
FROM jsonb_each(to_jsonb(OLD)) o
         JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
WHERE n.value IS DISTINCT FROM o.value AND n.key NOT LIKE 'status_%';

PERFORM pg_notify(
            'addon_marketplace_install_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'instance_id', NEW.instance_id::text,
                    'organization_owner', NEW.organization_id::text,
                    'spec_changed', (updated_non_status_columns > 0)::boolean,
                    'type', 'modified'::text,
                    'shard', shard::text
                )::text
        );
RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_updated_addon_marketplace_install ON addon_marketplace_installs;
CREATE TRIGGER on_updated_addon_marketplace_install
    AFTER UPDATE ON addon_marketplace_installs
    FOR EACH ROW
    EXECUTE PROCEDURE notify_addon_marketplace_install_updated();

CREATE OR REPLACE FUNCTION notify_addon_marketplace_install_deleted() RETURNS TRIGGER AS $$
DECLARE
shard VARCHAR;
BEGIN
SELECT INTO shard  argo_cd_instance.shard
FROM argo_cd_instance WHERE id = NEW.instance_id;
PERFORM pg_notify(
            'addon_marketplace_install_updated',
            json_build_object(
                    'id', OLD.id::text,
                    'instance_id', OLD.instance_id::text,
                    'organization_owner', OLD.organization_id::text,
                    'spec_changed', false::boolean,
                    'type', 'deleted'::text,
                    'shard', shard::text
                )::text
        );

RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_deleted_addon_marketplace_install ON addon_marketplace_installs;
CREATE TRIGGER on_deleted_addon_marketplace_install
    AFTER DELETE ON addon_marketplace_installs
    FOR EACH ROW
    EXECUTE PROCEDURE notify_addon_marketplace_install_deleted();

CREATE OR REPLACE FUNCTION bump_gen_addon_marketplace_install_updated() RETURNS TRIGGER AS $$
DECLARE
updated_non_status_columns numeric;
BEGIN
    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value AND n.key NOT LIKE 'status_%';

    IF updated_non_status_columns > 0 THEN
        NEW.generation = OLD.generation + 1;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS bump_gen_addon_marketplace_install_updated ON addon_marketplace_installs;
CREATE TRIGGER bump_gen_addon_marketplace_install_updated
    BEFORE UPDATE ON addon_marketplace_installs
    FOR EACH ROW
    EXECUTE PROCEDURE bump_gen_addon_marketplace_install_updated();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('147', 'gdsoumya', 'changelog-root.yaml', NOW(), 149, '9:62df3a9d6b27cbea1d1f5fae4d06180f', 'createTable tableName=addon_marketplace_installs; sqlFile path=addon_marketplace_install_trigger_v1.sql; sqlFile path=addon_marketplace_install_triggers_gen_bump.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::148::pavel
CREATE OR REPLACE FUNCTION notify_addons_inserted() RETURNS TRIGGER AS $$
DECLARE
    shard VARCHAR;
BEGIN
    SELECT INTO shard  argo_cd_instance.shard
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
                'addons_updated',
                json_build_object(
                        'id', NEW.id::text,
                        'instance_id', NEW.instance_id::text,
                        'organization_owner', NEW.organization_id::text,
                        'repo_id', NEW.repo_id::text,
                        'addon_name', NEW.spec->>'name'::text,
                        'spec_changed', true::boolean,
                        'type', 'added'::text,
                        'shard', shard::text
                    )::text
            );
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_addons ON addons;
CREATE TRIGGER on_inserted_addons
    AFTER INSERT ON addons
    FOR EACH ROW
    EXECUTE PROCEDURE notify_addons_inserted();

CREATE OR REPLACE FUNCTION notify_addons_updated() RETURNS TRIGGER AS $$
DECLARE
    updated_non_status_columns numeric;
    shard VARCHAR;
BEGIN
    SELECT INTO shard  argo_cd_instance.shard
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    SELECT COUNT(n.key)
    INTO updated_non_status_columns
    FROM jsonb_each(to_jsonb(OLD)) o
             JOIN jsonb_each(to_jsonb(NEW)) n USING (key)
    WHERE n.value IS DISTINCT FROM o.value AND n.key NOT LIKE 'status_%';

    PERFORM pg_notify(
                'addons_updated',
                json_build_object(
                        'id', NEW.id::text,
                        'instance_id', NEW.instance_id::text,
                        'organization_owner', NEW.organization_id::text,
                        'repo_id', NEW.repo_id::text,
                        'addon_name', NEW.spec->>'name'::text,
                        'spec_changed', (updated_non_status_columns > 0)::boolean,
                        'type', 'modified'::text,
                        'shard', shard::text
                    )::text
            );

    IF (
        -- if helmSourcePaths changed reconcile repo
        (coalesce(NEW.spec ->> 'helmValues', '') !=
         coalesce(OLD.spec ->> 'helmValues', ''))
    )THEN
        UPDATE addon_repo SET generation = generation + 1 WHERE id = NEW.repo_id;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_updated_addons ON addons;
CREATE TRIGGER on_updated_addons
    AFTER UPDATE ON addons
    FOR EACH ROW
    EXECUTE PROCEDURE notify_addons_updated();

CREATE OR REPLACE FUNCTION notify_addons_deleted() RETURNS TRIGGER AS $$
DECLARE
shard VARCHAR;
BEGIN
SELECT INTO shard  argo_cd_instance.shard
FROM argo_cd_instance WHERE id = NEW.instance_id;
PERFORM pg_notify(
            'addons_updated',
            json_build_object(
                    'id', OLD.id::text,
                    'instance_id', OLD.instance_id::text,
                    'organization_owner', OLD.organization_id::text,
                    'repo_id', OLD.repo_id::text,
                    'addon_name', OLD.spec->>'name'::text,
                    'spec_changed', false::boolean,
                    'type', 'deleted'::text,
                    'shard', shard::text
                )::text
        );

RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_deleted_addons ON addons;
CREATE TRIGGER on_deleted_addons
    AFTER DELETE ON addons
    FOR EACH ROW
    EXECUTE PROCEDURE notify_addons_deleted();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('148', 'pavel', 'changelog-root.yaml', NOW(), 150, '9:0e976e87a27e1c74b920f447f562437e', 'sqlFile path=addons_trigger_v3.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::149::anubhav06
ALTER TABLE public.organization ADD mfa_settings JSONB;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('149', 'anubhav06', 'changelog-root.yaml', NOW(), 151, '9:f526d3227b87a5dac8019e332fcf551d', 'addColumn tableName=organization', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::150::gdsoumya
CREATE TABLE public.logs (id VARCHAR(50) NOT NULL, creation_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL, organization_id VARCHAR(50) NOT NULL, type VARCHAR(50) NOT NULL, instance_id VARCHAR(50) NOT NULL, metadata JSONB, ttl_seconds INTEGER NOT NULL, log_dst TEXT NOT NULL, CONSTRAINT logs_pkey PRIMARY KEY (id), CONSTRAINT fk_logs_organization_id FOREIGN KEY (organization_id) REFERENCES public.organization(id) ON DELETE CASCADE);

CREATE INDEX log_org_instance ON logs (organization_id, instance_id);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('150', 'gdsoumya', 'changelog-root.yaml', NOW(), 152, '9:e9e5335c1bef934dc39e648135ad3183', 'createTable tableName=logs; sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::151::alexmt
update akuity_user set email = lower(email) where email != lower(email);

update organization_invite set invitee_email = lower(invitee_email) where invitee_email != lower(invitee_email);

update organization_invite set invited_by_email = lower(invited_by_email) where invited_by_email != lower(invited_by_email);

update billing set billing_email = lower(billing_email) where billing_email != lower(billing_email);

ALTER TABLE public.akuity_user ALTER COLUMN email TYPE VARCHAR(254) USING (email::VARCHAR(254));

ALTER TABLE public.organization_invite ALTER COLUMN invitee_email TYPE VARCHAR(254) USING (invitee_email::VARCHAR(254));

ALTER TABLE public.organization_invite ALTER COLUMN invited_by_email TYPE VARCHAR(254) USING (invited_by_email::VARCHAR(254));

ALTER TABLE public.billing ALTER COLUMN billing_email TYPE VARCHAR(254) USING (billing_email::VARCHAR(254));

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('151', 'alexmt', 'changelog-root.yaml', NOW(), 153, '9:b87ab56996155d01566163858f368a85', 'sql; modifyDataType columnName=email, tableName=akuity_user; modifyDataType columnName=invitee_email, tableName=organization_invite; modifyDataType columnName=invited_by_email, tableName=organization_invite; modifyDataType columnName=billing_email...', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::152::pavel
ALTER TABLE public.team ADD permissions JSONB;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('152', 'pavel', 'changelog-root.yaml', NOW(), 154, '9:06e22589cbe9985d9b8b96454f99fa3a', 'addColumn tableName=team', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::153::Marvin9
ALTER TABLE public.organization_plan ADD deprecated BOOLEAN;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('153', 'Marvin9', 'changelog-root.yaml', NOW(), 155, '9:a837cffff60a43ac4e90d88df24b5dba', 'addColumn tableName=organization_plan', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::154::alexmt
ALTER TABLE public.ai_conversation ADD contexts JSONB;

ALTER TABLE public.ai_conversation ADD metadata JSONB;

CREATE OR REPLACE FUNCTION notify_ai_conversation_updated() RETURNS TRIGGER AS $$
DECLARE
    type TEXT;
    shard VARCHAR;
BEGIN
    type := TG_ARGV[0];

    SELECT INTO shard i.shard
    FROM ai_conversation ai
    LEFT OUTER JOIN argo_cd_instance i ON ai.instance_id = i.id
    WHERE ai.id = NEW.id;
    -- Build a JSON object and pass it as text to pg_notify
    PERFORM pg_notify(
            'ai_conversation_updated',
            json_build_object(
                    'id', NEW.id::text,
                    'type', type::text,
                    'shard', shard::text
            )::text
            );
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Drop the trigger if it exists
DROP TRIGGER IF EXISTS on_updated_ai_conversation ON ai_conversation;

-- Create the trigger that fires AFTER an UPDATE on the ai_conversation table
CREATE TRIGGER on_updated_ai_conversation
    AFTER UPDATE ON ai_conversation
    FOR EACH ROW
EXECUTE PROCEDURE notify_ai_conversation_updated('modified');

-- Drop the trigger if it exists
DROP TRIGGER IF EXISTS on_inserted_ai_conversation ON ai_conversation;

-- Create the trigger that fires AFTER an INSERT on the ai_conversation table
CREATE TRIGGER on_inserted_ai_conversation
    AFTER INSERT ON ai_conversation
    FOR EACH ROW
EXECUTE PROCEDURE notify_ai_conversation_updated('added');

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('154', 'alexmt', 'changelog-root.yaml', NOW(), 156, '9:b4110dd9abd1c325f875f521162ffa49', 'addColumn tableName=ai_conversation; sqlFile path=ai_conversation_triggers.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::155::alexmt
ALTER TABLE public.organization ADD spec JSONB;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('155', 'alexmt', 'changelog-root.yaml', NOW(), 157, '9:670ef83f7f33c29472b0b42abdabecc4', 'addColumn tableName=organization', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::156::hanxiaop
ALTER TABLE public.ai_conversation ADD kargo_instance_id VARCHAR(50);

ALTER TABLE public.ai_conversation ADD CONSTRAINT fk_ai_conversation_kargo_instance_id FOREIGN KEY (kargo_instance_id) REFERENCES public.kargo_instance (id);

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('156', 'hanxiaop', 'changelog-root.yaml', NOW(), 158, '9:51dcf2fbd6e04c07a62a799bf97b01e5', 'addColumn tableName=ai_conversation', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::157::alexmt
ALTER TABLE public.ai_conversation ADD public BOOLEAN DEFAULT FALSE;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('157', 'alexmt', 'changelog-root.yaml', NOW(), 159, '9:36af149e7a92d3f7c40b617dc11e9604', 'addColumn tableName=ai_conversation', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::158::mingqiu
ALTER TABLE public.ai_conversation ADD feedbacks JSONB;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('158', 'mingqiu', 'changelog-root.yaml', NOW(), 160, '9:07b7b234c8515b2fed84b302aa36a3c7', 'addColumn tableName=ai_conversation', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::159::pavel
UPDATE argo_cd_instance_config
SET spec = jsonb_set(
  spec,
  '{app_reconciliations_rate_limiting}',
  '{
    "item_rate_limiting": {
      "enabled": true,
      "max_delay": 1000000000,
      "base_delay": 1000000,
      "backoff_factor": 1.5,
      "failure_cooldown": 10000000000
    },
    "bucket_rate_limiting": {
      "enabled": false,
      "bucket_qps": 50,
      "bucket_size": 500
    }
  }'::jsonb,
  true
);

CREATE OR REPLACE FUNCTION notify_argo_cd_instance_config_changed() RETURNS TRIGGER AS $$
DECLARE
    organization_owner VARCHAR;
    shard VARCHAR;
    workspace_id VARCHAR;
BEGIN
    SELECT INTO organization_owner argo_cd_instance.organization_owner
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    SELECT INTO shard argo_cd_instance.shard
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    SELECT INTO workspace_id argo_cd_instance.workspace_id
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'modified'::text,
                    'shard', shard::text,
                    'workspace_id', workspace_id::text
                )::text
        );
    UPDATE argo_cd_instance SET generation = generation + 1 WHERE id = NEW.instance_id;
    -- upgrade all clusters to latest version if:
    IF TG_OP = 'UPDATE' and (
            -- instance argocd version changed
                NEW.version != OLD.version or
            -- image updater enabled/disabled
                NEW.argocd_image_updater_enable != OLD.argocd_image_updater_enable or
            -- git delegate enabled/disabled
                (coalesce(NEW.spec ->> 'repo_server_delegate', '') !=
                 coalesce(OLD.spec ->> 'repo_server_delegate', '')) or
            -- image updater spec changed
                (coalesce(NEW.spec ->> 'image_updater_delegate', '') !=
                 coalesce(OLD.spec ->> 'image_updater_delegate', '')) or
            -- appset spec changed
                (coalesce(NEW.spec ->> 'app_set_delegate', '') !=
                 coalesce(OLD.spec ->> 'app_set_delegate', '')) or
            -- agent permissions rules spec changed
                (coalesce(NEW.spec ->> 'agent_permissions_rules', '') !=
                 coalesce(OLD.spec ->> 'agent_permissions_rules', '')) or
            -- config management plugins spec changed
                (coalesce(NEW.argocd_config_management_plugins::jsonb, '{}'::jsonb) !=
                coalesce(OLD.argocd_config_management_plugins::jsonb, '{}'::jsonb)) or
            -- multi-cluster kubernetes dashboard changed
                (coalesce(NEW.spec ->> 'multi_cluster_k8s_dashboard_enabled', 'false') !=
                 coalesce(OLD.spec ->> 'multi_cluster_k8s_dashboard_enabled', 'false')) or
            -- kubevision config changed
                (coalesce(NEW.spec ->> 'kube_vision_config', '') !=
                 coalesce(OLD.spec ->> 'kube_vision_config', '')) or
            -- image updater version changed
                (coalesce(NEW.spec ->> 'image_updater_version', '') !=
                 coalesce(OLD.spec ->> 'image_updater_version', '')) or
            -- app in any namespace changed
                (coalesce((NEW.spec -> 'app_in_any_namespace' ->> 'enabled')::bool, false) !=
                 coalesce((OLD.spec -> 'app_in_any_namespace' ->> 'enabled')::bool, false))
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1, spec=jsonb_set(spec::jsonb, '{targetVersion}', 'null') WHERE instance_id = NEW.instance_id;
    END IF;
    -- upgrade generation for resource inclusion/exclusion changes:
    IF TG_OP = 'UPDATE' and (
            -- resource inclusions changed
                (coalesce(NEW.argocd_cm ->> 'resource.inclusions', '') !=
                 coalesce(OLD.argocd_cm ->> 'resource.inclusions', '')) or
            -- resource exclusions changed
                (coalesce(NEW.argocd_cm ->> 'resource.exclusions', '') !=
                 coalesce(OLD.argocd_cm ->> 'resource.exclusions', '')) or
            -- appset enabled/disabled
                 (coalesce(NEW.spec ->> 'appset_disabled', 'false') !=
                 coalesce(OLD.spec ->> 'appset_disabled', 'false')) or
             -- declarative management app set policy changed when appset delegate is set
                (coalesce(NEW.spec->> 'appset_policy', '') !=
                coalesce(OLD.spec->> 'appset_policy', '') and
                coalesce(NEW.spec ->> 'app_set_delegate', '') != '') or
            -- progressive syncs feature changed when appset delegate is set
                (coalesce(NEW.spec->> 'appset_progressive_syncs_enabled', '') !=
                 coalesce(OLD.spec->> 'appset_progressive_syncs_enabled', '') and
                 coalesce(NEW.spec ->> 'app_set_delegate', '') != '') or
            -- app_reconciliations_rate_limiting changed
                (coalesce(NEW.spec ->> 'app_reconciliations_rate_limiting', '') !=
                 coalesce(OLD.spec ->> 'app_reconciliations_rate_limiting', ''))
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1 WHERE instance_id = NEW.instance_id;
    END IF;
    -- upgrade read-only spec changes:
    IF TG_OP = 'UPDATE' and (
            -- read-only spec changed
                (coalesce(NEW.spec ->> 'agent_permissions_rules', '') !=
                 coalesce(OLD.spec ->> 'agent_permissions_rules', ''))
        )THEN
        UPDATE argo_cd_cluster SET readonly_settings_changed_generation = generation WHERE instance_id = NEW.instance_id and spec->>'directClusterSpec' is null;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_inserted_argo_cd_instance_config
    AFTER INSERT ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();


DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_updated_argo_cd_instance_config
    AFTER UPDATE ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('159', 'pavel', 'changelog-root.yaml', NOW(), 161, '9:0ece82ce328893d9deb6e5e062fef17c', 'sql; sqlFile path=argo_cd_instance_config_triggers_v23.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::160::yiwei
UPDATE argo_cd_instance_config 
SET spec = jsonb_set(
    spec, 
    '{akuity_intelligence_extension}', 
    spec->'kubevision_argo_extension'
) - 'kubevision_argo_extension'
WHERE spec->'kubevision_argo_extension' IS NOT NULL;

UPDATE argo_cd_instance_config 
SET spec = jsonb_set(
    spec, 
    '{akuity_intelligence_extension,ai_support_engineer_enabled}', 
    spec->'ai_support_engineer_extension'->'enabled'
) - 'ai_support_engineer_extension'
WHERE spec->'ai_support_engineer_extension' IS NOT NULL;

UPDATE kargo_instance_config 
SET spec = jsonb_set(
    spec, 
    '{akuity_intelligence,ai_support_engineer_enabled}', 
    spec->'akuity_intelligence'->'ai_sre_enabled'
) - '{akuity_intelligence,ai_sre_enabled}'
WHERE spec->'akuity_intelligence' IS NOT NULL;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('160', 'yiwei', 'changelog-root.yaml', NOW(), 162, '9:d803254ce7de9304ee1e893fa602e51e', 'sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::161::alex
DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;

UPDATE argo_cd_instance_config
SET spec = jsonb_set(
  spec,
  '{app_reconciliations_rate_limiting}',
  '{
    "item_rate_limiting": {
      "enabled": false
    }
  }'::jsonb,
  true
);

CREATE OR REPLACE FUNCTION notify_argo_cd_instance_config_changed() RETURNS TRIGGER AS $$
DECLARE
    organization_owner VARCHAR;
    shard VARCHAR;
    workspace_id VARCHAR;
BEGIN
    SELECT INTO organization_owner argo_cd_instance.organization_owner
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    SELECT INTO shard argo_cd_instance.shard
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    SELECT INTO workspace_id argo_cd_instance.workspace_id
    FROM argo_cd_instance WHERE id = NEW.instance_id;
    PERFORM pg_notify(
            'argo_cd_instance_updated',
            json_build_object(
                    'id', NEW.instance_id::text,
                    'organization_owner', organization_owner::text,
                    'spec_changed', true::boolean,
                    'type', 'modified'::text,
                    'shard', shard::text,
                    'workspace_id', workspace_id::text
                )::text
        );
    UPDATE argo_cd_instance SET generation = generation + 1 WHERE id = NEW.instance_id;
    -- upgrade all clusters to latest version if:
    IF TG_OP = 'UPDATE' and (
            -- instance argocd version changed
                NEW.version != OLD.version or
            -- image updater enabled/disabled
                NEW.argocd_image_updater_enable != OLD.argocd_image_updater_enable or
            -- git delegate enabled/disabled
                (coalesce(NEW.spec ->> 'repo_server_delegate', '') !=
                 coalesce(OLD.spec ->> 'repo_server_delegate', '')) or
            -- image updater spec changed
                (coalesce(NEW.spec ->> 'image_updater_delegate', '') !=
                 coalesce(OLD.spec ->> 'image_updater_delegate', '')) or
            -- appset spec changed
                (coalesce(NEW.spec ->> 'app_set_delegate', '') !=
                 coalesce(OLD.spec ->> 'app_set_delegate', '')) or
            -- agent permissions rules spec changed
                (coalesce(NEW.spec ->> 'agent_permissions_rules', '') !=
                 coalesce(OLD.spec ->> 'agent_permissions_rules', '')) or
            -- config management plugins spec changed
                (coalesce(NEW.argocd_config_management_plugins::jsonb, '{}'::jsonb) !=
                coalesce(OLD.argocd_config_management_plugins::jsonb, '{}'::jsonb)) or
            -- multi-cluster kubernetes dashboard changed
                (coalesce(NEW.spec ->> 'multi_cluster_k8s_dashboard_enabled', 'false') !=
                 coalesce(OLD.spec ->> 'multi_cluster_k8s_dashboard_enabled', 'false')) or
            -- kubevision config changed
                (coalesce(NEW.spec ->> 'kube_vision_config', '') !=
                 coalesce(OLD.spec ->> 'kube_vision_config', '')) or
            -- image updater version changed
                (coalesce(NEW.spec ->> 'image_updater_version', '') !=
                 coalesce(OLD.spec ->> 'image_updater_version', '')) or
            -- app in any namespace changed
                (coalesce((NEW.spec -> 'app_in_any_namespace' ->> 'enabled')::bool, false) !=
                 coalesce((OLD.spec -> 'app_in_any_namespace' ->> 'enabled')::bool, false))
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1, spec=jsonb_set(spec::jsonb, '{targetVersion}', 'null') WHERE instance_id = NEW.instance_id;
    END IF;
    -- upgrade generation for resource inclusion/exclusion changes:
    IF TG_OP = 'UPDATE' and (
            -- resource inclusions changed
                (coalesce(NEW.argocd_cm ->> 'resource.inclusions', '') !=
                 coalesce(OLD.argocd_cm ->> 'resource.inclusions', '')) or
            -- resource exclusions changed
                (coalesce(NEW.argocd_cm ->> 'resource.exclusions', '') !=
                 coalesce(OLD.argocd_cm ->> 'resource.exclusions', '')) or
            -- appset enabled/disabled
                 (coalesce(NEW.spec ->> 'appset_disabled', 'false') !=
                 coalesce(OLD.spec ->> 'appset_disabled', 'false')) or
             -- declarative management app set policy changed when appset delegate is set
                (coalesce(NEW.spec->> 'appset_policy', '') !=
                coalesce(OLD.spec->> 'appset_policy', '') and
                coalesce(NEW.spec ->> 'app_set_delegate', '') != '') or
            -- progressive syncs feature changed when appset delegate is set
                (coalesce(NEW.spec->> 'appset_progressive_syncs_enabled', '') !=
                 coalesce(OLD.spec->> 'appset_progressive_syncs_enabled', '') and
                 coalesce(NEW.spec ->> 'app_set_delegate', '') != '') or
            -- app_reconciliations_rate_limiting changed
                (coalesce(NEW.spec ->> 'app_reconciliations_rate_limiting', '') !=
                 coalesce(OLD.spec ->> 'app_reconciliations_rate_limiting', ''))
        )THEN
        UPDATE argo_cd_cluster SET generation = generation + 1 WHERE instance_id = NEW.instance_id;
    END IF;
    -- upgrade read-only spec changes:
    IF TG_OP = 'UPDATE' and (
            -- read-only spec changed
                (coalesce(NEW.spec ->> 'agent_permissions_rules', '') !=
                 coalesce(OLD.spec ->> 'agent_permissions_rules', ''))
        )THEN
        UPDATE argo_cd_cluster SET readonly_settings_changed_generation = generation WHERE instance_id = NEW.instance_id and spec->>'directClusterSpec' is null;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_inserted_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_inserted_argo_cd_instance_config
    AFTER INSERT ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();


DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;
CREATE TRIGGER on_updated_argo_cd_instance_config
    AFTER UPDATE ON argo_cd_instance_config
    FOR EACH ROW
EXECUTE PROCEDURE notify_argo_cd_instance_config_changed();

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('161', 'alex', 'changelog-root.yaml', NOW(), 163, '9:4ceb3982574f189c82fac70138508f4b', 'sql; sql; sqlFile path=argo_cd_instance_config_triggers_v23.sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::162::alex
UPDATE organization
SET
feature_gates = feature_gates || '{
  "workspaces": null,
  "notification": null,
  "cluster_autoscaler": null,
  "pgpool": null,
  "pgbouncer": null,
  "k3s_proxy_informers": null,
  "kargo_analysis_logs": null,
  "kargo_enterprise": null
}'::jsonb;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('162', 'alex', 'changelog-root.yaml', NOW(), 164, '9:588ddb9097e54321e6f7808b0877a44f', 'sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::163::alex
ALTER TABLE public.organization ADD ai_tokens_count INTEGER DEFAULT 0 NOT NULL;

ALTER TABLE public.organization_kubevision_usage ADD ai_tokens_count INTEGER DEFAULT 0 NOT NULL;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('163', 'alex', 'changelog-root.yaml', NOW(), 165, '9:c8d389a94014729b8ebd9445e1e9e60f', 'addColumn tableName=organization; addColumn tableName=organization_kubevision_usage', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::164::pavel
ALTER TABLE public.addons ADD clean_git_on_delete BOOLEAN;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('164', 'pavel', 'changelog-root.yaml', NOW(), 166, '9:194c7931bdce56be2119927e90c0bf73', 'addColumn tableName=addons', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::165::gdsoumya
UPDATE organization
SET
feature_gates = feature_gates || '{
  "fleet_management": null
}'::jsonb;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('165', 'gdsoumya', 'changelog-root.yaml', NOW(), 167, '9:e4ca4a74227a6ecb0141d8b18abccd5a', 'sql', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::166::alex
ALTER TABLE public.organization ADD ai_usage JSONB;

ALTER TABLE public.organization_kubevision_usage ADD ai_usage JSONB;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('166', 'alex', 'changelog-root.yaml', NOW(), 168, '9:2c89e495b01550726adcf650c267e6f9', 'addColumn tableName=organization; addColumn tableName=organization_kubevision_usage', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Changeset changelog-root.yaml::167::ming qiu
ALTER TABLE public.ai_conversation ADD runbooks JSONB;

INSERT INTO public.databasechangelog (ID, AUTHOR, FILENAME, DATEEXECUTED, ORDEREXECUTED, MD5SUM, DESCRIPTION, COMMENTS, EXECTYPE, CONTEXTS, LABELS, LIQUIBASE, DEPLOYMENT_ID) VALUES ('167', 'ming qiu', 'changelog-root.yaml', NOW(), 169, '9:83384e4a6a0a591b387df828c6efd2c0', 'addColumn tableName=ai_conversation', '', 'EXECUTED', NULL, NULL, '4.29.1', '0');

-- Release Database Lock
UPDATE public.databasechangeloglock SET LOCKED = FALSE, LOCKEDBY = NULL, LOCKGRANTED = NULL WHERE ID = 1;

