import { proto3 } from '@bufbuild/protobuf';
import {
  faBar<PERSON>hart,
  faCog,
  faExternalLinkAlt,
  faServer,
  faShoePrints,
  IconDefinition
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Flex, Space, Tabs, Typography } from 'antd';
import React from 'react';
import {
  Navigate,
  Route,
  Routes,
  generatePath,
  matchPath,
  useLocation,
  useNavigate,
  useSearchParams
} from 'react-router-dom';

import { paths } from '@ui/config/paths';
import { DeletingInstanceInfo } from '@ui/feature/common/instance/deleting-instance-info';
import { UnsupportedVersionBanner } from '@ui/feature/common/unsupported-version-banner';
import { IconPropsForReconciliation, humananifyStatusCode } from '@ui/instances/components';
import {
  useGetKargoInstance,
  useWatchKargoAgents,
  useWatchKargoInstances
} from '@ui/lib/apiclient/kargo/kargo-queries';
import { StatusCode } from '@ui/lib/apiclient/types/status/health/v1/health_pb';
import { StatusCode as ReconciliationStatusCode } from '@ui/lib/apiclient/types/status/reconciliation/v1/reconciliation_pb';
import { Loading, PageContent } from '@ui/lib/components';
import { useMainContext } from '@ui/lib/context/main-context';
import { useRequiredParams } from '@ui/lib/hooks/use-required-params';
import { IconPropsForHealth } from '@ui/lib/utils';

import { AgentsManagement } from './agents-management/agents-management';
import { KargoAuditLog } from './kargo-audit-log';
import { kargoInstancePaths } from './kargo-instance-paths';
import { KargoMetrics } from './kargo-metrics/kargo-metrics';

const kargoInstanceViews: {
  [key: string]: {
    path: string;
    name: string;
    ico: IconDefinition;
    component: (props: { id: string }) => JSX.Element;
  };
} = {
  agents: {
    path: kargoInstancePaths.agents,
    name: 'Agents',
    ico: faServer,
    component: () => <AgentsManagement />
  },
  metrics: {
    path: kargoInstancePaths.metrics,
    name: 'Metrics',
    ico: faBarChart,
    component: (props: { id: string }) => <KargoMetrics id={props.id} enableFiltersURL />
  },
  audit: {
    path: kargoInstancePaths.audit,
    name: 'Audit Log',
    ico: faShoePrints,
    component: () => <KargoAuditLog />
  }
} as const;

export const KargoInstance = () => {
  const { name } = useRequiredParams<'name'>(['name']);
  const { currentOrg, permissionChecker } = useMainContext();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  const { data, isLoading, error } = useGetKargoInstance({
    name,
    organizationId: currentOrg.id,
    workspaceId: searchParams.get('workspaceId') || ''
  });

  const canGetAgents = permissionChecker.can({
    action: 'get',
    object: 'workspace/kargo-instance/agents',
    resource: `${data?.workspaceId}/${data?.id}`
  });

  useWatchKargoInstances(
    { instanceId: data?.id, workspaceId: searchParams.get('workspaceId') || '' },
    { instanceName: data?.name, enabled: !!data }
  );
  useWatchKargoAgents(
    { instanceId: data?.id || '', workspaceId: searchParams.get('workspaceId') || '' },
    { enabled: !!data && canGetAgents }
  );

  const location = useLocation();

  const currentTabPath = React.useMemo(
    () =>
      Object.values(kargoInstanceViews).find((item) =>
        matchPath(paths.kargoInstance.replace('*', item.path), location.pathname)
      )?.path,
    [location]
  );

  if (isLoading) {
    return <Loading />;
  }

  if (error || !data) {
    return <Navigate to={paths.kargoInstances} replace />;
  }

  const statusName = proto3.getEnumType(StatusCode).findNumber(data.healthStatus?.code || 0)?.name;

  // TODO: use React.memo
  if (!canGetAgents) {
    delete kargoInstanceViews.agents;
  }

  return (
    <PageContent
      breadcrumbs={[
        { label: 'Kargo', path: paths.kargoInstances },
        {
          label: (
            <Space>
              {name}
              {!data?.deleteTime &&
                data?.reconciliationStatus?.code !== ReconciliationStatusCode.SUCCESSFUL && (
                  <span className='text-sm' title={data?.reconciliationStatus.message}>
                    <FontAwesomeIcon
                      {...IconPropsForReconciliation(data?.reconciliationStatus.code)}
                    />
                  </span>
                )}
            </Space>
          ),
          path: generatePath(paths.kargoInstance, { name }),
          loading: isLoading
        }
      ]}
    >
      <UnsupportedVersionBanner instance={data} orgName={currentOrg.name} instanceType='kargo' />
      <DeletingInstanceInfo
        deleteTime={data?.deleteTime?.toDate().toDateString()}
        className='mb-4'
      />
      <Flex gap={8} justify='space-between'>
        <Space size={24}>
          <Typography.Title level={3} style={{ marginBottom: 0 }}>
            {data.name}
          </Typography.Title>

          {statusName && (
            <Space size={8}>
              <FontAwesomeIcon {...IconPropsForHealth(statusName)} />
              <Typography.Text data-qe-id='kargo-health-status'>
                {humananifyStatusCode(statusName)}
              </Typography.Text>
            </Space>
          )}

          <Typography.Link href={`https://${data.hostname}`} target='_blank' className='underline'>
            <FontAwesomeIcon icon={faExternalLinkAlt} className='mr-2' />
            {data.hostname}
          </Typography.Link>
        </Space>
        <div className='text-akuity-500'>
          ID:{' '}
          <span className='font-medium' data-qe-id='kargo-id'>
            {data?.id}
          </span>
        </div>
      </Flex>

      <Tabs
        destroyOnHidden
        className='mt-4'
        activeKey={currentTabPath}
        onChange={(key) => navigate(generatePath(key, { name }))}
        tabBarExtraContent={
          <Button
            icon={<FontAwesomeIcon icon={faCog} />}
            type='text'
            onClick={() => navigate(generatePath(paths.kargoInstanceSettings, { name }))}
            data-qe-id='settings-btn'
            size='small'
          >
            Settings
          </Button>
        }
        items={Object.values(kargoInstanceViews).map((i) => ({
          key: i.path,
          label: (
            <>
              <FontAwesomeIcon icon={i.ico} /> {i.name}
            </>
          )
        }))}
      />
      <Routes>
        <Route
          index
          element={
            <Navigate
              to={kargoInstanceViews[Object.keys(kargoInstanceViews)[0]].path}
              replace={true}
            />
          }
        />
        {Object.values(kargoInstanceViews).map((t) => (
          <Route key={t.path} path={t.path} element={<t.component id={data?.id} />} />
        ))}
      </Routes>
    </PageContent>
  );
};
