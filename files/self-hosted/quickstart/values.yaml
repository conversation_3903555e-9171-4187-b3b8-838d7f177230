image:
  password: <registry_auth_key>

portal:
  url: https://<my_fqdn>
  env:
    MIN_INSTANCE_NAME_LENGTH: 2
    MIN_SUBDOMAIN_LENGTH: 2

tls:
  secret:
    create: true # Set to false if using Cert Manager

  # If you are bringing your own certificate (not using Cert Manager), replace the below values
  # Otherwise crt and key can be removed
  crt: |-
    -----BEGIN CERTIFICATE-----
    -----END CERTIFICATE-----

  key: |-
    -----BEGIN PRIVATE KEY-----
    -----END PRIVATE KEY-----

database:
  host: <my_db_host>
  user: <my_db_user>
  password: <my_db_password>
  dataKey: rGC5fGQluTy29/yC3TlF/ygGJGpgdvtInYJTMoZz4UU= # Does not need to be changed

# Example using Dex + Azure AD for SSO
sso:
  oidc:
    enabled: true
    issuer: https://<my_fqdn>/dex
    clientID: "aaabbbcccddd" # Does not need to be changed when using Dex
    clientSecret: "abc123" # Does not need to be changed when using Dex

  dex:
    enabled: true
    config:

      # Example using Azure AD for SSO
      # For more examples and available connectors, refer to the Dex documentation here: https://dexidp.io/docs/connectors/
      connectors:
      - type: microsoft
        id: microsoft
        name: Azure AD
        config:
          clientID: <client_id>
          clientSecret: <client_secret>
          redirectURI: https:/<my_fqdn>/dex/callback
          tenant: <tenant_id>

      # Example using Dex static user/password (suitable for testing only, not recomended for production)
      # Remove the above `connectors` block, and uncommend the below section to use
      # enablePasswordDB: true
      # staticPasswords:
      # - email: "<EMAIL>"
      #   # bcrypt hash of the string "password": $(echo password | htpasswd -BinC 10 admin | cut -d: -f2)
      #   hash: "$2y$10$F6WXblv1ZcXMCJl3sFsWKO3KsKSH7UxneqhSTDRdIMHpeh/DFiTIe" # bcrypt hash of the string "password". To change password, run: `echo password | htpasswd -BinC 10 admin | cut -d: -f2`
      #   username: "admin" # Does not need to be changed
      #   userID: "08a8684b-db88-4b73-90a9-3cd1661f5466" # Does not need to be changed

licenseKey: <akp_license>
