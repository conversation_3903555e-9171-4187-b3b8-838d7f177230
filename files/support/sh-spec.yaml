apiVersion: troubleshoot.sh/v1beta2
kind: SupportBundle
metadata:
  name: sh-bundle
spec:
  collectors:
    - clusterInfo: {}
    - clusterResources: {}
    # ConfigMaps from akuity-platform namespace
    - configMap:
        name: argocd-cluster-autoscaler
        namespace: akuity-platform
        includeAllData: true
    - configMap:
        name: argocd-versions
        namespace: akuity-platform
        includeAllData: true
    - configMap:
        name: autoscaler
        namespace: akuity-platform
        includeAllData: true
    - configMap:
        name: dex
        namespace: akuity-platform
        includeAllData: true
    - configMap:
        name: platform-controller
        namespace: akuity-platform
        includeAllData: true
    - configMap:
        name: portal-server
        namespace: akuity-platform
        includeAllData: true
    - configMap:
        name: instance-values
        namespace: akuity-platform
        includeAllData: true
    # Akuity Platform logs
    - logs:
        selector:
          -  app.kubernetes.io/name=platform-controller
        name: logs/platform-controller
        namespace: akuity-platform
        limits:
        maxLines: 10000
        maxBytes: 5000000
    - logs:
        selector:
          -  app.kubernetes.io/name=portal-server
        name: logs/portal-server
        namespace: akuity-platform
        limits:
        maxLines: 10000
        maxBytes: 5000000
    # Argo CD control plane logs
    - logs:
        selector:
          -  app.kubernetes.io/name=agent-server
        name: logs/agent-server
        limits:
        maxLines: 10000
        maxBytes: 5000000
    - logs:
        selector:
          -  app.kubernetes.io/name=argocd-application-controller
        name: logs/argocd-application-controller
        limits:
        maxLines: 10000
        maxBytes: 5000000
    - logs:
        selector:
          -  app.kubernetes.io/name=argocd-applicationset-controller
        name: logs/argocd-applicationset-controller
        limits:
        maxLines: 10000
        maxBytes: 5000000
    - logs:
        selector:
          -  app.kubernetes.io/name=argocd-repo-server
        name: logs/argocd-repo-sever
        limits:
        maxLines: 10000
        maxBytes: 5000000
    - logs:
        selector:
          -  app.kubernetes.io/name=argocd-server
        name: logs/argocd-server
        limits:
        maxLines: 10000
        maxBytes: 5000000
    - logs:
        selector:
          - app.kubernetes.io/name=k3s
        name: logs/k3s
        limits:
        maxLines: 10000
        maxBytes: 5000000
    - logs:
        selector:
          -  app.kubernetes.io/name=k3s-webhook
        name: logs/k3s-webhook
        limits:
        maxLines: 10000
        maxBytes: 5000000
    - runPod:
        name: usage-query
        namespace: akuity-platform
        podSpec:
          restartPolicy: Never
          containers:
            - name: usage-query
              image: postgres:16
              env:
                - name: PGCONN_READ_ONLY
                  valueFrom:
                    secretKeyRef:
                      name: akuity-platform
                      key: K3S_RO_DB_CONNECTION
              command: [ "sh", "-c" ]
              args:
                - |
                  echo "Running USAGE query..."
                  psql "$PGCONN_READ_ONLY" -c "
                  SELECT org.name AS org_name,
                         org.id AS org_id,
                         instance.name AS instance_name,
                         instance.id AS instance_id,
                         instance.status_hostname AS instance_hostname,
                         instance.status_health->'code' AS instance_health,
                         COUNT(DISTINCT cluster.id) AS clusters, 
                         COALESCE(((instance.status_info->>'applicationsStatus')::json->>'count')::DECIMAL, 0) AS applications
                  FROM organization org 
                  LEFT OUTER JOIN argo_cd_instance instance ON org.id = instance.organization_owner
                  LEFT OUTER JOIN organization_user org_user ON org.id = org_user.organization_id 
                  LEFT OUTER JOIN argo_cd_cluster cluster ON instance.id = cluster.instance_id    
                  WHERE instance.id IS NOT NULL
                  GROUP BY org.id, instance.id
                  ORDER BY applications DESC, org.name DESC, clusters DESC;
                  "
