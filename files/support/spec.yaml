apiVersion: troubleshoot.sh/v1beta2
kind: SupportBundle
metadata:
  name: akuity-agent-bundle
spec:
  collectors:
    - clusterInfo: {}
    - clusterResources:
        namespaces:
          - akuity
    - logs:
        selector:
          - app.kubernetes.io/name=akuity-agent
        name: logs/akuity-agent
        namespace: akuity
        limits:
        maxLines: 10000
        maxBytes: 5000000
    - logs:
        selector:
          - app.kubernetes.io/name=argocd-notifications-controller
        name: logs/argocd-notifications-controller
        namespace: akuity
        limits:
        maxLines: 10000
        maxBytes: 5000000
    - logs:
        selector:
          - app.kubernetes.io/name=argocd-repo-server
        name: logs/argocd-repo-server
        namespace: akuity
        limits:
        maxLines: 10000
        maxBytes: 5000000
    - logs:
        selector:
          - app.kubernetes.io/name=argocd-application-controller
        name: logs/argocd-application-controller
        namespace: akuity
        limits:
        maxLines: 10000
        maxBytes: 5000000
    - logs:
        selector:
          - app.kubernetes.io/name=argocd-redis
        name: logs/argocd-redis
        namespace: akuity
        limits:
        maxLines: 10000
        maxBytes: 5000000
    - configMap:
        selector:
          - app.kubernetes.io/part-of=argocd
        namespace: akuity
        includeAllData: true
