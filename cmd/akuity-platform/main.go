package main

import (
	"fmt"
	"io"
	"os"
	"os/signal"
	"runtime"
	"syscall"
	"time"

	"github.com/getsentry/sentry-go"
	"github.com/spf13/cobra"

	"github.com/akuityio/akuity-platform/internal/addoncontroller"
	"github.com/akuityio/akuity-platform/internal/aimsserver"
	"github.com/akuityio/akuity-platform/internal/billingcontroller"
	"github.com/akuityio/akuity-platform/internal/logcleaner"
	"github.com/akuityio/akuity-platform/internal/notificationcontroller"
	"github.com/akuityio/akuity-platform/internal/platformcontroller"
	"github.com/akuityio/akuity-platform/internal/portalserver"
	"github.com/akuityio/akuity-platform/internal/version"
)

func main() {
	cmd := &cobra.Command{
		Use: "akuity-platform",
		Run: func(c *cobra.Command, args []string) {
			c.HelpFunc()(c, args)
		},
		DisableAutoGenTag: true,
	}

	ver := version.GetVersion()
	env := os.Getenv("ENV")
	if env == "" {
		env = "development"
	}

	sentryDSN := os.Getenv("PLATFORM_SENTRY_DSN")
	if sentryDSN == "" {
		fmt.Println("'PLATFORM_SENTRY_DSN' is missing, Sentry integration is disabled")
	} else {
		if sentryErr := sentry.Init(sentry.ClientOptions{
			AttachStacktrace: true,
			Dsn:              sentryDSN,
			Environment:      env,
			Release:          ver.Version,
		}); sentryErr != nil {
			fmt.Printf("Sentry init has failed: %s\n", sentryErr.Error())
		}
		defer sentry.Flush(2 * time.Second)
		defer sentry.Recover()
	}

	cmd.AddCommand(version.NewVersionCommand())
	cmd.AddCommand(platformcontroller.NewPlatformControllerCommand())
	cmd.AddCommand(billingcontroller.NewBillingControllerCommand())
	cmd.AddCommand(notificationcontroller.NewNotificationControllerCommand())
	cmd.AddCommand(addoncontroller.NewAddonControllerCommand())
	cmd.AddCommand(portalserver.NewPortalServerCommand())
	cmd.AddCommand(aimsserver.NewAimsServerCommand())
	cmd.AddCommand(logcleaner.NewLogCleanerCommand())

	sigChannel := make(chan os.Signal, 1)
	signal.Notify(sigChannel, syscall.SIGUSR2)

	go func() {
		for range sigChannel {
			_, _ = fmt.Fprintf(os.Stderr, "Received SIGUSR2 - Stack Trace:\n")
			_ = writeGoroutineStacks(os.Stderr)
		}
	}()

	if err := cmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}

// https://github.com/golang/go/blob/fa08befb25e6f4993021429aa222dad71a27ed07/src/runtime/pprof/pprof.go#L729-L748
func writeGoroutineStacks(w io.Writer) error {
	// We don't know how big the buffer needs to be to collect
	// all the goroutines. Start with 1 MB and try a few times, doubling each time.
	// Give up and use a truncated trace if 64 MB is not enough.
	buf := make([]byte, 1<<20)
	for i := 0; ; i++ {
		n := runtime.Stack(buf, true)
		if n < len(buf) {
			buf = buf[:n]
			break
		}
		if len(buf) >= 64<<20 {
			// Filled 64 MB - stop there.
			break
		}
		buf = make([]byte, 2*len(buf))
	}
	_, err := w.Write(buf)
	return err
}
