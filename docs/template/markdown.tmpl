# Notification Webhook Schema
<a name="top"></a>

{{range .Files}}

{{range .Messages}}
<a name="{{.FullName | anchor}}"></a>

### {{.LongName}}
{{.Description}}

{{if .HasFields}}
| Field | Type | Description |
| ----- | ---- | ----------- |
{{range .Fields -}}
  | {{.Name}} | [{{.LongType}}](#{{.FullType | anchor}}) | {{if (index .Options "deprecated"|default false)}}**Deprecated.** {{end}}{{nobr .Description}}{{if .DefaultValue}} Default: {{.DefaultValue}}{{end}} |
{{end}}
{{end}}


{{end}} <!-- end messages -->
{{end}}
