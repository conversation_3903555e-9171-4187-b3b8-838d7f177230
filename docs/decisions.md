# Frontend

We explored several technologies. Below is a summary of what we tried, and why we did or didn't use it in the end.

## React

## Golang Templating

## Ruby on Rails
Rails is an end-to-end, highly opinionated solution for building web applications. This is great for a more traditional monolithic web application, but it's not ideal for our microservices architecture, nor interacting with Kubernetes. As you'll see below, Rails are pioneering the "new old school" approach to web development, but it is difficult to decouple from Rails itself. 

## [Hotwire](https://hotwired.dev)
Hotwire is the modern Rails approach to the frontend. The website does a better job than I will here of explaining it, and it is great technology, but it doesn't work well outside of Rails. However, had we decided to use Golang templating, we would have used Turbo to provide SPA-like speed for navigation and live updates.

## Tailwind
We are using Tailwind because it greatly increases the speed at which we can iterate on designs, and it it difficult to abuse; by sticking to Tailwind utility classes, it is harder to make UI elements that *don't* look good than it is to make ones that do. Tailwind is also loved by developers, according to the 2020 (and prior) State of CSS surveys. 

## StencilJS or LitElement
Stencil was very seriously considered. A barebones first draft of the portal was created using Stencil; as far as WebComponents go, Stencil was the most attractive option. WebComponents have the advantage of being browser-native, and endorsed by the open web standard (something that cannot be said for React or Svelte). However, WebComponents still need JavaScript to work, and Stencil's bundle size is actually larger than a comparable bundle for Svelte. When using the Shadow DOM, a noticable Flash of Unstyled Content (FOUC) ocurred on every page refresh, especially when the browser cache was empty. This could be mitigated by disabling the Shadow DOM, but then we would lose the benefits of the Shadow DOM. Further, both Stencil and LitElement require a lot of boilerplate code to work. LitElement has a slightly smaller bundle, but was more difficult to use. Overall, WebComponents are not mature enough for our use case. 

## [Svelte](https://svelte.dev) / SvelteKit
