# Authentication

When a user logs in to the portal, the flow is as follows:

1. Portal login endpoint (e.g. `[PORTAL_URL]/login`): This is a simple redirect to the Auth0 login page. _Important note_: The redirect to the Auth0 login page must include the `audience=[AKUITY_API_IDENTIFIER]` query param so that we receive a _non-opaque_ access token in the following steps. The redirect URL here also includes the callback URL, which must be included in the callback URL allow-list in the Auth0 application settings.

This endpoint is currently defined in `ui/routes/login.ts`.

2. Auth0 login page: Simply the Auth0 login portal that allows users to login with any OIDC providers we configure (i.e. Google, GitHub, Apple etc.), email+password, or magic links. All query params passed to this page are used to determine the type of code that is returned to the callback URL in the next step.

3. Portal callback endpoint (e.g. `[PORTAL_URL]/callback`): After a successful login attempt, the Auth0 login page redirects users to this callback URL, with a query param `code` set to an authentication code. This code must be exchanged for a more permanent `access_token`: this is accomplished through a `fetch` call in the callback endpoint. The `access_token`, along with an `id_token` which contains user information used by the Portal UI, is then stored in a cookie, and the user is further redirected to the Portal homepage.

The callback endpoint is currently defined in `ui/routes/callback.ts`.

## Additional Notes

The logged in user's email is injected into their access token under the claim `https://auth.akuity.io/email`. This is configured in the Auth0 Dashboard in the Actions section, and the portal is configured to look for the email under this claim in `api/middleware/middleware.go`.
