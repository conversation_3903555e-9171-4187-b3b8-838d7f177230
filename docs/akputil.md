# akputil

The akputil is a CLI tool that helps AKP administrators manage platform settings and provides a set of debugging tools.

## Accessing Managed Clusters

The `akputil` provides a set of commands under the `clusters` subcommand to help administrators access managed clusters.
All commands require database access as well as `kubectl` access to the K8S cluster that host the AKP platform.

> NOTE: these commands provides powerful access and should be used with caution!

### Port forwarding

The `akputil cluster port-forward` starts port forwarding to the API server of the managed K8S cluster and create
kubeconfig file at `/tmp/akpconfig`.

The following examples connects to the `akp-001-tst-usw2` cluster via corp Argo CD instance:

```shell
akputil cluster port-forward --cluster-name akp-001-tst-usw2 --instance-id 3chgwubv20z9kqfx
```

The port-forwarding command have to be run first and kept running in the background.

### Copy tools to the argocd-application-controller pod

The `akputil cluster debug copy-tools` command copies the `kubectl` to the Argo CD application controller pod.

```shell
akputil cluster debug copy-tools --cluster-name akp-001-tst-usw2 --instance-id 3chgwubv20z9kqfx
```

### Generate kubeconfig in the argocd-application-controller pod

The `akputil cluster debug cluster-config` generates "god level" cluster config in the Argo CD application controller pod.

```shell
akputil cluster debug cluster-config --cluster-name akp-001-tst-usw2 --instance-id 3chgwubv20z9kqfx
```

### Exec into the argocd-application-controller pod

The `akputil cluster debug exec` command exec into the Argo CD application controller pod.

```shell
akputil cluster debug exec --cluster-name akp-001-tst-usw2 --instance-id 3chgwubv20z9kqfx
```

So if you've previously copied `kubectl` and generated the kubeconfig, you can now use `kubectl` to access the managed cluster:


```shell
./kubectl get pods -n akuity
```