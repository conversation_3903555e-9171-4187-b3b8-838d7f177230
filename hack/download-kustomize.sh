#!/usr/bin/env bash
# Adapted from https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh
# Copyright 2022 The Kubernetes Authors.
# SPDX-License-Identifier: Apache-2.0

set -e

# Unset CDPATH to restore default cd behavior. An exported CDPATH can
# cause cd to output the current directory to STDOUT.
unset CDPATH

if [ -z "$1" ]; then
  echo "The first argument should be the requested version."
  exit 1
fi

if [ -z "$2" ]; then
  echo "The second argument should be the binary path."
  exit 1
fi

where="$2"

dest_dir="${where:-/usr/local/bin}"

binary_type="$(go env GOOS)_$(go env GOARCH)"

./hack/get_gh_asset.sh akuityio fedramp-binaries "kustomize-v$1" "kustomize_v${1}_$binary_type.tar.gz"

tar xzf ./kustomize_v${1}_$binary_type.tar.gz

cp ./kustomize "$dest_dir"

echo "kustomize installed to ${dest_dir}kustomize"
