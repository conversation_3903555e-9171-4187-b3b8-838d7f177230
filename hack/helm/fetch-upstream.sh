#!/bin/bash

set -euo pipefail

SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )
CHART_DIR=$( cd "${SCRIPT_DIR}"/../../charts/akuity-platform && pwd )

dex_template_dir=$( cd ${CHART_DIR}/templates/dex && pwd )
dex_template_yaml=${dex_template_dir}/dex.yaml
rm -rf "${SCRIPT_DIR}"/dex/charts/dex*
kustomize build --enable-helm "${SCRIPT_DIR}/dex" > "${dex_template_yaml}"

traefik_template_dir=$( cd ${CHART_DIR}/templates/traefik && pwd )
traefik_template_yaml=${traefik_template_dir}/traefik.yaml
rm -rf "${SCRIPT_DIR}"/traefik/charts/traefik*
kustomize build --enable-helm "${SCRIPT_DIR}/traefik" > "${traefik_template_yaml}"

cd hack/helm/generate-deps && DEX_YAML="${dex_template_yaml}" TRAEFIK_YAML="${traefik_template_yaml}" go run main.go
