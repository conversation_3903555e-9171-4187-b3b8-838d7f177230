package main

import (
	"fmt"
	"os"
	"regexp"
	"strings"
)

func main() {
	dexYaml := os.Getenv("DEX_YAML")
	if dexYaml == "" {
		panic("ENV 'DEX_YAML' must be set")
	}

	traefikYaml := os.Getenv("TRAEFIK_YAML")
	if traefikYaml == "" {
		panic("ENV 'TRAEFIK_YAML' must be set")
	}

	// Last-mile Dex template changes
	dexLines := readFileContent(dexYaml, []string{"{{- if .Values.sso.dex.enabled }}", "# This is an autogenerated file. DO NOT EDIT"}, []string{"{{- end }}", ""})
	dexLines = findReplaceLine(dexYaml, dexLines, []string{"\\s+resources: {}"}, []string{"{{- with .Values.sso.dex.resources }}", "        resources:", "{{- toYaml . | nindent 10 }}", "{{- end }}"})
	dexLines = findReplaceLine(dexYaml, dexLines, []string{"\\s+image: .*"}, []string{"        image: {{ .Values.sso.dex.image.repository }}:{{ .Values.sso.dex.image.tag }}"})
	dexLines = findReplaceLine(dexYaml, dexLines, []string{"\\s+readOnly: true"}, []string{"          readOnly: true", "{{- if .Values.sso.dex.image.secret.create }}", "      imagePullSecrets:", "      - name: dex-pullsecrets", "{{- end }}"})
	dexLines = findReplaceLine(dexYaml, dexLines, []string{"\\s+runAsUser: 1000"}, []string{"{{- if not .Values.compatibility.openshift }}", "          runAsUser: 1000", "{{- end }}"})
	dexLines = findReplaceLine(dexYaml, dexLines, []string{"\\s+securityContext:", "\\s+fsGroup: .*"}, []string{"{{- if not .Values.compatibility.openshift }}", "      securityContext:", "        fsGroup: 2000", "{{- end }}"})

	// Last-mile Traefik template changes
	traefikLines := readFileContent(traefikYaml, []string{"{{- if .Values.traefik.enabled }}\n{{- if .Values.traefik.crd.enabled }}", "# This is an autogenerated file. DO NOT EDIT"}, []string{"{{- end }}", ""})
	traefikLines = findReplaceLine(traefikYaml, traefikLines, []string{"---", "apiVersion: v1", "automountServiceAccountToken: false", "kind: ServiceAccount", "metadata:", "  name: traefik"}, []string{"{{- end }}", "---", "apiVersion: v1", "automountServiceAccountToken: false", "kind: ServiceAccount", "metadata:", "  name: traefik"})
	traefikLines = findReplaceLine(traefikYaml, traefikLines, []string{"\\s+topologySpreadConstraints:.*"}, []string{"{{- if .Values.traefik.topologySpreadConstraints }}", "      topologySpreadConstraints:", "{{- toYaml .Values.traefik.topologySpreadConstraints | nindent 8}}", "{{- end }}"})
	traefikLines = findReplaceLine(traefikYaml, traefikLines, []string{"\\s+replicas:.*"}, []string{"{{- if not .Values.traefik.autoscaling.enabled }}", "  replicas: {{ .Values.traefik.replicas }}", "{{- end }}"})
	traefikLines = findReplaceLine(traefikYaml, traefikLines, []string{"\\s+resources: null"}, []string{"{{- with .Values.traefik.resources }}", "        resources:", "{{- toYaml . | nindent 10 }}", "{{- end }}"})
	traefikLines = findReplaceLine(traefikYaml, traefikLines, []string{"\\s+image: .*"}, []string{"        image: {{ .Values.traefik.image.repository }}:{{ .Values.traefik.image.tag }}"})
	traefikLines = findReplaceLine(traefikYaml, traefikLines, []string{"\\s+hostNetwork: false"}, []string{"      hostNetwork: false", "{{- if .Values.traefik.image.secret.create }}", "      imagePullSecrets:", "      - name: traefik-pullsecrets", "{{- end }}"})
	traefikLines = findReplaceLine(traefikYaml, traefikLines, []string{"\\s+service.beta.kubernetes.io/aws-load-balancer-type: external"}, []string{"    service.beta.kubernetes.io/aws-load-balancer-type: external", "{{- if .Values.compatibility.ipv6Only }}", "    service.beta.kubernetes.io/aws-load-balancer-ip-address-type: dualstack", "{{- end }}", "{{- end }}"})
	traefikLines = findReplaceLine(traefikYaml, traefikLines, []string{"\\s+annotations:", "\\s+service.beta.kubernetes.io/aws-load-balancer-backend-protocol: tcp"}, []string{"{{- if .Values.aws.enabled }}", "  annotations:", "    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: tcp"})
	traefikLines = findReplaceLine(traefikYaml, traefikLines, []string{"\\s+runAsUser: 65532"}, []string{"{{- if not .Values.compatibility.openshift }}", "        runAsUser: 65532", "{{- end }}"})

	// Handle TLS enabled
	traefikLines = findWrapIf(traefikYaml, traefikLines, []string{"\\s+- --entryPoints.web.http.redirections.entryPoint.to=:443", "\\s+- --entryPoints.web.http.redirections.entryPoint.scheme=https"}, "and .Values.traefik.websecureRedirect .Values.tls.terminationEnabled")
	traefikLines = findWrapIf(traefikYaml, traefikLines, []string{"\\s+- --entryPoints.websecure.http.tls=true"}, ".Values.tls.terminationEnabled")
	traefikLines = findWrapIf(traefikYaml, traefikLines, []string{"\\s+- --entryPoints.websecure.address=:8443/tcp"}, ".Values.tls.terminationEnabled")
	traefikLines = findWrapIf(traefikYaml, traefikLines, []string{"\\s+- containerPort: 8443", "\\s+name: websecure", "\\s+protocol: TCP"}, ".Values.tls.terminationEnabled")
	traefikLines = findWrapIf(traefikYaml, traefikLines, []string{"\\s+- name: websecure", "\\s+port: 443", "\\s+protocol: TCP", "\\s+targetPort: websecure"}, ".Values.tls.terminationEnabled")
	traefikLines = findReplaceLine(traefikYaml, traefikLines, []string{"\\s+type: LoadBalancer"}, []string{"{{- if or .Values.tls.terminationEnabled .Values.traefik.forceLoadBalancer }}", "  type: LoadBalancer", "{{- else }}", "  type: ClusterIP", "{{- end }}"})
	traefikLines = findWrapIf(traefikYaml, traefikLines, []string{"\\s+externalTrafficPolicy: Local"}, "or .Values.tls.terminationEnabled .Values.traefik.forceLoadBalancer")

	// Write Dex + Traefik template
	dexData := []byte(strings.Join(dexLines[:], "\n"))
	if err := os.WriteFile(dexYaml, dexData, 0o777); err != nil {
		panic(err)
	}

	traefikData := []byte(strings.Join(traefikLines[:], "\n"))
	if err := os.WriteFile(traefikYaml, traefikData, 0o777); err != nil {
		panic(err)
	}
}

func readFileContent(filePath string, prependLines, appendLines []string) []string {
	fileContent := stripEmptyLines(filePath)
	newContent := make([]string, len(prependLines)+len(fileContent)+len(appendLines))
	copy(newContent, prependLines)
	copy(newContent[len(prependLines):], fileContent)
	copy(newContent[len(prependLines)+len(fileContent):], appendLines)
	return newContent
}

func findReplaceLine(filePath string, content, findLines, replaceLines []string) []string {
	contentLength := len(content)
	findLinesLength := len(findLines)
	newContent := make([]string, 0, contentLength)
	linesFound := false

	for j := 0; j < contentLength; {
		if j+findLinesLength <= contentLength && linesMatch(content[j:j+findLinesLength], findLines) {
			newContent = append(newContent, replaceLines...)
			j += findLinesLength
			linesFound = true
		} else {
			newContent = append(newContent, content[j])
			j++
		}
	}

	if !linesFound {
		panic(fmt.Sprintf("No lines found in '%s' to replace \n===\n%s\n===\n with \n===\n%s\n===",
			filePath, findLines, replaceLines))
	}
	return newContent
}

// findWrapContent is a helper function to find matching lines and then wrap them with the given start and end lines.
func findWrapContent(filePath string, content, findLines []string, start, end string) []string {
	contentLength := len(content)
	findLinesLength := len(findLines)
	newContent := make([]string, 0, contentLength)
	linesFound := false

	for j := 0; j < contentLength; {
		if j+findLinesLength <= contentLength && linesMatch(content[j:j+findLinesLength], findLines) {
			newContent = append(newContent, start)
			newContent = append(newContent, content[j:j+findLinesLength]...)
			newContent = append(newContent, end)
			j += findLinesLength
			linesFound = true
		} else {
			newContent = append(newContent, content[j])
			j++
		}
	}

	if !linesFound {
		panic(fmt.Sprintf("No lines found in '%s' to wrap \n===\n%s\n===\n",
			filePath, findLines))
	}
	return newContent
}

// findWrapIf is a convenience wrapper for findWrapContent that is a shorthand for an if/end block.
// It takes a conditional string that will be used in the if statement.
func findWrapIf(filePath string, content, findLines []string, conditional string) []string {
	start := fmt.Sprintf("{{- if %s }}", conditional)
	end := "{{- end }}"
	return findWrapContent(filePath, content, findLines, start, end)
}

func linesMatch(contentLines, findLines []string) bool {
	for j, findLine := range findLines {
		if !regexCheck(findLine, contentLines[j]) {
			return false
		}
	}
	return true
}

func regexCheck(pattern, str string) bool {
	match, err := regexp.MatchString(pattern, str)
	if err != nil {
		panic(err)
	}
	return match
}

func stripEmptyLines(filePath string) []string {
	fileContent, err := os.ReadFile(filePath)
	if err != nil {
		panic(err)
	}

	var removed []string
	for _, str := range strings.Split(string(fileContent), "\n") {
		if strings.TrimSpace(str) == "" {
			continue
		}
		removed = append(removed, str)
	}
	return removed
}
