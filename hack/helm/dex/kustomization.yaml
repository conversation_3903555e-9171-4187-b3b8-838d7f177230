apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: "{{ .Release.Namespace }}"

helmCharts:
- name: dex
  repo: https://charts.dexidp.io
  # See: https://charts.dexidp.io/index.yaml
  version: 0.12.1
  valuesFile: values.yaml
  releaseName: dex

patches:
- target:
    labelSelector: app.kubernetes.io/name=dex
  patch: |-
    - op: remove
      path: /metadata/labels
- patch: |-
    $patch: delete
    apiVersion: v1
    kind: Secret
    metadata:
      name: dex-test-no-create
- patch: |-
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: dex
    spec:
      template:
        metadata:
          annotations:
            checksum/dexconfig: '{{ tpl (.Files.Get "config/dexconfig.yaml") . | sha256sum }}'
            checksum/dexsecret: '{{ printf "%s-%s" (include (print $.Template.BasePath "/dex/dex-secret.yaml") .) .Values.sso.oidc.clientSecret | sha256sum }}'
        spec:
          automountServiceAccountToken: false
          volumes:
            - name: config
              secret: null
              configMap:
                name: dex
