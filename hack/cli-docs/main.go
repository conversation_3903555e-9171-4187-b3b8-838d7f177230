package main

import (
	"os"

	"github.com/spf13/cobra/doc"

	"github.com/akuityio/akuity-platform/internal/akuitycli/config"
	"github.com/akuityio/akuity-platform/internal/akuitycli/config/v1alpha1"
	"github.com/akuityio/akuity-platform/internal/akuitycli/option"
	"github.com/akuityio/akuity-platform/internal/akuitycli/root"
	"github.com/akuityio/akuity-platform/internal/cli"
)

func main() {
	cmd, err := root.NewCommand(&option.CLI{ConfigClient: config.NewInMemoryClient(v1alpha1.Config{})})
	cli.CheckErr(err)
	out := "."
	if len(os.Args) > 1 {
		out = os.Args[1]
	}
	cli.CheckErr(doc.GenMarkdownTree(cmd, out))
}
