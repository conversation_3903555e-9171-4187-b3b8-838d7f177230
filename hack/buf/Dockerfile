ARG PROTOC_VERSION=28.3

# Pre-built binaries
FROM mirror.gcr.io/curlimages/curl:8.10.1 AS curl

ARG PROTOC_VERSION

WORKDIR /downloads

RUN ARCH=$(uname -m) && \
  if [ "$ARCH" = "aarch64" ]; then \
    ARCH="aarch_64"; \
  fi && \
  curl -fL \
    -o protoc.zip \
    "https://github.com/protocolbuffers/protobuf/releases/download/v${PROTOC_VERSION}/protoc-${PROTOC_VERSION}-linux-${ARCH}.zip"

# Go based plugins
FROM mirror.gcr.io/golang:1.23.2-bookworm AS go

RUN go install google.golang.org/protobuf/cmd/protoc-gen-go@v1.30.0
RUN go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@v1.3.0
RUN go install github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-grpc-gateway@v2.23.0
RUN go install github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2@v2.23.0
RUN go install github.com/akuity/grpc-gateway-client/protoc-gen-grpc-gateway-client@v0.0.0-20240726023031-e93310492571
RUN go install github.com/bufbuild/protoschema-plugins/cmd/protoc-gen-jsonschema@33ee3061c0eefaf0d3836dcb38d5a3c7ae03ff6c

# Buf
FROM mirror.gcr.io/bufbuild/buf:1.46.0 AS buf

# Final image
FROM mirror.gcr.io/node:23.1.0-bookworm-slim

# Install required tools
RUN apt update -y && apt install -y ca-certificates unzip

# Install buf
COPY --from=buf /usr/local/bin/buf /usr/local/bin/buf

# Install protoc
WORKDIR /downloads
COPY --from=curl /downloads/protoc.zip /downloads/protoc.zip
RUN unzip /downloads/protoc.zip -d /usr/local/bin/.protoc
ENV PATH="/usr/local/bin/.protoc/bin:${PATH}"

# Install go plugins
COPY --from=go /go/bin/protoc-gen-go /usr/local/bin/protoc-gen-go
COPY --from=go /go/bin/protoc-gen-go-grpc /usr/local/bin/protoc-gen-go-grpc
COPY --from=go /go/bin/protoc-gen-grpc-gateway /usr/local/bin/protoc-gen-grpc-gateway
COPY --from=go /go/bin/protoc-gen-openapiv2 /usr/local/bin/protoc-gen-openapiv2
COPY --from=go /go/bin/protoc-gen-grpc-gateway-client /usr/local/bin/protoc-gen-grpc-gateway-client
COPY --from=go /go/bin/protoc-gen-jsonschema /usr/local/bin/protoc-gen-jsonschema

# Install ES plugins
RUN npm install -g @bufbuild/protoc-gen-es@v1.2.0

WORKDIR /workspace
