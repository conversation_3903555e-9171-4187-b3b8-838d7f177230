package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"text/template"

	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
)

func main() {
	url := "https://kubepug.xyz/data/data.json"
	resp, err := http.Get(url)
	if err != nil {
		fmt.Println("Error fetching data:", err)
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Println("Error reading response body:", err)
		return
	}

	var apiVersions []k8sresource.APIVersionInfo
	err = json.Unmarshal(body, &apiVersions)
	if err != nil {
		fmt.Println("Error unmarshalling JSON:", err)
		return
	}

	res := make([]k8sresource.APIVersionInfo, 0, len(apiVersions))
	for _, apiVersion := range apiVersions {
		if apiVersion.DeprecatedVersion.VersionMajor == 0 && apiVersion.DeprecatedVersion.VersionMinor == 0 {
			continue
		}
		res = append(res, apiVersion)
	}

	tmpl := `// Code generated by go generate; DO NOT EDIT.

package k8sresource

import (
	"k8s.io/apimachinery/pkg/runtime/schema"
)

var deprecatedGVKMap = map[schema.GroupVersionKind]APIVersionInfo{
{{- range . }}
	{Group: "{{.Group}}", Version: "{{.Version}}", Kind: "{{.Kind}}"}: {
		Group:   "{{.Group}}",
		Version: "{{.Version}}",
		Kind:    "{{.Kind}}",
		DeprecatedVersion: Version{
			VersionMajor: {{.DeprecatedVersion.VersionMajor}},
			VersionMinor: {{.DeprecatedVersion.VersionMinor}},
		},
		RemovedVersion: Version{
			VersionMajor: {{.RemovedVersion.VersionMajor}},
			VersionMinor: {{.RemovedVersion.VersionMinor}},
		},
		{{- if .Replacement.Group}}
		Replacement: schema.GroupVersionKind{
			Group:   "{{.Replacement.Group}}",
			Version: "{{.Replacement.Version}}",
			Kind:    "{{.Replacement.Kind}}",
		},
		{{- end}}
	},
{{- end }}
}
`
	t, err := template.New("gvkMap").Parse(tmpl)
	if err != nil {
		fmt.Println("Error creating template:", err)
		return
	}

	file, err := os.Create("internal/services/k8sresource/deprecated_gvk_map.gen.go")
	if err != nil {
		fmt.Println("Error creating file:", err)
		return
	}
	defer file.Close()

	err = t.Execute(file, res)
	if err != nil {
		fmt.Println("Error executing template:", err)
		return
	}

	fmt.Println("File deprecated_gvk_map.gen.go generated successfully.")
}
