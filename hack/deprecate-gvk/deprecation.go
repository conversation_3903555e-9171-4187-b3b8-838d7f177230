package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"reflect"
	"sort"
	"text/template"

	"k8s.io/apimachinery/pkg/runtime/schema"

	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
)

func main() {
	url := "https://kubepug.xyz/data/data.json"
	resp, err := http.Get(url)
	if err != nil {
		fmt.Println("Error fetching data:", err)
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Println("Error reading response body:", err)
		return
	}

	var apiVersions []k8sresource.APIVersionInfo
	err = json.Unmarshal(body, &apiVersions)
	if err != nil {
		fmt.Println("Error unmarshalling JSON:", err)
		return
	}

	res := make([]k8sresource.APIVersionInfo, 0, len(apiVersions))
	for _, apiVersion := range apiVersions {
		if apiVersion.DeprecatedVersion.VersionMajor == 0 && apiVersion.DeprecatedVersion.VersionMinor == 0 {
			continue
		}
		res = append(res, apiVersion)
	}

	tmpl := `// Code generated by go generate; DO NOT EDIT.

package k8sresource

import (
	"k8s.io/apimachinery/pkg/runtime/schema"
)

var DeprecatedGVKMap = map[schema.GroupVersionKind]APIVersionInfo{
{{- range . }}
	{Group: "{{.Group}}", Version: "{{.Version}}", Kind: "{{.Kind}}"}: {
		Group:   "{{.Group}}",
		Version: "{{.Version}}",
		Kind:    "{{.Kind}}",
		DeprecatedVersion: Version{
			VersionMajor: {{.DeprecatedVersion.VersionMajor}},
			VersionMinor: {{.DeprecatedVersion.VersionMinor}},
		},
		RemovedVersion: Version{
			VersionMajor: {{.RemovedVersion.VersionMajor}},
			VersionMinor: {{.RemovedVersion.VersionMinor}},
		},
		{{- if .Replacement.Group}}
		Replacement: schema.GroupVersionKind{
			Group:   "{{.Replacement.Group}}",
			Version: "{{.Replacement.Version}}",
			Kind:    "{{.Replacement.Kind}}",
		},
		{{- end}}
	},
{{- end }}
}
`
	t, err := template.New("gvkMap").Parse(tmpl)
	if err != nil {
		fmt.Println("Error creating template:", err)
		return
	}

	file, err := os.Create("internal/services/k8sresource/deprecated_gvk_map.gen.go")
	if err != nil {
		fmt.Println("Error creating file:", err)
		return
	}
	defer file.Close()

	err = t.Execute(file, mergeEntries(k8sresource.DeprecatedGVKMap, res))
	if err != nil {
		fmt.Println("Error executing template:", err)
		return
	}

	fmt.Println("File deprecated_gvk_map.gen.go generated successfully.")
}

// mergeEntries merges new entries with existing ones, updating or adding as needed
func mergeEntries(old map[schema.GroupVersionKind]k8sresource.APIVersionInfo, new []k8sresource.APIVersionInfo) []k8sresource.APIVersionInfo {
	m := make(map[schema.GroupVersionKind]k8sresource.APIVersionInfo, len(old)+len(new))

	// Copy existing entries
	for gvk, info := range old {
		m[gvk] = info
	}

	// Add or update with new entries
	for _, newEntry := range new {
		gvk := schema.GroupVersionKind{
			Group:   newEntry.Group,
			Version: newEntry.Version,
			Kind:    newEntry.Kind,
		}

		existingEntry, exists := m[gvk]
		if !exists || !reflect.DeepEqual(existingEntry, newEntry) {
			m[gvk] = newEntry
			if exists {
				fmt.Printf("Updated entry: %s/%s %s\n", newEntry.Group, newEntry.Version, newEntry.Kind)
			} else {
				fmt.Printf("Added new entry: %s/%s %s\n", newEntry.Group, newEntry.Version, newEntry.Kind)
			}
		}
	}

	result := make([]k8sresource.APIVersionInfo, 0, len(m))
	for _, info := range m {
		result = append(result, info)
	}

	// Sort entries for consistent output
	sort.Slice(result, func(i, j int) bool {
		if result[i].Group != result[j].Group {
			return result[i].Group < result[j].Group
		}
		if result[i].Version != result[j].Version {
			return result[i].Version < result[j].Version
		}
		return result[i].Kind < result[j].Kind
	})

	return result
}
