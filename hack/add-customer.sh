#!/bin/bash

set -xe

CUSTOMER=$1
CUSTOMER=${CUSTOMER:-'customer'}

K3S_IMAGE=rancher/k3s:v1.31.3-k3s1
CUDA_IMAGE=nvidia/cuda:12.6.1-base-ubuntu24.04

IMAGE=$K3S_IMAGE
GPU_FLAGS=''

CUDA=${CUDA:-false}
if [[ "$CUDA" == "true" ]]; then
  IMAGE=$K3S_IMAGE-cuda
  docker build -f ./hack/k3s-cuda/Dockerfile --build-arg K3S_IMAGE=$K3S_IMAGE --build-arg CUDA_IMAGE=$CUDA_IMAGE -t $IMAGE ./hack/k3s-cuda/
  GPU_FLAGS='--gpus all'
fi

k3d cluster create --wait akuity-$CUSTOMER \
  --k3s-arg '--kubelet-arg=eviction-hard=imagefs.available<1%,nodefs.available<1%@agent:*' \
  --k3s-arg '--kubelet-arg=eviction-minimum-reclaim=imagefs.available=1%,nodefs.available=1%@agent:*' \
  --k3s-arg '--kubelet-arg=eviction-hard=imagefs.available<1%,nodefs.available<1%@server:0' \
  --k3s-arg '--kubelet-arg=eviction-minimum-reclaim=imagefs.available=1%,nodefs.available=1%@server:0' \
  --k3s-arg '--disable=traefik@server:*'\
  --image $IMAGE $GPU_FLAGS
# Ensure that the kubeconfig is consistent
k3d kubeconfig get akuity-$CUSTOMER > kubeconfig.akuity-$CUSTOMER
export KUBECONFIG=kubeconfig.akuity-$CUSTOMER

kubectl wait --timeout=60s deployment -n kube-system metrics-server --for condition=Available=True

# We need to wait CoreDNS to start running then patch the ConfigMap
while true; do
  COREDNS_STATUS=$(kubectl get pod -n kube-system -l k8s-app=kube-dns -o jsonpath="{.items[0].status.phase}" 2>/dev/null || echo "NotFound")
  if [ "$COREDNS_STATUS" == "Running" ]; then
    break
  else
    echo "CoreDNS Pod is not running yet. Waiting..."; sleep 5
  fi
done

# For coredns, we only want to patch coredns so that `akuity-platform` is mapped to host.k3d.internal.
# For that we first add a single line to the existing `Corefile` key, where that configuration will be.
#
# file /etc/coredns/akuity.db akuity-platform
mkdir -p dist
cat > dist/temp-patch.yaml << EOL
data:
  Corefile: |
$(kubectl -n kube-system get cm coredns -o json | jq -r '.data.Corefile' | grep -v 'file /etc/coredns/akuity.db akuity-platform' | sed 's/forward/file \/etc\/coredns\/akuity.db akuity-platform\n    forward/g' | sed 's/^/    /')
EOL

kubectl -n kube-system patch cm coredns --type strategic --patch-file dist/temp-patch.yaml

# Then we add a patch which only adds the mappings for akuity-platform
kubectl -n kube-system patch cm coredns --type strategic --patch-file hack/dev/custom-coredns-config.yaml

rm dist/temp-patch.yaml

kubectl -n kube-system patch deployment coredns --type='json' -p='[{"op":"add","path":"/spec/template/spec/volumes/0/configMap/items/-","value":{"key":"akuity.db", "path": "akuity.db"}}]'
