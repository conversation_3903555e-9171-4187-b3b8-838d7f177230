#!/usr/bin/env bash
# Adapted from https://carvel.dev/install.sh

set -e

if [ -z "$1" ]; then
  echo "The first argument should be the requested version."
  exit 1
fi

# Export GH_TOKEN to ensure it is available to child scripts
export GH_TOKEN

dest_dir="${K14SIO_INSTALL_BIN_DIR:-/usr/local/bin}"

binary_type="$(go env GOOS)-$(go env GOARCH)"

# https://github.com/carvel-dev/ytt/releases/tag/v0.50.0
case ${binary_type} in
  linux-amd64)
    ytt_checksum=5cc0481a1b5281877113d8a543b5a13fb5be396f4d4586c15398fd126a24c2ef
    ;;
  linux-arm64)
    ytt_checksum=c9e996c6b68e013b0230bc1727f9e4fcfd26ab80970e5cb0f31f3301046e1b73
    ;;
  *)
    echo "unknown os-arch: ${binary_type}"
    exit 1
    ;;
esac

./hack/get_gh_asset.sh akuityio fedramp-binaries "ytt-v$1" "ytt-$binary_type"
echo "${ytt_checksum}" "*ytt-$binary_type" | shasum -c -
mv "ytt-$binary_type" "${dest_dir}/ytt"
chmod +x ${dest_dir}/ytt
