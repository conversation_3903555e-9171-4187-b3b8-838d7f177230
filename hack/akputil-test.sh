#!/bin/bash

set -euo pipefail

function secretValue() {
  kubectl get secret "$1" -n akuity-platform -o jsonpath="{.data.$2}" | base64 --decode
}

function akputil() {
  echo
  echo "=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-="
  echo " $ akputil $*"
  echo "=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-="
  ./dist/akputil "$@"
}

export AWS_PROFILE=test-edit
kubectx view@akp-001-tst-usw2

go mod tidy && go mod verify && go mod download && go mod vendor
make akputil

# https://test.akuity.io/instances/akuity/argocd/akputil-test
akputil_argo_instance='0we7bmibdbfukchd'

# https://test.akuity.io/instances/kargo/akputil-test/agents
akputil_kargo_instance='ex79s0olt8g34xg7'

# https://test.akuity.io/instances/akuity/argocd/akputil-test?tab=clusters
# http://grafana-test.monitoring.svc/d/akuity-platform/akuity-platform?viewPanel=48
akputil_cluster='30y35oxgwsfxg803'

akputil version

# Agent

akputil agent supported-argo-versions
akputil agent supported-versions

# Archiver

akputil archiver verify-audit-log-records-journal --staleness-days   180
akputil archiver soft-delete-audit-log-records    --retention-period 90
akputil archiver archive-audit-log-records        --s3-bucket akuity.cloud.akp-001-tst-usw2.audit-log-archive

# Aggregator

akputil aggregator aggregate-audit-sync-records --aggregation-window=60

# Notification events

akputil event free-trial-expiring --days-until-expiration=1,3,7,29

# Instance

akputil instance argocd-account-token account-name --instance-id "${akputil_argo_instance}"
akputil instance bump-generation                   --instance-id "${akputil_argo_instance}"
akputil instance decrement-observed-generation     --instance-id "${akputil_argo_instance}"
akputil instance refresh-certs                     --instances   "${akputil_argo_instance}"
akputil instance set-override                      --instance-id "${akputil_argo_instance}" --file ./hack/instance-override-akputil-test.json
akputil instance set-override                      --instance-id "${akputil_argo_instance}" --remove
akputil instance ensure-healthy       --instance-ids "${akputil_argo_instance}"  --include-agents --stabilization-period 1
akputil instance ensure-healthy       --instance-ids "${akputil_kargo_instance}" --include-agents --stabilization-period 1
akputil instance ensure-agent-version --instance-id  "${akputil_argo_instance}"  --include-agents
akputil instance ensure-agent-version --instance-id  "${akputil_kargo_instance}" --include-agents

akputil instance k3s-upgrade "quay.io/akuity/rancher/k3s:v1.28.11-k3s2" --percentage=25 --stabilization-period=1 --delay=5 --skip-confirmation
akputil instance gradual-upgrade   --percentage=25 --stabilization-period=1 --delay=5 --skip-confirmation --include-agents
akputil instance gradual-reconcile --percentage=25 --stabilization-period=1 --delay=5 --skip-confirmation --include-agents

# Images Age

set -x
curl -s "http://prometheus-pushgateway-test.monitoring.svc/metrics" | tail -5
set +x

akputil image age \
  --image "us-docker.pkg.dev/akuity/akp/agent-server:latest" \
  --image "us-docker.pkg.dev/akuity/akp/agent-server:0.5.44" \
  --image "us-docker.pkg.dev/akuity/akp/agent:latest" \
  --image "us-docker.pkg.dev/akuity/akp/agent:0.5.44" \
  --image "us-docker.pkg.dev/akuity/akp/akuity-platform:latest" \
  --image "us-docker.pkg.dev/akuity/akp/akuity-platform:0.16.1-0.20240911134148-f5da4b366da8" \
  --image "quay.io/akuity/dexidp/dex:v2.38.0" \
  --image "quay.io/akuity/rancher/k3s:v1.28.11-k3s2" \
  --image "quay.io/prometheus/prometheus:v2.54.0" \
  --image "docker.io/traefik:v2.11.8" \
  --image "twingate/connector:1.69.0" \
  --image "grafana/grafana:11.1.4"

akputil image age \
  --timeout 5s \
  --pushgatewayURL http://prometheus-pushgateway-test.monitoring.svc/metrics/job/akputil_test \
  --image "us-docker.pkg.dev/akuity/akp/agent-server:latest" \
  --image "us-docker.pkg.dev/akuity/akp/agent-server:0.5.44" \
  --image "us-docker.pkg.dev/akuity/akp/agent:latest" \
  --image "us-docker.pkg.dev/akuity/akp/agent:0.5.44" \
  --image "us-docker.pkg.dev/akuity/akp/akuity-platform:latest" \
  --image "us-docker.pkg.dev/akuity/akp/akuity-platform:0.16.1-0.20240911134148-f5da4b366da8" \
  --image "quay.io/akuity/dexidp/dex:v2.38.0" \
  --image "quay.io/akuity/rancher/k3s:v1.28.11-k3s2" \
  --image "quay.io/prometheus/prometheus:v2.54.0" \
  --image "docker.io/traefik:v2.11.8" \
  --image "twingate/connector:1.69.0" \
  --image "grafana/grafana:11.1.4"

# License

# Skip the first 3 lines (for the header), then take the first 5 lines (for the private key)
akputil license pem-files | sed -n '/BEGIN EC PRIVATE KEY/,/END EC PRIVATE KEY/p' > /tmp/license-private.pem
akputil license generate --instances 1 --duration-days 1 --grace-period-days 1 --applications 1 --clusters 1 --issued-for "Akputil Test" --private-key-path /tmp/license-private.pem
rm /tmp/license-private.pem

# Organization

akputil organization remove-<NAME_EMAIL>
akputil organization add-member    akuity <EMAIL> owner
akputil organization billing-state  --org-name 'akuity' --state paid_customer --expiration-extension 3650
akputil organization billing-limits --org-name 'akuity' --max-clusters 10 --max-instances 10 --max-applications 50
akputil organization create-api-key --org-name akuity --description "test-$RANDOM"

# Cluster

akputil cluster maintenance-mode --cluster-id "${akputil_cluster}"
akputil cluster maintenance-mode --cluster-id "${akputil_cluster}" --disabled

kubectx edit@akp-001-tst-usw2

akputil cluster port-forward --cluster-id "${akputil_cluster}" --cluster-namespace 'akuity-akputil-test' &
sleep 5
akputil cluster debug exec   --cluster-id "${akputil_cluster}" --cluster-namespace 'akuity-akputil-test' -- pwd
akputil cluster debug exec   --cluster-id "${akputil_cluster}" --cluster-namespace 'akuity-akputil-test' -- uname -a
kill "$(pgrep -f 'akputil cluster port-forward' | head -1)"

kubectx view@akp-001-tst-usw2
