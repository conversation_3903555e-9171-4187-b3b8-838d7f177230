#!/bin/bash

set -e

versionlte() {
    [  "$1" = "`echo -e "$1\n$2" | sort -V | head -n1`" ]
}

changelog() {
    RELEASE_NOTES=""
    ARGOCD_AK_VERS=""
    ARGOCD_OSS_VERS=""
    AGENT_VERS=""
    for ITEM in $(gh release list --exclude-drafts --exclude-pre-releases --limit 10000 | tail -n +1 | awk '{print $1}')
    do
        RELEASE_NOTES+="### ${ITEM}
$(gh release view ${ITEM} -R **************:akuityio/akuity-platform.git --json body -q .body | \
            grep -v -e '* chore' | \
            grep -v -e 'Full Changelog' | \
            sed 's/</\\</g' | \
            sed 's/>/\\>/g' | \
            sed 's/by @.*$//g' | \
            sed 's/^#/###&/')

"

        if versionlte "0.11.0" ${ITEM:1}; then
            VERS=$(docker run --rm -t us-docker.pkg.dev/akuity/akp-sh/akuity-platform:${ITEM:1} sh -c "akputil agent supported-versions | sed -n '/^[0-9]\+\.[0-9]\+\.[0-9]\+$/p' | paste -sd ',' -" | sed "s/[^,]*/ \`&\`/g" | sed 's/..$//')
            AGENT_VERS+="|\`${ITEM}\`|${VERS}\`|
"
            ARGOCD_VERS=$(docker run --rm -t us-docker.pkg.dev/akuity/akp-sh/akuity-platform:${ITEM:1} akputil agent supported-argo-versions | grep '"version":')
            AK_VERS=$(echo "${ARGOCD_VERS}"  | grep -v 'latest' | grep -Po '\d+.\d+.\d+-ak.\d+' | sort -Vr | xargs printf -- '`v%s`' | sed 's/``/`, `/g')
            ARGOCD_AK_VERS+="|\`${ITEM}\`|${AK_VERS}|
"
            OSS_VERS=$(echo "${ARGOCD_VERS}" | grep -v '\-ak\|latest' | grep -Po '\d+.\d+.\d+' | sort -Vr | xargs printf -- '`v%s`' | sed 's/``/`, `/g')
            ARGOCD_OSS_VERS+="|\`${ITEM}\`|${OSS_VERS}|
"
        fi
    done

cat >${1}/index.mdx <<EOL
---
description: Information on the latest updates and additions to the Self-Hosted Akuity Platform
title: Self-Hosted Changelog
---

Information on the latest updates and additions to the Self-Hosted Akuity Platform.

## Versions
${RELEASE_NOTES}
EOL


    cat >${1}/20-argocd-compatibility.md <<EOL
---
description: Akuity Platform Self-Hosted Argo CD Compatibility Table
title: Akuity Argo CD Compatibility
sidebar_label: Argo CD Compatibility
---

## Akuity Patched Argo CD Versions
|Akuity Platform Version|Argo CD Versions|
|-|-|
${ARGOCD_AK_VERS}

## Open Source Argo CD Versions
|Akuity Platform Version|Argo CD Versions|
|-|-|
${ARGOCD_OSS_VERS}
EOL

    cat >${1}/10-agent-compatibility.md <<EOL
---
description: Akuity Platform Self-Hosted Agent Compatibility Table
title: Akuity Agent Compatibility
sidebar_label: Agent Compatibility
---

|Akuity Platform Version|Agent Versions|
|-|-|
${AGENT_VERS}
EOL

}

chart-docs() {
    readme-generator --values "${PWD}/charts/akuity-platform/values.yaml" --readme "${PWD}/charts/akuity-platform/README.md" --config "${PWD}/hack/selfhosted-docs/readme-generator-config.json"
    sed -i "/<!-- README.md -->/q" $1
    cat charts/akuity-platform/README.md >> $1
}

case ${1} in
changelog)
    changelog ${2}
    ;;
chart-docs)
    chart-docs ${2}
    ;;
*)
    exit 1
    ;;
esac
