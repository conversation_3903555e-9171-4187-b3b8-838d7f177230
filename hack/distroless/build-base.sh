#!/bin/sh

set -e

export APKO_IMAGE=export APKO_IMAGE=quay.io/akuity/apko:20250623

mkdir -p hack/distroless/dist && cp hack/distroless/latest.apko.yaml hack/distroless/dist/latest.apko.yaml && cd ./hack/distroless/dist
docker run -v $(pwd):/work -w /work --rm -t $APKO_IMAGE build  ./latest.apko.yaml akp-distroless-base ./image
docker image load -i ./image
docker tag akp-distroless-base:latest-arm64 localhost:5000/akp-distroless-base:latest-arm64
docker push localhost:5000/akp-distroless-base:latest-arm64
docker tag akp-distroless-base:latest-amd64 localhost:5000/akp-distroless-base:latest-amd64
docker push localhost:5000/akp-distroless-base:latest-amd64
docker manifest create localhost:5000/akp-distroless-base:latest localhost:5000/akp-distroless-base:latest-arm64 localhost:5000/akp-distroless-base:latest-amd64 --insecure --amend
docker manifest push localhost:5000/akp-distroless-base:latest --insecure
