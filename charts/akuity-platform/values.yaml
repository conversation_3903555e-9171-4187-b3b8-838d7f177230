## @section License Key

## @param licenseKey
licenseKey: ""

## @section Image Parameters

image:
  ## @param image.repository Image repository of the Akuity Platform
  repository: us-docker.pkg.dev/akuity/akp-sh/akuity-platform
  ## @param image.tag Overrides the image tag (default is the chart version)
  tag: ""
  secret:
    ## @param image.secret.create Creates the 'akuity-pullsecrets' secret
    create: true
  ## @param image.username Username to the Akuity Platform container registry
  username: _json_key_base64
  ## @param image.password Password to the Akuity Platform container registry
  password: ""

  argocd:
    ## @param image.argocd.host Overrides the Argo CD image host
    host: ""
    ## @param image.argocd.repo Overrides the Argo CD image repository
    repo: ""
  agentServer:
    ## @param image.agentServer.host Overrides the agent server image host. If not set, the host
    ## from image.repository will be used. This should be everything in the image reference except
    ## for the repository name and tag.
    host: null

## @section Portal Parameters

portal:
  ## @param portal.url Public URL to portal (e.g. `https://akuity.example.com`)
  url: ""
  ## @param portal.imagePullPolicy Portal server image pull policy
  imagePullPolicy: Always
  ## @param portal.maxEmailInvitationsPerBatch Maximum number of invitation emails which can be sent in one go
  maxEmailInvitationsPerBatch: 5

  ## @param portal.autoscaling.enabled Enables horizontal pod autoscaling for the portal server
  ## @param portal.autoscaling.minReplicas Sets the minimum number of replicas
  ## @param portal.autoscaling.maxReplicas Sets the maximum number of replicas
  ## @param portal.autoscaling.targetCPUUtilizationPercentage Sets the target CPU utilization percentage
  ## @param portal.autoscaling.targetMemoryUtilizationPercentage Sets the target memory utilization percentage
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 10
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80

  # seed:
  # organization:
  ## @param portal.seed.organization.name [nullable] Creates an organization with a given name
  # name: foobar
  ## @param portal.seed.organization.owner [nullable] Creates an owner for the given organization with the given e-mail address.
  # owner: <EMAIL>

  ## @param portal.resources Resources limits and requests for the portal server containers
  resources:
    {}
    # limits:
    #   cpu: "2"
    #   memory: 4Gi
    # requests:
    #   cpu: 500m
    #   memory: 512Mi

  ## @param portal.tolerations Tolerations for the portal-server pod
  tolerations: []

  serviceAccount:
    ## @param portal.serviceAccount.annotations Annotations for the portal-server service account
    annotations: {}

  ## @param portal.env Additional environment variables added to the portal server
  env:
    {}
    ## @param portal.env.MIN_ORGANIZATION_NAME_LENGTH [nullable] The minimum length of an organization name that is allowed on the platform, minimum value is `2`, defaults to `4` if undefined
    # MIN_ORGANIZATION_NAME_LENGTH: 4
    ## @param portal.env.MIN_CLUSTER_NAME_LENGTH [nullable] The minimum length of a cluster name that is allowed on the platform, minimum value is `2`, defaults to `3` if undefined
    # MIN_CLUSTER_NAME_LENGTH: 3
    ## @param portal.env.MIN_INSTANCE_NAME_LENGTH [nullable] The minimum length of an Argo CD instance name that is allowed on the platform, minimum value is `2`, defaults to `3` if undefined
    # MIN_INSTANCE_NAME_LENGTH: 3

  ## @param portal.topologySpreadConstraints [nullable] Sets topology spread constraints for the portal server deployment
  ## @skip portal.topologySpreadConstraints[0]
  ## @skip portal.topologySpreadConstraints[1]
  # Defaults to preferring scheduling replicas across zones and nodes when possible
  topologySpreadConstraints:
    - labelSelector:
        matchLabels:
          app.kubernetes.io/name: portal-server
      maxSkew: 1
      topologyKey: topology.kubernetes.io/zone
      whenUnsatisfiable: ScheduleAnyway
    - labelSelector:
        matchLabels:
          app.kubernetes.io/name: portal-server
      maxSkew: 1
      topologyKey: kubernetes.io/hostname
      whenUnsatisfiable: ScheduleAnyway

## @section Platform controller Parameters
platformController:
  ## @param platformController.imagePullPolicy Platform controller image pull policy
  imagePullPolicy: Always

  ## @param platformController.domainSuffix Platform controller domain suffix to use (defaults to hostname of `.portal.url`)
  domainSuffix: ""

  ## @param platformController.instanceSubDomains Enables using nested subdomains for managed argocd/kargo instances
  instanceSubDomains: true

  ## @param platformController.overrideDefaultDomains Enables setting subdomains for k3s, agent server etc. or setting the FQDN as a suffix for those services.
  overrideDefaultDomains: false

  ## @param platformController.tolerations Tolerations for the platform controller pod
  tolerations: []

  serviceAccount:
    ## @param platformController.serviceAccount.annotations Annotations for the platform controller service account
    annotations: {}

  ## @section Resources limits and requests for the platform controller containers
  ## @skip platformController.resources
  resources:
    limits:
      memory: 3Gi
    requests:
      cpu: "2"
      memory: 3Gi
  #  shard: "us1"

  ## @param platformController.env Adds additional environment variables to the platform controller configmap
  env:
    {}
    ## @param platformController.env.ARGOCD_APP_RESYNC_INTERVAL_SECONDS [nullable] Argo CD Application resync interval in seconds (if `0` or undefined then Argo CD built-in default is used)
    # ARGOCD_APP_RESYNC_INTERVAL_SECONDS: 0
    ## @param platformController.env.AGENT_STATUS_UPDATE_INTERVAL_SECONDS [nullable] Agent status update interval in seconds (if `0` or undefined then Agent built-in default is used)
    # AGENT_STATUS_UPDATE_INTERVAL_SECONDS: 0
    ## @param platformController.env.SHARED_K3S_DB_CONNECTION_AUTH [nullable] Set to `true` for all tenants to use database.user and database.password credentials rather than a personal credentials for each tenant. This might be needed when connecting to the database through RDS Proxy which has a limit of 200 users
    # SHARED_K3S_DB_CONNECTION_AUTH: true

  ## @param platformController.commonAgentCert common agent cert provides the shared certificate for both *.cdsvcs.akuity.example.com as well as *.kargosvcs.akuity.example.com domains used by agents
  commonAgentCert: ""
  ## @param platformController.argocdAgentCert argocd agent cert provides the certificate for only *.cdsvcs.akuity.example.com used by argocd agents
  argocdAgentCert: ""
  ## @param platformController.kargoAgentCert kargo agent cert provides the certificate for only *.kargosvcs.akuity.example.com domains used by kargo agents
  kargoAgentCert: ""

## @section Notification controller Parameters
notificationController:
  ## @param notificationController.enabled Enabled the notification controller
  enabled: false
  ## @param notificationController.imagePullPolicy Notification controller image pull policy
  imagePullPolicy: Always

  ## @param notificationController.tolerations Tolerations for the notification controller pod
  tolerations: []

  serviceAccount:
    ## @param notificationController.serviceAccount.annotations Annotations for the notification controller service account
    annotations: {}

  ## @param notificationController.resources Resources limits and requests for the notification controller containers
  resources: {}
  #    limits:
  #      cpu: "4"
  #      memory: 4Gi
  #    requests:
  #      cpu: "2"
  #      memory: 1Gi
  #  shard: "us1"

  ## @param notificationController.env Adds additional environment variables to the notification controller configmap
  env: {}

addonController:
  ## @param addonController.enabled Enabled the addon controller
  enabled: true
  ## @param addonController.imagePullPolicy Notification controller image pull policy
  imagePullPolicy: Always

  ## @param addonController.tolerations Tolerations for the addon controller pod
  tolerations: []

  serviceAccount:
    ## @param addonController.serviceAccount.annotations Annotations for the addon controller service account
    annotations: {}

  ## @param addonController.resources Resources limits and requests for the addon controller containers
  resources: {}
  #    limits:
  #      cpu: "4"
  #      memory: 4Gi
  #    requests:
  #      cpu: "2"
  #      memory: 1Gi
  #  shard: "us1"

  ## @param addonController.env Adds additional environment variables to the addon controller configmap
  env: {}

## @section Secret Parameters
secret:
  ## @param secret.create Creates the 'akuity-platform' Secret
  create: true

## @section TLS Parameters
## @descriptionStart
## TLS configuration for ingress. These details are used for configuring Traefik with the proper certificates.
## @descriptionEnd
tls:
  ## @param tls.terminationEnabled Enable TLS for the Akuity Platform. TLS can be disabled to allow for SSL termination to be handled by a load balancer or other proxy/ingress before it reaches the Akuity Platform
  terminationEnabled: true
  secret:
    ## @param tls.secret.create Creates the 'akuity-platform-tls' Secret used as the Traefik default certificate. Set to false if creating the secret in another way (e.g. cert-manager)
    create: true
  ## @param tls.crt TLS certificate. Can be valid for multiple domains (e.g. https://akuity.example.com, https://\*.cd.akuity.example.com, https://\*.cdsvcs.akuity.example.com)
  crt: ""
  ## @param tls.key TLS private key
  key: ""

  ## @param tls.additionalCertificates List of additional TLS certificates to serve in the form of Kubernetes Secrets. This may be necessary if different certificates are used for different domains (e.g. https://akuity.example.com, https://\*.cd.akuity.example.com, https://\*.cdsvcs.akuity.example.com)
  additionalCertificates: []
  # - # Name of the Kubernetes Secret containing the TLS certificate. Required.
  #   secretName: akuity-platform-cdsvcs-tls
  #   # Create the secret. Set to false if creating the secret in another way (e.g. cert-manager)
  #   create: true
  #   # TLS certificate
  #   crt: ""
  #   # TLS private key
  #   key: ""

## @section Database Parameters
database:
  ## @param database.host Database hostname
  host: ""
  ## @param database.port Database port
  port: 5432
  ## @param database.user Database username
  user: ""
  ## @param database.password Database password
  password: ""
  ## @param database.dbname Database name
  dbname: postgres
  ## @param database.schemaname Schema name
  schemaname: public
  ## @param database.createSchema create schema automatically
  createSchema: false
  ## @param database.dataKey 256-bit base64 encoded encryption key used for envelope encryption of sensitive data columns. A random key can be generated with the following command: `openssl rand -base64 32`.  NOTE: loss of this key will result in permanent and irrevocable data loss!
  dataKey: ""
  ## @param database.readOnlyHost Database read-only hostname. Used for connection load balancing of read requests to read-only database replicas. If omitted, will default to the write hostname.
  readOnlyHost: ""
  ## @param database.sslmode Database SSL mode
  sslmode: require

  ## @skip database.postgres
  postgres:
    # Create a local postgres deployment. For test purposes only.
    enabled: false
    persistentVolume:
      enabled: false
    image:
      repository: postgres
      tag: 13.7

## @section SSO Parameters
## @descriptionStart
## Single Sign-On configuration. Either OIDC or auth0 must be configured.
## @descriptionEnd
sso:
  oidc:
    ## @param sso.oidc.enabled Enable OIDC authentication
    enabled: true
    ## @param sso.oidc.issuer OIDC issuer URL. This value is ignored if dex is enabled and served as a subpath
    issuer: ""
    ## @param sso.oidc.clientID OIDC client ID. If dex is enabled, value will be used as Dex's client ID
    clientID: ""
    ## @param sso.oidc.clientSecret OIDC client secret. If dex is enabled, value will be used as Dex's client secret
    clientSecret: ""
    ## @param sso.oidc.scopes OIDC scopes to request (default: openid,profile,email,offline_access)
    scopes: "openid,profile,email,offline_access"
    ## @param sso.oidc.logoutURL OIDC logout url
    logoutURL: ""
    ## @param sso.oidc.insecureSkipTLSVerify Skip TLS verification of the OIDC provider. This will be needed if dex is served as a subpath, and TLS is not yet configured.
    insecureSkipTLSVerify: false

  auth0:
    ## @param sso.auth0.enabled Enable Auth0 configuration
    enabled: false
    ## @param sso.auth0.domain Auth0 domain (e.g. example.us.auth0.com)
    domain: ""
    ## @param sso.auth0.audience Auth0 Audience of the token
    audience: ""
    ## @param sso.auth0.clientID Auth0 client id for portal service
    clientID:
    ## @param sso.auth0.cliClientID Auth0 client id for CLI
    cliClientID:

  dex:
    ## @param sso.dex.enabled Install dex
    enabled: false

    image:
      ## @param sso.dex.image.repository Image repository for Dex
      repository: ghcr.io/dexidp/dex
      ## @param sso.dex.image.tag Overrides the Dex image tag
      tag: "v2.35.3"
      secret:
        ## @param sso.dex.image.secret.create Creates the 'dex-pullsecrets' secret
        create: false
      ## @param sso.dex.image.username Username for the Dex container registry
      username: ""
      ## @param sso.dex.image.password Password for the Dex container registry
      password: ""

    ## @param sso.dex.tolerations Tolerations for the Dex pod
    tolerations: []

    serviceAccount:
      ## @param sso.dex.serviceAccount.annotations Annotations for the Dex service account
      annotations: {}

    secret:
      ## @param sso.dex.secret.create Creates the 'dex' Secret whose data values will be mounted as environment variables to the Dex Deployment
      create: true
      ## @param sso.dex.secret.data Secret data keys and plain-text values to set in the 'dex' Secret. These will be environment variables to dex so they can be referenced in the dex/config.yaml
      data:
        {}
        # MICROSOFT_CLIENT_SECRET: s1HRQuAueS7JTT5ZHsHSzYiFPm1l

    ## @param sso.dex.resources Resources limits and requests for the Dex containers
    resources:
      {}
      # requests:
      #   cpu: 5m
      #   memory: 32Mi

    ## @param sso.dex.issuerSubPath Serve dex as a subpath of the portal URL (e.g. https://akuity.example.com/dex)
    issuerSubPath: true

    ingress:
      ## @param sso.dex.ingress.enabled Enable ingress to dex
      enabled: false
      ## @param sso.dex.ingress.host Host value to dex ingress
      host: ""

    ## @param sso.dex.config Additional dex/config.yaml configuration. See https://dexidp.io/docs/ for dex documentation. Configuration can reference environment variables in the 'dex' Secret (e.g. $MICROSOFT_CLIENT_SECRET)
    config:
      {}
      # oauth2:
      #   skipApprovalScreen: true

      # # Azure AD Example:
      # connectors:
      # - type: microsoft
      #   id: microsoft
      #   name: Microsoft
      #   config:
      #     clientID: aaaaa111-bb22-cc33-dd44-eeeeee555555
      #     clientSecret: $MICROSOFT_CLIENT_SECRET
      #     redirectURI: https://akuity.example.com/dex/callback
      #     tenant: example.onmicrosoft.com
      #     groups:
      #       - devops
      #     emailToLowercase: true

      # # Static Password Example:
      # enablePasswordDB: true
      # staticPasswords:
      # - email: "<EMAIL>"
      #   # bcrypt hash of the string "password": $(echo password | htpasswd -BinC 10 admin | cut -d: -f2)
      #   hash: "$2a$10$2b2cU8CPhOTaGrs1HRQuAueS7JTT5ZHsHSzYiFPm1leZck7Mc8T4W"
      #   username: "admin"
      #   userID: "08a8684b-db88-4b73-90a9-3cd1661f5466"

  ## @param sso.roleFromGroups [nullable] Contains the SSO groups that will be automatically assigned roles
  roleFromGroups:
    ## @param sso.roleFromGroups.member [nullable] Comma separated list of SSO groups that will be assigned the 'member' role
    member: ""
    ## @param sso.roleFromGroups.admin [nullable] Comma separated list of SSO groups that will be assigned the 'admin' role
    admin: ""
    ## @param sso.roleFromGroups.owner [nullable] Comma separated list of SSO groups that will be assigned the 'owner' role
    owner: ""
  ## @param sso.roleTeamFromGroups [nullable] Contains the slice of SSO groups with org and team mapping
  roleTeamFromGroups:
    ## @param sso.roleTeamFromGroups.oidcGroup [nullable] SSO group name
    - oidcGroup: ""
      ## @param sso.roleTeamFromGroups.orgRole [nullable] org role(`member` or `owner`)
      orgRole: ""
      ## @param sso.roleTeamFromGroups.teams [nullable] slice of teams to be assigned
      teams:
        ## @param sso.roleTeamFromGroups.teams.name [nullable] name of the team
        - name: ""

## @section Traefik Parameters
## @descriptionStart
## Traefik is a required component of the Akuity Platform.
##
## The Akuity Platform expects a `traefik-external` ingress class to be present and is installed with Traefik in this section.
## @descriptionEnd
traefik:
  ## @param traefik.enabled Install Traefik
  enabled: true

  service:
    ## @param traefik.service.annotations Annotations for the Traefik service. CAUTION: This has the potential to override annotations set by `aws.enabled` or `compatibility.ipv6`!
    annotations: {}

  ## @param traefik.tolerations Tolerations for the Traefik pod
  tolerations: []

  serviceAccount:
    ## @param traefik.serviceAccount.annotations Annotations for the Traefik service account
    annotations: {}

  image:
    ## @param traefik.image.repository Image repository for Traefik
    repository: public.ecr.aws/docker/library/traefik
    ## @param traefik.image.tag Overrides the Traefik image tag
    ## https://gallery.ecr.aws/docker/library/traefik
    tag: "v3.3.6"
    secret:
      ## @param traefik.image.secret.create Creates the 'traefik-pullsecrets' secret
      create: false
    ## @param traefik.image.username Username for the Traefik container registry
    username: ""
    ## @param traefik.image.password Password for the Traefik container registry
    password: ""

  ## @param traefik.crd.enabled Install Traefik CRDs
  crd:
    enabled: true

  ## @param traefik.websecureRedirect Redirect 80 to 443. Only set this to false for testing purposes
  websecureRedirect: true

  ## @skip traefik.ingressClass
  ingressClass:
    enabled: true
    isDefaultClass: false

  ## @param traefik.forceLoadBalancer Forces the traefik service to be of type LoadBalancer. By default, if tls.terminationEnabled is true, the service will be of type LoadBalancer. If tls.terminationEnabled is false, the service will be of type ClusterIP. This allows you to override the default behavior and force the service to be of type LoadBalancer.
  forceLoadBalancer: false

  ## @param traefik.autoscaling.enabled Enables horizontal pod autoscaling for Traefik
  ## @param traefik.autoscaling.minReplicas Sets the minimum number of replicas
  ## @param traefik.autoscaling.maxReplicas Sets the maximum number of replicas
  ## @param traefik.autoscaling.targetCPUUtilizationPercentage Sets the target CPU utilization percentage
  ## @param traefik.autoscaling.targetMemoryUtilizationPercentage Sets the target memory utilization percentage
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 20
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80

  ## @param traefik.replicas If autoscaling is not enabled, the number of replicas for the Traefik deployment
  replicas: 1

  ## @param traefik.resources Resources limits and requests for the Traefik containers
  resources:
    {}
    # requests:
    #   cpu: "1"
    #   memory: "1Gi"
    # limits:
    #   cpu: "2"
    #   memory: "2Gi"

  ## @param traefik.topologySpreadConstraints [nullable] Sets topology spread constraints for the Traefik deployment
  ## @skip traefik.topologySpreadConstraints[0]
  ## @skip traefik.topologySpreadConstraints[1]
  # Defaults to preferring scheduling replicas across zones and nodes when possible
  topologySpreadConstraints:
    - labelSelector:
        matchLabels:
          app.kubernetes.io/instance: traefik-traefik-external
          app.kubernetes.io/name: traefik
      maxSkew: 1
      topologyKey: topology.kubernetes.io/zone
      whenUnsatisfiable: ScheduleAnyway
    - labelSelector:
        matchLabels:
          app.kubernetes.io/instance: traefik-traefik-external
          app.kubernetes.io/name: traefik
      maxSkew: 1
      topologyKey: kubernetes.io/hostname
      whenUnsatisfiable: ScheduleAnyway

## @section Other Parameters
agent:
  ## @param agent.insecureSkipTLSVerify Skip TLS verification from agents to Akuity Platform
  insecureSkipTLSVerify: false

aws:
  ## @param aws.enabled Add AWS specific annotations to resources
  enabled: true

## @param kargoInstanceValues Kargo instance parameters
kargoInstanceValues:

## @param instanceValues Argo CD instance parameters
instanceValues:
## @param instanceValues.k3s [nullable] k3s parameters
#  k3s:
#    resources:
#      limits:
#        memory: 3Gi
#      requests:
#        cpu: 2000m
#        memory: 3Gi
#    autoscaling:
#      max_replicas: 8
## @param instanceValues.kustomization [nullable] Kustomizations to be applied to Argo CD instances
#  kustomization:
#    apiVersion: kustomize.config.k8s.io/v1beta1
#    kind: Kustomization
#    patches:
#    - target:
#        group: apps
#        kind: Deployment
#      patch: |-
#        apiVersion: apps/v1
#        kind: Deployment
#        metadata:
#          name: REPLACEME
#        spec:
#          template:
#            spec:
#              nodeSelector:
#                kubernetes.io/arch: amd64

## @param instanceValues.k3s_proxy [nullable] k3s proxy parameters
#  k3s_proxy:
#    resources:
#      requests:
#        cpu: 500m
#        memory: 1Gi

## @param instanceValues.pgpool [nullable] pgpool parameters
#  pgpool:
#    resources:
#      requests:
#        cpu: 1000m
#        memory: 1Gi

smtp:
  ## @param smtp.host SMTP host
  # For AWS see: https://docs.aws.amazon.com/general/latest/gr/ses.html
  host: ""
  ## @param smtp.port SMTP port
  port: 587
  ## @param smtp.user SMTP username
  user: ""
  ## @param smtp.password SMTP password
  password: ""

liquibase:
  image:
    ## @param liquibase.image.repository Image repository for Liquibase
    repository: quay.io/akuity/liquibase
    ## @param liquibase.image.tag Overrides the Liquibase image tag
    ## https://quay.io/repository/akuity/liquibase?tab=tags
    tag: "4.32"
    secret:
      ## @param liquibase.image.secret.create Creates the 'liquibase-pullsecrets' secret
      create: false
    ## @param liquibase.image.username Username for the Liquibase container registry
    username: ""
    ## @param liquibase.image.password Password for the Liquibase container registry
    password: ""

## @section Instance Upgrader Parameters
## @descriptionStart
## Instance Upgrader is a job that upgrades managed instances during Akuity Platform upgrade.
## @descriptionEnd
instanceUpgrader:
  ## @param instanceUpgrader.enabled Enables the instance upgrader job
  enabled: true

## @section Compatibility
## @descriptionStart
## Specify which compatibility modes will be required
## @descriptionEnd
compatibility:
  ## @param compatibility.openshift Enables Openshift compatibility. This option will modify platform install manifests, Argo CD instance manifests, and Kargo instance manifests to support running on Openshift.
  openshift: false
  ## @param compatibility.sidecarContainers enables use of the Kubernetes sidecar containers feature in the platform workloads
  sidecarContainers: true
  ## @param compatibility.ipv6Only Enables IPv6 only compatibility
  ipv6Only: false

## @section Log Cleanup Parameters
## @descriptionStart
## Log Cleaner is a  cron job that cleans up old logs stored from the database. These logs can be from different tasks like kargo analysis jobs etc.
## @descriptionEnd
logCleaner:
  ## @param logCleaner.enabled Enables the log cleaner job
  enabled: false
  ## @param logCleaner.dryRun Enables dry run mode for the log cleaner job, when enabled the entries aren't deleted
  dryRun: false
  ## @param logCleaner.imagePullPolicy image pull policy for the log cleaner
  imagePullPolicy: Always
  ## @param logCleaner.schedule Cron schedule for the log cleaner job
  schedule: "0 0 * * *"
  ## @param logCleaner.resources Resources limits and requests for the log cleaner job
  resources: {}

## @section KubeVision Parameters
## @descriptionStart
## KubeVision is a cron job that cleans up old events
## @descriptionEnd
kubeVision:
  ## @param kubeVision.imagePullPolicy image pull policy for kubeVision
  imagePullPolicy: Always
