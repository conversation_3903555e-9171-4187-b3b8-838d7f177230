{{- if .Values.addonController.enabled }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: addon-controller
  namespace: '{{ .Release.Namespace }}'
{{- with .Values.addonController.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
{{- end }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: addon-controller
  namespace: '{{ .Release.Namespace }}'
rules:
  - apiGroups:
      - ''
    resources:
      - secrets
    verbs:
      - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: addon-controller
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: addon-controller
subjects:
  - kind: ServiceAccount
    name: addon-controller
    namespace: {{ .Release.Namespace }}
{{- end }}
