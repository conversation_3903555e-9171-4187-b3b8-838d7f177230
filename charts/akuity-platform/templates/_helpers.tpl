{{/*
Akuity Platform Image
*/}}
{{- define "akuity-platform.image" -}}
{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.Version }}
{{- end }}

{{/*
Image repository for agent server (derived from akuity platform)
*/}}
{{- define "agentServerImageHost" -}}
{{- if .Values.image.agentServer.host -}}
{{ .Values.image.agentServer.host }}
{{- else -}}
{{ regexReplaceAll "(/[-a-zA-Z0-9]+)$" .Values.image.repository "" }}
{{- end -}}
{{- end }}

{{/*
Ingress hostname derived from Portal URL (stripping scheme and port)
*/}}
{{- define "ingressHostname" }}
{{- $host := regexReplaceAll "^http(s)?://" .Values.portal.url "" }}
{{- regexReplaceAll ":.*$" $host "" }}
{{- end }}

{{/*
OIDC Issuer URL
*/}}
{{- define "oidcIssuer" }}
{{- if and .Values.sso.dex.enabled .Values.sso.dex.issuerSubPath -}}
{{- .Values.portal.url }}/dex
{{- else -}}
{{- .Values.sso.oidc.issuer -}}
{{- end -}}
{{- end }}

{{/*
Database connection string
*/}}
{{- define "dbPortalConnectionString" }}
{{- printf "host=%s port=%d sslmode=%s dbname=%s user=%s password=%s search_path=%s" .host (.port | int) .sslmode .dbname .user .password .schemaname | b64enc | quote }}
{{- end }}

{{/*
Database connection string (read-only)
*/}}
{{- define "dbPortalConnectionStringReadOnly" }}
{{- printf "host=%s port=%d sslmode=%s dbname=%s user=%s password=%s search_path=%s" (.readOnlyHost | default .host) (.port | int) .sslmode .dbname .user .password .schemaname | b64enc | quote }}
{{- end }}

{{/*
Database connection string
*/}}
{{- define "dbK3SConnectionString" }}
{{- printf "host=%s port=%d sslmode=%s dbname=%s user=%s password=%s" .host (.port | int) .sslmode .dbname .user .password | b64enc | quote }}
{{- end }}

{{/*
Database connection string (read-only)
*/}}
{{- define "dbK3SConnectionStringReadOnly" }}
{{- printf "host=%s port=%d sslmode=%s dbname=%s user=%s password=%s" (.readOnlyHost | default .host) (.port | int) .sslmode .dbname .user .password | b64enc | quote }}
{{- end }}

{{/*
Value to be used for .dockerconfigjson in an imagePullSecret
*/}}
{{- define "imagePullSecret" }}
{{- with .image }}
{{- $registry := (split "/" .repository)._0 }}
{{- printf "{\"auths\":{\"%s\":{\"username\":\"%s\",\"password\":\"%s\",\"auth\":\"%s\"}}}" $registry .username .password (printf "%s:%s" .username .password | b64enc) | b64enc }}
{{- end }}
{{- end }}

{{/*
Create Traefik service annotations by merging AWS-specific annotations with user-defined ones
*/}}
{{- define "traefik.serviceAnnotations" -}}
{{- $annotations := dict -}}
{{- if .Values.aws.enabled -}}
{{- $awsAnnotations := dict -}}
{{- $_ := set $awsAnnotations "service.beta.kubernetes.io/aws-load-balancer-backend-protocol" "tcp" -}}
{{- $_ := set $awsAnnotations "service.beta.kubernetes.io/aws-load-balancer-nlb-target-type" "ip" -}}
{{- $_ := set $awsAnnotations "service.beta.kubernetes.io/aws-load-balancer-scheme" "internet-facing" -}}
{{- $_ := set $awsAnnotations "service.beta.kubernetes.io/aws-load-balancer-target-group-attributes" "preserve_client_ip.enabled=true" -}}
{{- $_ := set $awsAnnotations "service.beta.kubernetes.io/aws-load-balancer-type" "external" -}}
{{- if .Values.compatibility.ipv6Only -}}
{{- $_ := set $awsAnnotations "service.beta.kubernetes.io/aws-load-balancer-ip-address-type" "dualstack" -}}
{{- end -}}
{{- $annotations = merge $annotations $awsAnnotations -}}
{{- end -}}
{{- if .Values.traefik.service.annotations -}}
{{- $annotations = merge .Values.traefik.service.annotations $annotations -}}
{{- end -}}
{{- if $annotations -}}
{{- toYaml $annotations -}}
{{- end -}}
{{- end -}}
