apiVersion: batch/v1
kind: CronJob
metadata:
  name: blacklisted-token-cleanup
  namespace: '{{ .Release.Namespace }}'
spec:
  # Execute the job every Monday at 03:00
  schedule: "0 3 * * 1"
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      backoffLimit: 1
      # 3 minutes for job logs to stay around
      ttlSecondsAfterFinished: 180
      template:
        metadata:
          labels:
            app.kubernetes.io/name: blacklisted-tokens
        spec:
          serviceAccountName: blacklisted-tokens
          activeDeadlineSeconds: 300
          imagePullSecrets:
          - name: akuity-pullsecrets
          containers:
          - name: delete-expired-tokens
            image: {{ include "akuity-platform.image" . }}
            command: [akputil, tokens, delete-expired-tokens]
          restartPolicy: Never