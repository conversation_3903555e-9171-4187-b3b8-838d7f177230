apiVersion: v1
kind: ServiceAccount
metadata:
  name: kubevision
  namespace: '{{ .Release.Namespace }}'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: kubevision
  namespace: '{{ .Release.Namespace }}'
rules:
- apiGroups:
  - ''
  resources:
  - secrets
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: kubevision
  namespace: '{{ .Release.Namespace }}'
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: kubevision
subjects:
  - kind: ServiceAccount
    name: kubevision
