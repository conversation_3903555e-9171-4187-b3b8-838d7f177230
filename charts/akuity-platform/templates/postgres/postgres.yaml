{{- if .Values.database.postgres.enabled }}
apiVersion: v1
kind: Secret
metadata:
  name: postgres
  namespace: '{{ .Release.Namespace }}'
data:
  POSTGRES_USER: {{ .Values.database.user | b64enc | quote }}
  POSTGRES_PASSWORD: {{ .Values.database.password | b64enc | quote }}

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres
  namespace: '{{ .Release.Namespace }}'
data:
  POSTGRES_DB: "{{ .Values.database.dbname }}"
  PGPORT: "{{ .Values.database.port }}"
  PGSSLMODE: "{{ .Values.database.sslmode }}"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: '{{ .Release.Namespace }}'
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: postgres
  template:
    metadata:
      labels:
        app.kubernetes.io/name: postgres
    spec:
      containers:
      - name: postgres
        image: "{{ .Values.database.postgres.image.repository }}:{{ .Values.database.postgres.image.tag }}"
        envFrom:
        - secretRef:
            name: postgres
        - configMapRef:
            name: postgres
        ports:
        - containerPort: {{ .Values.database.port }}
          name: postgres
{{- if .Values.database.postgres.persistentVolume.enabled }}
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc
{{- end }}
{{- if .Values.database.postgres.persistentVolume.enabled }}
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: '{{ .Release.Namespace }}'
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
{{- end }}
---
apiVersion: v1
kind: Service
metadata:
  name: postgres
  namespace: '{{ .Release.Namespace }}'
spec:
  ports:
  - port: {{ .Values.database.port }}
  selector:
    app.kubernetes.io/name: postgres
{{- end }}
