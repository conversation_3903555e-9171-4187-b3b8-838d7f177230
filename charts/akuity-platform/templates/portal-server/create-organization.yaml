{{- if and .Values.portal.seed .Values.portal.seed.organization }}
apiVersion: batch/v1
kind: Job
metadata:
  name: create-organization
  namespace: '{{ .Release.Namespace }}'
  annotations:
    helm.sh/hook: post-upgrade
    helm.sh/hook-delete-policy: before-hook-creation
spec:
  ttlSecondsAfterFinished: 180
  template:
    spec:
      imagePullSecrets:
        - name: akuity-pullsecrets
      containers:
        - name: akputil
          image: {{ include "akuity-platform.image" . }}
          command:
            - akputil
            - organization
            - create
            - "{{ .Values.portal.seed.organization.name }}"
          {{- if .Values.portal.seed.organization.owner }}
            - "--owner"
            - "{{ .Values.portal.seed.organization.owner }}"
          {{- end }}
          env:
            - name: PORTAL_DB_CONNECTION
              valueFrom:
                secretKeyRef:
                  name: akuity-platform
                  key: PORTAL_DB_CONNECTION
      restartPolicy: Never
  backoffLimit: 4
{{- end }}
