apiVersion: apps/v1
kind: Deployment
metadata:
  name: portal-server
  namespace: '{{ .Release.Namespace }}'
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: portal-server
  template:
    metadata:
      labels:
        app.kubernetes.io/name: portal-server
      annotations:
        checksum/akuity-platform-secret: {{ include (print $.Template.BasePath "/akuity-platform-secret.yaml") . | sha256sum }}
        checksum/portal-server: {{ include (print $.Template.BasePath "/portal-server/portal-server-cm.yaml") . | sha256sum }}
    spec:
      serviceAccountName: portal-server
      automountServiceAccountToken: true
{{- if .Values.portal.topologySpreadConstraints }}
      topologySpreadConstraints:
{{- toYaml .Values.portal.topologySpreadConstraints | nindent 8}}
{{- end }}
      imagePullSecrets:
      - name: akuity-pullsecrets
{{- with .Values.portal.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
{{- end }}
      containers:
        - name: portal-server
          args:
{{- if .Values.agent.insecureSkipTLSVerify }}
          - --insecure
{{- end }}
          command: [akuity-platform, portal-server]
          image: {{ include "akuity-platform.image" . }}
          imagePullPolicy: "{{ .Values.portal.imagePullPolicy }}"
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            seccompProfile:
              type: RuntimeDefault
          volumeMounts:
            - mountPath: /tmp
              name: tmp
            - name: argocd-versions-volume
              mountPath: /etc/argocd-versions
              readOnly: true
          ports:
            - containerPort: 9501
              name: metrics
            - containerPort: 9090
              name: http
          env:
            - name: TLS_TERMINATION_ENABLED
              value: {{ .Values.tls.terminationEnabled | quote }}
          envFrom:
            - configMapRef:
                name: portal-server
            - secretRef:
                name: akuity-platform
{{- with .Values.portal.resources }}
          resources:
{{- toYaml . | nindent 12 }}
{{- end }}
          # We should avoid using livenessProbe to prevent cascading failures.
          # See https://srcco.de/posts/kubernetes-liveness-probes-are-dangerous.html for details.
          readinessProbe:
            httpGet:
              path: /api/healthz
              port: http
            initialDelaySeconds: 5
            periodSeconds: 3
            successThreshold: 1
            failureThreshold: 3
      # terminationGracePeriodSeconds should be greater than the `SHUTDOWN_GRACE_PERIOD`
      terminationGracePeriodSeconds: 30
      volumes:
        - name: argocd-versions-volume
          configMap:
            name: argocd-versions
        - name: tmp
          emptyDir: {}
