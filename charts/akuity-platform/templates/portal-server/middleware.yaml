{{- if .Values.traefik.enabled }}
apiVersion: traefik.io/v1alpha1
kind: Middleware
metadata:
  name: compress
  namespace: '{{ .Release.Namespace }}'
spec:
  compress:
    excludedContentTypes:
      - text/event-stream
    encodings:
      - gzip
      - zstd
      - br

{{- if .Values.tls.terminationEnabled }}
---
apiVersion: traefik.io/v1alpha1
kind: Middleware
metadata:
  name: redirect-to-https
  namespace: akuity-platform
spec:
  redirectScheme:
    scheme: https
    permanent: true
{{- end }}
{{- end -}}
