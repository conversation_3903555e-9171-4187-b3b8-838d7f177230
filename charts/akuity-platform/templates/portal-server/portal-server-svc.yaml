apiVersion: v1
kind: Service
metadata:
  name: portal-server
  namespace: '{{ .Release.Namespace }}'
  labels:
    app.kubernetes.io/name: portal-server
  annotations:
    traefik.ingress.kubernetes.io/service.sticky.cookie: "true"
    traefik.ingress.kubernetes.io/service.sticky.cookie.secure: "true"
    traefik.ingress.kubernetes.io/service.sticky.cookie.httponly: "true"
    traefik.ingress.kubernetes.io/service.sticky.cookie.samesite: "strict"
spec:
  ports:
    - name: metrics
      protocol: TCP
      port: 9501
      targetPort: 9501
    - name: http
      port: 9090
      targetPort: http
    - name: extensions
      port: 9092
      protocol: TCP
      targetPort: 9092
  selector:
    app.kubernetes.io/name: portal-server
