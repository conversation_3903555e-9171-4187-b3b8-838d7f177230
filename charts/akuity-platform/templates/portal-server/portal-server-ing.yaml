apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: portal-server
  namespace: '{{ .Release.Namespace }}'
{{- if .Values.traefik.enabled }}
  annotations:
    traefik.ingress.kubernetes.io/router.middlewares: traefik-external-headers-secure@kubernetescrd
{{- end }}
spec:
  ingressClassName: traefik-external
  rules:
    - host: "{{ if .Values.platformController.shard }}{{ .Values.platformController.shard }}.{{ template "ingressHostname" . }}{{ else }}{{ template "ingressHostname" . }}{{ end }}"
      http:
        paths:
{{- if and .Values.sso.oidc.enabled .Values.sso.dex.enabled .Values.sso.dex.issuerSubPath }}
        - path: /dex
          pathType: Prefix
          backend:
            service:
              name: dex
              port:
                number: 80
{{- end }}
        - path: /
          pathType: Prefix
          backend:
            service:
              name: portal-server
              port:
                number: 9090
