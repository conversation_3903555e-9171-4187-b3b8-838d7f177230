{{- if .Values.logCleaner.enabled }}
apiVersion: batch/v1
kind: CronJob
metadata:
  name: log-cleaner
  namespace: '{{ .Release.Namespace }}'
spec:
  schedule: "{{ .Values.logCleaner.schedule }}"
  concurrencyPolicy: Forbid  # Prevents overlapping job runs
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      backoffLimit: 1
      template:
        spec:
          restartPolicy: OnFailure
          containers:
            - name: cleaner
              command: [ akuity-platform, logs-cleaner ]
              args:
                {{- if .Values.logCleaner.dryRun }}
                - --dry-run
                {{- end }}
              image: {{ include "akuity-platform.image" . }}
              imagePullPolicy: "{{ .Values.logCleaner.imagePullPolicy }}"
              envFrom:
                - secretRef:
                    name: akuity-platform
                - configMapRef:
                    name: portal-server
              {{- with .Values.logCleaner.resources }}
              resources:
              {{- toYaml . | nindent 12 }}
              {{- end }}
          imagePullSecrets:
            - name: akuity-pullsecrets
{{- end }}