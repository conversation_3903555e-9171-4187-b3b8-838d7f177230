{{- if and .Values.traefik.enabled .Values.traefik.autoscaling.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: traefik
  namespace: traefik-external
spec:
  minReplicas: {{ .Values.traefik.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.traefik.autoscaling.maxReplicas }}
  metrics:
  - resource:
      name: memory
      target:
        averageUtilization: {{ .Values.traefik.autoscaling.targetMemoryUtilizationPercentage }}
        type: Utilization
    type: Resource
  - resource:
      name: cpu
      target:
        averageUtilization: {{ .Values.traefik.autoscaling.targetCPUUtilizationPercentage }}
        type: Utilization
    type: Resource
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: traefik
{{- end }}
