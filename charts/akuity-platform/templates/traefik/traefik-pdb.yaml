{{- if .Values.traefik.enabled }}
{{- if or (and .Values.traefik.autoscaling.enabled (gt (.Values.portal.autoscaling.maxReplicas | int64) 1)) (and (not .Values.traefik.autoscaling.enabled) (gt (.Values.traefik.replicas | int64) 1))  }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: traefik
  namespace: traefik-external
spec:
  minAvailable: 50%
  selector:
    matchLabels:
      app.kubernetes.io/instance: traefik-traefik-external
      app.kubernetes.io/name: traefik
{{- end }}
{{- end }}
