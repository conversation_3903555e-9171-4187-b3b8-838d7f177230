{{- if and .Values.traefik.enabled .Values.tls.terminationEnabled }}
apiVersion: traefik.io/v1alpha1
kind: TLSStore
metadata:
  name: default
  namespace: traefik-external
spec:
  defaultCertificate:
    secretName: akuity-platform-tls
{{- if .Values.tls.additionalCertificates }}
  certificates:
{{- range .Values.tls.additionalCertificates }}
  - secretName: {{ .secretName }}
{{- end }}
{{- end }}

{{- if .Values.tls.secret.create }}
---
apiVersion: v1
kind: Secret
metadata:
  name: akuity-platform-tls
  namespace: traefik-external
type: kubernetes.io/tls
data:
  tls.crt: {{ .Values.tls.crt | b64enc | quote }}
  tls.key: {{ .Values.tls.key | b64enc | quote }}
{{- end }}

{{- range .Values.tls.additionalCertificates }}
{{- if ne .create false }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ .secretName }}
  namespace: traefik-external
type: kubernetes.io/tls
data:
  tls.crt: {{ .crt | b64enc | quote }}
  tls.key: {{ .key | b64enc | quote }}
{{- end }}
{{- end }}
{{- end }}
