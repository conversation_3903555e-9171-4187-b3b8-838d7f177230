apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-cluster-autoscaler
  namespace: '{{ .Release.Namespace }}'
data:

  # The application-controller is scaled relative to number of managed
  # resource objects in the cluster.
  application-controller.resources.minimum.cpu: "250m"
  application-controller.resources.minimum.memory: "512Mi"
  application-controller.resources.maximum.cpu: "6.0"
  application-controller.resources.maximum.memory: "16Gi"
  application-controller.resources.multiplier.cpu: "0.04"
  application-controller.resources.multiplier.memory: "0.1"
  repo-server.resources.minimum.cpu: "250m"
  repo-server.resources.minimum.memory: "256Mi"
  repo-server.resources.maximum.cpu: "4.0"
  repo-server.resources.maximum.memory: "6Gi"
  repo-server.resources.multiplier.cpu: "1.5"
  repo-server.resources.multiplier.memory: "1.5"
  repo-server.replicas.inc-threshold: "10"
  repo-server.resources.inc-threshold: "80"
  repo-server.resources.dec-threshold: "30"
  repo-server.replicas.minimum: "1"
  repo-server.replicas.maximum: "10"
