apiVersion: v1
kind: ServiceAccount
metadata:
  name: platform-controller
  namespace: '{{ .Release.Namespace }}'
{{- with .Values.platformController.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
{{ end }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: platform-controller
rules:
- apiGroups:
  - ''
  - apps
  - networking.k8s.io
  - rbac.authorization.k8s.io
  - traefik.containo.us
  - traefik.io
  - autoscaling
  - policy
  - cert-manager.io
  - batch
  resources:
  - namespaces
  - deployments
  - statefulsets
  - serviceaccounts
  - roles
  - rolebindings
  - services
  - ingresses
  - configmaps
  - secrets
  - ingressroutetcps
  - networkpolicies
  - middlewaretcps
  - middlewares
  - horizontalpodautoscalers
  - poddisruptionbudgets
  - ingressroutes
  - traefikservices
  - certificates
  - cronjobs
  - jobs
  verbs:
  - create
  - get
  - update
  - patch
  - delete
  - deletecollection
  - list
  - watch
# get endpoints is needed because we create argocd-redis-ha-haproxy which needs this privilege
- apiGroups:
  - ""
  resources:
  - endpoints
  verbs:
  - get
# list/create pods/exec is used to `cat` the kubeconifg from k3s and place it in host cluster
- apiGroups:
  - ''
  resources:
  - pods
  - pods/exec
  verbs:
  - create
  - list
  - delete # needed for k3s-serving secret reset

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: platform-controller
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: platform-controller
subjects:
  - kind: ServiceAccount
    name: platform-controller
    namespace: {{ .Release.Namespace }}
