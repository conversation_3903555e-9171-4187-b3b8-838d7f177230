{{- if .Values.instanceUpgrader.enabled }}
apiVersion: batch/v1
kind: Job
metadata:
  name: instances-upgrader
  namespace: '{{ .Release.Namespace }}'
  annotations:
    helm.sh/hook: post-upgrade
    helm.sh/hook-delete-policy: before-hook-creation
spec:
  ttlSecondsAfterFinished: 180
  backoffLimit: 1
  template:
    spec:
      imagePullSecrets:
      - name: akuity-pullsecrets
{{- if .Values.liquibase.image.secret.create }}
      - name: liquibase-pullsecrets
{{- end }}
      containers:
      - name: upgrader
        image: {{ include "akuity-platform.image" . }}
        imagePullPolicy: {{ .Values.platformController.imagePullPolicy | quote }}
        command: [sh, -c]
        args:
          - akputil instance gradual-upgrade --stabilization-period 1 --percentage=100 --skip-confirmation --force --component k3s --component agent-server
      restartPolicy: Never
      serviceAccountName: instances-upgrader
{{- end }}
