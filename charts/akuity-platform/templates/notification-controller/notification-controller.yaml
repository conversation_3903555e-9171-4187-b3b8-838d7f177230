{{- if .Values.notificationController.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: notification-controller
  namespace: '{{ .Release.Namespace }}'
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: notification-controller
  template:
    metadata:
      labels:
        app.kubernetes.io/name: notification-controller
      annotations:
        checksum/akuity-platform-secret: {{ include (print $.Template.BasePath "/akuity-platform-secret.yaml") . | sha256sum }}
        checksum/portal-server-cm: {{ include (print $.Template.BasePath "/portal-server/portal-server-cm.yaml") . | sha256sum }}
        checksum/notification-controller-cm: {{ include (print $.Template.BasePath "/notification-controller/notification-controller-cm.yaml") . | sha256sum }}
    spec:
      serviceAccountName: notification-controller
{{- with .Values.notificationController.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
{{- end }}
      containers:
        - name: notification-controller
          command: [akuity-platform, notification-controller]
          args:
            # Matches the DB connections pool size - DB_CON_MAX_OPEN
            - --num-workers=20
          image: {{ include "akuity-platform.image" . }}
          imagePullPolicy: "{{ .Values.notificationController.imagePullPolicy }}"
          envFrom:
            - secretRef:
                name: akuity-platform
            - configMapRef:
                name: portal-server
            - configMapRef:
                name: notification-controller
          {{- with .Values.notificationController.resources }}
          resources:
          {{- toYaml . | nindent 12 }}
          {{- end }}
      imagePullSecrets:
        - name: akuity-pullsecrets
{{- end }}
