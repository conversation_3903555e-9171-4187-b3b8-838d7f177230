apiVersion: v1
kind: ServiceAccount
metadata:
  name: records-aggregator
  namespace: '{{ .Release.Namespace }}'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: records-aggregator
  namespace: '{{ .Release.Namespace }}'
rules:
- apiGroups:
  - ''
  resources:
  - secrets
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: records-aggregator
  namespace: '{{ .Release.Namespace }}'
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: records-aggregator
subjects:
  - kind: ServiceAccount
    name: records-aggregator
