# Example values.yaml for local testing including traefik ingress, ephemeral database, and dex config
# Need /etc/host mappings for the following:
# 127.0.0.1 portal-server.akuity-platform
# 127.0.0.1 dex.akuity-platform
portal:
  url: http://portal-server.akuity-platform
  autoscaling:
    enabled: false

tls:
  crt: |-
    -----BEGIN CERTIFICATE-----
    MIIDmDCCAoCgAwIBAgIESWpXCjANBgkqhkiG9w0BAQsFADBbMScwJQYDVQQDDB5SZWdlcnkgU2Vs
    Zi1TaWduZWQgQ2VydGlmaWNhdGUxIzAhBgNVBAoMGlJlZ2VyeSwgaHR0cHM6Ly9yZWdlcnkuY29t
    MQswCQYDVQQGEwJVQTAgFw0yMjEwMTIwMDAwMDBaGA8yMTIyMTAxMjA5NDExNFowWjEmMCQGA1UE
    AwwdcG9ydGFsLXNlcnZlci5ha3VpdHktcGxhdGZvcm0xIzAhBgNVBAoMGlJlZ2VyeSwgaHR0cHM6
    Ly9yZWdlcnkuY29tMQswCQYDVQQGEwJVQTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEB
    AMecZPpejys2JXQd6yWiQSnHe/9WpAFxb7w4cp8vi9lzw38Sv6UKxDGknuwfYXQFoC2ImkMmW3vB
    6i0S4mkry4kWmAH3PpjWljUAprwGNLMswXR2szak73k24jzCHk8rV14vJPuIlsAU2QRB8PX0JqCw
    P8QumC7qsbks8hPGNXmY1wOqXs/xt1XDfZte2k9261o/InrbO0YnRE7uMmyQxZrcGjm4kd/iDHHl
    8G9Ilu6lHclt7cU/8knL1m1GUNB8xRPLctke8e/a7RH9oDbOBuE4cNue+a6ecripkHjVKKNhVpWV
    vUAS/yeeBNJGsPM/zxu8+cR6Q2Xr69XodznRWyECAwEAAaNjMGEwDwYDVR0TAQH/BAUwAwEB/zAO
    BgNVHQ8BAf8EBAMCAYYwHQYDVR0OBBYEFHduSA170Y6e9SW/A6ha8FTTpzGhMB8GA1UdIwQYMBaA
    FHduSA170Y6e9SW/A6ha8FTTpzGhMA0GCSqGSIb3DQEBCwUAA4IBAQAbU8CjQFVy6YgC3JRB/6gg
    LCfYcV4fykLJICNOH+r7wb/r7hxtE3mae0fTvs6JEHiA3SF9shZ42FP7iM0oKlpGa27mXnXAqY8J
    c1yKmbQlyde1F+m5mjdcuiPbJekEQZDc0Cn/CGhANbhAAHnU/Yj0tTkgbb+OoG20n6YuOCVvQL05
    dORec3LEha9g7jnRHBn38kdUynZb7qT5+RNkIIMlSH8gCGlBX7c4t+dMLXexR8NoMRdiD/+k/1LN
    O17wYNrHZFWrPAODxokmMlm+mP7X8RRtQ7NzGwnG99tbgFHMTk5PQNn2i4zV7+dR7RX7Ze5odhd6
    p8tTlze2CbRIYwv5
    -----END CERTIFICATE-----

  key: |-
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

agent:
  insecureSkipTLSVerify: true

database:
  host: postgres.akuity-platform
  dbname: postgres
  user: postgres
  password: postgres
  sslmode: disable
  dataKey: m8PTL8tiENXfaOPqop78ljrdaoloXn+w/HeTIWMUgO4=
  postgres:
    enabled: true

sso:
  oidc:
    enabled: true
    issuer: http://dex.akuity-platform
    clientID: "aaabbbcccddd"
    clientSecret: "abc123"
  dex:
    enabled: true
    issuerSubPath: false
    ingress:
      enabled: true
      host: dex.akuity-platform
    config:
      oauth2:
        skipApprovalScreen: true
      enablePasswordDB: true
      staticPasswords:
        - email: "<EMAIL>"
          # bcrypt hash of the string "password": $(echo password | htpasswd -BinC 10 admin | cut -d: -f2)
          hash: "$2a$10$2b2cU8CPhOTaGrs1HRQuAueS7JTT5ZHsHSzYiFPm1leZck7Mc8T4W"
          username: "admin"
          userID: "08a8684b-db88-4b73-90a9-3cd1661f5466"

traefik:
  autoscaling:
    enabled: false
  websecureRedirect: false
