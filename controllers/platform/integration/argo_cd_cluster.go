package integration

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"os"
	"reflect"
	"strconv"
	"time"

	"github.com/volatiletech/null/v8"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/utils/ptr"

	"github.com/akuityio/agent/manifests"
	agentclient "github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/agent/pkg/client/apis/clusteragent"
	"github.com/akuityio/agent/pkg/client/apis/clusterupgrader"
	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/agent/pkg/upbound"
	"github.com/akuityio/akuity-platform/controllers/platform/integration/clusterautoscaler"
	"github.com/akuityio/akuity-platform/controllers/shared/tenant"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	customErrors "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/internal/utils/kustomize"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/internal/version"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	featuresv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/features/v1"
)

type argocdClusterReconciler struct {
	stateClient    tenant.StateClient
	settings       ControllerSettings
	tenantsFactory TenantsFactory
	inCluster      bool
	autoscaler     clusterautoscaler.Autoscaler
	featSvc        features.Service
	customCert     string
	argocdVersions []agentclient.ComponentVersion
}

func NewArgoCDClusterReconciler(
	tenantsFactory TenantsFactory,
	settings ControllerSettings,
	stateClient tenant.StateClient,
	featureSvc features.Service,
	autoscaler clusterautoscaler.Autoscaler,
	customAgentCert string,
	argocdVersions []agentclient.ComponentVersion,
) *argocdClusterReconciler {
	return &argocdClusterReconciler{
		tenantsFactory: tenantsFactory,
		settings:       settings,
		inCluster:      !settings.EnableIngress,
		stateClient:    stateClient,
		autoscaler:     autoscaler,
		featSvc:        featureSvc,
		customCert:     customAgentCert,
		argocdVersions: argocdVersions,
	}
}

func (r *argocdClusterReconciler) ItemToID(item *models.ArgoCDCluster) string {
	return item.ID
}

func (r *argocdClusterReconciler) IDColumn() string {
	return fmt.Sprintf("%s as %s", models.ArgoCDClusterTableColumns.ID, models.ArgoCDClusterColumns.ID)
}

func (r *argocdClusterReconciler) LogValuesFromID(id string) []interface{} {
	return []interface{}{"cluster_id", id}
}

func (r *argocdClusterReconciler) LogValuesFromItem(item *models.ArgoCDCluster) []interface{} {
	return []interface{}{"instance_id", item.InstanceID, "cluster_name", item.Name}
}

func (r *argocdClusterReconciler) Reconcile(ctx context.Context, cluster *models.ArgoCDCluster) error {
	status, err := cluster.GetStatus()
	if err != nil {
		return fmt.Errorf("failed to get cluster status: %w", err)
	}
	oldStatus := status.DeepCopy()
	reconcileError := r.reconcile(ctx, cluster, &status)
	if !reflect.DeepEqual(status, oldStatus) {
		if err := cluster.SetStatus(status); err != nil {
			return fmt.Errorf("failed to set cluster status: %w", err)
		}
		if err := r.settings.RepoSet.ArgoCDClusters().Update(context.Background(), cluster, models.ArgoCDClusterStatusColumns...); err != nil {
			return fmt.Errorf("failed to persist cluster status: %w", err)
		}
	}
	return reconcileError
}

type clusterRelatedData struct {
	cluster            *models.ArgoCDCluster
	instance           *models.ArgoCDInstance
	instanceConfig     *models.ArgoCDInstanceConfig
	organization       *models.Organization
	autoscaleEnabled   bool
	unsupportedVersion bool
	orgFeatureStatuses *featuresv1.FeatureStatuses
}

func (r *argocdClusterReconciler) loadClusterRelatedData(ctx context.Context, cluster *models.ArgoCDCluster) (*clusterRelatedData, error) {
	instance, err := r.settings.RepoSet.ArgoCDInstances().GetByID(ctx, cluster.InstanceID)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch instance: %w", err)
	}
	instanceConfig, err := r.settings.RepoSet.ArgoCDInstanceConfigs().GetByID(ctx, cluster.InstanceID)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch instance config: %w", err)
	}
	organization, err := r.settings.RepoSet.Organizations().GetByID(ctx, instance.OrganizationOwner)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch organization: %w", err)
	}
	orgFeatureStatuses := r.featSvc.GetFeatureStatusesWithOrg(ctx, organization)
	autoscaleEnabled := orgFeatureStatuses.GetClusterAutoscaler().Enabled()
	return &clusterRelatedData{
		cluster:            cluster,
		instance:           instance,
		instanceConfig:     instanceConfig,
		organization:       organization,
		autoscaleEnabled:   autoscaleEnabled,
		unsupportedVersion: misc.IsComponentVersionSupported(instanceConfig.Version.String, r.argocdVersions) != nil,
		orgFeatureStatuses: orgFeatureStatuses,
	}, nil
}

func (r *argocdClusterReconciler) reconcile(ctx context.Context, cluster *models.ArgoCDCluster, status *models.ArgoCDClusterStatus) error {
	tnt, err := r.tenantsFactory.NewTenant(cluster.InstanceID)
	if err != nil {
		return fmt.Errorf("failed to create new Argo CD tenant: %w", err)
	}

	if !cluster.DeletionTimestamp.IsZero() {
		if err := r.deleteCluster(ctx, cluster, tnt); err != nil {
			return fmt.Errorf("failed to delete cluster: %w", err)
		}
		return nil
	}

	cData, err := r.loadClusterRelatedData(ctx, cluster)
	if err != nil {
		return err
	}

	// update agent version to latest if target is empty before doing anything else and force a reconciliation
	// forcing reconciliation immediately to prevent duplicate processing of same cluster twice
	spec, err := cluster.GetUnmodifiedSpec()
	if err != nil {
		return err
	}

	if spec.DirectClusterSpec != nil && !spec.DirectClusterSpec.Type.IsEmpty() {
		return r.handleDirectClusterReconciliation(ctx, tnt, cData, spec, status)
	}

	latestVersion := version.GetLatestAgentVersion()
	if spec.TargetVersion == "" && !cData.unsupportedVersion {
		spec.TargetVersion = latestVersion
		if err := cluster.SetSpec(*spec); err != nil {
			return err
		}
		return r.settings.RepoSet.ArgoCDClusters().Update(ctx, cluster, "spec")
	}

	// generation should not be updated in case of deletions or errors with tenant instance creation
	// also skip setting observed generation if agent target version is being updated (see above)
	defer func() {
		status.ObservedGeneration = null.IntFrom(cluster.Generation)
	}()

	if status.Conditions.IsEstablished(models.ClusterConditionTypeConfigured) && !status.Conditions.IsNotEstablishedAndFailedWithReason(models.ClusterConditionTypePruned, "NewConfigurationApplied") && !cData.unsupportedVersion {
		if cData.autoscaleEnabled {
			clusterAutoscaled, err := r.autoscaler.AutoscaleCluster(ctx, cluster, nil)
			if err != nil {
				return fmt.Errorf("autoscaler failed to run for ArgoCDCluster %q: %w", cluster.ID, err)
			}
			if clusterAutoscaled {
				// Next iteration will apply the change due to the generation bump as a result of the clusterConfig update
				return nil
			}
		}
	}

	if !status.Conditions.IsEstablishedOrFailedWithReason(models.ClusterConditionTypeConfigured, models.ClusterInvalidConfigReason) || status.Manifests.IsZero() || status.ObservedGeneration != null.IntFrom(cluster.Generation) {
		// DEV: ensure no form of manifest apply/generation happens when this
		// condition is encountered or else it will error during manifest gen
		// stop reconciliation if instance version is unsupported
		if !cData.unsupportedVersion {
			if err := r.configureCluster(ctx, cData, tnt); err != nil {
				var kustomizationErr *agentclient.KustomizationError
				if errors.As(err, &kustomizationErr) {
					status.Conditions.SetNotEstablished(models.ClusterConditionTypeConfigured, models.ClusterInvalidConfigReason, kustomizationErr.Message)
					return fmt.Errorf("cluster has invalid user provided configuration: %w", err)
				}
				status.Conditions.SetNotEstablished(models.ClusterConditionTypeConfigured, "FailedToApplyConfiguration", err.Error())
				return customErrors.NewRetryableError(err, "cluster configuration failed")
			} else {
				if status.AgentState != nil {
					status.AgentState.ObservedGenerationAppliedAt = null.TimeFrom(time.Now())
				}
				status.Conditions.SetNotEstablished(models.ClusterConditionTypePruned, "NewConfigurationApplied", "")
				status.Conditions.SetEstablished(models.ClusterConditionTypeConfigured)
			}
		}
	}

	clusterData, err := r.stateClient.GetClusterData(ctx, cluster)
	if err != nil {
		return err
	}
	if clusterData != nil {
		status.Manifests = null.StringFrom(clusterData.Manifest)
	}

	state, err := r.stateClient.GetAgentState(ctx, cluster)
	if err != nil {
		return err
	}
	var prevObservedGenerationAppliedAt null.Time
	if status.AgentState != nil {
		prevObservedGenerationAppliedAt = status.AgentState.ObservedGenerationAppliedAt
	}

	status.AgentState = state
	if status.AgentState != nil {
		status.AgentState.ObservedGenerationAppliedAt = prevObservedGenerationAppliedAt
	}

	if state != nil && state.Status != nil && int(state.Status.MinObservedGeneration) == cluster.Generation && status.Conditions.IsEstablished(models.ClusterConditionTypeConfigured) && status.Conditions.IsNotEstablishedAndFailedWithReason(models.ClusterConditionTypePruned, "NewConfigurationApplied") {
		status.AgentState.ObservedGenerationAppliedAt = null.TimeFromPtr(nil)
		// if cluster is configured properly and the agent generation matches the current generation then delete credentials and run prune
		currentRotation, err := r.pruneCluster(ctx, cData, tnt)
		if err != nil {
			return err
		}
		status.Conditions.SetEstablished(models.ClusterConditionTypePruned)
		status.ObservedRotationCount = null.IntFrom(int(currentRotation))
	}
	return nil
}

func (r *argocdClusterReconciler) handleDirectClusterReconciliation(ctx context.Context, tnt Tenant, c *clusterRelatedData, spec *models.ClusterSpec, status *models.ArgoCDClusterStatus) error {
	defer func() {
		status.ObservedGeneration = null.IntFrom(c.cluster.Generation)
		// explicitly set empty manifest for all intg. types as these don't have installation manifests
		status.Manifests = null.StringFrom("")
	}()

	if !c.unsupportedVersion && (!status.Conditions.IsEstablishedOrFailedWithReason(models.ClusterConditionTypeConfigured, models.ClusterInvalidConfigReason) || status.ObservedGeneration != null.IntFrom(c.cluster.Generation)) {
		if err := r.configureDirectCluster(ctx, tnt, c, spec); err != nil {
			status.Conditions.SetNotEstablished(models.ClusterConditionTypeConfigured, "FailedToApplyConfiguration", err.Error())
			return customErrors.NewRetryableError(err, "cluster configuration failed")
		} else {
			status.Conditions.SetNotEstablished(models.ClusterConditionTypePruned, "NewConfigurationApplied", "")
			status.Conditions.SetEstablished(models.ClusterConditionTypeConfigured)
			status.Conditions.SetEstablished(models.ClusterConditionTypePruned)
			status.ObservedRotationCount = null.IntFrom(int(spec.AgentRotationCount))
		}
	}

	if !c.unsupportedVersion && spec.TargetVersion != version.GetLatestAgentVersion() {
		// update target version to latest
		spec.TargetVersion = version.GetLatestAgentVersion()
		if err := c.cluster.SetSpec(*spec); err != nil {
			return err
		}
		// updated in db and short circuit to force reconciliation
		return r.settings.RepoSet.ArgoCDClusters().Update(ctx, c.cluster, "spec")
	}

	// static agent state for direct clusters, always healthy
	status.AgentState = &models.AgentState{
		Version:       "v" + spec.TargetVersion,
		ArgoCDVersion: c.instanceConfig.Version.String,
		ObservedAt:    null.TimeFrom(time.Now()),
		Status: &agentclient.AggregatedHealthResponse{
			PriorityStatus: common.TenantPhaseHealthy,
		},
		AgentIDs: []string{},
	}

	return nil
}

func (r *argocdClusterReconciler) configureDirectCluster(ctx context.Context, tnt Tenant, c *clusterRelatedData, spec *models.ClusterSpec) error {
	cfg := agentclient.DirectClusterConfig{
		Name:        c.cluster.Name,
		Server:      "",
		Shard:       999,
		Generation:  ptr.To(uint64(c.cluster.Generation)),
		Labels:      spec.Labels,
		Annotations: spec.Annotations,
		Config:      common.ArgocdClusterConfig{},
	}

	if spec.DirectClusterSpec.Type.IsUpbound() {
		cfg.Server = spec.DirectClusterSpec.Upbound.Server
		cfg.Config = common.ArgocdClusterConfig{
			ExecProviderConfig: upbound.NewExecProviderConfig(
				spec.DirectClusterSpec.Upbound.Organization,
				spec.DirectClusterSpec.Upbound.Token),
			TLSClientConfig: common.TLSClientConfig{
				Insecure: false,
				CAData:   spec.DirectClusterSpec.Upbound.CAData,
			},
		}
	} else {
		// kargo details
		kargoCfg, err := r.settings.RepoSet.KargoInstanceConfigs().GetByID(ctx, spec.DirectClusterSpec.KargoInstanceID, "private_spec")
		if err != nil {
			return err
		}
		kargoPrivSpec, err := kargoCfg.GetPrivateSpec()
		if err != nil {
			return err
		}
		kubeCfg, err := kargoPrivSpec.GetGitopsConfig()
		if err != nil {
			return err
		}
		cfg.Server = kubeCfg.Clusters[kubeCfg.CurrentContext].Server
		cfg.Config = common.ArgocdClusterConfig{
			BearerToken: kubeCfg.AuthInfos[kubeCfg.CurrentContext].Token,
			TLSClientConfig: common.TLSClientConfig{
				Insecure: kubeCfg.Clusters[kubeCfg.CurrentContext].InsecureSkipTLSVerify,
			},
		}
		kargo, err := r.settings.RepoSet.KargoInstances().GetByID(ctx, spec.DirectClusterSpec.KargoInstanceID, "shard")
		if err != nil {
			return err
		}
		if r.settings.Shard == kargo.Shard {
			cfg.ExternalNameSvc = &agentclient.ExternalNameSvc{
				FQDN: fmt.Sprintf("k3s-proxy.kargo-%s.svc.cluster.local", spec.DirectClusterSpec.KargoInstanceID),
			}
			cfg.Config.Insecure = true
			cfg.Server = fmt.Sprintf("https://cluster-%v:6445", cfg.Name)
		}
	}

	if err := tnt.ApplyDirectCluster(ctx, cfg); err != nil {
		return fmt.Errorf("failed to apply cluster manifests: %w", err)
	}

	return nil
}

// pruneCluster prunes old credentials and k8s resources
func (r *argocdClusterReconciler) pruneCluster(ctx context.Context, c *clusterRelatedData, tnt Tenant) (uint64, error) {
	clusterConfig, err := r.fillClusterApplyConfig(ctx, c)
	if err != nil {
		return 0, err
	}
	if clusterConfig.agentRotationCount > 0 {
		currentSuffix := GetAgentSuffix(clusterConfig.agentRotationCount)
		for i := int(clusterConfig.agentRotationCount) - 1; i >= 0; i-- {
			oldSuffix := GetAgentSuffix(uint64(i))
			found, errs := tnt.DeleteClusterCredentials(ctx, clusterConfig.clusterName, oldSuffix, currentSuffix, clusterConfig.privileged)
			for _, er := range errs {
				if er != nil && !k8sErrors.IsNotFound(er) {
					return clusterConfig.agentRotationCount, er
				}
			}
			// if old credential is not found assume all old credentials cleaned up
			if !found {
				break
			}
		}
	}
	return clusterConfig.agentRotationCount, tnt.PruneCluster(ctx, c.cluster.Name, uint64(c.cluster.Generation), false)
}

func (r *argocdClusterReconciler) configureCluster(ctx context.Context, clusterInfo *clusterRelatedData, tnt Tenant) error {
	clusterApplyConfig, err := r.fillClusterApplyConfig(ctx, clusterInfo)
	if err != nil {
		return fmt.Errorf("failed to fill cluster apply configuration: %w", err)
	}

	if err := r.applyCluster(ctx, clusterInfo, clusterApplyConfig, tnt); err != nil {
		return fmt.Errorf("failed to apply cluster manifests: %w", err)
	}
	return nil
}

// deleteCluster deletes the cluster connected to the tenant including all its k8s resources and entries in db
func (r *argocdClusterReconciler) deleteCluster(ctx context.Context, cluster *models.ArgoCDCluster, tnt Tenant) error {
	spec, err := cluster.GetSpec()
	if err != nil {
		return err
	}

	if spec.DirectClusterSpec != nil && !spec.DirectClusterSpec.Type.IsEmpty() {
		errs := tnt.DeleteDirectCluster(ctx, cluster.Name)
		for _, er := range errs {
			if !k8sErrors.IsNotFound(er) {
				return er
			}
		}
	} else {
		errs := tnt.DeleteCluster(ctx, cluster.Name, GetAgentSuffix(uint64(spec.AgentRotationCount)))
		for _, er := range errs {
			if !k8sErrors.IsNotFound(er) {
				return er
			}
		}

		if err := tnt.PruneCluster(ctx, cluster.Name, uint64(cluster.Generation), false); err != nil {
			return err
		}
	}

	if err := r.settings.RepoSet.ArgoCDClusters().Delete(ctx, cluster.ID); err != nil {
		// If the cluster was just not found, it means it was already deleted and we can safely
		// ignore the error.
		if !errors.Is(err, sql.ErrNoRows) {
			return fmt.Errorf("failed to delete cluster from db: %w", err)
		}
	}

	return nil
}

type clusterApplyConfig struct {
	instanceID                     string
	clusterName                    string
	namespace                      string
	namespaceScoped                bool
	port                           int32
	repoPort                       int32
	redisPort                      int32
	appsetPort                     int32
	reverseProxyPort               int32
	generation                     uint64
	agentPassword                  string
	upgraderImage                  string
	argocdVersion                  string
	kustomization                  kustomize.Kustomization
	autoUpgradeDisabled            bool
	imageUpdaterEnabled            bool
	imageUpdaterVersion            string
	applicationSetEnabled          bool
	appsetPolicy                   models.AppsetPolicy
	appsetProgressiveSyncsEnabled  bool
	delegatedRepoServer            bool
	currentDelegatedRepoServer     bool
	resources                      models.AgentResources
	labels                         map[string]string
	annotations                    map[string]string
	privileged                     bool
	appReplication                 bool
	agentVersion                   string
	agentRotationCount             uint64
	configManagementPlugins        models.ConfigManagementPlugins
	redisTunneling                 bool
	agentPermissionsRules          []models.AgentPermissionsRule
	dataDogAnnotationsEnabled      bool
	managedClusterConfig           *models.ManagedClusterConfig
	multiClusterK8sDashboard       bool
	kubeVisionConfig               models.KubeVisionConfig
	appInAnyNamespaceEnabled       bool
	project                        string
	ipv6Only                       bool
	inClusterNotificationSettings  bool
	appReconciliationsRateLimiting *models.AppReconciliationsRateLimiting
}

func (r *argocdClusterReconciler) fillClusterApplyConfig(ctx context.Context, c *clusterRelatedData) (clusterApplyConfig, error) {
	if c.instanceConfig.Version.IsZero() || c.instanceConfig.Version.String == "" {
		return clusterApplyConfig{}, fmt.Errorf("failed to fetch tenant argocd version, tenant=%v, cluster=%v", c.cluster.InstanceID, c.cluster.Name)
	}

	instanceSpec, err := c.instanceConfig.GetSpec()
	if err != nil {
		return clusterApplyConfig{}, fmt.Errorf("failed to fetch instance spec %w", err)
	}
	// kube-server and repo-server ports are in sequence eg. port-prefix 3200 -> port=32000 repoPort=32001
	port := instances.ProxyPortRangeStart + (c.cluster.SequenceID * instances.PortsPerCluster)
	repoPort := port + 1
	appsetPort := repoPort + 1
	reverseProxyPort := appsetPort + 1
	redisPort := reverseProxyPort + 1
	privateSpec, err := c.cluster.GetPrivateSpec()
	if err != nil {
		return clusterApplyConfig{}, fmt.Errorf("failed to get private spec: %w", err)
	}

	spec, err := c.cluster.GetSpec()
	if err != nil {
		return clusterApplyConfig{}, fmt.Errorf("failed to get cluster spec: %w", err)
	}

	internalSpec, err := c.cluster.GetInternalSpec()
	if err != nil {
		return clusterApplyConfig{}, fmt.Errorf("failed to get cluster internal spec: %w", err)
	}

	versionResources, err := models.GetAgentResources(spec.SizeVersion)
	if err != nil {
		return clusterApplyConfig{}, err
	}

	var resources models.AgentResources
	if c.orgFeatureStatuses.GetClusterAutoscaler().Enabled() && spec.Size == models.ClusterSizeAuto {
		if internalSpec.AutoAgentResources != nil {
			resources = *internalSpec.AutoAgentResources
		} else {
			// Catch the edge case where resources for `Auto` have not been set yet. This will be the case on new
			// clusters. Default to using medium sizing temporarily.
			var ok bool
			resources, ok = versionResources[models.ClusterSizeMedium]
			if !ok {
				// should never happen
				return clusterApplyConfig{}, fmt.Errorf("unknown cluster size: %s", models.ClusterSizeMedium)
			}
		}

		agentState, err := c.cluster.GetAgentState(true)
		if err != nil {
			return clusterApplyConfig{}, err
		}
		if agentState != nil && agentState.Resources != nil {
			repoServerResources, ok := agentState.Resources[common.ArgoCDRepoServer]
			// get actual resources from agent state
			// because repo server autoscaled in the agent clustre by syncer
			if ok {
				resources.RepoServerReplicas = int32(repoServerResources.Replicas)
				resources.RepoServerRequirements = repoServerResources.Requirements
			}
		}

		autoscalerConfig := clusterautoscaler.OverrideClusterAutoscalerConfig(r.autoscaler.GetConfig(), spec.AutoscalerConfig)
		resources.ControllerRequirements = clusterautoscaler.EnforceAppControllerMinMax(autoscalerConfig.ApplicationController, resources.ControllerRequirements)

		replicas, repoServerRequirements := clusterautoscaler.EnforceRepoServerMinMax(autoscalerConfig.RepoServer, resources.RepoServerReplicas, resources.RepoServerRequirements)
		resources.RepoServerRequirements = repoServerRequirements
		resources.RepoServerReplicas = replicas
	} else {
		size := spec.Size
		var ok bool
		resources, ok = versionResources[size]
		if !ok {
			return clusterApplyConfig{}, fmt.Errorf("unknown cluster size: %s", size)
		}
	}

	agentVersion := spec.TargetVersion
	if agentVersion == "" {
		agentVersion = version.GetLatestAgentVersion()
	}

	upgraderImage := version.GetAgentImage(fmt.Sprintf("%v:%v", models.DefaultAgentImage, agentVersion))
	kustomization := spec.Kustomization
	if kustomization == nil {
		kustomization = kustomize.Kustomization{}
	}
	if imageOverride := kustomization.FindImageByName(models.DefaultAgentImage); imageOverride != nil {
		upgraderImage = fmt.Sprintf("%v:%v", imageOverride.NewName, agentVersion)
	}

	isCurrentDelegatedRepoServer := false
	isDelegated := false
	appSetEnabled := false
	imageUpdaterEnabled := c.instanceConfig.ArgocdImageUpdaterEnable.Bool
	privileged := false
	switch {
	// repo server is in control plan and delegated for all clusters
	case instanceSpec.IsGitDelegateInControlPlane():
		isDelegated = true
	// repo server is in one of the clusters and delegated for all other clusters
	case instanceSpec.IsGitDelegateInManagedCluster():
		isDelegated = c.cluster.Name != instanceSpec.RepoServerDelegate.ManagedCluster.ClusterName
		// if isDelegated is true that means some other managed cluster delegated as repo server
		isCurrentDelegatedRepoServer = !isDelegated
	}

	if instanceSpec.IsAppSetInManagedCluster() {
		appSetFeatureEnabled := false
		if config.IsSelfHosted {
			appSetFeatureEnabled = true
		} else {
			appSetFeatureEnabled = c.orgFeatureStatuses.GetApplicationSetController().Enabled()
		}

		// enable appset only for this cluster if cluster matches spec and feature is enabled
		appSetEnabled = !instanceSpec.AppsetDisabled && c.cluster.Name == instanceSpec.AppSetDelegate.ManagedCluster.ClusterName && appSetFeatureEnabled
		privileged = privileged || appSetEnabled
	}

	// image updater is in only one of the clusters
	if instanceSpec.IsImageUpdaterInManagedCluster() {
		// enable image updater only for this cluster if cluster matches spec
		imageUpdaterEnabled = c.instanceConfig.ArgocdImageUpdaterEnable.Bool && c.cluster.Name == instanceSpec.ImageUpdaterDelegate.ManagedCluster.ClusterName
		privileged = privileged || imageUpdaterEnabled
	} else if instanceSpec.IsImageUpdaterInControlPlane() {
		imageUpdaterEnabled = false
	}

	configManagementPlugins, err := c.instanceConfig.GetArgoCDConfigManagementPlugins()
	if err != nil {
		return clusterApplyConfig{}, err
	}

	appReplication := spec.AppReplication && c.orgFeatureStatuses.GetArgocdAgentStateReplication().Enabled()

	if len(configManagementPlugins.Plugins) > 0 &&
		!c.orgFeatureStatuses.GetConfigManagementPlugins().Enabled() {
		configManagementPlugins.Plugins = []*argocdv1.ConfigManagementPlugin{}
	}

	config := clusterApplyConfig{
		agentRotationCount:             uint64(spec.AgentRotationCount),
		instanceID:                     c.cluster.InstanceID,
		clusterName:                    c.cluster.Name,
		namespace:                      c.cluster.Namespace,
		namespaceScoped:                c.cluster.NamespaceScoped,
		port:                           int32(port),
		repoPort:                       int32(repoPort),
		redisPort:                      int32(redisPort),
		appsetPort:                     int32(appsetPort),
		reverseProxyPort:               int32(reverseProxyPort),
		agentPassword:                  privateSpec.AgentPassword,
		agentVersion:                   agentVersion,
		argocdVersion:                  c.instanceConfig.Version.String,
		generation:                     uint64(c.cluster.Generation),
		upgraderImage:                  upgraderImage,
		autoUpgradeDisabled:            c.cluster.AutoUpgradeDisabled,
		imageUpdaterEnabled:            imageUpdaterEnabled,
		imageUpdaterVersion:            instanceSpec.ImageUpdaterVersion,
		delegatedRepoServer:            isDelegated,
		currentDelegatedRepoServer:     isCurrentDelegatedRepoServer,
		applicationSetEnabled:          appSetEnabled,
		appsetPolicy:                   instanceSpec.AppsetPolicy,
		appsetProgressiveSyncsEnabled:  instanceSpec.AppsetProgressiveSyncsEnabled,
		kustomization:                  kustomization,
		resources:                      resources,
		labels:                         spec.Labels,
		annotations:                    spec.Annotations,
		privileged:                     privileged,
		appReplication:                 appReplication,
		configManagementPlugins:        configManagementPlugins,
		redisTunneling:                 spec.RedisTunneling,
		agentPermissionsRules:          instanceSpec.AgentPermissionsRules,
		dataDogAnnotationsEnabled:      spec.DatadogAnnotationsEnabled,
		managedClusterConfig:           spec.ManagedClusterConfig,
		multiClusterK8sDashboard:       instanceSpec.MultiClusterK8sDashboardEnabled && spec.MultiClusterK8SDashboardEnabled,
		kubeVisionConfig:               instanceSpec.KubeVisionConfig,
		appInAnyNamespaceEnabled:       instanceSpec.AppInAnyNamespace != nil && instanceSpec.AppInAnyNamespace.Enabled,
		project:                        spec.Project,
		ipv6Only:                       spec.Compatibility.IPV6Only,
		inClusterNotificationSettings:  spec.ArgoCDNotifications.InClusterSettings,
		appReconciliationsRateLimiting: instanceSpec.AppReconciliationsRateLimiting,
	}
	return config, nil
}

func (r *argocdClusterReconciler) applyCluster(ctx context.Context, c *clusterRelatedData, config clusterApplyConfig, tnt Tenant) error {
	suffix := GetAgentSuffix(config.agentRotationCount)
	clusterConfig := agentclient.ClusterConfig{
		UsernameSuffix:      suffix,
		Namespace:           config.namespace,
		NamespaceScoped:     config.namespaceScoped,
		Name:                config.clusterName,
		AgentPasswd:         config.agentPassword,
		ClusterPort:         uint64(config.port),
		RepoServerPort:      uint64(config.repoPort),
		RedisPort:           uint64(config.redisPort),
		AppSetWebhookPort:   uint64(config.appsetPort),
		ReverseProxyPort:    uint64(config.reverseProxyPort),
		UpgraderImage:       config.upgraderImage,
		Generation:          &config.generation,
		Labels:              config.labels,
		Annotations:         config.annotations,
		AutoUpgradeDisabled: config.autoUpgradeDisabled,
		Privileged:          config.privileged,
		AppSetEnabled:       config.applicationSetEnabled,
		AppInAnyNamespace:   config.appInAnyNamespaceEnabled,
		Project:             config.project,
	}
	if config.redisTunneling {
		clusterConfig.AgentRemotes = []string{"argocd-redis-ha-haproxy:6379"}
	}
	if err := tnt.ApplyCluster(ctx, clusterConfig); err != nil {
		return err
	}

	// initialize cluster data cm
	values, err := tnt.GetClusterAgentDataValues(ctx, config.clusterName, suffix, r.inCluster, clusterConfig.Privileged)
	// if cluster data cm is not found ignore error as this is the first time the cluster is being created
	if err != nil && !errors.Is(err, agentclient.ErrClusterDataSecretNotFound) {
		return err
	}
	values.Ipv6OnlyCompatible = &config.ipv6Only
	if values.NotificationsController == nil {
		values.NotificationsController = clusteragent.NewDataValuesNotificationsController()
	}
	values.NotificationsController.InClusterSettings = &config.inClusterNotificationSettings

	if values.Agent == nil {
		values.Agent = clusteragent.NewDataValuesAgent()
		values.Agent.SetVersion(version.GetLatestAgentVersion())
	}

	if config.currentDelegatedRepoServer {
		values.RedisHa = clusteragent.NewDataValuesRedisHa()
		values.RedisHa.Enabled = &config.currentDelegatedRepoServer
		values.RedisHa.Password = &config.agentPassword
	}
	if config.delegatedRepoServer {
		values.RepoServer = clusteragent.NewDataValuesAgentServer()
		if r.inCluster {
			values.RepoServer.SetAddress(fmt.Sprintf("argocd-repo-server.%s:9081/plaintext", common.ArgoCDHostNamespace(config.instanceID)))
		} else {
			shardSubdomain := ""
			if r.settings.Shard != "" {
				shardSubdomain = fmt.Sprintf("%s.", r.settings.Shard)
			}
			values.RepoServer.Address = ptr.To(fmt.Sprintf(repoServerFQDNFormat, config.instanceID, shardSubdomain, r.settings.DomainSuffix) + ":443")
		}
	} else if c.autoscaleEnabled {
		spec, err := c.cluster.GetSpec()
		if err != nil {
			return fmt.Errorf("failed to get cluster spec: %w", err)
		}
		// autoscaling repo server on the cluster if repo server is running
		if spec.Size == models.ClusterSizeAuto {
			values.Agent.Autoscale = ptr.To(true)
			autoscalerConfig := clusterautoscaler.OverrideClusterAutoscalerConfig(r.autoscaler.GetConfig(), spec.AutoscalerConfig)
			autoscaleValues, err := autoscalerConfig.UnmarshalledMap()
			if err != nil {
				return err
			}
			// only set repo server details
			delete(autoscaleValues, "applicationController")
			values.Agent.AutoscaleConfig = autoscaleValues
		}
	}

	values.Applicationset = clusteragent.NewDataValuesApplicationset()
	values.Applicationset.Enabled = &config.applicationSetEnabled
	values.Applicationset.Policy = &config.appsetPolicy.Policy
	values.Applicationset.PolicyOverride = &config.appsetPolicy.PolicyOverrideEnabled
	values.Applicationset.ProgressiveSyncsEnabled = &config.appsetProgressiveSyncsEnabled

	values.ImageUpdater.Enabled = ptr.To(config.imageUpdaterEnabled)
	values.ImageUpdater.Tag = ptr.To(config.imageUpdaterVersion)
	values.SetNamespace(config.namespace)

	values.Agent.AppReplication = ptr.To(config.appReplication)
	if config.redisTunneling {
		values.Agent.ForwardRemotes = []clusteragent.DataValuesAgentForwardRemotes{{
			Name:    ptr.To("redis"),
			Port:    ptr.To(int32(6379)),
			Address: ptr.To("argocd-redis-ha-haproxy:6379"),
		}}
		values.Redis.Address = ptr.To("akuity-agent:6379")
		values.Redis.UseTls = ptr.To(false)
	}
	if values.ArgoCd == nil {
		values.ArgoCd = clusteragent.NewDataValuesArgoCd()
	}

	_, values.ArgoCd.Features = getArgoCDFeatures(config.argocdVersion, c.orgFeatureStatuses, config.appInAnyNamespaceEnabled)
	// redis port should be open only if it is supported by argocd feature and cluster is delegated repo server
	if !config.currentDelegatedRepoServer {
		values.ArgoCd.Features.ArgocdServerDelegateRedis = ptr.To(false)
	}

	// if custom akp version is used update image host
	if isAKPImageVersion(config.argocdVersion) {
		values.ArgoCd.SetImageHost(AkpVersionImgHost)
	}
	values.ArgoCd.AppReconciliationsRateLimiting = clusteragent.NewDataValuesArgoCdAppReconciliationsRateLimiting()
	if config.appReconciliationsRateLimiting != nil {
		values.ArgoCd.AppReconciliationsRateLimiting = &clusteragent.DataValuesArgoCdAppReconciliationsRateLimiting{
			Bucket: &clusteragent.DataValuesArgoCdAppReconciliationsRateLimitingBucket{
				Enabled:    ptr.To(config.appReconciliationsRateLimiting.BucketRateLimiting.Enabled),
				BucketSize: ptr.To(int32(config.appReconciliationsRateLimiting.BucketRateLimiting.BucketSize)),
				BucketQps:  ptr.To(int32(config.appReconciliationsRateLimiting.BucketRateLimiting.BucketQps)),
			},
			Item: &clusteragent.DataValuesArgoCdAppReconciliationsRateLimitingItem{
				Enabled:         ptr.To(config.appReconciliationsRateLimiting.ItemRateLimiting.Enabled),
				FailureCooldown: ptr.To(strconv.Itoa(int(config.appReconciliationsRateLimiting.ItemRateLimiting.FailureCooldown))),
				BaseDelay:       ptr.To(strconv.Itoa(int(config.appReconciliationsRateLimiting.ItemRateLimiting.BaseDelay))),
				MaxDelay:        ptr.To(strconv.Itoa(int(config.appReconciliationsRateLimiting.ItemRateLimiting.MaxDelay))),
				BackoffFactor:   ptr.To(float32(config.appReconciliationsRateLimiting.ItemRateLimiting.BackoffFactor)),
			},
		}
	}

	values.Agent.SetK3sTrafficReduction(c.orgFeatureStatuses.GetK3STrafficReduction().Enabled())
	values.Agent.SetStatusUpdateInterval(int32(r.settings.InstanceConfig.AgentStatusUpdateIntervalSeconds))
	values.ArgoCd.SetAppResyncInterval(int32(r.settings.InstanceConfig.ArgoCDAppResyncIntervalSeconds))

	values.ArgoCd.ControllerResources = config.resources.ControllerRequirements
	values.ArgoCd.RepoServerResources = config.resources.RepoServerRequirements
	values.ArgoCd.RepoServerReplicas = ptr.To(int32(config.resources.RepoServerReplicas))

	var permissionsRules []clusteragent.DataValuesAgentPermissionsRules
	for _, rule := range config.agentPermissionsRules {
		permissionsRules = append(permissionsRules, clusteragent.DataValuesAgentPermissionsRules{ApiGroups: rule.APIGroups, Resources: rule.Resources, Verbs: rule.Verbs})
	}

	values.Agent.PermissionsRules = permissionsRules

	kustomization := config.kustomization
	if r.settings.InstanceConfig.AgentImage != "" && kustomization.FindImageByName(models.DefaultAgentImage) == nil {
		host, repo, version, err := manifests.SplitImage(r.settings.InstanceConfig.AgentImage)
		if err != nil {
			return err
		}
		kustomization.SetImage(kustomize.Image{Name: models.DefaultAgentImage, NewName: fmt.Sprintf("%s/%s", host, repo), NewTag: version})
		values.Agent.SetVersion(version)
		clusterConfig.UpgraderImage = r.settings.InstanceConfig.AgentImage
	}
	// Argo CD and Agent image tags and managed explicitly in platform settings
	if image := kustomization.FindImageByName(models.DefaultArgoCDImage); image != nil {
		image.NewTag = config.argocdVersion
	}
	if image := kustomization.FindImageByName(models.DefaultAgentImage); image != nil {
		image.NewTag = config.agentVersion
	}

	upgraderValues := clusterupgrader.NewDataValues()
	upgraderValues.SetNamespace(config.namespace)
	upgraderValues.SetClusterName(config.clusterName)
	upgraderValues.SetGeneration(int32(config.generation))

	values.Kustomization = kustomization.AsMap()
	values.ArgoCd.SetGeneration(int32(config.generation))
	values.ArgoCd.SetVersion(config.argocdVersion)
	values.ArgoCd.SetNamespaceScoped(config.namespaceScoped)

	datadogAnnotationsEnabled := config.dataDogAnnotationsEnabled
	values.Datadog = &clusteragent.DataValuesDatadog{
		Annotations: &clusteragent.DataValuesDatadogAnnotations{
			Enabled: &datadogAnnotationsEnabled,
		},
	}

	if config.agentVersion != "" {
		upgraderValues.SetAgent(clusterupgrader.DataValuesAgent{Version: ptr.To(config.agentVersion), AppReplication: ptr.To(config.appReplication)})
		version.UpdateAgentImageHost(upgraderValues.Agent)
		values.Agent.SetVersion(config.agentVersion)
	}
	version.UpdateAgentImageHost(values.Agent)

	if config.managedClusterConfig != nil {
		secretKey := "config.yaml"
		if config.managedClusterConfig.SecretKey != "" {
			secretKey = config.managedClusterConfig.SecretKey
		}
		values.Agent.SetManagedClusterConfig(clusteragent.DataValuesAgentManagedClusterConfig{
			SecretName: ptr.To(config.managedClusterConfig.SecretName),
			SecretKey:  ptr.To(secretKey),
		})
	}

	_, _, clusterAgentCMPEnabled := instances.GetConfigManagementPluginEnabled(config.configManagementPlugins)
	configManagementPlugins := clusteragent.DataValuesArgoCdConfigManagementPlugins{}
	configManagementPlugins.Enabled = ptr.To(clusterAgentCMPEnabled)
	if clusterAgentCMPEnabled {
		var plugins []clusteragent.DataValuesArgoCdConfigManagementPluginsPlugins
		for _, p := range config.configManagementPlugins.Plugins {
			if p != nil && p.Enabled {
				plugins = append(plugins, clusteragent.DataValuesArgoCdConfigManagementPluginsPlugins{
					Name:  ptr.To(p.Name),
					Image: ptr.To(p.Image),
					Spec:  p.Spec,
				})
			}
		}
		configManagementPlugins.Plugins = plugins
	}
	values.ArgoCd.SetConfigManagementPlugins(configManagementPlugins)

	if config.multiClusterK8sDashboard {
		portalURL := r.settings.PortalURL
		if val := os.Getenv("AGENT_PORTAL_URL"); val != "" {
			portalURL = val
		}
		values.SetMultiClusterK8sDashboard(clusteragent.DataValuesMultiClusterK8sDashboard{
			Enabled:                    ptr.To(config.multiClusterK8sDashboard),
			ResourceUrl:                ptr.To(fmt.Sprintf("%s/agent-api/v1/clusters/%s/k8s-resources", portalURL, c.cluster.ID)),
			ResourceTypeUrl:            ptr.To(fmt.Sprintf("%s/agent-api/v1/clusters/%s/k8s-info", portalURL, c.cluster.ID)),
			EventUrl:                   ptr.To(fmt.Sprintf("%s/agent-api/v1/clusters/%s/k8s-events", portalURL, c.cluster.ID)),
			InsecureSkipTlsVerify:      ptr.To(r.settings.MultiClusterK8sDashboardConfig.SkipTLSVerify),
			ResourcesSyncPeriod:        ptr.To(r.settings.MultiClusterK8sDashboardConfig.ResourcesSyncPeriod.String()),
			ClusterInfoSyncPeriod:      ptr.To(r.settings.MultiClusterK8sDashboardConfig.ClusterInfoSyncPeriod.String()),
			EventSyncPeriod:            ptr.To(r.settings.MultiClusterK8sDashboardConfig.EventSyncPeriod.String()),
			GarbageCollectionPeriod:    ptr.To(r.settings.MultiClusterK8sDashboardConfig.GarbageCollectionPeriod.String()),
			GarbageCollectionThreshold: ptr.To(r.settings.MultiClusterK8sDashboardConfig.GarbageCollectionThreshold.String()),
			CveScanConfig: ptr.To(clusteragent.DataValuesMultiClusterK8sDashboardCveScanConfig{
				ScanEnabled:    ptr.To(config.kubeVisionConfig.CveScanConfig.ScanEnabled),
				RescanInterval: ptr.To(config.kubeVisionConfig.CveScanConfig.RescanInterval),
			}),
		})
	}

	if r.customCert != "" {
		values.Agent.CustomCplaneCert = ptr.To(r.customCert)
	}

	return tnt.InitializeClusterData(ctx, clusterConfig, values, upgraderValues)
}

func GetAgentSuffix(count uint64) string {
	if count > 0 {
		return fmt.Sprintf(".rot-%v", count)
	}
	return ""
}
