CREATE OR REPLACE FUNCTION notify_kine_inserted() RETURNS TRIGGER AS $$
BEGIN
    if NEW.name like '/registry/events/argocd/%' then
        PERFORM pg_notify(
                'default.on_instance_changed',
                json_build_object(
                        'instance_id', TG_ARGV[0]::text,
                        'type', 'k8s_event_created'::text
                    )::text
            );
    elsif NEW.name like '/registry/configmaps/argocd/cluster-%.status' then
        PERFORM pg_notify(
                'default.on_instance_changed',
                json_build_object(
                        'instance_id', TG_ARGV[0]::text,
                        'cluster_name', substring(NEW.name, '/registry/configmaps/argocd/cluster-(.*).status'),
                        'type', 'cluster_status_changed'::text
                    )::text
            );
    elsif NEW.name like '/registry/secrets/argocd/cluster-%.data' then
        PERFORM pg_notify(
                'default.on_instance_changed',
                json_build_object(
                        'instance_id', TG_ARGV[0]::text,
                        'cluster_name', substring(NEW.name, '/registry/secrets/argocd/cluster-(.*).data'),
                        'type', 'cluster_status_changed'::text
                    )::text
            );
    -- this assumes that the app resource is stored in json format and not protobuf
    elsif NEW.name like '/registry/argoproj.io/applications/argocd/%' then
        PERFORM pg_notify(
                'default.on_app_update',
                json_build_object(
                        'instance_id', TG_ARGV[0]::text,
                        'app_name', substring(NEW.name, '/registry/argoproj.io/applications/argocd/(.*)'),
                        'type', 'app_update'::text,
                        'cluster_addon_id', coalesce(convert_from(NEW.value,'utf8')::jsonb -> 'metadata' -> 'labels' ->> 'addon.akuity.io/cid', '')::text
                    )::text
            );
    end if;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;
