package integration

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"net"
	"reflect"
	"regexp"
	"strings"
	"time"

	"github.com/go-logr/logr"
	"github.com/golang-jwt/jwt/v5"
	"github.com/samber/lo"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/util/sets"
	listersAppsV1 "k8s.io/client-go/listers/apps/v1"
	"k8s.io/utils/ptr"

	agentclient "github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/agent/pkg/client/apis/controlplane"
	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/agent/pkg/kube"
	"github.com/akuityio/akuity-platform/controllers/platform/integration/autoscaler"
	"github.com/akuityio/akuity-platform/controllers/shared/tenant"
	"github.com/akuityio/akuity-platform/internal/argoproj/argocd"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/internal/utils/io"
	"github.com/akuityio/akuity-platform/internal/utils/logging"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
	"github.com/akuityio/akuity-platform/models/util/status"
	featuresv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/features/v1"

	_ "embed"
)

const (
	// quickOperationsDuration holds timeout of operations that must be done quickly. e.g. getting status from k3s/tenant db
	quickOperationsDuration = 5 * time.Second
	// Schema and username cannot be numeric strings, e.g. 1, so we have to add a prefix to explicitly tell it's a string.
	k3sDBSchemaUsernamePrefix = "instance_"

	instanceHealthyMessage    = "Instance is up and running."
	instanceIsDegradedMessage = "Instance is degraded, we are working on it!"
	// repoServerFQDNFormatLegacy is a deprecated format for the repo server FQDN that should be removed in a future
	// see https://github.com/akuityio/akuity-platform/issues/3949 for details
	repoServerFQDNFormatLegacy = "%s.cd-repo.%s%s"
	repoServerFQDNFormat       = "%s-repo.cdsvcs.%s%s"
	forceReconcileClusters     = ` 
 		UPDATE argo_cd_cluster 
    	SET 
 		   status_observed_generation = generation - 1,
 		   status_conditions = null
 		WHERE 
 			instance_id = $1;
	`
	bumpClusterGeneration = `
 		UPDATE argo_cd_cluster
    	SET 
			generation = generation + 1
 		WHERE 
 			instance_id = $1;
`
	kubeVisionArgoAccountName = "system-kubevision"
)

var (
	kubeVisionTokenKey          = fmt.Sprintf("accounts.%s.tokens", kubeVisionArgoAccountName)
	kubeVisionRBACReadonlyRules = []string{
		fmt.Sprintf("g, %s, role:readonly", kubeVisionArgoAccountName),
		fmt.Sprintf("p, %s, applications, action/*, *, allow", kubeVisionArgoAccountName),
		fmt.Sprintf("p, %s, applications, get, *, allow", kubeVisionArgoAccountName),
		fmt.Sprintf("p, %s, applications, update, *, allow", kubeVisionArgoAccountName),
		fmt.Sprintf("p, %s, applications, sync, *, allow", kubeVisionArgoAccountName),
	}
)

var statusColumns []string

func init() {
	for _, column := range models.ArgoCDInstanceStatusColumns {
		// StatusRecentProcessedEventInfo is written by events controller
		// (platform/controller/integration/argo_cd_instance_events.go)
		if column != models.ArgoCDInstanceColumns.StatusRecentProcessedEventInfo {
			statusColumns = append(statusColumns, column)
		}
	}
}

type argocdInstanceReconciler struct {
	log                       *logr.Logger
	stateClient               tenant.StateClient
	settings                  ControllerSettings
	tenantsFactory            TenantsFactory
	imagePullSecretGetter     func() (map[string][]byte, error)
	gracePeriodDuration       time.Duration
	sqlOpen                   func(string, string) (*sql.DB, error)
	instanceDeploymentsLister listersAppsV1.DeploymentLister
	autoscaler                *autoscaler.ArgocdInstanceAutoscaler
	featuresSvc               features.Service
	inCluster                 bool
	argocdVersions            []agentclient.ComponentVersion
}

func NewArgoCDInstanceReconciler(
	log *logr.Logger,
	tenantsFactory TenantsFactory,
	settings ControllerSettings,
	stateClient tenant.StateClient,
	imagePullSecretGetter func() (map[string][]byte, error),
	gracePeriodDuration time.Duration,
	instanceDeploymentsLister listersAppsV1.DeploymentLister,
	autoscaler *autoscaler.ArgocdInstanceAutoscaler,
	featureSvc features.Service,
	argocdVersions []agentclient.ComponentVersion,
) *argocdInstanceReconciler {
	return &argocdInstanceReconciler{
		log:                       log,
		imagePullSecretGetter:     imagePullSecretGetter,
		stateClient:               stateClient,
		tenantsFactory:            tenantsFactory,
		settings:                  settings,
		gracePeriodDuration:       gracePeriodDuration,
		sqlOpen:                   sql.Open,
		instanceDeploymentsLister: instanceDeploymentsLister,
		autoscaler:                autoscaler,
		inCluster:                 !settings.EnableIngress,
		featuresSvc:               featureSvc,
		argocdVersions:            argocdVersions,
	}
}

// Close closes Portal and K3s DBs, only used in unit tests
func (r *argocdInstanceReconciler) Close() error {
	if r.settings.PortalDBRawClient != nil {
		if err := r.settings.PortalDBRawClient.Close(); err != nil {
			return err
		}
	}
	if r.settings.K3sDBRawClient != nil {
		if err := r.settings.K3sDBRawClient.Close(); err != nil {
			return err
		}
	}
	return nil
}

func (r *argocdInstanceReconciler) ItemToID(item *models.ArgoCDInstance) string {
	return item.ID
}

func (r *argocdInstanceReconciler) IDColumn() string {
	return models.ArgoCDInstanceTableColumns.ID
}

func (r *argocdInstanceReconciler) LogValuesFromID(id string) []interface{} {
	return []interface{}{"instance_id", id}
}

func (r *argocdInstanceReconciler) LogValuesFromItem(item *models.ArgoCDInstance) []interface{} {
	return []interface{}{}
}

func (r *argocdInstanceReconciler) Reconcile(ctx context.Context, instance *models.ArgoCDInstance) error {
	timer, endStep := agentclient.GetTimerFromContextWithStartStep(ctx, "Reconcile")
	defer endStep()

	status, err := instance.GetStatus()
	if err != nil {
		return fmt.Errorf("failed to get instance status: %w", err)
	}
	oldStatus := status.DeepCopy()
	reconcileError := r.reconcile(ctx, instance, &status)

	if !reflect.DeepEqual(status, oldStatus) {
		timer.StartStep("Reconcile.InstanceSetStatus")
		if err := instance.SetStatus(status); err != nil {
			return fmt.Errorf("failed to set instance status: %w", err)
		}
		timer.StartStep("Reconcile.ArgoCDInstancesUpdate")
		if err := r.settings.RepoSet.ArgoCDInstances().Update(context.Background(), instance, statusColumns...); err != nil {
			return fmt.Errorf("failed to persist instance status: %w", err)
		}
	}

	return reconcileError
}

type cdInstanceRelatedData struct {
	instanceConfig     *models.ArgoCDInstanceConfig
	organization       *models.Organization
	instance           *models.ArgoCDInstance
	certStatus         *status.CertificateStatus
	unsupportedVersion bool
	orgFeatureStatuses *featuresv1.FeatureStatuses
}

func (r *argocdInstanceReconciler) loadInstanceRelatedData(ctx context.Context, instance *models.ArgoCDInstance) (*cdInstanceRelatedData, error) {
	instanceConfig, err := r.settings.RepoSet.ArgoCDInstanceConfigs().GetByID(ctx, instance.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get instance config: %w", err)
	}

	org, err := r.settings.RepoSet.Organizations().GetByID(ctx, instance.OrganizationOwner)
	if err != nil {
		return nil, fmt.Errorf("failed to get organization: %w", err)
	}
	orgFeatureStatuses := r.featuresSvc.GetFeatureStatusesWithOrg(ctx, org)
	return &cdInstanceRelatedData{
		instanceConfig:     instanceConfig,
		organization:       org,
		instance:           instance,
		unsupportedVersion: misc.IsComponentVersionSupported(instanceConfig.Version.String, r.argocdVersions) != nil,
		orgFeatureStatuses: orgFeatureStatuses,
	}, nil
}

func (r *argocdInstanceReconciler) getAppOfAppsMatcher(ctx context.Context, instanceID string) (func(app argocd.Application) bool, error) {
	kargoClusters, err := r.settings.RepoSet.ArgoCDClusters().Filter(
		models.ArgoCDClusterWhere.InstanceID.EQ(instanceID),
		qm.And("(spec->'directClusterSpec'->>'type')::text = 'kargo'"),
	).ListAll(ctx, models.ArgoCDClusterColumns.Name)
	if err != nil {
		return nil, err
	}
	clusterNames := sets.New(lo.Map(kargoClusters, func(item *models.ArgoCDCluster, _ int) string {
		return item.Name
	})...)
	clusterNames.Insert(argocd.KubernetesInternalServerName)
	clusterUrls := sets.New(lo.Map(kargoClusters, func(item *models.ArgoCDCluster, _ int) string {
		return fmt.Sprintf("https://cluster-%v:6445", item.Name)
	})...)
	clusterUrls.Insert(argocd.KubernetesInternalServerAddr)
	return func(app argocd.Application) bool {
		return clusterNames.Has(app.Spec.Destination.Name) || clusterUrls.Has(app.Spec.Destination.Server)
	}, nil
}

func (r *argocdInstanceReconciler) reconcile(ctx context.Context, instance *models.ArgoCDInstance, instanceStatus *models.ArgoCDInstanceStatus) error {
	timer, endStep := agentclient.GetTimerFromContextWithStartStep(ctx, "Reconcile.reconcile")
	defer endStep()

	defer func() {
		instanceStatus.ObservedGeneration = null.IntFrom(instance.Generation)
		if instanceStatus.Health.Code == status.HealthStatusCodeHealthy {
			instanceStatus.Info.ReachedGeneration = instance.Generation
		}
	}()
	instanceID := instance.ID

	newTenant, err := r.tenantsFactory.NewTenant(instanceID)
	if err != nil {
		return fmt.Errorf("failed to create tenant for ArgoCDInstance with id %s: %w", instanceID, err)
	}

	if !instance.DeletionTimestamp.IsZero() && instance.DeletionTimestamp.Time.Before(time.Now()) {
		if err := r.deleteTenant(ctx, newTenant, instanceID); err != nil {
			return fmt.Errorf("failed to delete tenant: %w", err)
		}
		return nil
	}

	timer.StartStep("Reconcile.reconcile.LoadInstanceRelatedData")
	// Get instance data only after checking for deletion or else might get into partially deleted error state
	// where instance config is already deleted but instance entry is not
	instanceData, err := r.loadInstanceRelatedData(ctx, instance)
	if err != nil {
		return err
	}

	generationMismatch := instanceStatus.ObservedGeneration.Int != instance.Generation

	// set all relevant status conditions to false if generation mismatch
	// if unsupported version, we will not reconcile instance so we will not unset any conditions
	if generationMismatch && !instanceData.unsupportedVersion {
		delete(instanceStatus.Conditions, models.InstanceConditionTypeApplied)
	}

	// If instance namespace is lost (disaster scenario), force reconcile instance and all instance clusters
	timer.StartStep("Reconcile.reconcile.DeploymentsCheck")
	if instanceStatus.Conditions.IsEstablished(models.InstanceConditionTypeApplied) && !instanceData.unsupportedVersion {
		deployments, err := r.instanceDeploymentsLister.Deployments(common.ArgoCDHostNamespace(instanceID)).List(labels.Everything())
		if err != nil && !k8sErrors.IsNotFound(err) {
			return fmt.Errorf("failed to list instance deployments: %w", err)
		}
		if len(deployments) == 0 {
			delete(instanceStatus.Conditions, models.InstanceConditionTypeApplied)

			if _, err := r.settings.PortalDBRawClient.Exec(forceReconcileClusters, instanceID); err != nil {
				return fmt.Errorf("failed to requeue clusters: %w", err)
			}
		}
	}

	timer.StartStep("Reconcile.reconcile.GetInstanceHealth")
	health, err := r.getInstanceHealth(ctx, newTenant, instanceData)
	if err != nil {
		return fmt.Errorf("failed to get health for ArgoCDInstance with id %s: %w", instanceID, err)
	}
	instanceStatus.Health = health

	timer.StartStep("Reconcile.reconcile.CertStatusUpdate")
	requireCertInit := false
	if !config.IsSelfHosted && instanceData.instanceConfig.FQDN.String != "" {
		internalUrl := getArgocdInternalInstanceURL(instanceData.instanceConfig, r.settings.Shard, r.settings.DomainSuffix)
		cert, err := getCertificateStatus(ctx, newTenant, internalUrl, instanceData.instanceConfig.FQDN.String)
		if err != nil && !k8sErrors.IsNotFound(err) {
			return err
		}
		if k8sErrors.IsNotFound(err) {
			requireCertInit = true
		}
		instanceStatus.Info.CertificateStatus = cert
		instanceData.certStatus = &cert
	}

	timer.StartStep("Reconcile.reconcile.GetApplicationInfo")

	appInfoCtx, cancel := context.WithTimeout(ctx, quickOperationsDuration)
	defer cancel()

	appOfAppsMatcher, err := r.getAppOfAppsMatcher(ctx, instanceID)
	if err != nil {
		return err
	}
	appsStatus, err := r.stateClient.GetApplicationInfo(appInfoCtx, instanceID, appOfAppsMatcher)

	if err == nil && appsStatus != nil {
		instanceStatus.Info.ApplicationsStatus = *appsStatus
	}
	// ignore relation not found error, schema might not be initialized yet
	if err != nil {
		if ok, _ := regexp.MatchString(relationNotFoundError, err.Error()); !ok {
			r.log.Error(err, "failed to get application status")
		}
	}

	// DEV: ensure no form of manifest apply/generation happens when this
	// condition is encountered or else it will error during manifest gen
	// stop reconciliation if instance version is unsupported
	if instanceData.unsupportedVersion {
		r.log.Info("unsupported argocd version", "instance_id", instanceID, "version", instanceData.instanceConfig.Version.String)
		return nil
	}

	timer.StartStep("Reconcile.reconcile.RefreshRestrictions")
	if err := r.refreshRestrictions(ctx, instanceData); err != nil {
		return err
	}

	timer.StartStep("Reconcile.reconcile.UpdateInstanceConfigPrivateSpec")
	if err := r.updateInstanceConfigPrivateSpec(ctx, instanceData); err != nil {
		return err
	}

	timer.StartStep("Reconcile.reconcile.GetK3SCertCnReset")
	if !generationMismatch && r.featuresSvc.GetFeatureStatuses(ctx, nil).GetK3SCertCnReset().Enabled() {
		yes, err := r.stateClient.NeedK3sCNReset(ctx, instance.ID)
		if err != nil {
			if ok, _ := regexp.MatchString(relationNotFoundError, err.Error()); !ok {
				// ignore relation not found error, schema might not be initialized yet
				return err
			}
		}
		if yes {
			r.log.Info("need K3s Cert CN Reset", "instance_id", instanceID)
			instanceStatus.Conditions.SetNotEstablished(models.InstanceConditionTypeInitialized, "NeedK3sCertCnReset", "K3s TLS cert CNs need to be reset")
		}
	}

	instanceApplied := instanceStatus.Conditions.IsEstablished(models.InstanceConditionTypeApplied)
	instanceInitialized := instanceStatus.Conditions.IsEstablished(models.InstanceConditionTypeInitialized)
	instancePruned := instanceStatus.Conditions.IsEstablished(models.InstanceConditionTypePruned)
	reconciliationNecessary := generationMismatch || !instanceApplied || !instanceInitialized || requireCertInit

	var dataValues *controlplane.DataValues

	if reconciliationNecessary {
		timer.StartStep("Reconcile.reconcile.BuildAgentConfigurations")
		dataValues, err = newAgentConfigBuilder(r, instanceData, r.featuresSvc, r.settings.IngressConfig).buildAgentConfigurations(ctx, int32(instance.Generation))
		if err != nil {
			return fmt.Errorf("failed to build data values for ArgoCDInstance with id %q: %w", instanceID, err)
		}
		if r.settings.EnableIngress {
			if instanceStatus.Hostname.String != dataValues.Ingress.ArgoCd.GetFqdn() && r.settings.OverrideDefaultDomains {
				if _, err := r.settings.PortalDBRawClient.Exec(bumpClusterGeneration, instanceID); err != nil {
					return fmt.Errorf("failed to set new hostname when requeuing clusters: %w", err)
				}
			}
			instanceStatus.Hostname = null.StringFrom(dataValues.Ingress.ArgoCd.GetFqdn())
		} else {
			instanceStatus.Hostname = null.StringFrom(fmt.Sprintf("argocd-server.argocd-%s%s", instanceID, agentclient.GetInClusterServicePostfix()))
		}
	} else if r.autoscaler != nil {
		timer.StartStep("Reconcile.reconcile.AutoscalerUpdateInstanceConfig")
		// Running autoscaler in a steady state only
		instanceConfigUpdated, err := r.autoscaler.UpdateInstanceConfig(ctx, instance, instanceData.instanceConfig)
		if err != nil {
			return fmt.Errorf("autoscaler failed to run for ArgoCDInstance %q: %w", instanceID, err)
		}
		if instanceConfigUpdated {
			// Next iteration will apply the change due to the generation bump as a result of the instanceConfig update
			return nil
		}
	}

	if generationMismatch || !instanceApplied || requireCertInit {
		delete(instanceStatus.Conditions, models.InstanceConditionTypeInitialized)
		delete(instanceStatus.Conditions, models.InstanceConditionTypePruned)
		if err := newTenant.Apply(ctx, dataValues, kube.ApplyOpts{}); err != nil {
			instanceStatus.Conditions.SetNotEstablished(models.InstanceConditionTypeApplied, "FailedToApplyManifests", err.Error())
			return errorsutil.NewRetryableError(err, "failed to apply tenant manifests")
		}
		instanceStatus.Info.AkuityServerVersion = dataValues.AgentServer.GetVersion()
		instanceStatus.Conditions.SetEstablished(models.InstanceConditionTypeApplied)
	}

	if generationMismatch || !instanceInitialized {
		if err := r.initializeTenant(ctx, instanceData, newTenant, dataValues); err != nil {
			instanceStatus.Conditions.SetNotEstablished(models.InstanceConditionTypeInitialized, "FailedToInitializeTenant", err.Error())
			return errorsutil.NewRetryableError(err, "failed to initialize tenant")
		}
		instanceStatus.Conditions.SetEstablished(models.InstanceConditionTypeInitialized)
		// clear the requested version after instance is applied and initialized
		instanceStatus.Info.RequestedAkuityServerVersion = ""
	}

	// Prune only when the instance is healthy
	if (generationMismatch || !instancePruned) && instanceStatus.Health.Code == status.HealthStatusCodeHealthy {
		if err := newTenant.PruneTenant(ctx, uint64(instance.Generation), false); err != nil {
			instanceStatus.Conditions.SetNotEstablished(models.InstanceConditionTypePruned, "FailedToPruneTenant", err.Error())
			return errorsutil.NewRetryableError(err, "failed to prune tenant")
		}
		internalSpec, err := instanceData.instanceConfig.GetInternalSpec()
		if err != nil {
			return err
		}
		if internalSpec.K3sSchemaMigration != 0 {
			internalSpec.K3sSchemaMigration = 0
			if err := instanceData.instanceConfig.SetInternalSpec(internalSpec); err != nil {
				return err
			}
			if err := r.settings.RepoSet.ArgoCDInstanceConfigs().Update(ctx, instanceData.instanceConfig, models.ArgoCDInstanceConfigColumns.InternalSpec); err != nil {
				return err
			}
		}
		instanceStatus.Conditions.SetEstablished(models.InstanceConditionTypePruned)
	}

	return nil
}

func (r *argocdInstanceReconciler) refreshRestrictions(ctx context.Context, instanceData *cdInstanceRelatedData) error {
	maxApps, currentAppCount, expired, err := instances.GetApplicationLimits(ctx, r.gracePeriodDuration, r.settings.PortalDBRawClient, instanceData.organization)
	canCreateApps := (maxApps == 0 || currentAppCount < maxApps) && !expired
	if err != nil {
		return err
	}
	internalSpec, err := instanceData.instanceConfig.GetInternalSpec()
	if err != nil {
		return err
	}
	if internalSpec.GetCanCreateApps() != canCreateApps {
		internalSpec.CanCreateApps = ptr.To(canCreateApps)
		if err := instanceData.instanceConfig.SetInternalSpec(internalSpec); err != nil {
			return err
		}
		if err := r.settings.RepoSet.ArgoCDInstanceConfigs().Update(ctx, instanceData.instanceConfig, models.ArgoCDInstanceConfigColumns.InternalSpec); err != nil {
			return err
		}
	}
	return nil
}

func (r *argocdInstanceReconciler) initializeTenant(ctx context.Context, instanceData *cdInstanceRelatedData, tenant Tenant, dataValues *controlplane.DataValues) error {
	timer, endStep := agentclient.GetTimerFromContextWithStartStep(ctx, "Reconcile.InitializeTenant")
	defer endStep()

	if err := tenant.InitializeTenant(ctx, dataValues, kube.ApplyOpts{},
		r.featuresSvc.GetFeatureStatuses(ctx, nil).GetK3SCertCnReset().Enabled()); err != nil {
		return err
	}

	timer.StartStep("Reconcile.InitializeTenant.ControlPlaneExternalKubeConfig")
	data, err := tenant.ControlPlaneExternalKubeConfig(ctx, dataValues.Ingress.K3sProxy.GetFqdn(), r.inCluster)
	if err != nil {
		return err
	}

	privateSpec, err := instanceData.instanceConfig.GetPrivateSpec()
	if err != nil {
		return err
	}

	if string(privateSpec.Kubeconfig) != string(data) {
		privateSpec.Kubeconfig = data
		timer.StartStep("Reconcile.InitializeTenant.SetPrivateSpec")
		if err := instanceData.instanceConfig.SetPrivateSpec(privateSpec); err != nil {
			return err
		}
		timer.StartStep("Reconcile.InitializeTenant.ArgoCDInstanceConfigs.Update")
		if err := r.settings.RepoSet.ArgoCDInstanceConfigs().Update(ctx, instanceData.instanceConfig, models.ArgoCDInstanceConfigColumns.PrivateSpec); err != nil {
			return err
		}
	}
	timer.StartStep("Reconcile.InitializeTenant.CreateEventsTrigger")
	return r.createEventsTrigger(ctx, instanceData)
}

func (r *argocdInstanceReconciler) getInstanceHealth(ctx context.Context, tenant Tenant, instanceData *cdInstanceRelatedData) (status.HealthStatus, error) {
	spec, err := instanceData.instanceConfig.GetSpec()
	if err != nil {
		return status.HealthStatus{}, err
	}

	// if we cannot retrieve the tenant status in 5 seconds, we will return an unknown status
	ctx, cancel := context.WithTimeout(ctx, quickOperationsDuration)
	defer cancel()
	tenantStatus, err := tenant.Status(ctx, spec.DeclarativeManagementEnabled, spec.AppsetDisabled)
	if err != nil {
		return status.HealthStatus{
			Code:    status.HealthStatusCodeUnknown,
			Message: fmt.Sprintf("failed to get tenant status: %v", err),
		}, nil
	}

	switch tenantStatus.PriorityStatus {
	case common.TenantPhaseHealthy:
		return status.HealthStatus{
			Code:    status.HealthStatusCodeHealthy,
			Message: instanceHealthyMessage,
		}, nil
	case common.TenantPhaseProgressing:
		workloads := "workloads"
		progressingResourcesCount := len(tenantStatus.Progressing)
		if progressingResourcesCount == 1 {
			workloads = "workload"
		}
		return status.HealthStatus{
			Code:    status.HealthStatusCodeProgressing,
			Message: fmt.Sprintf("Instance is progressing, waiting for %d %s to start.", progressingResourcesCount, workloads),
		}, nil
	case common.TenantPhaseDegraded:
		return status.HealthStatus{
			Code:    status.HealthStatusCodeDegraded,
			Message: instanceIsDegradedMessage,
		}, nil
	default:
		resourcesHave := "resources have"
		unknownResourcesCount := len(tenantStatus.Unknown)
		if unknownResourcesCount == 1 {
			resourcesHave = "resource has"
		}
		return status.HealthStatus{
			Code:    status.HealthStatusCodeUnknown,
			Message: fmt.Sprintf("%d tenant %s an unknown status", unknownResourcesCount, resourcesHave),
		}, nil
	}
}

// deleteTenant deletes the tenant resources in k8s and database entries for configs
// and all clusters details belonging to the tenant with provided instanceID
func (r *argocdInstanceReconciler) deleteTenant(ctx context.Context, tnt Tenant, instanceID string) error {
	// delete external dependencies, user initiated deletion would already have cleaned up these resources
	// but in case this was an automated or aims deletion we need to clean up
	// 1. kargo agents using argocd direct cluster
	updates, err := models.KargoAgents(models.KargoAgentWhere.RemoteArgocdInstanceID.EQ(null.StringFrom(instanceID))).UpdateAll(ctx, r.settings.PortalDBRawClient, models.M{"deletion_timestamp": time.Now().Format(time.RFC3339)})
	if err != nil {
		return fmt.Errorf("failed to delete kargo agents: %w", err)
	}
	if updates > 0 {
		return errorsutil.NewRetryableError(fmt.Errorf("deleting dependent kargo agents: count=%d", updates), "")
	} else {
		// check if there are any pending deletions
		agentsCount, err := models.KargoAgents(models.KargoAgentWhere.RemoteArgocdInstanceID.EQ(null.StringFrom(instanceID))).Count(ctx, r.settings.PortalDBRawClient)
		if err != nil {
			return fmt.Errorf("failed to get kargo agents: %w", err)
		}
		if agentsCount > 0 {
			return errorsutil.NewRetryableError(fmt.Errorf("waiting for dependent kargo agents to be deleted: count=%d", agentsCount), "")
		}
	}

	// delete instance ns and resources only after external dependencies(like kargo agents) are cleaned up
	if err := tnt.Delete(ctx); err != nil {
		// ignore error if ns already deleted in previous reconcile
		if !k8sErrors.IsNotFound(err) {
			return fmt.Errorf("failed to delete tenant ns: %w", err)
		}
	}

	schemaName := k3sDBSchemaUsernamePrefix + instanceID
	if _, err := r.settings.K3sDBRawClient.ExecContext(ctx, fmt.Sprintf(`DROP SCHEMA IF EXISTS %s CASCADE`, schemaName)); err != nil {
		return fmt.Errorf("failed to delete tenant schema %q: %w", schemaName, err)
	}

	userName := schemaName
	if _, err := r.settings.K3sDBRawClient.ExecContext(ctx, fmt.Sprintf(`DROP USER IF EXISTS %s`, userName)); err != nil {
		return fmt.Errorf("failed to delete tenant user %q: %w", userName, err)
	}

	if err := r.settings.RepoSet.ClusterAddons().Filter(models.ClusterAddonWhere.InstanceID.EQ(instanceID)).DeleteAll(ctx); err != nil {
		// ignore error if already deleted in previous reconcile
		if !errors.Is(err, sql.ErrNoRows) {
			return fmt.Errorf("failed to delete instance cluster addon entries: %w", err)
		}
	}

	if err := r.settings.RepoSet.Addons().Filter(models.AddonWhere.InstanceID.EQ(instanceID)).DeleteAll(ctx); err != nil {
		// ignore error if already deleted in previous reconcile
		if !errors.Is(err, sql.ErrNoRows) {
			return fmt.Errorf("failed to delete instance addon entries: %w", err)
		}
	}

	if err := r.settings.RepoSet.AddonRepo().Filter(models.AddonRepoWhere.InstanceID.EQ(instanceID)).DeleteAll(ctx); err != nil {
		// ignore error if already deleted in previous reconcile
		if !errors.Is(err, sql.ErrNoRows) {
			return fmt.Errorf("failed to delete instance addon repo entries: %w", err)
		}
	}

	if err := r.settings.RepoSet.AddonMarketplaceInstalls().Filter(models.AddonMarketplaceInstallWhere.InstanceID.EQ(instanceID)).DeleteAll(ctx); err != nil {
		// ignore error if already deleted in previous reconcile
		if !errors.Is(err, sql.ErrNoRows) {
			return fmt.Errorf("failed to delete instance addon marketplace install entries: %w", err)
		}
	}

	if err := r.settings.RepoSet.ArgoCDClusters().Filter(models.ArgoCDClusterWhere.InstanceID.EQ(instanceID)).DeleteAll(ctx); err != nil {
		// ignore error if tenant clusters already deleted in previous reconcile
		if !errors.Is(err, sql.ErrNoRows) {
			return fmt.Errorf("failed to delete tenant cluster entries: %w", err)
		}
	}

	if err := r.settings.RepoSet.AIConversations().Filter(models.AiConversationWhere.InstanceID.EQ(null.StringFrom(instanceID))).DeleteAll(ctx); err != nil {
		// ignore error if already deleted in previous reconcile
		if !errors.Is(err, sql.ErrNoRows) {
			return fmt.Errorf("failed to delete tenant ai conversations: %w", err)
		}
	}

	db, txBeginner := database.WithTxBeginner(r.settings.PortalDBRawClient)
	repoSet := client.NewRepoSet(db)
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()
	tx, err := txBeginner.Begin(ctx)
	if err != nil {
		return err
	}

	if err := repoSet.ArgoCDInstanceConfigs().Delete(ctx, instanceID); err != nil {
		// ignore error if tenant config already deleted in previous reconcile
		if !errors.Is(err, sql.ErrNoRows) {
			return fmt.Errorf("failed to delete tenant instance config entry: %w", err)
		}
	}

	if err := repoSet.ArgoCDSyncOperations().Filter(models.ArgoCDSyncOperationWhere.InstanceID.EQ(instanceID)).DeleteAll(ctx); err != nil {
		// ignore error if sync events are already deleted in previous reconcile
		if !errors.Is(err, sql.ErrNoRows) {
			return fmt.Errorf("failed to delete tenant sync operation entries: %w", err)
		}
	}

	if err := repoSet.ArgoCDInstances().Delete(ctx, instanceID); err != nil {
		return fmt.Errorf("failed to delete tenant instance: %w", err)
	}

	return tx.Commit()
}

//go:embed functions.sql
var sqlFunctions string

var eventsTriggerSQL = []string{
	`DROP TRIGGER IF EXISTS on_kine_inserted ON instance_%[1]s.kine;`,

	`CREATE TRIGGER on_kine_inserted
		AFTER INSERT ON instance_%[1]s.kine
		FOR EACH ROW
	EXECUTE PROCEDURE instance_%[1]s.notify_kine_inserted('%[1]s');`,
}

func (r *argocdInstanceReconciler) createEventsTrigger(ctx context.Context, instanceData *cdInstanceRelatedData) error {
	dbInfo, err := database.ExtractDBInfo(r.settings.K3sDBConnection)
	if err != nil {
		return err
	}

	if err := r.dropLegacyFunctions(ctx, instanceData.instance.ID, dbInfo); err != nil {
		return err
	}

	privateSpec, err := instanceData.instanceConfig.GetPrivateSpec()
	if err != nil {
		return err
	}

	// Tenant schema is identical to tenant's username
	postgresSchema := privateSpec.K3sUsername
	postgresUsername := privateSpec.K3sUsername
	postgresPassword := privateSpec.K3sPassword

	if r.settings.SharedK3sDBConnectionAuth {
		// During DB migration - all tenants temporarily use a shared K3sDBConnection auth to connect to RDS Proxy
		postgresUsername = dbInfo.User
		postgresPassword = dbInfo.Password
	}

	connectionString := fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=%s&&search_path=%s",
		postgresUsername,
		postgresPassword,
		dbInfo.Host,
		dbInfo.Port,
		dbInfo.DBName,
		dbInfo.SSLMode,
		postgresSchema)
	k3sExecutor, err := r.sqlOpen("postgres", connectionString)
	if err != nil {
		return err
	}
	defer io.Close(k3sExecutor)

	separator := "$$ LANGUAGE plpgsql;"
	for _, functionSQL := range strings.Split(sqlFunctions, separator) {
		if strings.TrimSpace(functionSQL) == "" {
			continue
		}

		functionSQL += separator
		functionSQL = strings.Replace(functionSQL, "CREATE OR REPLACE FUNCTION ", fmt.Sprintf("CREATE OR REPLACE FUNCTION instance_%s.", instanceData.instance.ID), 1)
		if _, err := k3sExecutor.ExecContext(ctx, functionSQL); err != nil {
			return err
		}
	}

	for _, statement := range eventsTriggerSQL {
		if _, err := k3sExecutor.ExecContext(ctx, fmt.Sprintf(statement, instanceData.instance.ID)); err != nil {
			return err
		}
	}
	return nil
}

func formatRepoServerDelegate(spec *models.InstanceConfigSpec) string {
	switch {
	case spec.RepoServerDelegate != nil && spec.RepoServerDelegate.ControlPlane != nil:
		return "control-plane"
	case spec.RepoServerDelegate != nil && spec.RepoServerDelegate.ManagedCluster != nil:
		return fmt.Sprintf("cluster-%s", spec.RepoServerDelegate.ManagedCluster.ClusterName)
	default:
		return ""
	}
}

func formatAppSetDelegate(spec *models.InstanceConfigSpec) string {
	switch {
	case spec.AppSetDelegate != nil && spec.AppSetDelegate.ManagedCluster != nil:
		return fmt.Sprintf("cluster-%s", spec.AppSetDelegate.ManagedCluster.ClusterName)
	case !spec.AppsetDisabled:
		return "control-plane"
	default:
		return ""
	}
}

func (r *argocdInstanceReconciler) updateInstanceConfigPrivateSpec(ctx context.Context, instanceData *cdInstanceRelatedData) error {
	privateSpec, err := instanceData.instanceConfig.GetPrivateSpec()
	if err != nil {
		return err
	}

	multiClusterK8sDashboardFeature := instanceData.orgFeatureStatuses.GetMultiClusterK8SDashboard().Enabled()

	privateSpecModified, err := modifyPrivateSpec(&privateSpec, instanceData, featuresAvailability{
		multiClusterK8sDashboard: multiClusterK8sDashboardFeature,
	})
	if err != nil {
		return err
	}

	if privateSpecModified {
		logging.GetContextLogger(ctx).Info("regenerating private spec")
		if err := instanceData.instanceConfig.SetPrivateSpec(privateSpec); err != nil {
			return err
		}

		if err = r.settings.RepoSet.ArgoCDInstanceConfigs().Update(ctx, instanceData.instanceConfig, models.ArgoCDInstanceConfigColumns.PrivateSpec); err != nil {
			return err
		}
	}

	return nil
}

// TODO(Marvin9): move to some better place
type featuresAvailability struct {
	multiClusterK8sDashboard bool
}

func modifyPrivateSpec(privateSpec *models.InstanceConfigPrivateSpec, instanceData *cdInstanceRelatedData, featureGates featuresAvailability) (privateSpecModified bool, err error) {
	if privateSpec.FqdnVersion == "" {
		privateSpec.FqdnVersion = "1"
		privateSpecModified = true
	}

	if privateSpec.K3sUsername == "" || privateSpec.K3sPassword == "" || privateSpec.RedisPassword == "" {
		username := k3sDBSchemaUsernamePrefix + instanceData.instanceConfig.InstanceID
		password, err := database.RandomAlphabetString()
		if err != nil {
			return false, err
		}
		privateSpec.K3sUsername = username
		privateSpec.K3sPassword = password
		privateSpec.RedisPassword = password
		privateSpecModified = true
	}

	if privateSpec.WebhookKey == "" || privateSpec.WebhookCert == "" {
		webhookKey, webhookCert, err := GetWebhookCert(instanceData.instanceConfig.InstanceID, false)
		if err != nil {
			return false, err
		}
		privateSpec.WebhookKey = webhookKey
		privateSpec.WebhookCert = webhookCert
		privateSpecModified = true
	} else {
		cert, err := ParseCert(privateSpec.WebhookCert)
		if err != nil {
			return false, err
		}
		// if webhook cert is expiring in 5 days, renew it
		if cert.NotAfter.Before(time.Now().AddDate(0, 0, 5)) {
			webhookKey, webhookCert, err := GetWebhookCert(instanceData.instanceConfig.InstanceID, false)
			if err != nil {
				return false, err
			}
			privateSpec.WebhookKey = webhookKey
			privateSpec.WebhookCert = webhookCert
			privateSpecModified = true
		}
	}

	if privateSpec.K3sToken == "" {
		k3sToken, err := client.NanoID(client.DefaultNanoIDLength)
		if err != nil {
			return false, err
		}
		privateSpec.K3sToken = k3sToken
		privateSpecModified = true
	}

	if privateSpec.ArgoCDServerSecretKey == "" {
		data, err := argocd.MakeServerSignature()
		if err != nil {
			return false, err
		}
		privateSpec.ArgoCDServerSecretKey = string(data)
		privateSpecModified = true
	}

	spec, err := instanceData.instanceConfig.GetSpec()
	if err != nil {
		return privateSpecModified, err
	}
	if spec.MultiClusterK8sDashboardEnabled && featureGates.multiClusterK8sDashboard && (privateSpec.KubeVisionArgoToken == "" || privateSpec.KubeVisionArgoTokenIssueTime.Add(360*24*time.Hour).Before(time.Now())) {
		kubeVisionArgoTokenID, err := client.NanoID(client.DefaultNanoIDLength)
		if err != nil {
			return privateSpecModified, err
		}
		now := time.Now()
		claims := jwt.RegisteredClaims{
			IssuedAt:  jwt.NewNumericDate(now),
			Issuer:    "argocd",
			NotBefore: jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(now.Add(365 * 24 * time.Hour)),
			Subject:   kubeVisionArgoAccountName + ":" + models.AccountCapabilityAPIKey,
			ID:        kubeVisionArgoTokenID,
		}
		kubeVisionArgoToken, err := jwt.NewWithClaims(jwt.SigningMethodHS256, claims).SignedString([]byte(privateSpec.ArgoCDServerSecretKey))
		if err != nil {
			return privateSpecModified, err
		}
		privateSpec.KubeVisionArgoToken = kubeVisionArgoToken
		privateSpec.KubeVisionArgoTokenID = kubeVisionArgoTokenID
		privateSpec.KubeVisionArgoTokenIssueTime = now
		return true, nil
	}
	if !spec.MultiClusterK8sDashboardEnabled && privateSpec.KubeVisionArgoToken != "" {
		privateSpec.KubeVisionArgoToken = ""
		privateSpec.KubeVisionArgoTokenID = ""
		privateSpec.KubeVisionArgoTokenIssueTime = time.Time{}
		return true, nil
	}
	return
}

const dropLegacyTriggerSQL = `
SELECT
    pg_catalog.pg_get_userbyid(proowner)
FROM
    pg_catalog.pg_namespace n
INNER JOIN
    pg_catalog.pg_proc p
ON pronamespace = n.oid
    WHERE proname = 'notify_kine_inserted' AND nspname = 'instance_%s'
`

func (r *argocdInstanceReconciler) dropLegacyFunctions(ctx context.Context, instanceID string, info *database.DBInfo) error {
	// The platform used to create function & trigger in instance schema using "main" user.
	// After migration to schema user we need to make sure previously created objects are deleted.
	row := r.settings.K3sDBRawClient.QueryRowContext(ctx, fmt.Sprintf(dropLegacyTriggerSQL, instanceID))
	var triggerOwner string
	if err := row.Scan(&triggerOwner); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil
		}
		return err
	}
	if triggerOwner == info.User {
		_, err := r.settings.K3sDBRawClient.ExecContext(ctx, fmt.Sprintf(`
DROP TRIGGER IF EXISTS on_kine_inserted ON instance_%[1]s.kine;
DROP FUNCTION IF EXISTS instance_%[1]s.notify_kine_inserted();`, instanceID))
		return err
	}
	return nil
}

type CertificateStatusGetter interface {
	CertificateStatus(ctx context.Context) (*agentclient.CertificateStatus, error)
}

func getCertificateStatus(ctx context.Context, tenant CertificateStatusGetter, internalUrl, fqdn string) (status.CertificateStatus, error) {
	st := status.CertificateStatus{}
	cname, err := net.LookupCNAME(fqdn)
	if err != nil {
		st.IsCNameSet = false
		st.Message = err.Error()
		return st, nil
	}
	cname = strings.TrimSuffix(cname, ".")
	if cname == internalUrl {
		st.IsCNameSet = true
		cert, err := tenant.CertificateStatus(ctx)
		if err != nil {
			return st, err
		}
		st.IsIssued = cert.IsIssued
		st.Message = cert.Message
		return st, nil
	}
	st.IsCNameSet = false
	st.Message = "CNAME is not set"
	return st, nil
}
