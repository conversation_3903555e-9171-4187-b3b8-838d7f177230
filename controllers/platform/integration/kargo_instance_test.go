package integration

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/volatiletech/null/v8"
	"k8s.io/client-go/rest"

	agentclient "github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/agent/pkg/client/apis/clusterupgrader"
	kargoagent "github.com/akuityio/agent/pkg/client/apis/kargo/clusteragent"
	kargoControlplane "github.com/akuityio/agent/pkg/client/apis/kargo/controlplane"
	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/agent/pkg/kube"
	tenanttesting "github.com/akuityio/akuity-platform/controllers/shared/tenant/testing"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/license"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/utils/io"
	"github.com/akuityio/akuity-platform/internal/utils/logging"
	"github.com/akuityio/akuity-platform/models/client"
	testinutil "github.com/akuityio/akuity-platform/models/client/testing"
	"github.com/akuityio/akuity-platform/models/models"
	modelsstatus "github.com/akuityio/akuity-platform/models/util/status"
	"github.com/akuityio/akuity-platform/test/utils"
)

func init() {
	utils.InitializeTestDataKey()
}

const initKargoTriggersStatementsCount = 5

type fakeKargoTenant struct {
	ReturnStatus agentclient.AggregatedHealthResponse

	ReceivedCplaneValues *kargoControlplane.DataValues
	ReceivedApplyOpts    *kube.ApplyOpts
	ReceivedAgentConfig  *agentclient.KargoAgentConfig
}

func (f *fakeKargoTenant) NewKargoTenant(string) (KargoTenant, error) {
	return f, nil
}

func (f *fakeKargoTenant) AkuityManagedAgentStatus(ctx context.Context, instanceID string) (*agentclient.AggregatedHealthResponse, error) {
	return &f.ReturnStatus, nil
}

func (f *fakeKargoTenant) Apply(ctx context.Context, cplaneValues *kargoControlplane.DataValues, applyOpts kube.ApplyOpts) error {
	f.ReceivedCplaneValues = cplaneValues
	f.ReceivedApplyOpts = &applyOpts
	return nil
}

func (f *fakeKargoTenant) ApplyAgent(ctx context.Context, clusterConfig agentclient.KargoAgentConfig, argocdConfig *rest.Config) error {
	f.ReceivedAgentConfig = &clusterConfig
	return nil
}

func (f *fakeKargoTenant) UpdateAgentStatusCM(ctx context.Context, agentName string, status *agentclient.AgentStatusData) error {
	return nil
}

func (f *fakeKargoTenant) Delete(ctx context.Context) error {
	return nil
}

func (f *fakeKargoTenant) DeleteAgent(context.Context, string, *rest.Config, bool) []error {
	return nil
}

func (f *fakeKargoTenant) DeleteAgentCredentials(ctx context.Context, name, currentSuffix string, argocdRestConfig *rest.Config) (bool, []error) {
	return false, nil
}

func (f *fakeKargoTenant) GetAgentDataValues(ctx context.Context, name, suffix string, argocdRestConfig *rest.Config, inClusterK3sService bool) (*kargoagent.DataValues, error) {
	return &kargoagent.DataValues{Kargo: &kargoagent.DataValuesKargo{}, RemoteArgocd: &kargoagent.DataValuesRemoteArgocd{}}, nil
}

func (f *fakeKargoTenant) GetGitOpsKubeConfig(ctx context.Context, fqdn string, inCluster bool) ([]byte, error) {
	return []byte{}, nil
}

func (f *fakeKargoTenant) InitializeAgentData(ctx context.Context, config agentclient.KargoAgentConfig, agentValues *kargoagent.DataValues, upgraderValues *clusterupgrader.DataValues) error {
	return nil
}

func (f *fakeKargoTenant) InitializeTenant(ctx context.Context, cplaneValues *kargoControlplane.DataValues, applyOpts kube.ApplyOpts, enableK3sCertCNReset bool) error {
	return nil
}

func (f *fakeKargoTenant) PruneAgent(ctx context.Context, agentName string, generation uint64, dryRun bool) error {
	return nil
}

func (f *fakeKargoTenant) PruneTenant(ctx context.Context, generation uint64, dryRun bool) error {
	return nil
}

func (f *fakeKargoTenant) Status(ctx context.Context) (*agentclient.AggregatedHealthResponse, error) {
	return &f.ReturnStatus, nil
}

func (f *fakeKargoTenant) CertificateStatus(ctx context.Context) (*agentclient.CertificateStatus, error) {
	return &agentclient.CertificateStatus{}, nil
}

func (f *fakeKargoTenant) AKPAdminServiceAccountToken(ctx context.Context) string {
	return "test-token"
}

func newTestKargoReconciler(t *testing.T, tenantPhase common.ArgoCDTenantPhase, sharedK3sDBConnectionAuth bool) (*kargoInstanceReconciler, client.RepoSet, *fakeKargoTenant) {
	portalDB, portalDBMock, err := sqlmock.New()
	require.NoError(t, err)

	k3sDB, k3sDBMock, err := sqlmock.New()
	require.NoError(t, err)

	portalDBMock.ExpectQuery(`.*`).WillReturnRows(sqlmock.NewRows([]string{"first_instance_id", "agents_count", "instances_count", "projects_count"}).AddRow(testInstanceId, 0, 1, 0))

	k3sDBMock.ExpectExec(`CREATE SCHEMA IF NOT EXISTS kargo_instance_` + testInstanceId).WillReturnResult(sqlmock.NewResult(0, 0))
	k3sDBMock.ExpectQuery(`.*`).WillReturnRows(sqlmock.NewRows([]string{"id"}))

	k3sDBMock.ExpectExec(fmt.Sprintf(`
CREATE USER kargo_instance_%[1]s WITH PASSWORD .*;
GRANT USAGE ON SCHEMA kargo_instance_%[1]s to kargo_instance_%[1]s;
GRANT CREATE ON SCHEMA kargo_instance_%[1]s to kargo_instance_%[1]s;
`, testInstanceId)).WillReturnResult(sqlmock.NewResult(0, 0))

	if sharedK3sDBConnectionAuth {
		k3sDBMock.ExpectExec(fmt.Sprintf(`ALTER TABLE IF EXISTS kargo_instance_%[1]s.kine OWNER TO kargo_instance_%[1]s;`,
			testInstanceId)).
			WillReturnResult(sqlmock.NewResult(0, 0))
	}
	k3sDBMock.ExpectExec(fmt.Sprintf(`CREATE OR REPLACE FUNCTION kargo_instance_%[1]s.notify_kine_inserted\(\) RETURNS TRIGGER AS .*`, testInstanceId)).WillReturnResult(sqlmock.NewResult(0, 0))
	k3sDBMock.ExpectExec(fmt.Sprintf(`DROP TRIGGER IF EXISTS on_kine_inserted ON kargo_instance_%[1]s.kine;`, testInstanceId)).WillReturnResult(sqlmock.NewResult(0, 0))

	for i := 0; i < initKargoTriggersStatementsCount; i++ {
		k3sDBMock.ExpectExec(`.*`).WillReturnResult(sqlmock.NewResult(0, 0))
	}
	repoSet := testinutil.NewInMemoryRepoSet()
	factory := fakeKargoTenant{ReturnStatus: agentclient.AggregatedHealthResponse{PriorityStatus: tenantPhase}}
	log, err := logging.NewLogger()
	require.NoError(t, err)
	repoSet.KargoInstancesRepo.Items[testInstanceId] = &models.KargoInstance{ID: testInstanceId, OrganizationOwner: null.StringFrom(testInstanceId)}
	instanceConfig := &models.KargoInstanceConfig{InstanceID: testInstanceId, Version: null.StringFrom("v1.0.0-unstable-20231001")}
	webhookKey, webhookCert, err := GetWebhookCert(testInstanceId, false)
	require.NoError(t, err)
	err = instanceConfig.SetPrivateSpec(models.KargoInstanceConfigPrivateSpec{
		K3sUsername: "kargo_instance_" + testInstanceId,
		K3sPassword: "my-password",
		FqdnVersion: "1",
		WebhookKey:  webhookKey,
		WebhookCert: webhookCert,
	})
	require.NoError(t, err)
	repoSet.KargoInstanceConfigsRepo.Items[testInstanceId] = instanceConfig
	repoSet.OrganizationsRepo.Items[testInstanceId] = &models.Organization{ID: testInstanceId}
	featureSvc := features.NewService(repoSet, nil, false, false, features.FeatureGatesSourceDev, license.License{})

	reconciler := NewKargoInstanceReconciler(&factory, ControllerSettings{
		InstanceSubDomains:        true,
		PortalDBRawClient:         portalDB,
		K3sDBRawClient:            k3sDB,
		RepoSet:                   repoSet,
		K3sDBConnection:           connectionString,
		SharedK3sDBConnectionAuth: sharedK3sDBConnectionAuth,
		DomainSuffix:              "akuity.cloud",
		InstanceConfig:            config.InstanceConfig{},
		IngressConfig:             config.IngressConfig{},
		Shard:                     "",
		PortalIPs:                 []string{},
		Log:                       &log,
	}, nil, tenanttesting.FakeKargoTenantStateClient{}, getImagePullSecret, 1*time.Hour, nil, featureSvc, []agentclient.ComponentVersion{{Version: "latest"}}, &agentclient.ComponentVersion{Version: "v1.0.0-unstable-20231001"})
	reconciler.sqlOpen = fakeSQLOpen(k3sDB)
	return reconciler, repoSet, &factory
}

func TestKargoReconcileSuccessful(t *testing.T) {
	reconciler, repoSet, factory := newTestKargoReconciler(t, common.TenantPhaseHealthy, false)
	defer io.Close(reconciler)

	require.NoError(t, reconciler.Reconcile(context.Background(), &models.KargoInstance{ID: testInstanceId, OrganizationOwner: null.StringFrom(testInstanceId)}))
	instance, err := repoSet.KargoInstances().GetByID(context.Background(), testInstanceId)
	require.NoError(t, err)

	status, err := instance.GetStatus()
	require.NoError(t, err)
	assert.Equal(t, modelsstatus.HealthStatusCodeHealthy, status.Health.Code)

	config, err := repoSet.KargoInstanceConfigs().GetByID(context.Background(), testInstanceId)
	require.NoError(t, err)
	privateSpec, err := config.GetPrivateSpec()
	require.NoError(t, err)
	assert.Equal(t, "kargo_instance_"+testInstanceId, privateSpec.K3sUsername)
	assert.Equal(t, "my-password", privateSpec.K3sPassword)
	assert.Equal(t, testInstanceId+"-cplane.kargosvcs.akuity.cloud", *factory.ReceivedCplaneValues.Ingress.K3sProxy.Fqdn)
	assert.Equal(t, testInstanceId+".kargo.akuity.cloud", *factory.ReceivedCplaneValues.Ingress.KargoApi.Fqdn)
	assert.Equal(t, "test-token", privateSpec.AkpAdminKargoToken)
}

func TestKargoReconcileProgessing(t *testing.T) {
	reconciler, repoSet, factory := newTestKargoReconciler(t, common.TenantPhaseProgressing, false)
	defer io.Close(reconciler)

	require.NoError(t, reconciler.Reconcile(context.Background(), &models.KargoInstance{ID: testInstanceId, OrganizationOwner: null.StringFrom(testInstanceId)}))

	instance, err := repoSet.KargoInstances().GetByID(context.Background(), testInstanceId)
	require.NoError(t, err)
	status, err := instance.GetStatus()
	require.NoError(t, err)
	assert.False(t, status.Conditions.IsEstablished(models.InstanceConditionTypePruned), "not healthy tenant should not be pruned")

	assert.Equal(t, modelsstatus.HealthStatusCodeProgressing, status.Health.Code)

	config, err := repoSet.KargoInstanceConfigs().GetByID(context.Background(), testInstanceId)
	require.NoError(t, err)
	privateSpec, err := config.GetPrivateSpec()
	require.NoError(t, err)
	assert.Equal(t, "kargo_instance_"+testInstanceId, privateSpec.K3sUsername)
	assert.NotEmpty(t, privateSpec.K3sPassword)
	assert.Equal(t, testInstanceId+"-cplane.kargosvcs.akuity.cloud", *factory.ReceivedCplaneValues.Ingress.K3sProxy.Fqdn)
	assert.Equal(t, testInstanceId+".kargo.akuity.cloud", *factory.ReceivedCplaneValues.Ingress.KargoApi.Fqdn)
}

func TestKargoInstanceItemToID(t *testing.T) {
	reconciler, _, _ := newTestKargoReconciler(t, common.TenantPhaseProgressing, false)
	defer io.Close(reconciler)

	id := reconciler.ItemToID(&models.KargoInstance{ID: testInstanceId, OrganizationOwner: null.StringFrom(testInstanceId)})
	assert.Equal(t, testInstanceId, id)
}
