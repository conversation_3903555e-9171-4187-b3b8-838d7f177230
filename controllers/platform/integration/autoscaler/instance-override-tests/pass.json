{"k3s": {"resources": {"limits": {"memory": "500Mi"}, "requests": {"cpu": "50m", "memory": "500Mi"}}, "autoscaling": {"max_replicas": 1, "min_replicas": 1}}, "webhook": {"resources": {"requests": {"cpu": "200m", "memory": "50Mi"}}}, "k3s_proxy": {"replicas": 1, "resources": {"requests": {"cpu": "20m", "memory": "75Mi"}}}, "argocd_server": {"resources": {"requests": {"cpu": "1", "memory": "1Gi"}}}, "kustomization": {"kind": "Kustomization", "patches": [{"patch": "- op: add\n  path: /spec/ingress/-\n  value:\n    from:\n     - namespaceSelector:\n         matchLabels:\n           kubernetes.io/metadata.name: monitoring", "target": {"kind": "NetworkPolicy", "name": "agent-server-network-policy", "group": "networking.k8s.io"}}], "apiVersion": "kustomize.config.k8s.io/v1beta1"}}