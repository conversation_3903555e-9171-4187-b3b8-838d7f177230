unknown resource type "redis.large" in "k3s.xlarge, k3s-proxy.xlarge, application-controller.small, redis.large"
-------------------------------------------------
k3s.xlarge: "cpu: 3, memory: 3Gi, replicas: 4"
k3s-proxy.xlarge: "cpu: 3, memory: 3Gi, replicas: 4"
application-controller.small:  "cpu: 0.01, memory: 50Mi"
redis.small: "cpu: 0.1, memory: 750Mi"

# Unknown "redis.large" type
threshold.apps.150: k3s.xlarge, k3s-proxy.xlarge, application-controller.small, redis.large
