duplicate Redis type "redis.small" in "k3s.small, k3s-proxy.small, application-controller.small, redis.small, redis.small"
-------------------------------------------------
k3s.small: "cpu: 0.1, memory: 750Mi, replicas: 2"
k3s-proxy.small: "cpu: 0.1, memory: 750Mi, replicas: 2"
application-controller.small:  "cpu: 0.01, memory: 50Mi"
redis.small: "cpu: 0.1, memory: 750Mi"

# Duplicate "redis.small" type
threshold.apps.150: k3s.small, k3s-proxy.small, application-controller.small, redis.small, redis.small
