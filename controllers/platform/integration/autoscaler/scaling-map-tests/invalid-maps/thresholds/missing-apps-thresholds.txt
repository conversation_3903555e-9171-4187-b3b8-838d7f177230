missing Applications thresholds
-------------------------------------------------
k3s.small: "cpu: 0.1, memory: 750Mi, replicas: 2"
k3s.medium: "cpu: 0.5, memory: 1.5Gi, replicas: 2"
k3s.large: "cpu: 1, memory: 2Gi, replicas: 3"
k3s.xlarge: "cpu: 3, memory: 3Gi, replicas: 4"

k3s-proxy.small: "cpu: 0.1, memory: 750Mi, replicas: 2"
k3s-proxy.medium: "cpu: 0.5, memory: 1.5Gi, replicas: 2"
k3s-proxy.large: "cpu: 1, memory: 2Gi, replicas: 3"
k3s-proxy.xlarge: "cpu: 3, memory: 3Gi, replicas: 4"

application-controller.small:  "cpu: 0.01, memory: 50Mi"
application-controller.medium: "cpu: 0.05, memory: 250Mi"
application-controller.large:  "cpu: 0.2, memory: 500Mi"
application-controller.xlarge: "cpu: 3.0, memory: 1.5Gi"

redis.small: "cpu: 0.1, memory: 750Mi"
redis.medium: "cpu: 0.5, memory: 1.5Gi"
redis.large: "cpu: 1, memory: 2Gi"

threshold.clusters.10: k3s.small, k3s-proxy.small
threshold.clusters.20: k3s.medium, k3s-proxy.medium
threshold.clusters.50: k3s.large, k3s-proxy.large
threshold.clusters.300: k3s.xlarge, k3s-proxy.xlarge

# Missing Apps thresholds

threshold.resources.1000: k3s.small, k3s-proxy.small
threshold.resources.3000: k3s.medium, k3s-proxy.medium
threshold.resources.10000: k3s.large, k3s-proxy.large
threshold.resources.40000: k3s.xlarge, k3s-proxy.xlarge
