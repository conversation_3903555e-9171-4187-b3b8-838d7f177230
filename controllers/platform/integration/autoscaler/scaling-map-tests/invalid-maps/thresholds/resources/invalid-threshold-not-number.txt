unable to parse "threshold.resources.aaa" threshold value "aaa" to int64: strconv.ParseInt
-------------------------------------------------
k3s.small: "cpu: 0.1, memory: 750Mi, replicas: 2"
k3s-proxy.small: "cpu: 0.1, memory: 750Mi, replicas: 2"
application-controller.small:  "cpu: 0.01, memory: 50Mi"
redis.small: "cpu: 0.1, memory: 750Mi"

# Invalid threshold format - value is not a number
threshold.resources.aaa: k3s.small
