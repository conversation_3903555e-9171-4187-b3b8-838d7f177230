unknown resource type "redis.medium" in "k3s.small, k3s-proxy.small, redis.medium"
-------------------------------------------------
k3s.small: "cpu: 0.1, memory: 750Mi, replicas: 2"
k3s-proxy.small: "cpu: 0.1, memory: 750Mi, replicas: 2"
application-controller.small:  "cpu: 0.01, memory: 50Mi"
redis.small: "cpu: 0.1, memory: 750Mi"

threshold.clusters.10: k3s.small, k3s-proxy.small
threshold.apps.150: k3s.small, k3s-proxy.small, application-controller.small, redis.small
threshold.resources.1000: k3s.small, k3s-proxy.small

# "redis.medium" is not mapped in the instance override
instance.id: k3s.small, k3s-proxy.small, redis.medium
