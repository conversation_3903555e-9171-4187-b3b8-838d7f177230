package autoscaler

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestCompareMaps(t *testing.T) {
	assert.True(t, identicalMaps(map[string]any{}, map[string]any{}))
	assert.True(t, identicalMaps(map[string]any{"a": "b"}, map[string]any{"a": "b"}))
	assert.False(t, identicalMaps(map[string]any{"a": "b"}, map[string]any{"a": "c"}))
	assert.False(t, identicalMaps(map[string]any{"a": "b"}, map[string]any{"b": "a"}))
	assert.False(t, identicalMaps(map[string]any{"a": "b"}, map[string]any{"a": "b", "a1": "b1"}))
	assert.True(t, identicalMaps(map[string]any{"a": "b", "a1": "b1"}, map[string]any{"a1": "b1", "a": "b"}))
	assert.False(t, identicalMaps(map[string]any{"a": "b", "a1": "b1"}, map[string]any{"a": "b"}))
}

func TestValidateInstanceOverridePass(t *testing.T) {
	overrides, err := os.ReadFile("instance-override-tests/pass.json")
	require.NoError(t, err)
	overridesMaps, err := ValidateInstanceOverride(overrides)
	require.NoError(t, err)
	assert.Len(t, overridesMaps, 5)
	assert.Len(t, overridesMaps["k3s"], 2)
	assert.Len(t, overridesMaps["webhook"], 1)
	assert.Len(t, overridesMaps["k3s_proxy"], 2)
	assert.Len(t, overridesMaps["argocd_server"], 1)
	assert.Len(t, overridesMaps["kustomization"], 3)
}

func TestValidateInstanceOverrideUnknownKey(t *testing.T) {
	overrides, err := os.ReadFile("instance-override-tests/unknown-key.json")
	require.NoError(t, err)
	overridesMaps, err := ValidateInstanceOverride(overrides)
	require.Nil(t, overridesMaps)
	require.Error(t, err)
	require.Contains(t, err.Error(), "produced a result that is different from the original override")
}

func TestValidateInstanceOverrideEmpty(t *testing.T) {
	overrides, err := os.ReadFile("instance-override-tests/empty.json")
	require.NoError(t, err)
	overridesMaps, err := ValidateInstanceOverride(overrides)
	require.Nil(t, overridesMaps)
	require.Error(t, err)
	require.Contains(t, err.Error(), "instance override is empty")
}
