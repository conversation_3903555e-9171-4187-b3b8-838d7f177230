package autoscaler

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-logr/logr"
	"k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	"github.com/akuityio/akuity-platform/internal/utils/consts"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

const (
	nsName                         = consts.AkuityPlatformNamespace
	configMapReadTimeout           = 20 * time.Second
	autoscalerConfigMapName        = "autoscaler"
	instanceOverridesConfigMapName = "instance-overrides"
	arrowStringFormat              = "%s => %s"
)

func NewArgoCDInstanceAutoscaler(log *logr.Logger, k8sClient kubernetes.Interface, repoSet client.RepoSet) (*ArgocdInstanceAutoscaler, error) {
	if log == nil || k8sClient == nil || repoSet == nil {
		return nil, fmt.Errorf("nil value passed to an autoscaler.NewArgoCDInstanceAutoscaler(%v, %v, %v)", log, k8sClient, repoSet)
	}

	ctx, cancel := context.WithTimeout(context.Background(), configMapReadTimeout)
	defer cancel()

	configMap, err := k8sClient.CoreV1().ConfigMaps(nsName).Get(ctx, autoscalerConfigMapName, v1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to read Autoscaler ConfigMap '%s:%s': %w", nsName, autoscalerConfigMapName, err)
	}

	scalingMap, err := newDynamicScalingMap(configMap.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to build a dynamic scaling map from ConfigMap (%s) '%s:%s': %w",
			configMap.Data, nsName, autoscalerConfigMapName, err)
	}

	log.Info(fmt.Sprintf("Autoscaler constructed from '%s:%s' ConfigMap", nsName, autoscalerConfigMapName), "data", configMap.Data)

	return &ArgocdInstanceAutoscaler{
		log:        log,
		repoSet:    repoSet,
		k8sClient:  k8sClient,
		ScalingMap: scalingMap,
	}, nil
}

// UpdateInstanceConfig - updates InstanceConfig.InternalSpec with the new K3s, K3s Proxy and Redis types
func (a *ArgocdInstanceAutoscaler) UpdateInstanceConfig(ctx context.Context, instance *models.ArgoCDInstance, instanceConfig *models.ArgoCDInstanceConfig) (bool, error) {
	if ctx == nil || instance == nil || instanceConfig == nil {
		return false, fmt.Errorf("nil value passed to an autoscaler.UpdateInstanceConfig(%v, %v, %v)", ctx, instance, instanceConfig)
	}

	if _, ok := a.ScalingMap.orgsDisabled[instance.OrganizationOwner]; ok {
		// Org is opted-out from the Autoscaler
		return false, nil
	}

	if len(a.ScalingMap.orgsEnabled) > 0 {
		// Opt-in mechanism is active.
		// If orgsEnabled is empty - opt-in mechanism is not active and *all* Orgs are opted-in.
		if _, ok := a.ScalingMap.orgsEnabled[instance.OrganizationOwner]; !ok {
			// .. but this Org isn't opted-in
			return false, nil
		}
	}

	instanceData, err := a.buildInstanceData(ctx, instance, instanceConfig)
	if err != nil {
		return false, fmt.Errorf("failed to read tenant's %q instance properties: %w", instance.ID, err)
	}

	currentK3sType := instanceData.internalSpec.K3sType
	currentK3sProxyType := instanceData.internalSpec.K3sProxyType
	currentApplicationControllerType := instanceData.internalSpec.ApplicationControllerType
	currentRedisType := instanceData.internalSpec.RedisType

	newK3sType := a.newK3sType(instanceData, currentK3sType)
	newK3sProxyType := a.newK3sProxyType(instanceData, currentK3sProxyType)
	newApplicationControllerType := a.newApplicationControllerType(instanceData, currentApplicationControllerType)
	newRedisType := a.newRedisType(instanceData, currentRedisType)

	sameK3sType := newK3sType == currentK3sType
	sameK3sProxyType := newK3sProxyType == currentK3sProxyType
	sameApplicationControllerType := newApplicationControllerType == currentApplicationControllerType
	sameRedisType := newRedisType == currentRedisType

	instanceOverrideApplied := false
	if instanceOverridesConfigMap, err := a.k8sClient.CoreV1().ConfigMaps(nsName).Get(ctx, instanceOverridesConfigMapName, v1.GetOptions{}); err == nil {
		// Instance overrides Map entries are "<InstanceId>.json" => instance override values JSON
		// See akuity-platform-deploy/env/prod/aws/akuity-platform/instance-overrides
		overrideEntryName := fmt.Sprintf("%s.json", instance.ID)
		if overrideValues, ok := instanceOverridesConfigMap.Data[overrideEntryName]; ok {
			instanceOverrideValues, err := ValidateInstanceOverride([]byte(overrideValues))
			if err != nil {
				return false, err
			}
			if !identicalMaps(instanceData.internalSpec.Values, instanceOverrideValues) {
				instanceData.internalSpec.Values = instanceOverrideValues
				instanceOverrideApplied = true
				a.log.Info("Instance override applied",
					"instanceId", instanceData.id,
					"instanceOverride", instanceOverrideValues)
			}
		}
	}

	if sameK3sType && sameK3sProxyType && sameApplicationControllerType && sameRedisType && (!instanceOverrideApplied) {
		// No change in types and no instance override applied - update is unnecessary
		return false, nil
	}

	a.log.Info("Applying new auto-scaling config", "instance_id", instanceData.id, "before", map[string]string{
		"k3s":                    currentK3sType,
		"k3s-proxy":              currentK3sProxyType,
		"application-controller": currentApplicationControllerType,
		"redis":                  currentRedisType,
	}, "after", map[string]string{
		"k3s":                    newK3sType,
		"k3s-proxy":              newK3sProxyType,
		"application-controller": newApplicationControllerType,
		"redis":                  newRedisType,
	})

	instanceData.internalSpec.K3sType = newK3sType
	instanceData.internalSpec.K3sProxyType = newK3sProxyType
	instanceData.internalSpec.ApplicationControllerType = newApplicationControllerType
	instanceData.internalSpec.RedisType = newRedisType

	if err := instanceConfig.SetInternalSpec(instanceData.internalSpec); err != nil {
		return false, err
	}

	if err := a.repoSet.ArgoCDInstanceConfigs().Update(ctx, instanceConfig, models.ArgoCDInstanceConfigColumns.InternalSpec); err != nil {
		return false, err
	}

	return true, nil
}

// buildInstanceData - populates and returns argocdInstanceData{} with instance-related properties
func (a *ArgocdInstanceAutoscaler) buildInstanceData(ctx context.Context, instance *models.ArgoCDInstance, instanceConfig *models.ArgoCDInstanceConfig) (*argocdInstanceData, error) {
	instanceData := &argocdInstanceData{id: instance.ID}

	// Getting ArgoCDInstanceInfo => number of Argo Apps and K8s resources

	instanceInfo := models.ArgoCDInstanceInfo{}

	if !instance.StatusInfo.IsZero() {
		if err := json.Unmarshal(instance.StatusInfo.JSON, &instanceInfo); err != nil {
			return nil, fmt.Errorf("failed to unmarshal tenant's %q instance.StatusInfo: %w", instanceData.id, err)
		}
	}

	instanceData.tenantApplications = uint32(instanceInfo.ApplicationsStatus.Count)
	instanceData.applicationK8sResources = uint32(instanceInfo.ApplicationsStatus.ResourcesCount)

	// Getting a number of connected Clusters

	tenantClusters, err := a.repoSet.ArgoCDClusters().Filter(models.ArgoCDClusterWhere.InstanceID.EQ(instanceData.id),
		models.ArgoCDClusterWhere.StatusAgentState.IsNotNull()).ListAll(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to list tenant %q clusters: %w", instanceData.id, err)
	}

	instanceData.connectedClusters = 0

	for _, tenantCluster := range tenantClusters {
		if agentState, err := tenantCluster.GetAgentState(false); err != nil {
			return nil, fmt.Errorf("failed to get agent state of cluster %q, tenant %q: %w",
				tenantCluster.ID, instanceData.id, err)
		} else if agentState != nil {
			// agentState is nil when it's missing (the cluster was never connected) or stale (the cluster is disconnected)
			instanceData.connectedClusters++
		}
	}

	// Getting InternalSpec

	internalSpec, err := instanceConfig.GetInternalSpec()
	if err != nil {
		return nil, fmt.Errorf("failed to read tenant's %q instanceConfig.InternalSpec: %w", instanceData.id, err)
	}

	instanceData.internalSpec = internalSpec

	return instanceData, nil
}

// newK3sType - returns the new K3s type based on the number of tenant's Clusters, Argo Applications and K8s Resources
func (a *ArgocdInstanceAutoscaler) newK3sType(instanceData *argocdInstanceData, currentK3sType string) string {
	newK3sType := a.ScalingMap.selectK3sType(instanceData.id, instanceData.connectedClusters, instanceData.tenantApplications, instanceData.applicationK8sResources, currentK3sType)

	if currentK3sType == newK3sType {
		return currentK3sType
	}

	scalingDirection := "up"
	if a.ScalingMap.compareK3sTypes(newK3sType, currentK3sType) < 0 {
		// newK3sType < currentK3sType
		scalingDirection = "down"
	}

	a.log.Info(fmt.Sprintf("scaling %s a K3s type", scalingDirection),
		"instanceId", instanceData.id,
		"k3sType", fmt.Sprintf(arrowStringFormat, currentK3sType, newK3sType),
		"connectedClusters", instanceData.connectedClusters,
		"tenantApplications", instanceData.tenantApplications,
		"applicationK8sResources", instanceData.applicationK8sResources)

	return newK3sType
}

// newK3sProxyType - returns the new K3s Proxy type based on the number of tenant's Clusters, Argo Applications and K8s Resources
func (a *ArgocdInstanceAutoscaler) newK3sProxyType(instanceData *argocdInstanceData, currentK3sProxyType string) string {
	newK3sProxyType := a.ScalingMap.selectK3sProxyType(instanceData.id, instanceData.connectedClusters, instanceData.tenantApplications, instanceData.applicationK8sResources, currentK3sProxyType)

	if currentK3sProxyType == newK3sProxyType {
		return currentK3sProxyType
	}

	scalingDirection := "up"
	if a.ScalingMap.compareK3sProxyTypes(newK3sProxyType, currentK3sProxyType) < 0 {
		// newK3sProxyType < currentK3sProxyType
		scalingDirection = "down"
	}

	a.log.Info(fmt.Sprintf("scaling %s a K3s Proxy type", scalingDirection),
		"instanceId", instanceData.id,
		"k3sProxyType", fmt.Sprintf(arrowStringFormat, currentK3sProxyType, newK3sProxyType),
		"connectedClusters", instanceData.connectedClusters,
		"tenantApplications", instanceData.tenantApplications,
		"applicationK8sResources", instanceData.applicationK8sResources)

	return newK3sProxyType
}

// newApplicationControllerType - returns the new ApplicationController type based on the number of tenant's Argo Applications
func (a *ArgocdInstanceAutoscaler) newApplicationControllerType(instanceData *argocdInstanceData, currentApplicationControllerType string) string {
	newApplicationControllerType := a.ScalingMap.selectApplicationControllerType(instanceData.id, instanceData.tenantApplications, currentApplicationControllerType)

	if currentApplicationControllerType == newApplicationControllerType {
		return currentApplicationControllerType
	}

	scalingDirection := "up"
	if a.ScalingMap.compareApplicationControllerTypes(newApplicationControllerType, currentApplicationControllerType) < 0 {
		// newK3sProxyType < currentK3sProxyType
		scalingDirection = "down"
	}

	a.log.Info(fmt.Sprintf("scaling %s an Application Controller type", scalingDirection),
		"instanceId", instanceData.id,
		"applicationControllerType", fmt.Sprintf(arrowStringFormat, currentApplicationControllerType, newApplicationControllerType),
		"tenantApplications", instanceData.tenantApplications)

	return newApplicationControllerType
}

// newRedisType - returns the new Redis type based on the number of tenant's Argo Applications
func (a *ArgocdInstanceAutoscaler) newRedisType(instanceData *argocdInstanceData, currentRedisType string) string {
	newRedisType := a.ScalingMap.selectRedisType(instanceData.id, instanceData.tenantApplications, currentRedisType)

	if currentRedisType == newRedisType {
		return currentRedisType
	}

	scalingDirection := "up"
	if a.ScalingMap.compareRedisTypes(newRedisType, currentRedisType) < 0 {
		// newRedisType < currentRedisType
		scalingDirection = "down"
	}

	a.log.Info(fmt.Sprintf("scaling %s a Redis type", scalingDirection),
		"instanceId", instanceData.id,
		"redisType", fmt.Sprintf(arrowStringFormat, currentRedisType, newRedisType),
		"tenantApplications", instanceData.tenantApplications)

	return newRedisType
}
