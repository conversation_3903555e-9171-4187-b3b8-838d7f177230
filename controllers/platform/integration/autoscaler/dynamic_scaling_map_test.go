package autoscaler

import (
	"os"
	"path"
	"strconv"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

const (
	testConfigMap  = "scaling-map-tests/test-configmap.yaml"
	chartConfigMap = "../../../../charts/akuity-platform/templates/platform-controller/autoscaler-cm.yaml"
)

var (
	configMapIgnorePrefixes = []string{"apiVersion:", "kind:", "metadata:", "name:", "namespace:", "data:"}

	scalingMap *dynamicScalingMap
)

// Lines to ignore when loading the ConfigMap from the file

func Setup(t *testing.T) {
	if scalingMap == nil {
		var err error
		scalingMap, err = readScalingMapFromFile(t, testConfigMap)
		require.NoError(t, err)
		require.Len(t, scalingMap.K3sTypes, 7)
		require.Len(t, scalingMap.K3sProxyTypes, 4)
		require.Len(t, scalingMap.ApplicationControllerTypes, 4)
		require.Len(t, scalingMap.RedisTypes, 4)
		require.Len(t, scalingMap.clustersThresholds, 4)
		require.Len(t, scalingMap.appsThresholds, 4)
		require.Len(t, scalingMap.resourcesThresholds, 4)
		require.Len(t, scalingMap.instanceOverrides, 8)
		require.Len(t, scalingMap.orgsEnabled, 2)
		require.Len(t, scalingMap.orgsDisabled, 2)
	}
}

func TestChartConfigMap(t *testing.T) {
	chartScalingMap, err := readScalingMapFromFile(t, chartConfigMap)
	require.NoError(t, err)
	require.Len(t, chartScalingMap.K3sTypes, 4)
	require.Len(t, chartScalingMap.K3sProxyTypes, 4)
	require.Len(t, chartScalingMap.ApplicationControllerTypes, 4)
	require.Len(t, chartScalingMap.RedisTypes, 4)
	require.Len(t, chartScalingMap.clustersThresholds, 3)
	require.Len(t, chartScalingMap.appsThresholds, 5)
	require.Len(t, chartScalingMap.resourcesThresholds, 3)
}

func TestCompareK3sTypes(t *testing.T) {
	Setup(t)

	compareTypes := scalingMap.compareK3sTypes

	t.Run("Unknown Types", func(t *testing.T) {
		assert.Zero(t, compareTypes("", ""))
		assert.Zero(t, compareTypes("redis.small", "redis.medium"))
		assert.Negative(t, compareTypes("unknown", "k3s.small"))
		assert.Positive(t, compareTypes("k3s.small", "unknown"))
	})

	t.Run("Known Types - Less Than", func(t *testing.T) {
		assert.Negative(t, compareTypes("k3s.small.1", "k3s.small"))
		assert.Negative(t, compareTypes("k3s.small", "k3s.small.3"))
		assert.Negative(t, compareTypes("k3s.small", "k3s.medium"))
		assert.Negative(t, compareTypes("k3s.small", "k3s.large"))
		assert.Negative(t, compareTypes("k3s.small", "k3s.xlarge"))

		assert.Negative(t, compareTypes("k3s.medium", "k3s.large"))
		assert.Negative(t, compareTypes("k3s.medium", "k3s.xlarge"))

		assert.Negative(t, compareTypes("k3s.large", "k3s.xlarge"))
	})

	t.Run("Known Types - Equal", func(t *testing.T) {
		assert.Zero(t, compareTypes("k3s.small", "k3s.small"))
		assert.Zero(t, compareTypes("k3s.small", "k3s.small.2"))
		assert.Zero(t, compareTypes("k3s.small.2", "k3s.small"))
		assert.Zero(t, compareTypes("k3s.medium", "k3s.medium"))
		assert.Zero(t, compareTypes("k3s.large", "k3s.large"))
		assert.Zero(t, compareTypes("k3s.xlarge", "k3s.xlarge"))
	})

	t.Run("Known Types - Greater Than", func(t *testing.T) {
		assert.Positive(t, compareTypes("k3s.small", "k3s.small.1"))
		assert.Positive(t, compareTypes("k3s.small.3", "k3s.small"))
		assert.Positive(t, compareTypes("k3s.medium", "k3s.small"))
		assert.Positive(t, compareTypes("k3s.large", "k3s.small"))
		assert.Positive(t, compareTypes("k3s.xlarge", "k3s.small"))

		assert.Positive(t, compareTypes("k3s.large", "k3s.medium"))
		assert.Positive(t, compareTypes("k3s.xlarge", "k3s.medium"))

		assert.Positive(t, compareTypes("k3s.xlarge", "k3s.large"))
	})
}

func TestCompareK3sProxyTypes(t *testing.T) {
	Setup(t)

	compareTypes := scalingMap.compareK3sProxyTypes

	t.Run("Unknown Types", func(t *testing.T) {
		assert.Zero(t, compareTypes("", ""))
		assert.Zero(t, compareTypes("redis.small", "redis.medium"))
		assert.Negative(t, compareTypes("unknown", "k3s-proxy.small"))
		assert.Positive(t, compareTypes("k3s-proxy.small", "unknown"))
	})

	t.Run("Known Types - Less Than", func(t *testing.T) {
		assert.Negative(t, compareTypes("k3s-proxy.small", "k3s-proxy.medium"))
		assert.Negative(t, compareTypes("k3s-proxy.small", "k3s-proxy.large"))
		assert.Negative(t, compareTypes("k3s-proxy.small", "k3s-proxy.xlarge"))

		assert.Negative(t, compareTypes("k3s-proxy.medium", "k3s-proxy.large"))
		assert.Negative(t, compareTypes("k3s-proxy.medium", "k3s-proxy.xlarge"))

		assert.Negative(t, compareTypes("k3s-proxy.large", "k3s-proxy.xlarge"))
	})

	t.Run("Known Types - Equal", func(t *testing.T) {
		assert.Zero(t, compareTypes("k3s-proxy.small", "k3s-proxy.small"))
		assert.Zero(t, compareTypes("k3s-proxy.medium", "k3s-proxy.medium"))
		assert.Zero(t, compareTypes("k3s-proxy.large", "k3s-proxy.large"))
		assert.Zero(t, compareTypes("k3s-proxy.xlarge", "k3s-proxy.xlarge"))
	})

	t.Run("Known Types - Greater Than", func(t *testing.T) {
		assert.Positive(t, compareTypes("k3s-proxy.medium", "k3s-proxy.small"))
		assert.Positive(t, compareTypes("k3s-proxy.large", "k3s-proxy.small"))
		assert.Positive(t, compareTypes("k3s-proxy.xlarge", "k3s-proxy.small"))

		assert.Positive(t, compareTypes("k3s-proxy.large", "k3s-proxy.medium"))
		assert.Positive(t, compareTypes("k3s-proxy.xlarge", "k3s-proxy.medium"))

		assert.Positive(t, compareTypes("k3s-proxy.xlarge", "k3s-proxy.large"))
	})
}

func TestCompareApplicationControllerTypes(t *testing.T) {
	Setup(t)

	compareTypes := scalingMap.compareApplicationControllerTypes

	t.Run("Unknown Types", func(t *testing.T) {
		assert.Zero(t, compareTypes("", ""))
		assert.Zero(t, compareTypes("k3s.large", "k3s.medium"))
		assert.Negative(t, compareTypes("unknown", "application-controller.small"))
		assert.Positive(t, compareTypes("application-controller.small", "unknown"))
	})

	t.Run("Known Types - Less Than", func(t *testing.T) {
		assert.Negative(t, compareTypes("application-controller.small", "application-controller.medium"))
		assert.Negative(t, compareTypes("application-controller.small", "application-controller.large"))
		assert.Negative(t, compareTypes("application-controller.small", "application-controller.xlarge"))

		assert.Negative(t, compareTypes("application-controller.medium", "application-controller.large"))
		assert.Negative(t, compareTypes("application-controller.medium", "application-controller.xlarge"))

		assert.Negative(t, compareTypes("application-controller.large", "application-controller.xlarge"))
	})

	t.Run("Known Types - Equal", func(t *testing.T) {
		assert.Zero(t, compareTypes("application-controller.small", "application-controller.small"))
		assert.Zero(t, compareTypes("application-controller.medium", "application-controller.medium"))
		assert.Zero(t, compareTypes("application-controller.large", "application-controller.large"))
		assert.Zero(t, compareTypes("application-controller.xlarge", "application-controller.xlarge"))
	})

	t.Run("Known Types - Greater Than", func(t *testing.T) {
		assert.Positive(t, compareTypes("application-controller.medium", "application-controller.small"))
		assert.Positive(t, compareTypes("application-controller.large", "application-controller.small"))
		assert.Positive(t, compareTypes("application-controller.xlarge", "application-controller.small"))

		assert.Positive(t, compareTypes("application-controller.large", "application-controller.medium"))
		assert.Positive(t, compareTypes("application-controller.xlarge", "application-controller.medium"))

		assert.Positive(t, compareTypes("application-controller.xlarge", "application-controller.large"))
	})
}

func TestCompareRedisTypes(t *testing.T) {
	Setup(t)

	compareTypes := scalingMap.compareRedisTypes

	t.Run("Unknown Types", func(t *testing.T) {
		assert.Zero(t, compareTypes("", ""))
		assert.Zero(t, compareTypes("k3s.large", "k3s.medium"))
		assert.Negative(t, compareTypes("unknown", "redis.small"))
		assert.Positive(t, compareTypes("redis.small", "unknown"))
	})

	t.Run("Known Types - Less Than", func(t *testing.T) {
		assert.Negative(t, compareTypes("redis.small", "redis.medium"))
		assert.Negative(t, compareTypes("redis.small", "redis.large"))
		assert.Negative(t, compareTypes("redis.small", "redis.xlarge"))

		assert.Negative(t, compareTypes("redis.medium", "redis.large"))
		assert.Negative(t, compareTypes("redis.medium", "redis.xlarge"))

		assert.Negative(t, compareTypes("redis.large", "redis.xlarge"))
	})

	t.Run("Known Types - Equal", func(t *testing.T) {
		assert.Zero(t, compareTypes("redis.small", "redis.small"))
		assert.Zero(t, compareTypes("redis.medium", "redis.medium"))
		assert.Zero(t, compareTypes("redis.large", "redis.large"))
		assert.Zero(t, compareTypes("redis.xlarge", "redis.xlarge"))
	})

	t.Run("Known Types - Greater Than", func(t *testing.T) {
		assert.Positive(t, compareTypes("redis.medium", "redis.small"))
		assert.Positive(t, compareTypes("redis.large", "redis.small"))
		assert.Positive(t, compareTypes("redis.xlarge", "redis.small"))

		assert.Positive(t, compareTypes("redis.large", "redis.medium"))
		assert.Positive(t, compareTypes("redis.xlarge", "redis.medium"))

		assert.Positive(t, compareTypes("redis.xlarge", "redis.large"))
	})
}

func TestSelectThreshold(t *testing.T) {
	Setup(t)

	clustersThresholds := scalingMap.clustersThresholds
	resourcesThresholds := scalingMap.resourcesThresholds
	appsThresholds := scalingMap.appsThresholds
	selectThreshold := scalingMap.selectThreshold
	scalingDown := func(t autoscalerThreshold) bool { return true }
	scalingUp := func(t autoscalerThreshold) bool { return false }

	assert.Equal(t, "", selectThreshold(5, nil, scalingUp).types.k3sType)
	assert.Equal(t, "", selectThreshold(5, []autoscalerThreshold{}, scalingUp).types.k3sType)

	t.Run("Scaling Up", func(t *testing.T) {
		assert.Equal(t, "k3s.small", selectThreshold(0, resourcesThresholds, scalingUp).types.k3sType)
		assert.Equal(t, "k3s.small", selectThreshold(800, resourcesThresholds, scalingUp).types.k3sType)
		assert.Equal(t, "k3s.small", selectThreshold(1000, resourcesThresholds, scalingUp).types.k3sType)

		assert.Equal(t, "k3s.medium", selectThreshold(1001, resourcesThresholds, scalingUp).types.k3sType)
		assert.Equal(t, "k3s.medium", selectThreshold(1500, resourcesThresholds, scalingUp).types.k3sType)
		assert.Equal(t, "k3s.medium", selectThreshold(3000, resourcesThresholds, scalingUp).types.k3sType)

		assert.Equal(t, "k3s.large", selectThreshold(3001, resourcesThresholds, scalingUp).types.k3sType)
		assert.Equal(t, "k3s.large", selectThreshold(5000, resourcesThresholds, scalingUp).types.k3sType)
		assert.Equal(t, "k3s.large", selectThreshold(8000, resourcesThresholds, scalingUp).types.k3sType)
		assert.Equal(t, "k3s.large", selectThreshold(10000, resourcesThresholds, scalingUp).types.k3sType)

		assert.Equal(t, "k3s.xlarge", selectThreshold(10001, resourcesThresholds, scalingUp).types.k3sType)
		assert.Equal(t, "k3s.xlarge", selectThreshold(12000, resourcesThresholds, scalingUp).types.k3sType)
		assert.Equal(t, "k3s.xlarge", selectThreshold(40000, resourcesThresholds, scalingUp).types.k3sType)
		assert.Equal(t, "k3s.xlarge", selectThreshold(100000, resourcesThresholds, scalingUp).types.k3sType)
		assert.Equal(t, "k3s.xlarge", selectThreshold(120000, resourcesThresholds, scalingUp).types.k3sType)
		assert.Equal(t, "k3s.xlarge", selectThreshold(1000000, resourcesThresholds, scalingUp).types.k3sType)
		assert.Equal(t, "k3s.xlarge", selectThreshold(1200000, resourcesThresholds, scalingUp).types.k3sType)
		assert.Equal(t, "k3s.xlarge", selectThreshold(12000000, resourcesThresholds, scalingUp).types.k3sType)
	})

	t.Run("Scaling Down", func(t *testing.T) {
		// No scaling down - clusters number has NOT fallen below the threshold of 10 by 20%
		assert.Equal(t, "k3s.medium", selectThreshold(9, clustersThresholds, scalingDown).types.k3sType)
		assert.Equal(t, "k3s.medium", selectThreshold(10, clustersThresholds, scalingDown).types.k3sType)

		// Scaling down to "k3s.small" - clusters number has fallen below the threshold of 10 by 20%
		assert.Equal(t, "k3s.small", selectThreshold(5, clustersThresholds, scalingDown).types.k3sType)
		assert.Equal(t, "k3s.small", selectThreshold(8, clustersThresholds, scalingDown).types.k3sType)

		assert.Equal(t, "k3s.medium", selectThreshold(350, appsThresholds, scalingDown).types.k3sType)

		// No scaling down - apps number has NOT fallen below the threshold of 500 by 20%
		assert.Equal(t, "k3s.large", selectThreshold(500, appsThresholds, scalingDown).types.k3sType)
		assert.Equal(t, "k3s.large", selectThreshold(450, appsThresholds, scalingDown).types.k3sType)

		// Scaling down to "k3s.medium" - apps number has fallen below the threshold of 500 by 20%
		assert.Equal(t, "k3s.medium", selectThreshold(400, appsThresholds, scalingDown).types.k3sType)

		// Scaling down to "k3s.medium" - apps number has fallen below the threshold of 500 by 20% (but not enough to scale down to "k3s.small")
		assert.Equal(t, "k3s.medium", selectThreshold(150, appsThresholds, scalingDown).types.k3sType)

		// Scaling down to "k3s.small" - apps number has fallen below the threshold of 150 by 20%
		assert.Equal(t, "k3s.small", selectThreshold(120, appsThresholds, scalingDown).types.k3sType)
	})
}

func TestSelectK3sType(t *testing.T) {
	Setup(t)

	for _, test := range []string{
		// No current K3s type
		// <clusters>:<applications>:<resources>: => <new K3s type>
		"0:0:0:          => k3s.small",
		"2:10:150:       => k3s.small",
		"5:105:890:      => k3s.small",
		"5:200:300:      => k3s.medium",
		"30:300:5000:    => k3s.large",
		"70:2000:20000:  => k3s.xlarge",
		"500:5000:50000: => k3s.xlarge",
		// Scaling Up
		// <clusters>:<applications>:<resources>:<current K3s type> => <new K3s type>
		"0:0:0:k3s.small              => k3s.small",
		"2:10:150:k3s.small           => k3s.small",
		"5:105:890:k3s.small          => k3s.small",
		"5:200:300:k3s.small          => k3s.medium",
		"20:500:3000:k3s.medium       => k3s.medium",
		"30:300:5000:k3s.medium       => k3s.large",
		"70:2000:20000:k3s.large      => k3s.xlarge",
		"500:5000:50000:k3s.large     => k3s.xlarge",
		"5000:50000:500000:k3s.xlarge => k3s.xlarge",
		// Scaling Down
		// <clusters>:<applications>:<resources>:<current K3s type> => <new K3s type>
		"8:120:800:k3s.medium         => k3s.small",  // Scaling down from "k3s.medium" to "k3s.small"
		"10:120:800:k3s.medium        => k3s.medium", // No scaling down - the resource hasn't fallen well enough below the threshold
		"8:140:800:k3s.medium         => k3s.medium", // Same
		"8:120:1000:k3s.medium        => k3s.medium", // Same
		"8:120:800:k3s.xlarge         => k3s.small",  // Scaling down from "k3s.xlarge" to "k3s.small"
		"16:400:2400:k3s.xlarge       => k3s.medium", // Scaling down from "k3s.xlarge" to "k3s.medium"
		"40:800:8000:k3s.xlarge       => k3s.large",  // Scaling down from "k3s.xlarge" to "k3s.large"
		"41:800:8000:k3s.xlarge       => k3s.xlarge", // No scaling down - the resource hasn't fallen well enough below the threshold
		"40:801:8000:k3s.xlarge       => k3s.xlarge", // Same
		"40:800:8001:k3s.xlarge       => k3s.xlarge", // Same
	} {
		t.Run(test, func(t *testing.T) {
			// "0:0:0: => k3s.small" => ["0:0:0:", "k3s.small"]
			// "0:0:0:k3s.small => k3s.small" => ["0:0:0:k3s.small", "k3s.small"]
			testSplit := strings.Split(test, "=>")
			require.Len(t, testSplit, 2)

			// "0:0:0:" => ["0", "0", "0", ""]
			// "0:0:0:k3s.small" => ["0", "0", "0", "k3s.small"]
			inputResources := strings.Split(strings.TrimSpace(testSplit[0]), ":")
			require.Len(t, inputResources, 4)

			tenantClusters, err := strconv.ParseInt(strings.TrimSpace(inputResources[0]), 10, 32)
			require.NoError(t, err)

			tenantApplications, err := strconv.ParseInt(strings.TrimSpace(inputResources[1]), 10, 32)
			require.NoError(t, err)

			applicationK8sResources, err := strconv.ParseInt(strings.TrimSpace(inputResources[2]), 10, 32)
			require.NoError(t, err)

			currentK3sType := strings.TrimSpace(inputResources[3])
			expectedK3sType := strings.TrimSpace(testSplit[1])
			newK3sType := scalingMap.selectK3sType("id", uint32(tenantClusters), uint32(tenantApplications), uint32(applicationK8sResources), currentK3sType)

			assert.Equal(t, expectedK3sType, newK3sType)
		})
	}
}

func TestSelectK3sProxyType(t *testing.T) {
	Setup(t)

	for _, test := range []string{
		// No current K3s Proxy type
		// <clusters>:<applications>:<resources>: => <new K3s Proxy type>
		"0:0:0:          => k3s-proxy.small",
		"2:10:150:       => k3s-proxy.small",
		"5:105:890:      => k3s-proxy.small",
		"5:200:300:      => k3s-proxy.medium",
		"30:300:5000:    => k3s-proxy.large",
		"70:2000:20000:  => k3s-proxy.xlarge",
		"500:5000:50000: => k3s-proxy.xlarge",
		// Scaling Up
		// <clusters>:<applications>:<resources>:<current K3s Proxy type> => <new K3s Proxy type>
		"0:0:0:k3s-proxy.small              => k3s-proxy.small",
		"2:10:150:k3s-proxy.small           => k3s-proxy.small",
		"5:105:890:k3s-proxy.small          => k3s-proxy.small",
		"5:200:300:k3s-proxy.small          => k3s-proxy.medium",
		"20:500:3000:k3s-proxy.medium       => k3s-proxy.medium",
		"30:300:5000:k3s-proxy.medium       => k3s-proxy.large",
		"70:2000:20000:k3s-proxy.large      => k3s-proxy.xlarge",
		"500:5000:50000:k3s-proxy.large     => k3s-proxy.xlarge",
		"5000:50000:500000:k3s-proxy.xlarge => k3s-proxy.xlarge",
		// Scaling Down
		// <clusters>:<applications>:<resources>:<current K3s Proxy type> => <new K3s Proxy type>
		"8:120:800:k3s-proxy.medium         => k3s-proxy.small",  // Scaling down from "k3s-proxy.medium" to "k3s-proxy.small"
		"10:120:800:k3s-proxy.medium        => k3s-proxy.medium", // No scaling down - the resource hasn't fallen well enough below the threshold
		"8:140:800:k3s-proxy.medium         => k3s-proxy.medium", // Same
		"8:120:1000:k3s-proxy.medium        => k3s-proxy.medium", // Same
		"8:120:800:k3s-proxy.xlarge         => k3s-proxy.small",  // Scaling down from "k3s-proxy.xlarge" to "k3s-proxy.small"
		"16:400:2400:k3s-proxy.xlarge       => k3s-proxy.medium", // Scaling down from "k3s-proxy.xlarge" to "k3s-proxy.medium"
		"40:800:8000:k3s-proxy.xlarge       => k3s-proxy.large",  // Scaling down from "k3s-proxy.xlarge" to "k3s-proxy.large"
		"41:800:8000:k3s-proxy.xlarge       => k3s-proxy.xlarge", // No scaling down - the resource hasn't fallen well enough below the threshold
		"40:801:8000:k3s-proxy.xlarge       => k3s-proxy.xlarge", // Same
		"40:800:8001:k3s-proxy.xlarge       => k3s-proxy.xlarge", // Same
	} {
		t.Run(test, func(t *testing.T) {
			// "0:0:0: => k3s-proxy.small" => ["0:0:0:", "k3s-proxy.small"]
			// "0:0:0:k3s-proxy.small => k3s-proxy.small" => ["0:0:0:k3s-proxy.small", "k3s-proxy.small"]
			testSplit := strings.Split(test, "=>")
			require.Len(t, testSplit, 2)

			// "0:0:0:" => ["0", "0", "0", ""]
			// "0:0:0:k3s-proxy.small" => ["0", "0", "0", "k3s-proxy.small"]
			inputResources := strings.Split(strings.TrimSpace(testSplit[0]), ":")
			require.Len(t, inputResources, 4)

			tenantClusters, err := strconv.ParseInt(strings.TrimSpace(inputResources[0]), 10, 32)
			require.NoError(t, err)

			tenantApplications, err := strconv.ParseInt(strings.TrimSpace(inputResources[1]), 10, 32)
			require.NoError(t, err)

			applicationK8sResources, err := strconv.ParseInt(strings.TrimSpace(inputResources[2]), 10, 32)
			require.NoError(t, err)

			currentK3sProxyType := strings.TrimSpace(inputResources[3])
			expectedK3sProxyType := strings.TrimSpace(testSplit[1])
			newK3sProxyType := scalingMap.selectK3sProxyType("id", uint32(tenantClusters), uint32(tenantApplications), uint32(applicationK8sResources), currentK3sProxyType)

			assert.Equal(t, expectedK3sProxyType, newK3sProxyType)
		})
	}
}

func TestSelectApplicationControllerType(t *testing.T) {
	Setup(t)

	for _, test := range []string{
		// No current Application Controller type
		// <applications>: => <new Application Controller type>
		"0:     => application-controller.small",
		"100:   => application-controller.small",
		"150:   => application-controller.small",
		"450:   => application-controller.medium",
		"1000:  => application-controller.large",
		"2900:  => application-controller.xlarge",
		"50000: => application-controller.xlarge",
		// Scaling Up
		// <applications>:<current Application Controller type> => <new Application Controller type>
		"0:application-controller.small       => application-controller.small",
		"100:application-controller.small     => application-controller.small",
		"150:application-controller.small     => application-controller.small",
		"151:application-controller.small     => application-controller.medium",
		"500:application-controller.small     => application-controller.medium",
		"1001:application-controller.small    => application-controller.xlarge",
		"500:application-controller.medium    => application-controller.medium",
		"501:application-controller.medium    => application-controller.large",
		"1001:application-controller.medium   => application-controller.xlarge",
		"1000:application-controller.large    => application-controller.large",
		"1001:application-controller.large    => application-controller.xlarge",
		"2000:application-controller.large    => application-controller.xlarge",
		"50000:application-controller.large   => application-controller.xlarge",
		"500000:application-controller.xlarge => application-controller.xlarge",
		// Scaling Down
		// <applications>:<current Application Controller type> => <new Application Controller type>
		"120:application-controller.medium   => application-controller.small",  // Scaling down from "application-controller.medium" to "application-controller.small"
		"140:application-controller.medium   => application-controller.medium", // No scaling down - the resource hasn't fallen well enough below the threshold
		"150:application-controller.medium   => application-controller.medium", // Same
		"350:application-controller.large    => application-controller.medium", // Scaling down from "application-controller.large" to "application-controller.medium"
		"100:application-controller.xlarge   => application-controller.small",  // Scaling down from "application-controller.xlarge" to "application-controller.small"
		"400:application-controller.xlarge   => application-controller.medium", // Scaling down from "application-controller.xlarge" to "application-controller.medium"
		"800:application-controller.xlarge   => application-controller.large",  // Scaling down from "application-controller.xlarge" to "application-controller.large"
		"801:application-controller.xlarge   => application-controller.xlarge", // No scaling down - the resource hasn't fallen well enough below the threshold
		"1000:application-controller.xlarge  => application-controller.xlarge", // Same
	} {
		t.Run(test, func(t *testing.T) {
			// "0: => application-controller.small" => ["0:", "application-controller.small"]
			// "120:application-controller.medium => application-controller.small" => ["120:application-controller.medium", "application-controller.small"]
			testSplit := strings.Split(test, "=>")
			require.Len(t, testSplit, 2)

			// "0:" => ["0", ""]
			// "120:application-controller.medium" => ["120", "application-controller.medium"]
			inputResources := strings.Split(strings.TrimSpace(testSplit[0]), ":")
			require.Len(t, inputResources, 2)

			tenantApplications, err := strconv.ParseInt(strings.TrimSpace(inputResources[0]), 10, 32)
			require.NoError(t, err)

			currentApplicationControllerType := strings.TrimSpace(inputResources[1])
			expectedApplicationControllerType := strings.TrimSpace(testSplit[1])
			newApplicationControllerType := scalingMap.selectApplicationControllerType("id", uint32(tenantApplications), currentApplicationControllerType)

			assert.Equal(t, expectedApplicationControllerType, newApplicationControllerType)
		})
	}
}

func TestSelectRedisType(t *testing.T) {
	Setup(t)

	for _, test := range []string{
		// No current Redis type
		// <applications>: => <new Redis type>
		"0:     => redis.small",
		"100:   => redis.small",
		"150:   => redis.small",
		"450:   => redis.medium",
		"1000:  => redis.large",
		"2900:  => redis.xlarge",
		"50000: => redis.xlarge",
		// Scaling Up
		// <applications>:<current Redis type> => <new Redis type>
		"0:redis.small       => redis.small",
		"100:redis.small     => redis.small",
		"150:redis.small     => redis.small",
		"151:redis.small     => redis.medium",
		"500:redis.small     => redis.medium",
		"1001:redis.small    => redis.xlarge",
		"500:redis.medium    => redis.medium",
		"501:redis.medium    => redis.large",
		"1001:redis.medium   => redis.xlarge",
		"1000:redis.large    => redis.large",
		"1001:redis.large    => redis.xlarge",
		"2000:redis.large    => redis.xlarge",
		"50000:redis.large   => redis.xlarge",
		"500000:redis.xlarge => redis.xlarge",
		// Scaling Down
		// <applications>:<current Redis type> => <new Redis type>
		"120:redis.medium   => redis.small",  // Scaling down from "redis.medium" to "redis.small"
		"140:redis.medium   => redis.medium", // No scaling down - the resource hasn't fallen well enough below the threshold
		"150:redis.medium   => redis.medium", // Same
		"350:redis.large    => redis.medium", // Scaling down from "redis.large" to "redis.medium"
		"100:redis.xlarge   => redis.small",  // Scaling down from "redis.xlarge" to "redis.small"
		"400:redis.xlarge   => redis.medium", // Scaling down from "redis.xlarge" to "redis.medium"
		"800:redis.xlarge   => redis.large",  // Scaling down from "redis.xlarge" to "redis.large"
		"801:redis.xlarge   => redis.xlarge", // No scaling down - the resource hasn't fallen well enough below the threshold
		"1000:redis.xlarge  => redis.xlarge", // Same
	} {
		t.Run(test, func(t *testing.T) {
			// "0: => redis.small" => ["0:", "redis.small"]
			// "120:redis.medium => redis.small" => ["120:redis.medium", "redis.small"]
			testSplit := strings.Split(test, "=>")
			require.Len(t, testSplit, 2)

			// "0:" => ["0", ""]
			// "120:redis.medium" => ["120", "redis.medium"]
			inputResources := strings.Split(strings.TrimSpace(testSplit[0]), ":")
			require.Len(t, inputResources, 2)

			tenantApplications, err := strconv.ParseInt(strings.TrimSpace(inputResources[0]), 10, 32)
			require.NoError(t, err)

			currentRedisType := strings.TrimSpace(inputResources[1])
			expectedRedisType := strings.TrimSpace(testSplit[1])
			newRedisType := scalingMap.selectRedisType("id", uint32(tenantApplications), currentRedisType)

			assert.Equal(t, expectedRedisType, newRedisType)
		})
	}
}

func TestInstanceOverride(t *testing.T) {
	Setup(t)

	for _, test := range []string{
		// instanceId => <K3sType>:<K3sProxyType>:<ApplicationControllerType>:<RedisType>
		"id1 => k3s.xlarge:::",
		"id2 => :k3s-proxy.xlarge::",
		"id3 => ::application-controller.xlarge:",
		"id4 => :::redis.xlarge",
		"id5 => k3s.xlarge:k3s-proxy.xlarge::",
		"id6 => ::application-controller.xlarge:redis.xlarge",
		"id7 => k3s.large:k3s-proxy.large:application-controller.large:redis.large",
		"id8 => k3s.medium:k3s-proxy.medium:application-controller.medium:redis.medium",
	} {
		t.Run(test, func(t *testing.T) {
			// "id => k3s.large:k3s-proxy.large:application-controller.large:redis.large" => ["id", "k3s.large:k3s-proxy.large:application-controller.large:redis.large"]
			testSplit := strings.Split(test, "=>")
			require.Len(t, testSplit, 2)

			instanceId := strings.TrimSpace(testSplit[0])

			// "k3s.large:k3s-proxy.large:application-controller.large:redis.large" => ["k3s.large", "k3s-proxy.large", "application-controller.large", "redis.large"]
			outputExpected := strings.Split(strings.TrimSpace(testSplit[1]), ":")
			require.Len(t, outputExpected, 4)

			expectedK3sType := outputExpected[0]
			expectedK3sProxyType := outputExpected[1]
			expectedApplicationControllerType := outputExpected[2]
			expectedRedisType := outputExpected[3]

			if expectedK3sType == "" {
				expectedK3sType = "k3s.small"
			}
			if expectedK3sProxyType == "" {
				expectedK3sProxyType = "k3s-proxy.small"
			}
			if expectedApplicationControllerType == "" {
				expectedApplicationControllerType = "application-controller.small"
			}
			if expectedRedisType == "" {
				expectedRedisType = "redis.small"
			}

			for k3sType := range scalingMap.K3sTypes {
				newK3sType := scalingMap.selectK3sType(instanceId, 0, 0, 0, k3sType)
				assert.Equal(t, expectedK3sType, newK3sType, "selectK3sType(%s)", k3sType)
			}

			for k3sProxyType := range scalingMap.K3sProxyTypes {
				newK3sProxyType := scalingMap.selectK3sProxyType(instanceId, 0, 0, 0, k3sProxyType)
				assert.Equal(t, expectedK3sProxyType, newK3sProxyType, "selectK3sProxyType(%s)", k3sProxyType)
			}

			for applicationControllerType := range scalingMap.ApplicationControllerTypes {
				newApplicationControllerType := scalingMap.selectApplicationControllerType(instanceId, 0, applicationControllerType)
				assert.Equal(t, expectedApplicationControllerType, newApplicationControllerType, "selectApplicationControllerType(%s)", applicationControllerType)
			}

			for redisType := range scalingMap.RedisTypes {
				newRedisType := scalingMap.selectRedisType(instanceId, 0, redisType)
				assert.Equal(t, expectedRedisType, newRedisType, "selectRedisType(%s)", redisType)
			}
		})
	}
}

func TestReadingInvalidMap(t *testing.T) {
	t.Run("Null Map", func(t *testing.T) {
		scalingMap, err := newDynamicScalingMap(nil)
		assert.Nil(t, scalingMap)
		assert.Error(t, err)
		assert.Equal(t, "scaling ConfigMap is empty", err.Error())
	})

	t.Run("Empty Map", func(t *testing.T) {
		scalingMap, err := newDynamicScalingMap(map[string]string{})
		assert.Nil(t, scalingMap)
		assert.Error(t, err)
		assert.Equal(t, "scaling ConfigMap is empty", err.Error())
	})

	// All files in these folders contain an invalid ConfigMap in the following format:
	// =========================================================================
	// <expected error message>
	// -------------------------------------------------
	// <invalid config map data>
	// =========================================================================
	processTestFilesWithError(t, "scaling-map-tests/invalid-maps")
}

// processTestFilesWithError - processes all test files recursively below the path specified
func processTestFilesWithError(t *testing.T, dirPath string) {
	_, err := os.Stat(dirPath)
	require.NoError(t, err, "directory %q is missing", dirPath)

	entries, err := os.ReadDir(dirPath)
	require.NotEmpty(t, entries, "directory %q is empty", dirPath)
	require.NoError(t, err, "failed reading directory %q", dirPath)

	for _, entry := range entries {
		entryPath := path.Join(dirPath, entry.Name())

		if entry.IsDir() {
			processTestFilesWithError(t, entryPath)
			continue
		}

		t.Run(strings.Replace(entryPath, ".txt", "", 1), func(t *testing.T) {
			fileBytes, err := os.ReadFile(entryPath)
			require.NoError(t, err, "failed reading file %q", entryPath)
			require.NotEmpty(t, fileBytes, "file %q is empty", entryPath)

			fileLines := strings.Split(string(fileBytes), "\n")
			require.True(t, len(fileLines) > 3, "file %q contains too few lines", entryPath)

			errorMessage := strings.TrimSpace(fileLines[0])
			require.NotEmpty(t, errorMessage, "file %q contains an empty error message", entryPath)

			separator := strings.TrimSpace(fileLines[1])
			require.Equal(t, "-------------------------------------------------", separator)

			scalingMap, err := readScalingMapFromLines(t, fileLines[2:])
			assert.Nil(t, scalingMap)
			require.Error(t, err)
			assert.Contains(t, err.Error(), errorMessage, "Actual error message [%s] does not contain expected [%s]", err.Error(), errorMessage)
		})
	}
}

// readScalingMapFromFile - reads dynamicScalingMap from the file specified
func readScalingMapFromFile(t *testing.T, filePath string) (*dynamicScalingMap, error) {
	_, err := os.Stat(filePath)
	require.NoError(t, err, "file %q is missing", filePath)

	bytes, err := os.ReadFile(filePath)
	require.NoError(t, err, "failed reading file %q", filePath)
	require.NotEmpty(t, bytes, "file %q is empty", filePath)

	return readScalingMapFromLines(t, strings.Split(string(bytes), "\n"))
}

func readScalingMapFromLines(t *testing.T, scalingMapLines []string) (*dynamicScalingMap, error) {
	require.NotEmpty(t, scalingMapLines, "scaling map lines are empty")
	scalingMap := map[string]string{}

configMapLines:
	for _, mapLine := range scalingMapLines {
		mapLine = strings.TrimSpace(mapLine)
		if len(mapLine) < 1 || mapLine[:1] == "#" {
			continue
		}

		for _, ignorePrefix := range configMapIgnorePrefixes {
			if strings.HasPrefix(mapLine, ignorePrefix) {
				continue configMapLines
			}
		}

		colonIndex := strings.Index(mapLine, ":")
		require.Positive(t, colonIndex)

		mapKey := strings.TrimSpace(mapLine[:colonIndex])
		mapValue := strings.Replace(strings.TrimSpace(mapLine[colonIndex+1:]), "\"", "", 2)
		require.NotEmpty(t, mapKey)
		scalingMap[mapKey] = mapValue
	}

	require.NotEmpty(t, scalingMap, "scaling map is empty")
	return newDynamicScalingMap(scalingMap)
}
