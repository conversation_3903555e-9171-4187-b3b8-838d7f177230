package integration

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	v1 "k8s.io/api/core/v1"

	"github.com/akuityio/akuity-platform/controllers/shared/tenant"
	"github.com/akuityio/akuity-platform/internal/argoproj/argocd"
	"github.com/akuityio/akuity-platform/models/models"
)

func TestProcessSyncCompleted(t *testing.T) {
	r := argocdInstanceEventsReconciler{}
	operation, err := r.processSyncCompleted(context.Background(),
		&models.ArgoCDInstance{ID: "abc"},
		tenant.Event{Event: v1.Event{
			Message:        "Sync operation to abc succeeded",
			InvolvedObject: v1.ObjectReference{Name: "app1"},
		}}, &appsCache{
			apps: map[string]*argocd.Application{
				"app1": {},
			},
		})

	require.NoError(t, err)
	assert.Equal(t, "Succeeded", operation.ResultPhase)

	details, err := operation.GetDetails()
	require.NoError(t, err)
	assert.Equal(t, "abc", details.Revision)
}

func TestProcessSyncCompleted_PartialSync(t *testing.T) {
	r := argocdInstanceEventsReconciler{}
	operation, err := r.processSyncCompleted(context.Background(),
		&models.ArgoCDInstance{ID: "abc"},
		tenant.Event{Event: v1.Event{
			Message:        "Partial sync operation to abc succeeded",
			InvolvedObject: v1.ObjectReference{Name: "app1"},
		}}, &appsCache{
			apps: map[string]*argocd.Application{
				"app1": {},
			},
		})

	require.NoError(t, err)
	assert.Equal(t, "Succeeded", operation.ResultPhase)

	details, err := operation.GetDetails()
	require.NoError(t, err)
	assert.Equal(t, "abc", details.Revision)
}

func TestProcessOperationStarted_AutoSync(t *testing.T) {
	r := argocdInstanceEventsReconciler{}
	operation, err := r.processOperationStarted(
		&models.ArgoCDInstance{ID: "abc"},
		tenant.Event{Event: v1.Event{
			Message:        "Initiated automated sync to 'abc'",
			InvolvedObject: v1.ObjectReference{Name: "app1"},
		}},
	)

	require.NoError(t, err)
	actor, err := operation.GetActor()
	require.NoError(t, err)
	assert.Equal(t, "argocd_auto_sync", actor.ID)
	assert.Equal(t, operation.Action, models.AppSyncStarted)
}
