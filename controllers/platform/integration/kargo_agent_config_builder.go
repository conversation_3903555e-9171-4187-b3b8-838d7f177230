package integration

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/Masterminds/semver"
	gosemver "golang.org/x/mod/semver"
	"k8s.io/utils/ptr"

	"github.com/akuityio/agent/manifests"
	"github.com/akuityio/agent/pkg/client/apis/kargo/controlplane"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/version"
	"github.com/akuityio/akuity-platform/models/models"
	"github.com/akuityio/akuity-platform/models/util/status"
	featuresv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/features/v1"

	_ "embed"
)

// Aggregates all methods responsible for building tenant's controlplane.DataValues struct
type kargoAgentConfigBuilder struct {
	reconciler            *kargoInstanceReconciler
	instanceConfig        *models.KargoInstanceConfig
	instanceSpec          *models.KargoInstanceConfigSpec
	instance              *models.KargoInstance
	certStatus            *status.CertificateStatus
	featureSvc            features.Service
	org                   *models.Organization
	tlsTerminationEnabled bool
	orgFeatureStatuses    *featuresv1.FeatureStatuses
}

func newKargoAgentConfigBuilder(reconciler *kargoInstanceReconciler, instance *models.KargoInstance, instanceConfig *models.KargoInstanceConfig, certStatus status.CertificateStatus, featureSvc features.Service, org *models.Organization, ingressConfig config.IngressConfig, orgFeatureStatuses *featuresv1.FeatureStatuses) *kargoAgentConfigBuilder {
	return &kargoAgentConfigBuilder{
		reconciler:            reconciler,
		instanceConfig:        instanceConfig,
		instance:              instance,
		certStatus:            &certStatus,
		featureSvc:            featureSvc,
		org:                   org,
		tlsTerminationEnabled: ingressConfig.TLSTerminationEnabled,
		orgFeatureStatuses:    orgFeatureStatuses,
	}
}

func (b *kargoAgentConfigBuilder) buildAgentConfigurations(ctx context.Context, generation int32) (*controlplane.DataValues, error) {
	instanceSpec, err := b.instanceConfig.GetSpec()
	if err != nil {
		return nil, err
	}

	b.instanceSpec = &instanceSpec

	dbInfo, err := database.ExtractDBInfo(b.reconciler.settings.K3sDBConnection)
	if err != nil {
		return nil, err
	}

	privateSpec, err := b.instanceConfig.GetPrivateSpec()
	if err != nil {
		return nil, err
	}

	internalSpec, err := b.instanceConfig.GetInternalSpec()
	if err != nil {
		return nil, err
	}

	// Tenant schema is identical to tenant's username
	postgresSchema := privateSpec.K3sUsername
	postgresUsername := privateSpec.K3sUsername
	postgresPassword := privateSpec.K3sPassword

	// Tenant schema is identical to tenant's username
	if err := database.CreateTenantDatabaseSchema(b.reconciler.settings.K3sDBRawClient, ctx, postgresSchema, postgresUsername, postgresPassword, b.reconciler.settings.SharedK3sDBConnectionAuth); err != nil {
		return nil, err
	}

	ingressConfig, err := b.buildIngressConfig(privateSpec.FqdnVersion, b.instanceConfig.InstanceID, getKargoSubdomain(b.instanceConfig))
	if err != nil {
		return nil, err
	}

	imagePullSecretData, err := b.reconciler.imagePullSecretGetter()
	if err != nil {
		return nil, err
	}

	if b.reconciler.settings.SharedK3sDBConnectionAuth {
		// During DB migration - all tenants temporarily use a shared K3sDBConnection auth to connect to RDS Proxy
		postgresUsername = dbInfo.User
		postgresPassword = dbInfo.Password
	}
	var k3sImage *string
	if internalSpec.K3sImage != "" {
		k3sImage = &internalSpec.K3sImage
	}

	// calculate argocd urls
	argocdUrls := ""
	for key, val := range internalSpec.ArgoCDUrls {
		if strings.HasPrefix(val, "https://") || strings.HasPrefix(val, "http://") {
			argocdUrls += fmt.Sprintf("%s=%s,", key, val)
		} else {
			argocdUrls += fmt.Sprintf("%s=https://%s,", key, val)
		}
	}

	var k3sReadonlyHostname *string
	if (b.orgFeatureStatuses.GetPgpool().Enabled()) || (b.orgFeatureStatuses.GetPgbouncer().Enabled()) {
		k3sReadonlyHostname = ptr.To(b.reconciler.settings.K3sReadonlyHostname)
	}

	var logExtension *controlplane.DataValuesExtensionsLogs
	if b.orgFeatureStatuses.GetKargoAnalysisLogs().Enabled() {
		logExtension = &controlplane.DataValuesExtensionsLogs{
			UrlTemplate: ptr.To("http://portal-server.akuity-platform.svc.cluster.local:9092/ext-api/v1/kargo/extensions/logs/${{project}}/${{analysisRun}}/${{container}}"),
			Headers:     ptr.To("Authorization=\"Bearer ${{token}}\",instance-id=" + b.instanceConfig.InstanceID),
		}
	}

	defaultAgentName := ""
	if b.instanceSpec.DefaultShardAgentID != "" {
		agent, err := b.reconciler.settings.RepoSet.KargoAgents().GetByID(ctx, b.instanceSpec.DefaultShardAgentID, "id", "name")
		if err != nil {
			return nil, fmt.Errorf("failed to get default shard agent: %w", err)
		}
		defaultAgentName = agent.Name
	}

	// Fill in the data values for Agent
	dataValues := controlplane.DataValues{
		Kargo: &controlplane.DataValuesKargo{
			InstanceId:             &b.instanceConfig.InstanceID,
			Generation:             &generation,
			Version:                b.instanceConfig.Version.Ptr(),
			ArgocdUrls:             &argocdUrls,
			GlobalCredentialsNs:    ptr.To(strings.Join(b.instanceSpec.GlobalCredentialsNs, ",")),
			GlobalServiceAccountNs: ptr.To(strings.Join(b.instanceSpec.GlobalServiceAccountNs, ",")),
			DefaultShard:           ptr.To(defaultAgentName),
		},
		AkuityPlatform: &controlplane.DataValuesAkuityPlatform{
			ImagePullSecret: imagePullSecretData,
			Config: &controlplane.DataValuesAkuityPlatformConfig{
				CanCreateStages: ptr.To(internalSpec.GetCanCreateStages()),
			},
		},
		KargoExtensionServer: &controlplane.DataValuesKargoExtensionServer{
			Configmap: map[string]interface{}{
				"AKUITY_PORTAL_URL": "http://portal-server.akuity-platform.svc.cluster.local:9092",
				"INSTANCE_ID":       b.instanceConfig.InstanceID,
			},
		},
		K3s: &controlplane.DataValuesK3s{
			Image:               k3sImage,
			KineSchemaMigration: ptr.To(fmt.Sprintf("%v", internalSpec.K3sSchemaMigration)),
			// This token can be useful for K3s HA mode. It's a shared secret to allow other masters to join the pool of masters.
			Token: ptr.To(privateSpec.K3sToken),
			Postgres: &controlplane.DataValuesK3sPostgres{
				Create:     ptr.To(false),
				Hostname:   ptr.To(dbInfo.Host),
				RoHostname: k3sReadonlyHostname,
				Schema:     ptr.To(postgresSchema),
				Username:   ptr.To(postgresUsername),
				Password:   ptr.To(postgresPassword),
				Port:       ptr.To(dbInfo.Port),
				Database:   ptr.To(dbInfo.DBName),
				Sslmode:    ptr.To(dbInfo.SSLMode),
			},
		},
		Pgbouncer: &controlplane.DataValuesPgbouncer{
			Enabled: ptr.To(b.orgFeatureStatuses.GetPgbouncer().Enabled()),
		},
		K3sWebhook: &controlplane.DataValuesK3sWebhook{
			Key:  ptr.To(privateSpec.WebhookKey),
			Cert: ptr.To(privateSpec.WebhookCert),
		},
		KargoWebhook: &controlplane.DataValuesKargoWebhook{
			Key:  ptr.To(privateSpec.WebhookKey),
			Cert: ptr.To(privateSpec.WebhookCert),
		},
		Ingress: ingressConfig,
		Extensions: &controlplane.DataValuesExtensions{
			Logs: logExtension,
		},
		Tls: &controlplane.DataValuesTLS{
			TerminationEnabled: &b.tlsTerminationEnabled,
		},
	}

	if err := b.updateDataValuesConfig(&dataValues, privateSpec.KargoServerSecretKey); err != nil {
		return nil, err
	}

	return b.updateDataValues(ctx, &dataValues)
}

func (b *kargoAgentConfigBuilder) updateDataValuesConfig(dataValuesConfig *controlplane.DataValues, serverSecretKey string) error {
	if !b.instanceConfig.OidcConfig.IsZero() {
		oidcConfig, err := b.instanceConfig.GetOidcConfig()
		if err != nil {
			return err
		}
		dexConfig := []byte{}
		if oidcConfig.DexConfig != "" {
			dexConfig, err = b.instanceConfig.HydrateAndValidateDexConfig(oidcConfig.DexConfig, b.reconciler.settings.DomainSuffix, oidcConfig.DexConfigSecrets, b.reconciler.inCluster, true)
			if err != nil {
				return err
			}
		}

		googleSASecret := oidcConfig.DexConfigSecrets[models.AkpDexGoogleSASecretKey]
		delete(oidcConfig.DexConfigSecrets, models.AkpDexGoogleSASecretKey)

		dataValuesConfig.Oidc = &controlplane.DataValuesOidc{
			Enabled:          ptr.To(oidcConfig.Enabled),
			IssuerURL:        ptr.To(oidcConfig.IssuerURL),
			ClientID:         ptr.To(oidcConfig.ClientID),
			CliClientID:      ptr.To(oidcConfig.CliClientID),
			AdditionalScopes: ptr.To(strings.Join(oidcConfig.AdditionalScopes, ",")),
			Dex: &controlplane.DataValuesOidcDex{
				Enabled:        ptr.To(oidcConfig.DexEnabled),
				Config:         ptr.To(string(dexConfig)),
				Secret:         oidcConfig.DexConfigSecrets,
				GoogleSaSecret: ptr.To(googleSASecret),
			},
			PredefinedSa: &controlplane.DataValuesOidcPredefinedSa{
				Viewer: convertKargoAccount(oidcConfig.ViewerAccount),
				Admin:  convertKargoAccount(oidcConfig.AdminAccount),
				User:   convertKargoAccount(oidcConfig.UserAccount),
			},
		}
	}

	if !b.instanceConfig.ControllerCM.IsZero() {
		data, err := b.instanceConfig.GetControllerCMValues()
		if err != nil {
			return err
		}
		if dataValuesConfig.KargoController == nil {
			dataValuesConfig.KargoController = &controlplane.DataValuesKargoController{}
		}
		dataValuesConfig.KargoController.Configmap = data
	}

	if !b.instanceConfig.WebhookCM.IsZero() {
		data, err := b.instanceConfig.GetWebhookCMValues()
		if err != nil {
			return err
		}
		if dataValuesConfig.KargoWebhook == nil {
			dataValuesConfig.KargoWebhook = &controlplane.DataValuesKargoWebhook{}
		}
		dataValuesConfig.KargoWebhook.Configmap = data
	}

	if dataValuesConfig.ArgoRollouts == nil {
		dataValuesConfig.ArgoRollouts = controlplane.NewDataValuesArgoRollouts()
	}
	dataValuesConfig.ArgoRollouts.Secrets = nil // necessary due to ytt schema quirk
	if !b.instanceConfig.MiscellaneousSecrets.IsZero() {
		data, err := b.instanceConfig.GetRolloutsSecrets()
		if err != nil {
			return err
		}
		dataValuesConfig.ArgoRollouts.Secrets = data
	}

	if !b.instanceConfig.APICM.IsZero() {
		data, err := b.instanceConfig.GetApiCMValues()
		if err != nil {
			return err
		}
		if dataValuesConfig.KargoApi == nil {
			dataValuesConfig.KargoApi = &controlplane.DataValuesKargoApi{}
		}
		dataValuesConfig.KargoApi.Configmap = data
	}

	apiSecret := map[string]interface{}{}
	if !b.instanceConfig.APISecret.IsZero() {
		data, err := b.instanceConfig.GetAPISecretValues()
		if err != nil {
			return err
		}
		apiSecret = data
	}
	if dataValuesConfig.KargoApi == nil {
		dataValuesConfig.KargoApi = &controlplane.DataValuesKargoApi{}
	}
	apiSecret["ADMIN_ACCOUNT_TOKEN_SIGNING_KEY"] = serverSecretKey
	dataValuesConfig.KargoApi.Secret = apiSecret

	return nil
}

func (b *kargoAgentConfigBuilder) buildIngressConfig(fqdnVersion, instanceId, subdomain string) (*controlplane.DataValuesIngress, error) {
	if fqdnVersion != "1" {
		return nil, fmt.Errorf("unsupported fqdnVersion: %s", fqdnVersion)
	}
	separator := b.reconciler.settings.GetDomainSeparator()

	issueCert := !config.IsSelfHosted && b.instanceConfig.FQDN.String != "" && b.certStatus.IsCNameSet

	dc := &domainConfig{
		InstanceID:             instanceId,
		Subdomain:              subdomain,
		CustomerDomain:         b.instanceConfig.FQDN.String,
		OverrideDefaultDomains: b.reconciler.settings.OverrideDefaultDomains,
		UseHyphens:             separator == "-",
		DefaultDomain:          fmt.Sprintf("kargosvcs.%s", b.reconciler.settings.DomainSuffix),
	}

	ingressConfig := controlplane.DataValuesIngress{
		Enabled:     ptr.To(b.reconciler.settings.EnableIngress),
		ExternalDns: b.reconciler.externalDNS,
		KargoApi: &controlplane.DataValuesIngressKargoApi{
			Fqdn:        ptr.To(getKargoInstanceURL(b.instanceConfig.FQDN.String, separator, subdomain, b.reconciler.settings.DomainSuffix)),
			InternalUrl: ptr.To(getKargoInternalURL(instanceId, b.reconciler.settings.DomainSuffix)),
			LetsEncrypt: &controlplane.DataValuesIngressKargoApiLetsEncrypt{
				Enabled:           ptr.To(issueCert),
				ClusterIssuerName: ptr.To(b.reconciler.settings.ClusterIssuerName),
			},
		},
		K3sProxy: &controlplane.DataValuesIngressK3sProxy{Fqdn: ptr.To(dc.GenerateDomain("cplane"))},
	}

	if len(b.instanceSpec.IpAllowlist) > 0 {
		var allowList []string
		for _, ip := range b.instanceSpec.IpAllowlist {
			allowList = append(allowList, ip.Ip)
		}
		ingressConfig.IpAllowlist = allowList
		ingressConfig.BackendIpAllowlist = ptr.To(b.instanceSpec.BackendIpAllowlistEnabled)
	}

	return &ingressConfig, nil
}

func (b *kargoAgentConfigBuilder) updateDataValues(ctx context.Context, dataValues *controlplane.DataValues) (*controlplane.DataValues, error) {
	if dataValues.AgentServer == nil {
		dataValues.AgentServer = controlplane.NewDataValuesAgentServer()
		dataValues.AgentServer.SetVersion(version.GetLatestAgentVersion())
	}
	if b.reconciler.settings.InstanceConfig.AgentServerImage != "" {
		host, repo, version, err := manifests.SplitImage(b.reconciler.settings.InstanceConfig.AgentServerImage)
		if err != nil {
			return dataValues, err
		}
		dataValues.AgentServer.SetImageHost(host)
		dataValues.AgentServer.SetImageRepo(repo)
		dataValues.AgentServer.SetVersion(version)
	} else {
		status, err := b.instance.GetStatus()
		if err != nil {
			return dataValues, err
		}
		// The status.Info.RequestedAkuityServerVersion is set by the upgrader job to upgrade instance to the specific version.
		// Otherwise, latest version should be used
		if status.Info.RequestedAkuityServerVersion != "" {
			currentVer, currentVerErr := semver.NewVersion(status.Info.AkuityServerVersion)
			requestedVer, requestedVerErr := semver.NewVersion(status.Info.RequestedAkuityServerVersion)
			if currentVerErr == nil && requestedVerErr == nil && requestedVer.GreaterThan(currentVer) {
				dataValues.AgentServer.SetVersion(status.Info.RequestedAkuityServerVersion)
			}
		}
		// "us-docker.pkg.dev/akuity/akp"
		if b.reconciler.settings.InstanceConfig.AgentServerImageHost != "" {
			dataValues.AgentServer.SetImageHost(b.reconciler.settings.InstanceConfig.AgentServerImageHost)
		}
	}

	version.UpdateAgentImageHost(dataValues.AgentServer)
	// if custom akp version or >= 1.6.0 rc is used update image host to akuity repo
	// NOTE: even if the enterprise feature is disabled for the customer
	// allow the instance to run if ak version is selected or else it might error out,
	// this is possible if user demoed the feature and then later had it turned off
	if b.featureSvc.GetFeatureStatuses(ctx, nil).GetKargoEnterprise().Enabled() {
		if isAKPImageVersion(dataValues.Kargo.GetVersion()) || (gosemver.Compare(dataValues.Kargo.GetVersion(), "v1.6.0-rc.0") >= 0 && strings.Contains(dataValues.Kargo.GetVersion(), "rc")) {
			dataValues.Kargo.SetImageHost(AkpVersionImgHost)
		}
	}

	// if unstable version use unstable image repo
	if strings.Contains(dataValues.Kargo.GetVersion(), "unstable") {
		if b.orgFeatureStatuses.GetKargoEnterprise().Enabled() {
			// use ak nightly release for unstable if enterprise enabled
			dataValues.Kargo.SetImageHost(AkpVersionImgHost)
		}
		dataValues.Kargo.SetImageRepo(UnstableKargoImageRepo)
	}

	// set openshift compatibility when enabled
	if b.reconciler.settings.InstanceConfig.OpenshiftCompatibility {
		dataValues.SetOpenshiftCompatible(b.reconciler.settings.InstanceConfig.OpenshiftCompatibility)
	}

	// set sidecar compatibility
	dataValues.SetSidecarFeatureCompatible(b.reconciler.settings.InstanceConfig.SidecarFeatureCompatibility)
	dataValues.SetIpv6OnlyCompatible(b.reconciler.settings.InstanceConfig.IPV6OnlyCompatibility)

	return b.patchDataValues(dataValues)
}

func (b *kargoAgentConfigBuilder) patchDataValues(dataValues *controlplane.DataValues) (*controlplane.DataValues, error) {
	internalSpec, err := b.instanceConfig.GetInternalSpec()
	if err != nil {
		return dataValues, err
	}

	var patches []interface{}
	if b.reconciler.settings.InstanceConfig.KargoInstanceValues.Values != nil {
		patches = append(patches, b.reconciler.settings.InstanceConfig.KargoInstanceValues.Values)
	}
	if len(internalSpec.Values) != 0 {
		patches = append(patches, internalSpec.Values)
	}

	if len(patches) == 0 {
		return dataValues, nil
	}

	dataValuesMap, err := valueToMap(dataValues)
	if err != nil {
		return nil, err
	}

	for _, patch := range patches {
		patchMap, err := valueToMap(patch)
		if err != nil {
			return nil, err
		}
		dataValuesMap = mergeMaps(dataValuesMap, patchMap)
	}

	dataValuesBytes, err := json.Marshal(dataValuesMap)
	if err != nil {
		return nil, err
	}

	var patchedDataValues controlplane.DataValues
	if err := json.Unmarshal(dataValuesBytes, &patchedDataValues); err != nil {
		return nil, err
	}

	return &patchedDataValues, nil
}

func getKargoInternalURL(instanceID, domainSuffix string) string {
	return fmt.Sprintf("%s.kargo.%s", instanceID, domainSuffix)
}

func getKargoInstanceURL(fqdn, separator, subdomain, domainSuffix string) string {
	if fqdn != "" {
		return fqdn
	}
	return fmt.Sprintf("%s%skargo.%s", subdomain, separator, domainSuffix)
}

func convertKargoAccount(data models.KargoPredefinedAccountData) *controlplane.DataValuesOidcPredefinedSaViewer {
	return &controlplane.DataValuesOidcPredefinedSaViewer{
		Sub:    ptr.To(strings.Join(data.Claims["sub"], ",")),
		Email:  ptr.To(strings.Join(data.Claims["email"], ",")),
		Groups: ptr.To(strings.Join(data.Claims["groups"], ",")),
		Claims: convertKargoClaimsToDataValueClaims(data.Claims),
	}
}

func convertKargoClaimsToDataValueClaims(claims map[string][]string) map[string]string {
	converted := map[string]string{}
	for k, v := range claims {
		converted[k] = strings.Join(v, ",")
	}
	return converted
}
