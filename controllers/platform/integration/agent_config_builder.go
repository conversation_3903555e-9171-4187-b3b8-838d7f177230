package integration

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"slices"
	"strconv"
	"strings"

	mmSemver "github.com/Masterminds/semver/v3"
	"github.com/volatiletech/null/v8"
	"golang.org/x/mod/semver"
	"gopkg.in/yaml.v3"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/utils/ptr"

	"github.com/akuityio/agent/manifests"
	"github.com/akuityio/agent/pkg/client/apis/controlplane"
	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/controllers/platform/integration/autoscaler"
	"github.com/akuityio/akuity-platform/internal/argoproj"
	"github.com/akuityio/akuity-platform/internal/config"
	featuresservice "github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/version"
	"github.com/akuityio/akuity-platform/models/models"
	"github.com/akuityio/akuity-platform/models/util/status"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	featuresv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/features/v1"

	_ "embed"
)

const (
	AkpVersionImgHost      = "quay.io/akuity"
	UnstableKargoImageRepo = "kargo-unstable"
	cpuFloatFormat         = "%.2f"
	// appSetActionsYAML is the default actions for ApplicationSet
	appSetActionsYAML = `
discovery.lua: |
  actions = {}
  actions["refresh"] = {iconClass = "fa fa-redo"}
  return actions
definitions:
  - name: refresh
    action.lua: |
      if obj.metadata.annotations == nil then
        obj.metadata.annotations = {}
      end
      obj.metadata.annotations["argocd.argoproj.io/application-set-refresh"] = "true"
      return obj
`

	// crossplane customization that puts the correct health-checks on the given resource
	// provide the wildcard group matching - ie. *.crossplane.io/*
	crossplaneCustomization = `
health_status = {
  status = "Progressing",
  message = "Provisioning ..."
}

local function contains(table, val)
  for i, v in ipairs(table) do
	if v == val then
	  return true
	end
  end
  return false
end

local has_no_status = {
  "Composition",
  "CompositionRevision",
  "DeploymentRuntimeConfig",
  "ControllerConfig",
  "ProviderConfig",
  "ProviderConfigUsage"
}
if obj.status == nil or next(obj.status) == nil and contains(has_no_status, obj.kind) then
  health_status.status = "Healthy"
  health_status.message = "Resource is up-to-date."
  return health_status
end

if obj.status == nil or next(obj.status) == nil or obj.status.conditions == nil then
  if obj.kind == "ProviderConfig" and obj.status.users ~= nil then
	health_status.status = "Healthy"
	health_status.message = "Resource is in use."
	return health_status
  end
  return health_status
end

for i, condition in ipairs(obj.status.conditions) do
  if condition.type == "LastAsyncOperation" then
	if condition.status == "False" then
	  health_status.status = "Degraded"
	  health_status.message = condition.message
	  return health_status
	end
  end

  if condition.type == "Synced" then
	if condition.status == "False" then
	  health_status.status = "Degraded"
	  health_status.message = condition.message
	  return health_status
	end
  end

  if contains({"Ready", "Healthy", "Offered", "Established"}, condition.type) then
	if condition.status == "True" then
	  health_status.status = "Healthy"
	  health_status.message = "Resource is up-to-date."
	  return health_status
	end
  end
end

return health_status
`
	deprecatedResourceCustomizationAttributeInArgocdConfigMap = "resource.customizations"
	resourceTrackingMethod                                    = "application.resourceTrackingMethod"
)

// Aggregates all methods responsible for building tenant's controlplane.DataValues struct
type agentConfigBuilder struct {
	reconciler            *argocdInstanceReconciler
	instanceConfig        *models.ArgoCDInstanceConfig
	instanceSpec          *models.InstanceConfigSpec
	instance              *models.ArgoCDInstance
	internalSpec          *models.InstanceInternalSpec
	organization          *models.Organization
	featureSvc            featuresservice.Service
	certStatus            *status.CertificateStatus
	orgFeatureStatus      *featuresv1.FeatureStatuses
	tlsTerminationEnabled bool
}

func newAgentConfigBuilder(reconciler *argocdInstanceReconciler, instanceData *cdInstanceRelatedData, featuresSVC featuresservice.Service, ingressConfig config.IngressConfig) *agentConfigBuilder {
	return &agentConfigBuilder{
		reconciler:            reconciler,
		instance:              instanceData.instance,
		instanceConfig:        instanceData.instanceConfig,
		organization:          instanceData.organization,
		featureSvc:            featuresSVC,
		certStatus:            instanceData.certStatus,
		tlsTerminationEnabled: ingressConfig.TLSTerminationEnabled,
		orgFeatureStatus:      instanceData.orgFeatureStatuses,
	}
}

func (b *agentConfigBuilder) resetFeatureGatedValues(_ context.Context) error {
	if b.instanceConfig == nil {
		return nil
	}

	instanceSpec, err := b.instanceConfig.GetSpec()
	if err != nil {
		return err
	}

	b.instanceSpec = &instanceSpec

	if b.instanceSpec != nil {
		if b.instanceSpec.Css != "" &&
			!b.orgFeatureStatus.GetArgocdCustomStyles().Enabled() {
			b.instanceSpec.Css = ""
		}

		if len(b.instanceSpec.Extensions) > 0 &&
			!b.orgFeatureStatus.GetAkuityArgocdExtensions().Enabled() {
			b.instanceSpec.Extensions = []models.ArgoCDExtensionInstallEntry{}
		}

		if b.instanceSpec.DeclarativeManagementEnabled &&
			!b.orgFeatureStatus.GetAppOfApps().Enabled() {
			b.instanceSpec.DeclarativeManagementEnabled = false
		}

		if b.instanceSpec.MultiClusterK8sDashboardEnabled &&
			!b.orgFeatureStatus.GetMultiClusterK8SDashboard().Enabled() {
			b.instanceSpec.MultiClusterK8sDashboardEnabled = false
		}

		if err = b.instanceConfig.SetSpec(instanceSpec); err != nil {
			return err
		}
	}

	if b.instanceConfig.FQDN.String != "" &&
		!b.orgFeatureStatus.GetArgocdCustomDomain().Enabled() {
		b.instanceConfig.FQDN = null.StringFrom("")
	}

	if b.instanceConfig.Subdomain != "" &&
		!b.orgFeatureStatus.GetArgocdCustomSubdomain().Enabled() {
		b.instanceConfig.Subdomain = ""
	}

	cm, err := b.instanceConfig.GetArgoCDConfigMap()
	if err != nil {
		return err
	}

	if cm != nil {
		if (cm.DexConfig != "" || cm.OidcConfig != "") &&
			!b.orgFeatureStatus.GetArgocdSso().Enabled() {
			cm.DexConfig = ""
			cm.OidcConfig = ""
		}

		if len(cm.ProjectLinks)+len(cm.ApplicationLinks)+len(cm.ResourceLinks) > 0 &&
			!b.orgFeatureStatus.GetArgocdDeepLinks().Enabled() {
			cm.ProjectLinks = nil
			cm.ApplicationLinks = nil
			cm.ResourceLinks = nil
		}

		err = b.instanceConfig.SetArgoCDConfigMap(cm)
		if err != nil {
			return err
		}
	}

	cmp, err := b.instanceConfig.GetArgoCDConfigManagementPlugins()
	if err != nil {
		return err
	}

	if len(cmp.Plugins) > 0 &&
		!b.orgFeatureStatus.GetConfigManagementPlugins().Enabled() {
		cmp.Plugins = []*argocdv1.ConfigManagementPlugin{}
		return b.instanceConfig.SetArgoCDConfigManagementPlugins(cmp)
	}

	return nil
}

func (b *agentConfigBuilder) buildAgentConfigurations(ctx context.Context, generation int32) (*controlplane.DataValues, error) {
	if err := b.resetFeatureGatedValues(ctx); err != nil {
		return nil, err
	}

	instanceSpec, err := b.instanceConfig.GetSpec()
	if err != nil {
		return nil, err
	}

	b.instanceSpec = &instanceSpec

	internalSpec, err := b.instanceConfig.GetInternalSpec()
	if err != nil {
		return nil, err
	}

	b.internalSpec = internalSpec

	var hostAliases []controlplane.DataValuesAdvancedHostAliases
	if instanceSpec.HostAliases != nil {
		hostAliases = make([]controlplane.DataValuesAdvancedHostAliases, len(instanceSpec.HostAliases))
		for i, ha := range instanceSpec.HostAliases {
			hostAliases[i] = controlplane.DataValuesAdvancedHostAliases{Ip: ptr.To(ha.Ip), Hostnames: ha.Hostnames}
		}
	}

	dbInfo, err := database.ExtractDBInfo(b.reconciler.settings.K3sDBConnection)
	if err != nil {
		return nil, err
	}

	privateSpec, err := b.instanceConfig.GetPrivateSpec()
	if err != nil {
		return nil, err
	}

	// Tenant schema is identical to tenant's username
	postgresSchema := privateSpec.K3sUsername
	postgresUsername := privateSpec.K3sUsername
	postgresPassword := privateSpec.K3sPassword

	// Tenant schema is identical to tenant's username
	if err := database.CreateTenantDatabaseSchema(b.reconciler.settings.K3sDBRawClient, ctx, postgresSchema, postgresUsername, postgresPassword, b.reconciler.settings.SharedK3sDBConnectionAuth); err != nil {
		return nil, err
	}

	ingressConfig, err := b.buildIngressConfig(privateSpec.FqdnVersion, b.instanceConfig.InstanceID)
	if err != nil {
		return nil, err
	}

	dataValuesConfig := &controlplane.DataValuesConfig{}
	if err := b.updateDataValuesConfig1(dataValuesConfig, privateSpec.ArgoCDServerSecretKey); err != nil {
		return nil, err
	}

	if err := b.updateDataValuesConfig2(dataValuesConfig, ingressConfig); err != nil {
		return nil, err
	}

	imageUpdaterConfig, err := b.buildImageUpdaterConfig()
	if err != nil {
		return nil, err
	}

	extensions := b.buildExtensions()

	imagePullSecretData, err := b.reconciler.imagePullSecretGetter()
	if err != nil {
		return nil, err
	}

	fallbackRepoServerEnabled, configManagementPlugins, err := b.buildConfigManagementPlugins()
	if err != nil {
		return nil, err
	}

	if b.reconciler.settings.SharedK3sDBConnectionAuth {
		// During DB migration - all tenants temporarily use a shared K3sDBConnection auth to connect to RDS Proxy
		postgresUsername = dbInfo.User
		postgresPassword = dbInfo.Password
	}
	var k3sImage *string
	if internalSpec.K3sImage != "" {
		k3sImage = &internalSpec.K3sImage
	}

	// For the self-hosted version, if k3s proxy informer system level feature gate is set to true, it will be enabled for every organization so that customers don't need to edit database entry per organization to enable that.
	// For the SaaS version, in addition to setting the system level feature gate to true, the organization level feature gate also needs to be set to true to enable informers-based k3s proxy for a specific organization.
	var enableK3sProxyInformers bool
	if config.IsSelfHosted {
		enableK3sProxyInformers = b.featureSvc.GetFeatureStatuses(ctx, nil).GetK3SProxyInformers().Enabled()
	} else {
		enableK3sProxyInformers = b.orgFeatureStatus.GetK3SProxyInformers().Enabled()
	}

	var k3sReadonlyHostname *string
	if b.orgFeatureStatus.GetPgpool().Enabled() || b.orgFeatureStatus.GetPgbouncer().Enabled() {
		k3sReadonlyHostname = ptr.To(b.reconciler.settings.K3sReadonlyHostname)
	}

	if privateSpec.WebhookKey == "" || privateSpec.WebhookCert == "" {
		return nil, fmt.Errorf("webhook key and cert must be set")
	}
	var delegateRedisPassword *string
	if b.instanceSpec.RepoServerDelegate != nil && b.instanceSpec.RepoServerDelegate.ManagedCluster != nil {
		delegateCluster, err := b.reconciler.settings.RepoSet.ArgoCDClusters(
			models.ArgoCDClusterWhere.InstanceID.EQ(b.instanceConfig.InstanceID),
			models.ArgoCDClusterWhere.Name.EQ(b.instanceSpec.RepoServerDelegate.ManagedCluster.ClusterName),
		).One(ctx, "private_spec")
		if err != nil {
			return nil, fmt.Errorf("failed to get delegate cluster: %w", err)
		}
		delegateClusterPrivateSpec, err := delegateCluster.GetPrivateSpec()
		if err != nil {
			return nil, fmt.Errorf("failed to get delegate cluster private spec: %w", err)
		}
		delegateRedisPassword = &delegateClusterPrivateSpec.AgentPassword
	}

	// Fill in the data values for Agent
	features, _ := getArgoCDFeatures(
		b.instanceConfig.Version.String,
		b.orgFeatureStatus,
		instanceSpec.AppInAnyNamespace != nil && instanceSpec.AppInAnyNamespace.Enabled)

	// cannot be nil as ytt will represent it as a {} instead of a []
	allowListSelectors := []*metav1.LabelSelector{}
	for i := range b.instanceSpec.Secrets.Sources {
		allowListSelectors = append(allowListSelectors, b.instanceSpec.Secrets.Sources[i].Secrets.ToMetaLabelSelector())
	}

	// appset plugins
	plugins := []controlplane.DataValuesApplicationsetControllerPlugins{}
	for _, plugin := range instanceSpec.AppsetPlugins {
		plugins = append(plugins, controlplane.DataValuesApplicationsetControllerPlugins{
			Name:           ptr.To(plugin.Name),
			Token:          ptr.To(plugin.Token),
			BaseUrl:        ptr.To(plugin.BaseUrl),
			RequestTimeout: ptr.To(plugin.RequestTimeout),
		})
	}

	dataValues := &controlplane.DataValues{
		ArgoCd: &controlplane.DataValuesArgoCd{
			InstanceId:              &b.instanceConfig.InstanceID,
			Version:                 b.instanceConfig.Version.Ptr(),
			InclusterEnabled:        ptr.To(instanceSpec.DeclarativeManagementEnabled),
			ConfigManagementPlugins: configManagementPlugins,
			Generation:              &generation,
			Features:                features,
			Basepath:                ptr.To(strings.Trim(b.instanceConfig.Basepath, "/")),
		},
		AkuityPlatform: &controlplane.DataValuesAkuityPlatform{
			Config: &controlplane.DataValuesAkuityPlatformConfig{
				CanCreateApps:                internalSpec.CanCreateApps,
				SyncSecretAllowListSelectors: allowListSelectors,
				// Only Argo CD repo-creds are allowed to be mutated
				SystemSecretAllowListSelectors: []*metav1.LabelSelector{
					{
						MatchLabels: map[string]string{
							argoproj.LabelKeySecretType: argoproj.LabelValueSecretTypeRepository,
						},
					},
					{
						MatchLabels: map[string]string{
							argoproj.LabelKeySecretType: argoproj.LabelValueSecretTypeRepoCreds,
						},
					},
				},
			},
			ImagePullSecret: imagePullSecretData,
		},
		K3s: &controlplane.DataValuesK3s{
			Image:               k3sImage,
			KineSchemaMigration: ptr.To(fmt.Sprintf("%v", internalSpec.K3sSchemaMigration)),
			// This token can be useful for K3s HA mode. It's a shared secret to allow other masters to join the pool of masters.
			Token: ptr.To(privateSpec.K3sToken),
			Postgres: &controlplane.DataValuesK3sPostgres{
				Create:     ptr.To(false),
				Hostname:   ptr.To(dbInfo.Host),
				RoHostname: k3sReadonlyHostname,
				Schema:     ptr.To(postgresSchema),
				Username:   ptr.To(postgresUsername),
				Password:   ptr.To(postgresPassword),
				Port:       ptr.To(dbInfo.Port),
				Database:   ptr.To(dbInfo.DBName),
				Sslmode:    ptr.To(dbInfo.SSLMode),
			},
		},
		Pgbouncer: &controlplane.DataValuesPgbouncer{
			Enabled: ptr.To(b.orgFeatureStatus.GetPgbouncer().Enabled()),
		},
		K3sProxy: &controlplane.DataValuesK3sProxy{
			TrafficReduction:       ptr.To(b.orgFeatureStatus.GetK3STrafficReduction().Enabled()),
			UseApplicationInformer: ptr.To(enableK3sProxyInformers),
			UseSecretInformer:      ptr.To(enableK3sProxyInformers),
		},
		Webhook: &controlplane.DataValuesWebhook{
			Key:  ptr.To(privateSpec.WebhookKey),
			Cert: ptr.To(privateSpec.WebhookCert),
		},
		Redis: &controlplane.DataValuesRedis{
			Password:         ptr.To(privateSpec.RedisPassword),
			DelegatePassword: delegateRedisPassword,
		},
		Ingress:      ingressConfig,
		Config:       dataValuesConfig,
		ImageUpdater: imageUpdaterConfig,
		RepoServer: &controlplane.DataValuesRepoServer{
			Delegate: ptr.To(formatRepoServerDelegate(&instanceSpec)),
		},
		ApplicationsetController: &controlplane.DataValuesApplicationsetController{
			Delegate:                ptr.To(formatAppSetDelegate(&instanceSpec)),
			Enabled:                 ptr.To(instanceSpec.IsAppSetInControlPlane()),
			Policy:                  ptr.To(instanceSpec.AppsetPolicy.Policy),
			PolicyOverride:          ptr.To(instanceSpec.AppsetPolicy.PolicyOverrideEnabled),
			ProgressiveSyncsEnabled: ptr.To(instanceSpec.AppsetProgressiveSyncsEnabled),
			Plugins:                 plugins,
		},
		Extensions: extensions,
		ReposerverProxy: &controlplane.DataValuesReposerverProxy{
			Fallback: &controlplane.DataValuesReposerverProxyFallback{
				Enabled: ptr.To(fallbackRepoServerEnabled),
			},
		},
		Advanced: &controlplane.DataValuesAdvanced{
			HostAliases: hostAliases,
		},
		Tls: &controlplane.DataValuesTLS{
			TerminationEnabled: &b.tlsTerminationEnabled,
		},
	}
	dataValues.ArgoCd.AppReconciliationsRateLimiting = controlplane.NewDataValuesArgoCdAppReconciliationsRateLimiting()
	if instanceSpec.AppReconciliationsRateLimiting != nil {
		dataValues.ArgoCd.AppReconciliationsRateLimiting = &controlplane.DataValuesArgoCdAppReconciliationsRateLimiting{
			Bucket: &controlplane.DataValuesArgoCdAppReconciliationsRateLimitingBucket{
				Enabled:    ptr.To(instanceSpec.AppReconciliationsRateLimiting.BucketRateLimiting.Enabled),
				BucketSize: ptr.To(int32(instanceSpec.AppReconciliationsRateLimiting.BucketRateLimiting.BucketSize)),
				BucketQps:  ptr.To(int32(instanceSpec.AppReconciliationsRateLimiting.BucketRateLimiting.BucketQps)),
			},
			Item: &controlplane.DataValuesArgoCdAppReconciliationsRateLimitingItem{
				Enabled:         ptr.To(instanceSpec.AppReconciliationsRateLimiting.ItemRateLimiting.Enabled),
				FailureCooldown: ptr.To(strconv.Itoa(int(instanceSpec.AppReconciliationsRateLimiting.ItemRateLimiting.FailureCooldown))),
				BaseDelay:       ptr.To(strconv.Itoa(int(instanceSpec.AppReconciliationsRateLimiting.ItemRateLimiting.BaseDelay))),
				MaxDelay:        ptr.To(strconv.Itoa(int(instanceSpec.AppReconciliationsRateLimiting.ItemRateLimiting.MaxDelay))),
				BackoffFactor:   ptr.To(float32(instanceSpec.AppReconciliationsRateLimiting.ItemRateLimiting.BackoffFactor)),
			},
		}
	}

	haControlPlaneEnabled := b.orgFeatureStatus.GetArgocdHaControlPlane().Enabled()

	if !haControlPlaneEnabled && !config.IsSelfHosted {
		// Set all configurable replicas to 1 for non-HA control plane
		dataValues.ArgoCd.SetReplicas(int32(1))

		dataValues.K3s.SetAutoscaling(controlplane.DataValuesK3sAutoscaling{
			MinReplicas: ptr.To(int32(1)),
			MaxReplicas: ptr.To(int32(1)),
		})

		dataValues.K3sProxy.SetReplicas(int32(1))

		dataValues.SetPgpool(*controlplane.NewDataValuesPgpool())
		dataValues.Pgpool.SetReplicas(int32(1))

		dataValues.Webhook.SetReplicas(int32(1))

		dataValues.SetRedisHaproxy(*controlplane.NewDataValuesRedisHaproxy())
		dataValues.RedisHaproxy.SetReplicas(int32(1))
	}

	updaterFunctions := []func(*controlplane.DataValues) (*controlplane.DataValues, error){
		// Functions updating the dataValues
		b.updateDataValues,
		b.scaleControlPlane,
		b.patchDataValues,
	}

	for _, updaterFunction := range updaterFunctions {
		dataValues, err = updaterFunction(dataValues)
		if err != nil {
			return nil, err
		}
	}

	return dataValues, nil
}

func (b *agentConfigBuilder) buildIngressConfig(fqdnVersion, instanceId string) (*controlplane.DataValuesIngress, error) {
	if fqdnVersion != "1" {
		return nil, fmt.Errorf("unsupported fqdnVersion: %s", fqdnVersion)
	}
	separator := b.reconciler.settings.GetDomainSeparator()
	shardSubdomain := getShardDomain(b.reconciler.settings.Shard)
	argoCDInstanceUrl := getArgocdInstanceURL(b.instanceConfig, separator, shardSubdomain, b.reconciler.settings.DomainSuffix)
	issueCert := !config.IsSelfHosted && b.instanceConfig.FQDN.String != "" && b.certStatus.IsCNameSet

	dc := &domainConfig{
		InstanceID:             instanceId,
		Subdomain:              b.instanceConfig.Subdomain,
		CustomerDomain:         b.instanceConfig.FQDN.String,
		OverrideDefaultDomains: b.reconciler.settings.OverrideDefaultDomains,
		UseHyphens:             separator == "-",
		DefaultDomain:          fmt.Sprintf("cdsvcs.%s%s", shardSubdomain, b.reconciler.settings.DomainSuffix),
	}

	ingressConfig := controlplane.DataValuesIngress{
		Enabled:     ptr.To(b.reconciler.settings.EnableIngress),
		ExternalDns: b.reconciler.settings.ExternalDNS,
		ArgoCd: &controlplane.DataValuesIngressArgoCd{
			Fqdn: ptr.To(argoCDInstanceUrl),
			LetsEncrypt: &controlplane.DataValuesIngressArgoCdLetsEncrypt{
				Enabled:           ptr.To(issueCert),
				ClusterIssuerName: ptr.To(b.reconciler.settings.ClusterIssuerName),
			},
			InternalUrl: ptr.To(getArgocdInternalInstanceURL(b.instanceConfig, shardSubdomain, b.reconciler.settings.DomainSuffix)),
		},
		ArgoCdRepo: &controlplane.DataValuesIngressArgoCdRepo{
			Fqdn:       ptr.To(dc.GenerateDomain("repo")),
			LegacyFqdn: ptr.To(fmt.Sprintf(repoServerFQDNFormatLegacy, instanceId, shardSubdomain, b.reconciler.settings.DomainSuffix)),
		},
		Redis:       &controlplane.DataValuesIngressRedis{Fqdn: ptr.To(dc.GenerateDomain("cache"))},
		AgentServer: &controlplane.DataValuesIngressRedis{Fqdn: ptr.To(dc.GenerateDomain("agentsvr"))},
		K3sProxy:    &controlplane.DataValuesIngressRedis{Fqdn: ptr.To(dc.GenerateDomain("cplane"))},
	}

	if len(b.instanceSpec.IpAllowlist) > 0 {
		var allowList []string
		for _, ip := range b.instanceSpec.IpAllowlist {
			allowList = append(allowList, ip.Ip)
		}
		if b.reconciler.settings.Shard != "" {
			// it's a shard
			allowList = append(allowList, b.reconciler.settings.PortalIPs...)
		}
		ingressConfig.IpAllowlist = allowList
		ingressConfig.BackendIpAllowlist = ptr.To(b.instanceSpec.BackendIpAllowlistEnabled)
	}

	return &ingressConfig, nil
}

// https://docs.crossplane.io/knowledge-base/integrations/argo-cd-crossplane/#set-health-status
// make sure that we inject the proper health checks to the custom crossplane resources
func generateCustomizationForCrossplaneCustomResources(extension models.CrossplaneExtension, resourceCustomizations []models.ResourceCustomization) []models.ResourceCustomization {
	// we assume that this resource contains the default crossplane groups
	// *.upbound.io/* and *.crossplane.io/*
	for _, resource := range extension.Resources {
		groupKindParts := strings.Split(resource.Group, "/")
		customization := models.ResourceCustomization{Group: groupKindParts[0]}
		if len(groupKindParts) > 1 {
			customization.Kind = groupKindParts[1]
		}

		customization.Health = crossplaneCustomization

		index := slices.IndexFunc(resourceCustomizations, func(item models.ResourceCustomization) bool {
			return item.Group == customization.Group && item.Kind == customization.Kind
		})

		if index < 0 {
			resourceCustomizations = append(resourceCustomizations, customization)
		} else {
			resourceCustomizations[index] = customization
		}
	}

	return resourceCustomizations
}

func (b *agentConfigBuilder) updateDataValuesConfig1(dataValuesConfig *controlplane.DataValuesConfig, argoCDServerSecretKey string) error {
	argoCDConfigMap, err := b.argoCDConfigMap()
	if err != nil {
		return err
	}
	dataValuesConfig.ArgoCd = argoCDConfigMap

	argoCDRbac, err := b.argoCDRBAC()
	if err != nil {
		return err
	}
	dataValuesConfig.Rbac = argoCDRbac

	argoCDSecret, err := b.argoCDSecret(argoCDServerSecretKey)
	if err != nil {
		return err
	}

	googleSa := argoCDSecret[models.AkpDexGoogleSASecretKey]
	if googleSa != "" {
		dataValuesConfig.DexGoogleSaSecret = ptr.To(googleSa)
	}
	delete(argoCDSecret, models.AkpDexGoogleSASecretKey)

	dataValuesConfig.Secret = argoCDSecret

	return nil
}

func (b *agentConfigBuilder) argoCDConfigMap() (map[string]interface{}, error) {
	cm, err := b.instanceConfig.GetArgoCDConfigMap()
	if err != nil {
		return nil, err
	}

	var customization models.ResourceCustomization
	index := slices.IndexFunc(cm.ResourceCustomizations, func(item models.ResourceCustomization) bool {
		return item.Group == "argoproj.io" && item.Kind == "ApplicationSet"
	})
	if index < 0 {
		customization = models.ResourceCustomization{Group: "argoproj.io", Kind: "ApplicationSet"}
		cm.ResourceCustomizations = append(cm.ResourceCustomizations, customization)
		index = len(cm.ResourceCustomizations) - 1
	} else {
		customization = cm.ResourceCustomizations[index]
	}
	if customization.Actions == "" {
		customization.Actions = appSetActionsYAML
		cm.ResourceCustomizations[index] = customization
	}

	if b.instanceSpec != nil && len(b.instanceSpec.CrossplaneExtension.Resources) > 0 {
		cm.ResourceCustomizations = generateCustomizationForCrossplaneCustomResources(b.instanceSpec.CrossplaneExtension, cm.ResourceCustomizations)
	}

	if cm.DexConfig != "" {
		secrets, err := b.instanceConfig.GetArgocdSecret()
		if err != nil {
			return nil, err
		}
		cfg, err := b.instanceConfig.HydrateAndValidateDexConfig(cm.DexConfig, secrets, true)
		if err != nil {
			return nil, fmt.Errorf("failed to hydrate dex config: %w", err)
		}
		cm.DexConfig = cfg
	}

	data, err := json.Marshal(cm)
	if err != nil {
		return nil, err
	}

	var argoCDCM map[string]interface{}
	if err := json.Unmarshal(data, &argoCDCM); err != nil {
		return nil, err
	}

	if b.instanceSpec != nil && len(b.instanceSpec.CrossplaneExtension.Resources) > 0 {
		argoCDCM[resourceTrackingMethod] = "annotation"
	}

	if b.instanceSpec.MultiClusterK8sDashboardEnabled {
		argoCDCM["accounts."+kubeVisionArgoAccountName] = models.AccountCapabilityAPIKey
	} else {
		delete(argoCDCM, "accounts."+kubeVisionArgoAccountName)
	}
	return argoCDCM, nil
}

func argoCDRBACWithKubevisionReadonlyRules(existingPolicyCSV string) string {
	lines := strings.Split(existingPolicyCSV, "\n")

	for _, rule := range kubeVisionRBACReadonlyRules {
		foundRBAC := false

		for _, line := range lines {
			line = strings.TrimSpace(line)

			if line == rule {
				foundRBAC = true
				break
			}
		}

		if !foundRBAC {
			lines = append(lines, rule)
		}
	}

	return strings.Join(lines, "\n")
}

func argoCDRBACWithoutKubevisionReadonlyRules(existingPolicyCSV string) string {
	lines := strings.Split(existingPolicyCSV, "\n")
	newLines := make([]string, 0, len(lines))

	for _, line := range lines {
		shouldKeep := true
		line = strings.TrimSpace(line)

		for _, rule := range kubeVisionRBACReadonlyRules {
			if line == rule {
				shouldKeep = false
				break
			}
		}

		if shouldKeep {
			newLines = append(newLines, line)
		}
	}

	return strings.Join(newLines, "\n")
}

func (b *agentConfigBuilder) argoCDRBAC() (*models.ArgoCDRbacConfigMap, error) {
	rbacConfigMap, err := b.instanceConfig.GetArgoCDRbacConfigMap()
	if err != nil {
		return nil, err
	}
	policyCSV := rbacConfigMap.PolicyCSV
	if b.instanceSpec.MultiClusterK8sDashboardEnabled {
		rbacConfigMap.PolicyCSV = strings.TrimSpace(argoCDRBACWithKubevisionReadonlyRules(policyCSV))
	} else {
		rbacConfigMap.PolicyCSV = strings.TrimSpace(argoCDRBACWithoutKubevisionReadonlyRules(policyCSV))
	}
	return rbacConfigMap, nil
}

func (b *agentConfigBuilder) argoCDSecret(argoCDServerSecretKey string) (map[string]string, error) {
	argoCDSecret, err := b.instanceConfig.GetArgocdSecret()
	if err != nil {
		return nil, err
	}
	if argoCDSecret == nil {
		argoCDSecret = map[string]string{}
	}

	argoCDSecret["server.secretkey"] = argoCDServerSecretKey

	privateSpec, err := b.instanceConfig.GetPrivateSpec()
	if err != nil {
		return nil, err
	}
	if b.instanceSpec.MultiClusterK8sDashboardEnabled {
		token, _ := json.Marshal([]struct {
			IssuedAt int64  `json:"iat"`
			ID       string `json:"id"`
		}{{privateSpec.KubeVisionArgoTokenIssueTime.Unix(), privateSpec.KubeVisionArgoTokenID}})
		argoCDSecret[kubeVisionTokenKey] = string(token)
	} else {
		delete(argoCDSecret, kubeVisionTokenKey)
	}

	return argoCDSecret, nil
}

func (b *agentConfigBuilder) updateDataValuesConfig2(dataValuesConfig *controlplane.DataValuesConfig, ingressConfig *controlplane.DataValuesIngress) error {
	if !b.instanceConfig.ArgocdAppsetSecret.IsZero() {
		argoCDAppsetSecret, err := b.instanceConfig.GetArgoCDAppsetSecret()
		if err != nil {
			return err
		}
		dataValuesConfig.AppsetSecret = argoCDAppsetSecret
	}

	if !b.instanceConfig.ArgocdNotificationsSecret.IsZero() {
		argoCDNotificationsSecret, err := b.instanceConfig.GetArgoCDNotificationsSecret()
		if err != nil {
			return err
		}
		dataValuesConfig.NotificationsSecret = argoCDNotificationsSecret
	}

	if !b.instanceConfig.ArgocdNotificationsCM.IsZero() {
		argoCDNotificationsCM, err := b.notificationConfig(ingressConfig)
		if err != nil {
			return err
		}
		dataValuesConfig.NotificationsConfig = argoCDNotificationsCM
	}

	dataValuesConfig.CustomCss = &b.instanceSpec.Css

	// set privileged accounts for image updater and app set
	dataValuesConfig.PrivilegedAccount = ptr.To(b.privilegedAccounts())
	return nil
}

func (b *agentConfigBuilder) notificationConfig(ingressConfig *controlplane.DataValuesIngress) (map[string]interface{}, error) {
	var argoCDNotificationsCM map[string]interface{}
	if err := json.Unmarshal(b.instanceConfig.ArgocdNotificationsCM.JSON, &argoCDNotificationsCM); err != nil {
		return nil, err
	}

	if argoCDNotificationsCM == nil {
		return map[string]interface{}{}, nil
	}

	//nolint:staticcheck
	// not much we can do about that, so just skip
	_ = b.setArgocdUrl(argoCDNotificationsCM, "https://"+ingressConfig.ArgoCd.GetFqdn())
	return argoCDNotificationsCM, nil
}

func (b *agentConfigBuilder) setArgocdUrl(argoCDNotificationsCM map[string]interface{}, argocdUrl string) error {
	argoContext := map[string]interface{}{}

	if contextValue, ok := argoCDNotificationsCM["context"]; ok {
		contextString, ok := contextValue.(string)
		if !ok {
			return errors.New("context should be string")
		}
		if err := yaml.Unmarshal([]byte(contextString), &argoContext); err != nil {
			return err
		}
		if argoContext == nil {
			argoContext = map[string]interface{}{}
		}
	}

	if _, ok := argoContext["argocdUrl"]; !ok {
		argoContext["argocdUrl"] = argocdUrl
		data, err := yaml.Marshal(argoContext)
		if err != nil {
			return err
		}
		argoCDNotificationsCM["context"] = string(data)
	}

	return nil
}

func (b *agentConfigBuilder) privilegedAccounts() string {
	privilegedAccounts := []string{"portal-server", "argocd-application-controller"}
	if b.instanceSpec.IsAppSetInManagedCluster() {
		privilegedAccounts = append(privilegedAccounts, common.PrivilegedClusterAccountPrefix+common.ClusterPrefix+b.instanceSpec.AppSetDelegate.ManagedCluster.ClusterName)
	}

	if b.instanceSpec.IsImageUpdaterInManagedCluster() {
		privilegedAccounts = append(privilegedAccounts, common.PrivilegedClusterAccountPrefix+common.ClusterPrefix+b.instanceSpec.ImageUpdaterDelegate.ManagedCluster.ClusterName)
	}
	return strings.Join(privilegedAccounts, ",")
}

func (b *agentConfigBuilder) buildImageUpdaterConfig() (*controlplane.DataValuesImageUpdater, error) {
	imageUpdaterConfig := &controlplane.DataValuesImageUpdater{
		Enabled: ptr.To(b.instanceConfig.ArgocdImageUpdaterEnable.Bool && b.instanceSpec.IsImageUpdaterInControlPlane()),
	}

	if !b.instanceConfig.ArgocdImageUpdaterCM.IsZero() {
		var argocdImageUpdaterCM map[string]interface{}
		if err := json.Unmarshal(b.instanceConfig.ArgocdImageUpdaterCM.JSON, &argocdImageUpdaterCM); err != nil {
			return nil, err
		}
		imageUpdaterConfig.ImageUpdaterConfig = argocdImageUpdaterCM
	}

	if !b.instanceConfig.ArgocdImageUpdaterSSHCM.IsZero() {
		var argocdImageUpdaterSSHCM map[string]interface{}
		if err := json.Unmarshal(b.instanceConfig.ArgocdImageUpdaterSSHCM.JSON, &argocdImageUpdaterSSHCM); err != nil {
			return nil, err
		}
		imageUpdaterConfig.ImageUpdaterSshConfig = argocdImageUpdaterSSHCM
	}

	if !b.instanceConfig.ArgocdImageUpdaterSecret.IsZero() {
		argocdImageUpdaterSecret, err := b.instanceConfig.GetArgoCDImageUpdaterSecret()
		if err != nil {
			return nil, err
		}
		imageUpdaterConfig.ImageUpdaterSecret = argocdImageUpdaterSecret
	}

	spec, err := b.instanceConfig.GetSpec()
	if err != nil {
		return nil, err
	}
	imageUpdaterConfig.Tag = &spec.ImageUpdaterVersion
	return imageUpdaterConfig, nil
}

func (b *agentConfigBuilder) buildExtensions() []controlplane.DataValuesExtensions {
	var extensions []controlplane.DataValuesExtensions
	for _, ext := range b.instanceSpec.Extensions {
		extensions = append(extensions, controlplane.DataValuesExtensions{
			Id:      &ext.ID,
			Version: &ext.Version,
		})
	}
	return extensions
}

func (b *agentConfigBuilder) buildConfigManagementPlugins() (bool, *controlplane.DataValuesArgoCdConfigManagementPlugins, error) {
	cmPlugins, err := b.instanceConfig.GetArgoCDConfigManagementPlugins()
	if err != nil {
		return false, nil, err
	}

	fallbackRepoServerEnabled, controlPlaneCMPEnabled, _ := instances.GetConfigManagementPluginEnabled(cmPlugins)
	configManagementPlugins := controlplane.DataValuesArgoCdConfigManagementPlugins{}
	configManagementPlugins.Enabled = ptr.To(controlPlaneCMPEnabled)
	if controlPlaneCMPEnabled {
		var plugins []controlplane.DataValuesArgoCdConfigManagementPluginsPlugins
		for _, p := range cmPlugins.Plugins {
			if p != nil && p.Enabled {
				plugins = append(plugins, controlplane.DataValuesArgoCdConfigManagementPluginsPlugins{
					Name:  ptr.To(p.Name),
					Image: ptr.To(p.Image),
					Spec:  p.Spec,
				})
			}
		}
		configManagementPlugins.Plugins = plugins
	}
	return fallbackRepoServerEnabled, &configManagementPlugins, nil
}

func (b *agentConfigBuilder) updateDataValues(dataValues *controlplane.DataValues) (*controlplane.DataValues, error) {
	if dataValues.AgentServer == nil {
		dataValues.AgentServer = controlplane.NewDataValuesAgentServer()
		dataValues.AgentServer.SetVersion(version.GetLatestAgentVersion())
	}
	if b.reconciler.settings.InstanceConfig.AgentServerImage != "" {
		// "us-docker.pkg.dev/akuity/akp/agent-server:0.4.27" => ["us-docker.pkg.dev/akuity/akp", "agent-server", "0.4.27"]
		host, repo, version, err := manifests.SplitImage(b.reconciler.settings.InstanceConfig.AgentServerImage)
		if err != nil {
			return dataValues, err
		}
		dataValues.AgentServer.SetImageHost(host)
		dataValues.AgentServer.SetImageRepo(repo)
		dataValues.AgentServer.SetVersion(version)
	} else {
		status, err := b.instance.GetStatus()
		if err != nil {
			return dataValues, err
		}
		// The status.Info.RequestedAkuityServerVersion is set by the upgrader job to upgrade instance to the specific version.
		// Otherwise, latest version should be used
		if status.Info.RequestedAkuityServerVersion != "" {
			currentVer, currentVerErr := mmSemver.NewVersion(status.Info.AkuityServerVersion)
			requestedVer, requestedVerErr := mmSemver.NewVersion(status.Info.RequestedAkuityServerVersion)
			if currentVerErr == nil && requestedVerErr == nil && requestedVer.GreaterThan(currentVer) {
				dataValues.AgentServer.SetVersion(status.Info.RequestedAkuityServerVersion)
			}
		}
		// "us-docker.pkg.dev/akuity/akp"
		if b.reconciler.settings.InstanceConfig.AgentServerImageHost != "" {
			dataValues.AgentServer.SetImageHost(b.reconciler.settings.InstanceConfig.AgentServerImageHost)
		}
	}

	agentVersion := dataValues.AgentServer.GetVersion()
	// we need to make sure that we still have applicationset-controller in privileged accounts prior to v0.5.50
	if dataValues.Config.PrivilegedAccount != nil && semver.Compare("v"+agentVersion, "v0.5.49") <= 0 {
		l := strings.Split(*dataValues.Config.PrivilegedAccount, ",")
		l = append(l, "argocd-applicationset-controller")
		dataValues.Config.PrivilegedAccount = ptr.To(strings.Join(l, ","))
	}
	version.UpdateAgentImageHost(dataValues.AgentServer)

	if isAKPImageVersion(dataValues.ArgoCd.GetVersion()) {
		dataValues.ArgoCd.SetImageHost(AkpVersionImgHost)
	}
	// override image host when explicitly mentioned
	if b.reconciler.settings.InstanceConfig.ArgoCDImageHost != "" {
		dataValues.ArgoCd.SetImageHost(b.reconciler.settings.InstanceConfig.ArgoCDImageHost)
	}

	if b.reconciler.settings.InstanceConfig.ArgoCDImageRepo != "" {
		dataValues.ArgoCd.SetImageRepo(b.reconciler.settings.InstanceConfig.ArgoCDImageRepo)
	}

	// set openshift compatibility when enabled
	if b.reconciler.settings.InstanceConfig.OpenshiftCompatibility {
		dataValues.SetOpenshiftCompatible(b.reconciler.settings.InstanceConfig.OpenshiftCompatibility)
	}

	// set sidecar compatibility
	dataValues.SetSidecarFeatureCompatible(b.reconciler.settings.InstanceConfig.SidecarFeatureCompatibility)
	dataValues.SetIpv6OnlyCompatible(b.reconciler.settings.InstanceConfig.IPV6OnlyCompatibility)

	return dataValues, nil
}

func (b *agentConfigBuilder) scaleControlPlane(dataValues *controlplane.DataValues) (*controlplane.DataValues, error) {
	if b.reconciler == nil || b.reconciler.autoscaler == nil || b.reconciler.autoscaler.ScalingMap == nil {
		return dataValues, nil
	}

	scalingMap := b.reconciler.autoscaler.ScalingMap

	k3sType := b.internalSpec.K3sType
	k3sProxyType := b.internalSpec.K3sProxyType
	applicationControllerType := b.internalSpec.ApplicationControllerType
	redisType := b.internalSpec.RedisType
	instanceID := b.instanceConfig.InstanceID

	if k3sType == "" || k3sProxyType == "" || applicationControllerType == "" || redisType == "" {
		// Some or all autoscaler types are not specified yet
		return dataValues, nil
	}

	if resources, ok := scalingMap.K3sTypes[k3sType]; ok {
		updateK3sAutoscalerDataValues(dataValues, resources)
	} else {
		return nil, fmt.Errorf("unknown K3s type %q in instance %q internalSpec", k3sType, instanceID)
	}

	if resources, ok := scalingMap.K3sProxyTypes[k3sProxyType]; ok {
		updateK3sProxyAutoscalerDataValues(dataValues, resources)
	} else {
		return nil, fmt.Errorf("unknown K3s Proxy type %q in instance %q internalSpec", k3sProxyType, instanceID)
	}

	if resources, ok := scalingMap.ApplicationControllerTypes[applicationControllerType]; ok {
		updateApplicationControllerAutoscalerDataValues(dataValues, resources)
	} else {
		return nil, fmt.Errorf("unknown Application Controller type %q in instance %q internalSpec", redisType, instanceID)
	}

	if resources, ok := scalingMap.RedisTypes[redisType]; ok {
		updateRedisAutoscalerDataValues(dataValues, resources)
	} else {
		return nil, fmt.Errorf("unknown Redis type %q in instance %q internalSpec", redisType, instanceID)
	}

	return dataValues, nil
}

func updateK3sAutoscalerDataValues(dataValues *controlplane.DataValues, resources autoscaler.AutoscalerResources) {
	cpu := fmt.Sprintf(cpuFloatFormat, resources.Cpu)
	memory := autoscaler.MemoryValueToString(resources.Memory)
	replicas := int32(resources.Replicas)

	if dataValues.K3s == nil {
		dataValues.SetK3s(controlplane.DataValuesK3s{})
	}

	dataValues.K3s.SetResources(controlplane.DataValuesK3sResources{
		Limits: &controlplane.DataValuesK3sResourcesLimits{
			Memory: &memory,
		},
		Requests: &controlplane.DataValuesK3sResourcesRequests{
			Cpu:    &cpu,
			Memory: &memory,
		},
	})

	dataValues.K3s.SetAutoscaling(controlplane.DataValuesK3sAutoscaling{
		MinReplicas: &replicas,
		MaxReplicas: &replicas,
	})
}

func updateK3sProxyAutoscalerDataValues(dataValues *controlplane.DataValues, resources autoscaler.AutoscalerResources) {
	cpu := fmt.Sprintf(cpuFloatFormat, resources.Cpu)
	memory := autoscaler.MemoryValueToString(resources.Memory)
	replicas := int32(resources.Replicas)

	if dataValues.K3sProxy == nil {
		dataValues.SetK3sProxy(controlplane.DataValuesK3sProxy{})
	}

	dataValues.K3sProxy.SetResources(controlplane.DataValuesK3sProxyResources{
		Requests: &controlplane.DataValuesK3sProxyResourcesRequests{
			Cpu:    &cpu,
			Memory: &memory,
		},
	})

	dataValues.K3sProxy.SetReplicas(replicas)
}

func updateApplicationControllerAutoscalerDataValues(dataValues *controlplane.DataValues, resources autoscaler.AutoscalerResources) {
	cpu := fmt.Sprintf(cpuFloatFormat, resources.Cpu)
	memory := autoscaler.MemoryValueToString(resources.Memory)

	if dataValues.ApplicationController == nil {
		dataValues.SetApplicationController(controlplane.DataValuesApplicationController{})
	}

	dataValues.ApplicationController.SetResources(controlplane.DataValuesApplicationControllerResources{
		Requests: &controlplane.DataValuesApplicationControllerResourcesRequests{
			Cpu:    &cpu,
			Memory: &memory,
		},
	})
}

func updateRedisAutoscalerDataValues(dataValues *controlplane.DataValues, resources autoscaler.AutoscalerResources) {
	cpu := fmt.Sprintf(cpuFloatFormat, resources.Cpu)
	memory := autoscaler.MemoryValueToString(resources.Memory)

	if dataValues.Redis == nil {
		dataValues.SetRedis(controlplane.DataValuesRedis{})
	}

	dataValues.Redis.SetResources(controlplane.DataValuesRedisResources{
		Requests: &controlplane.DataValuesRedisResourcesRequests{
			Cpu:    &cpu,
			Memory: &memory,
		},
	})
}

// Merges all srcMaps into a single result map, appending slices and merging maps recursively
func mergeMaps(srcMaps ...map[string]any) map[string]any {
	result := map[string]any{}
	for _, src := range srcMaps {
		for keySrc, valueSrc := range src {
			newValue := valueSrc
			switch valueSrc := valueSrc.(type) {
			case map[string]any:
				if valueDst, ok := result[keySrc].(map[string]any); ok {
					// Both valueSrc and valueDst are maps, merging them recursively - valueSrc => valueDst
					newValue = mergeMaps(valueDst, valueSrc)
				}
			case []any:
				if valueDst, ok := result[keySrc].([]any); ok {
					// Both valueSrc and valueDst are slices, appending them - valueSrc after valueDst
					newValue = append(valueDst, valueSrc...)
				}
			}
			result[keySrc] = newValue
		}
	}

	return result
}

// Converts a value into a map through JSON marshalling and unmarshalling.
func valueToMap(value any) (map[string]any, error) {
	valueBytes, err := json.Marshal(value)
	if err != nil {
		return nil, err
	}

	var valueMap map[string]any
	if err := json.Unmarshal(valueBytes, &valueMap); err != nil {
		return nil, err
	}

	return valueMap, nil
}

func (b *agentConfigBuilder) patchDataValues(dataValues *controlplane.DataValues) (*controlplane.DataValues, error) {
	internalSpec, err := b.instanceConfig.GetInternalSpec()
	if err != nil {
		return dataValues, err
	}

	var patches []any
	if b.reconciler.settings.InstanceConfig.InstanceValues.Values != nil {
		patches = append(patches, b.reconciler.settings.InstanceConfig.InstanceValues.Values)
	}
	if len(internalSpec.Values) > 0 {
		patches = append(patches, internalSpec.Values)
	}

	if len(patches) == 0 {
		return dataValues, nil
	}

	dataValuesMap, err := valueToMap(dataValues)
	if err != nil {
		return nil, err
	}

	for _, patch := range patches {
		patchMap, err := valueToMap(patch)
		if err != nil {
			return nil, err
		}
		dataValuesMap = mergeMaps(dataValuesMap, patchMap)
	}

	dataValuesBytes, err := json.Marshal(dataValuesMap)
	if err != nil {
		return nil, err
	}

	var patchedDataValues controlplane.DataValues
	if err := json.Unmarshal(dataValuesBytes, &patchedDataValues); err != nil {
		return nil, err
	}

	return &patchedDataValues, nil
}

func getArgocdInternalInstanceURL(instanceConfig *models.ArgoCDInstanceConfig, shardSubdomain, domainSuffix string) string {
	return fmt.Sprintf("%s.cd.%s%s", instanceConfig.InstanceID, shardSubdomain, domainSuffix)
}

func getArgocdInstanceURL(instanceConfig *models.ArgoCDInstanceConfig, separator, shardSubdomain, domainSuffix string) string {
	if instanceConfig.FQDN.String != "" {
		return instanceConfig.FQDN.String
	}
	subdomain := instanceConfig.InstanceID
	if instanceConfig.Subdomain != "" {
		subdomain = instanceConfig.Subdomain
	}

	return fmt.Sprintf("%s%scd.%s%s", subdomain, separator, shardSubdomain, domainSuffix)
}

func getShardDomain(shard string) string {
	if shard != "" {
		return fmt.Sprintf("%s.", shard)
	}
	return ""
}
