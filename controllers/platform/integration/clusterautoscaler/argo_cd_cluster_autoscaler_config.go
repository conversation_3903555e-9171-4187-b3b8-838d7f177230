package clusterautoscaler

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/go-logr/logr"
	"k8s.io/apimachinery/pkg/api/resource"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/internal/utils/consts"
)

const (
	configMapNamespace     = consts.AkuityPlatformNamespace
	configMapName          = "argocd-cluster-autoscaler"
	k8sResourceReadTimeout = 20 * time.Second

	AppCtrlrMinCpuKey        = "application-controller.resources.minimum.cpu"
	AppCtrlrMinMemKey        = "application-controller.resources.minimum.memory"
	AppCtrlrMaxCpuKey        = "application-controller.resources.maximum.cpu"
	AppCtrlrMaxMemKey        = "application-controller.resources.maximum.memory"
	AppCtrlrMultiplierCpuKey = "application-controller.resources.multiplier.cpu"
	AppCtrlrMultiplierMemKey = "application-controller.resources.multiplier.memory"

	RepoServerMinCpuKey              = "repo-server.resources.minimum.cpu"
	RepoServerMinMemKey              = "repo-server.resources.minimum.memory"
	RepoServerMaxCpuKey              = "repo-server.resources.maximum.cpu"
	RepoServerMaxMemKey              = "repo-server.resources.maximum.memory"
	RepoServerMultiplierCpuKey       = "repo-server.resources.multiplier.cpu"
	RepoServerMultiplierMemKey       = "repo-server.resources.multiplier.memory"
	RepoServerIncThresholdKey        = "repo-server.resources.inc-threshold"
	RepoServerDecThresholdKey        = "repo-server.resources.dec-threshold"
	RepoServerReplicaIncThresholdKey = "repo-server.replicas.inc-threshold"
	RepoServerMinReplicaKey          = "repo-server.replicas.minimum"
	RepoServerMaxReplicaKey          = "repo-server.replicas.maximum"

	mustBeSetInConfigMapErrStrFmt = "%s must be set in the Cluster Autoscaler configmap"
)

func NewConfig(log *logr.Logger, k8sClient kubernetes.Interface) (common.AutoScalerConfig, error) {
	if log == nil {
		return common.AutoScalerConfig{}, errors.New("failed to build Cluster Autoscaler config, logger cannot be nil")
	}

	if k8sClient == nil {
		return common.AutoScalerConfig{}, errors.New("failed to build Cluster Autoscaler config, k8s client cannot be nil")
	}

	ctx, cancel := context.WithTimeout(context.Background(), k8sResourceReadTimeout)
	defer cancel()

	configMap, err := k8sClient.CoreV1().ConfigMaps(configMapNamespace).Get(ctx, configMapName, v1.GetOptions{})
	if configMap == nil || err != nil {
		return common.AutoScalerConfig{}, fmt.Errorf("failed to read Cluster Autoscaler ConfigMap: %w", err)
	}

	log.Info("Cluster Autoscaler ConfigMap was successfully read", "configMap", configMap.Data)
	conf, err := buildConfigFromConfigMapData(configMap.Data)
	if err != nil {
		return common.AutoScalerConfig{}, err
	}

	log.Info("Cluster Autoscaler was successfully built using ConfigMap data")

	return conf, nil
}

func buildConfigFromConfigMapData(configMapData map[string]string) (common.AutoScalerConfig, error) {
	conf := common.AutoScalerConfig{}

	appCtrlrMinCpu, ok := configMapData[AppCtrlrMinCpuKey]
	if !ok {
		return conf, fmt.Errorf(mustBeSetInConfigMapErrStrFmt, AppCtrlrMinCpuKey)
	}

	appCtrlrMinCpuQty, err := resource.ParseQuantity(appCtrlrMinCpu)
	if err != nil {
		return conf, fmt.Errorf("%s in the Cluster Autoscaler configmap must be a valid CPU quantity", AppCtrlrMinCpuKey)
	}

	appCtrlrMaxCpu, ok := configMapData[AppCtrlrMaxCpuKey]
	if !ok {
		return conf, fmt.Errorf(mustBeSetInConfigMapErrStrFmt, AppCtrlrMaxCpuKey)
	}

	appCtrlrMaxCpuQty, err := resource.ParseQuantity(appCtrlrMaxCpu)
	if err != nil {
		return conf, fmt.Errorf("%s in the Cluster Autoscaler configmap must be a valid CPU quantity", AppCtrlrMaxCpuKey)
	}

	appCtrlrMinMem, ok := configMapData[AppCtrlrMinMemKey]
	if !ok {
		return conf, fmt.Errorf(mustBeSetInConfigMapErrStrFmt, AppCtrlrMinMemKey)
	}

	appCtrlrMinMemQty, err := resource.ParseQuantity(appCtrlrMinMem)
	if err != nil {
		return conf, fmt.Errorf("%s in the Cluster Autoscaler configmap must be a valid Memory quantity", AppCtrlrMinMemKey)
	}

	appCtrlrMaxMem, ok := configMapData[AppCtrlrMaxMemKey]
	if !ok {
		return conf, fmt.Errorf(mustBeSetInConfigMapErrStrFmt, AppCtrlrMaxMemKey)
	}

	appCtrlrMaxMemQty, err := resource.ParseQuantity(appCtrlrMaxMem)
	if err != nil {
		return conf, fmt.Errorf("%s in the Cluster Autoscaler configmap must be a valid Memory quantity", AppCtrlrMinMemKey)
	}

	appCtrlrMultiplierCpuStr, ok := configMapData[AppCtrlrMultiplierCpuKey]
	if !ok {
		return conf, fmt.Errorf(mustBeSetInConfigMapErrStrFmt, AppCtrlrMultiplierCpuKey)
	}

	appCtrlrMultiplierCpu, err := strconv.ParseFloat(appCtrlrMultiplierCpuStr, 64)
	if err != nil {
		return conf, fmt.Errorf("%s in the Cluster Autoscaler configmap must be a number", AppCtrlrMultiplierCpuKey)
	}

	appCtrlrMultiplierMemStr, ok := configMapData[AppCtrlrMultiplierMemKey]
	if !ok {
		return conf, fmt.Errorf(mustBeSetInConfigMapErrStrFmt, AppCtrlrMultiplierMemKey)
	}

	appCtrlrMultiplierMem, err := strconv.ParseFloat(appCtrlrMultiplierMemStr, 64)
	if err != nil {
		return conf, fmt.Errorf("%s in the Cluster Autoscaler configmap must be a number", AppCtrlrMultiplierMemKey)
	}

	conf.ApplicationController = &common.AppControllerAutoScalingConfig{
		ResourceMinimum: common.WorkloadResourceValues{
			CPU:    appCtrlrMinCpuQty,
			Memory: appCtrlrMinMemQty,
		},
		ResourceMaximum: common.WorkloadResourceValues{
			CPU:    appCtrlrMaxCpuQty,
			Memory: appCtrlrMaxMemQty,
		},
		ResourceMultiplier: common.WorkloadResourcesMultiplier{
			CPU:    appCtrlrMultiplierCpu,
			Memory: appCtrlrMultiplierMem,
		},
	}

	repoServerMinCpu, ok := configMapData[RepoServerMinCpuKey]
	if !ok {
		return conf, fmt.Errorf(mustBeSetInConfigMapErrStrFmt, RepoServerMinCpuKey)
	}

	repoServerMinCpuQty, err := resource.ParseQuantity(repoServerMinCpu)
	if err != nil {
		return conf, fmt.Errorf("%s in the Cluster Autoscaler configmap must be a valid CPU quantity", RepoServerMinCpuKey)
	}

	repoServerMaxCpu, ok := configMapData[RepoServerMaxCpuKey]
	if !ok {
		return conf, fmt.Errorf(mustBeSetInConfigMapErrStrFmt, RepoServerMaxCpuKey)
	}

	repoServerMaxCpuQty, err := resource.ParseQuantity(repoServerMaxCpu)
	if err != nil {
		return conf, fmt.Errorf("%s in the Cluster Autoscaler configmap must be a valid CPU quantity", RepoServerMaxCpuKey)
	}

	repoServerMinMem, ok := configMapData[RepoServerMinMemKey]
	if !ok {
		return conf, fmt.Errorf(mustBeSetInConfigMapErrStrFmt, RepoServerMinMemKey)
	}

	repoServerMinMemQty, err := resource.ParseQuantity(repoServerMinMem)
	if err != nil {
		return conf, fmt.Errorf("%s in the Cluster Autoscaler configmap must be a valid Memory quantity", RepoServerMinMemKey)
	}

	repoServerMaxMem, ok := configMapData[RepoServerMaxMemKey]
	if !ok {
		return conf, fmt.Errorf(mustBeSetInConfigMapErrStrFmt, RepoServerMaxMemKey)
	}

	repoServerMaxMemQty, err := resource.ParseQuantity(repoServerMaxMem)
	if err != nil {
		return conf, fmt.Errorf("%s in the Cluster Autoscaler configmap must be a valid Memory quantity", RepoServerMaxMemKey)
	}

	repoServerMultiplierCpuStr, ok := configMapData[RepoServerMultiplierCpuKey]
	if !ok {
		return conf, fmt.Errorf(mustBeSetInConfigMapErrStrFmt, RepoServerMultiplierCpuKey)
	}

	repoServerMultiplierCpu, err := strconv.ParseFloat(repoServerMultiplierCpuStr, 64)
	if err != nil {
		return conf, fmt.Errorf("%s in the Cluster Autoscaler configmap must be a number", RepoServerMultiplierCpuKey)
	}

	repoServerMultiplierMemStr, ok := configMapData[RepoServerMultiplierMemKey]
	if !ok {
		return conf, fmt.Errorf(mustBeSetInConfigMapErrStrFmt, RepoServerMultiplierMemKey)
	}

	repoServerMultiplierMem, err := strconv.ParseFloat(repoServerMultiplierMemStr, 64)
	if err != nil {
		return conf, fmt.Errorf("%s in the Cluster Autoscaler configmap must be a number", RepoServerMultiplierMemKey)
	}

	repoServerIncThresholdStr, ok := configMapData[RepoServerIncThresholdKey]
	if !ok {
		return conf, fmt.Errorf(mustBeSetInConfigMapErrStrFmt, RepoServerIncThresholdKey)
	}

	repoServerIncThreshold, err := strconv.ParseInt(repoServerIncThresholdStr, 10, 64)
	if err != nil {
		return conf, fmt.Errorf("%s in the Cluster Autoscaler configmap must be a number", RepoServerIncThresholdKey)
	}

	repoServerDecThresholdStr, ok := configMapData[RepoServerDecThresholdKey]
	if !ok {
		return conf, fmt.Errorf(mustBeSetInConfigMapErrStrFmt, RepoServerDecThresholdKey)
	}

	repoServerDecThreshold, err := strconv.ParseInt(repoServerDecThresholdStr, 10, 64)
	if err != nil {
		return conf, fmt.Errorf("%s in the Cluster Autoscaler configmap must be a number", RepoServerDecThresholdKey)
	}

	repoServerReplicaIncThresholdStr, ok := configMapData[RepoServerReplicaIncThresholdKey]
	if !ok {
		return conf, fmt.Errorf(mustBeSetInConfigMapErrStrFmt, RepoServerReplicaIncThresholdKey)
	}

	repoServerReplicaIncThreshold, err := strconv.ParseInt(repoServerReplicaIncThresholdStr, 10, 64)
	if err != nil {
		return conf, fmt.Errorf("%s in the Cluster Autoscaler configmap must be a number", RepoServerReplicaIncThresholdKey)
	}

	repoServerMinReplicaStr, ok := configMapData[RepoServerMinReplicaKey]
	if !ok {
		return conf, fmt.Errorf(mustBeSetInConfigMapErrStrFmt, RepoServerMinReplicaKey)
	}

	repoServerMinReplica, err := strconv.ParseInt(repoServerMinReplicaStr, 10, 32)
	if err != nil {
		return conf, fmt.Errorf("%s in the Cluster Autoscaler configmap must be a number", RepoServerMinReplicaKey)
	}

	repoServerMaxReplicaStr, ok := configMapData[RepoServerMaxReplicaKey]
	if !ok {
		return conf, fmt.Errorf(mustBeSetInConfigMapErrStrFmt, RepoServerMaxReplicaKey)
	}

	repoServerMaxReplica, err := strconv.ParseInt(repoServerMaxReplicaStr, 10, 32)
	if err != nil {
		return conf, fmt.Errorf("%s in the Cluster Autoscaler configmap must be a number", RepoServerMaxReplicaKey)
	}

	conf.RepoServer = &common.RepoServerAutoScalingConfig{
		ResourceMinimum: common.WorkloadResourceValues{
			CPU:    repoServerMinCpuQty,
			Memory: repoServerMinMemQty,
		},
		ResourceMaximum: common.WorkloadResourceValues{
			CPU:    repoServerMaxCpuQty,
			Memory: repoServerMaxMemQty,
		},
		ResourceMultiplier: common.WorkloadResourcesMultiplier{
			CPU:    repoServerMultiplierCpu,
			Memory: repoServerMultiplierMem,
		},
		ReplicaMaximum:       int32(repoServerMaxReplica),
		ReplicaMinimum:       int32(repoServerMinReplica),
		ReplicaIncThreshold:  repoServerReplicaIncThreshold,
		ResourceIncThreshold: int32(repoServerIncThreshold),
		ResourceDecThreshold: int32(repoServerDecThreshold),
	}

	return conf, nil
}
