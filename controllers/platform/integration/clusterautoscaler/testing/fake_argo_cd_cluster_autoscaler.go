package clusterautoscalertesting

import (
	"context"

	corev1 "k8s.io/api/core/v1"

	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/controllers/platform/integration/clusterautoscaler"
	"github.com/akuityio/akuity-platform/internal/argoproj/argocd"
	"github.com/akuityio/akuity-platform/models/models"
)

type FakeAutoscaler struct{}

func (f FakeAutoscaler) AutoscaleCluster(ctx context.Context, cluster *models.ArgoCDCluster, getClusterInfo clusterautoscaler.GetClusterInfoFunc) (bool, error) {
	return false, nil
}

func (f FakeAutoscaler) BuildApplicationControllerResources(
	ctx context.Context,
	cluster *models.ArgoCDCluster,
	currentApplicationControllerResources corev1.ResourceRequirements,
	getClusterInfo clusterautoscaler.GetClusterInfoFunc,
) (bool, corev1.ResourceRequirements, error) {
	return false, corev1.ResourceRequirements{}, nil
}

func (f FakeAutoscaler) BuildRepoServerResources(
	clusterID string,
	clusterSpec *models.ClusterSpec,
	currentRepoServerResources corev1.ResourceRequirements,
	currentRepoServerReplicas int32,
	appControllerResources corev1.ResourceRequirements,
) (corev1.ResourceRequirements, int32, error) {
	return corev1.ResourceRequirements{}, 0, nil
}

func (f FakeAutoscaler) UseAgentSizeForRepoServerResources(agentResources models.AgentResources, appControllerResources corev1.ResourceRequirements) (bool, error) {
	return false, nil
}

func (f FakeAutoscaler) GetClusterInfo(
	ctx context.Context,
	cluster *models.ArgoCDCluster,
	getClusterInfoFromCache clusterautoscaler.GetClusterInfoFromRedisFunc,
) (argocd.ClusterInfo, error) {
	return argocd.ClusterInfo{}, nil
}

func (f FakeAutoscaler) GetConfig() common.AutoScalerConfig {
	return common.AutoScalerConfig{}
}
