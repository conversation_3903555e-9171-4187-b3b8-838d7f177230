package clusterautoscaler

import (
	"context"
	"errors"
	"fmt"

	"github.com/go-logr/logr"
	"github.com/redis/go-redis/v9"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/internal/argoproj/argocd"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

const (
	// Only scale down when new resources are <= 80% of current resources.
	ScaleDownThreshold      = 0.8
	redisPasswordSecretName = "argocd-redis-ha"
	RedisPasswordSecretKey  = "REDIS_PASSWORD"
	BytesPerMebibyte        = 1048576

	oldCPURequestLogKey = "old CPU request"
	oldCPULimitLogKey   = "old CPU limit"
	oldMemRequestLogKey = "old memory request"
	oldMemLimitLogKey   = "old memory limit"

	newCPURequestLogKey = "new CPU request"
	newCPULimitLogKey   = "new CPU limit"
	newMemRequestLogKey = "new memory request"
	newMemLimitLogKey   = "new memory limit"
)

type (
	GetClusterInfoFromRedisFunc func(ctx context.Context, password string, cluster *models.ArgoCDCluster) (argocd.ClusterInfo, error)
	GetClusterInfoFunc          func(context.Context, *models.ArgoCDCluster, GetClusterInfoFromRedisFunc) (argocd.ClusterInfo, error)
)

type Autoscaler interface {
	AutoscaleCluster(ctx context.Context, cluster *models.ArgoCDCluster, fn GetClusterInfoFunc) (bool, error)
	BuildApplicationControllerResources(ctx context.Context, cluster *models.ArgoCDCluster, currentApplicationControllerResources corev1.ResourceRequirements, fn GetClusterInfoFunc) (bool, corev1.ResourceRequirements, error)
	GetClusterInfo(ctx context.Context, cluster *models.ArgoCDCluster, fn GetClusterInfoFromRedisFunc) (argocd.ClusterInfo, error)
	GetConfig() common.AutoScalerConfig
}

type autoscaler struct {
	log       *logr.Logger
	conf      common.AutoScalerConfig
	k8sClient kubernetes.Interface
	repoSet   client.RepoSet
}

type ArgocdClusterData struct {
	InternalSpec *models.ClusterInternalSpec
}

func NewAutoscaler(log *logr.Logger, conf common.AutoScalerConfig, k8sClient kubernetes.Interface, repoSet client.RepoSet) (Autoscaler, error) {
	if log == nil {
		return autoscaler{}, errors.New("failed to build Cluster Autoscaler, logger cannot be nil")
	}

	if k8sClient == nil {
		return autoscaler{}, errors.New("failed to build Cluster Autoscaler, k8sClient cannot be nil")
	}

	if repoSet == nil {
		return autoscaler{}, errors.New("failed to build Cluster Autoscaler, repoSet cannot be nil")
	}

	log.Info("Cluster Autoscaler initialized")

	return autoscaler{
		log:       log,
		conf:      conf,
		k8sClient: k8sClient,
		repoSet:   repoSet,
	}, nil
}

func (a autoscaler) GetConfig() common.AutoScalerConfig {
	return a.conf
}

// AutoscaleCluster - updates Cluster.InternalSpec with the resource requests/limits for the Application Controller
// and Repo Server if agent size is set to `Auto`.
func (a autoscaler) AutoscaleCluster(ctx context.Context, cluster *models.ArgoCDCluster, getClusterInfo GetClusterInfoFunc) (bool, error) {
	if ctx == nil {
		return false, errors.New("ctx cannot be empty")
	}

	if cluster == nil {
		return false, errors.New("cluster cannot be empty")
	}

	spec, err := cluster.GetSpec()
	if err != nil {
		return false, fmt.Errorf("failed to get cluster %q spec: %w", cluster.ID, err)
	}

	if spec.Size != models.ClusterSizeAuto || cluster.AutoUpgradeDisabled {
		return false, nil
	}

	internalSpec, err := cluster.GetInternalSpec()
	if err != nil {
		return false, fmt.Errorf("failed to get cluster %q internal spec: %w", cluster.ID, err)
	}

	changed, newAgentResources, err := a.buildResources(ctx, cluster, spec, internalSpec, getClusterInfo)
	if !changed || err != nil {
		return false, err
	}

	clusterData := &ArgocdClusterData{}
	if internalSpec != nil {
		clusterData.InternalSpec = internalSpec
	} else {
		clusterData.InternalSpec = &models.ClusterInternalSpec{}
	}

	clusterData.InternalSpec.AutoAgentResources = newAgentResources

	if err = cluster.SetInternalSpec(*clusterData.InternalSpec); err != nil {
		return false, err
	}

	if err := a.repoSet.ArgoCDClusters().Update(ctx, cluster, "internal_spec"); err != nil {
		return false, err
	}

	return true, nil
}

func (a autoscaler) buildResources(ctx context.Context, cluster *models.ArgoCDCluster, spec *models.ClusterSpec, internalSpec *models.ClusterInternalSpec, getClusterInfo GetClusterInfoFunc) (bool, *models.AgentResources, error) {
	// If this is a new cluster, or the cluster agent size has just been changed to auto,
	// the auto agent resources in the internal spec will be nil. In this case, use empty
	// resource requirements to compare the newly calculated resource requirements to.
	currentAppControllerResources := corev1.ResourceRequirements{}
	if internalSpec != nil && internalSpec.AutoAgentResources != nil {
		currentAppControllerResources = internalSpec.AutoAgentResources.ControllerRequirements
	}

	var appControllerResourcesChanged bool
	var appControllerResources corev1.ResourceRequirements
	var err error
	if getClusterInfo == nil {
		appControllerResourcesChanged, appControllerResources, err = a.BuildApplicationControllerResources(
			ctx, cluster, currentAppControllerResources, a.GetClusterInfo)
	} else {
		appControllerResourcesChanged, appControllerResources, err = a.BuildApplicationControllerResources(
			ctx, cluster, currentAppControllerResources, getClusterInfo)
	}

	if !appControllerResourcesChanged || err != nil {
		return false, nil, err
	}

	versionResources, err := models.GetAgentResources(spec.SizeVersion)
	if err != nil {
		return false, nil, err
	}

	// we don't scale repo server and use medium size.
	// in the future we will be doing repo server autoscaling using repo server metrics.
	mediumResources := versionResources[models.ClusterSizeMedium]
	repoServerResources := mediumResources.RepoServerRequirements
	repoServerReplicas := mediumResources.RepoServerReplicas

	return true, &models.AgentResources{
		ControllerRequirements: appControllerResources,
		RepoServerRequirements: repoServerResources,
		RepoServerReplicas:     repoServerReplicas,
	}, nil
}

// BuildApplicationControllerResources calculates resources to use for the Application Controller based on the number
// of Kubernetes API resource objects being managed. The count of managed objects is pulled from Redis. We multiple the
// count by a multiplier for CPU and a multiplier for Memory. This gives us the desired resourcing for the Application Controller.
func (a autoscaler) BuildApplicationControllerResources(
	ctx context.Context,
	cluster *models.ArgoCDCluster,
	currentApplicationControllerResources corev1.ResourceRequirements,
	getClusterInfo GetClusterInfoFunc,
) (bool, corev1.ResourceRequirements, error) {
	info, err := getClusterInfo(ctx, cluster, getClusterInfoFromRedis)
	if err != nil {
		return false, currentApplicationControllerResources, fmt.Errorf("could not build application controller resources: %w", err)
	}
	if info.ConnectionState.Status != argocd.ConnectionStatusSuccessful {
		// skip autoscaling until application controller is stable
		a.log.Info("Skip Cluster autoscaler, waiting for application controller connection state to became successful", "status", info.ConnectionState.Status, "cluster", cluster.ID)
		return false, currentApplicationControllerResources, nil
	}
	spec, err := cluster.GetSpec()
	if err != nil {
		return false, currentApplicationControllerResources, err
	}
	conf := OverrideClusterAutoscalerConfig(a.GetConfig(), spec.AutoscalerConfig)
	numberOfManagedResourceObjects := info.CacheInfo.ResourcesCount
	newApplicationControllerCPU := float64(numberOfManagedResourceObjects) * conf.ApplicationController.ResourceMultiplier.CPU

	newApplicationControllerMemory := float64(numberOfManagedResourceObjects) * conf.ApplicationController.ResourceMultiplier.Memory * BytesPerMebibyte

	newApplicationControllerResources := corev1.ResourceRequirements{
		Requests: map[corev1.ResourceName]resource.Quantity{
			corev1.ResourceCPU:    *resource.NewMilliQuantity(int64(newApplicationControllerCPU), resource.BinarySI),
			corev1.ResourceMemory: *resource.NewQuantity(int64(newApplicationControllerMemory), resource.BinarySI),
		},
		Limits: map[corev1.ResourceName]resource.Quantity{
			corev1.ResourceMemory: *resource.NewQuantity(int64(newApplicationControllerMemory), resource.BinarySI),
		},
	}

	newApplicationControllerResources = EnforceAppControllerMinMax(conf.ApplicationController, newApplicationControllerResources)

	// We catch two cases here.
	// 1) If the current Application Controller resource requests are nil or zero, scale to the new resource requests.
	// 2) Don't scale down unless the new resources are under 80% of the current resources. The idea here is to prevent thrashing.
	// A better approach may be to only allow scale down of x% at a time, and store a timestamp of the last scale down in the db so
	// we can limit scale down to once every x minutes.
	cpuScaleDownThreshold := resource.NewMilliQuantity(int64(currentApplicationControllerResources.Requests.Cpu().AsApproximateFloat64()*ScaleDownThreshold*1000), resource.BinarySI)
	memoryScaleDownThreshold := resource.NewQuantity(int64(currentApplicationControllerResources.Requests.Memory().AsApproximateFloat64()*ScaleDownThreshold), resource.BinarySI)
	if currentApplicationControllerResources.Requests.Cpu() != nil && currentApplicationControllerResources.Requests.Memory() != nil &&
		currentApplicationControllerResources.Requests.Cpu().AsApproximateFloat64() > 0 && currentApplicationControllerResources.Requests.Memory().AsApproximateFloat64() > 0 &&
		newApplicationControllerResources.Requests.Cpu().AsApproximateFloat64() >= cpuScaleDownThreshold.AsApproximateFloat64() &&
		newApplicationControllerResources.Requests.Cpu().AsApproximateFloat64() <= currentApplicationControllerResources.Requests.Cpu().AsApproximateFloat64() &&
		newApplicationControllerResources.Requests.Memory().AsApproximateFloat64() >= memoryScaleDownThreshold.AsApproximateFloat64() &&
		newApplicationControllerResources.Requests.Memory().AsApproximateFloat64() <= currentApplicationControllerResources.Requests.Memory().AsApproximateFloat64() {
		a.log.Info("Application Controller does not need to be scaled", "clusterId", cluster.ID,
			oldCPURequestLogKey, currentApplicationControllerResources.Requests.Cpu(),
			newCPURequestLogKey, newApplicationControllerResources.Requests.Cpu(),
			oldCPULimitLogKey, currentApplicationControllerResources.Limits.Cpu(),
			newCPULimitLogKey, newApplicationControllerResources.Limits.Cpu(),
			oldMemRequestLogKey, currentApplicationControllerResources.Requests.Memory(),
			newMemRequestLogKey, newApplicationControllerResources.Requests.Memory(),
			oldMemLimitLogKey, currentApplicationControllerResources.Limits.Memory(),
			newMemLimitLogKey, newApplicationControllerResources.Limits.Memory(),
		)

		return false, currentApplicationControllerResources, nil
	}

	a.log.Info("Scaling an Application Controller",
		"clusterId", cluster.ID,
		oldCPURequestLogKey, currentApplicationControllerResources.Requests.Cpu(),
		newCPURequestLogKey, newApplicationControllerResources.Requests.Cpu(),
		oldCPULimitLogKey, currentApplicationControllerResources.Limits.Cpu(),
		newCPULimitLogKey, newApplicationControllerResources.Limits.Cpu(),
		oldMemRequestLogKey, currentApplicationControllerResources.Requests.Memory(),
		newMemRequestLogKey, newApplicationControllerResources.Requests.Memory(),
		oldMemLimitLogKey, currentApplicationControllerResources.Limits.Memory(),
		newMemLimitLogKey, newApplicationControllerResources.Limits.Memory(),
	)

	return true, newApplicationControllerResources, nil
}

func (a autoscaler) GetClusterInfo(ctx context.Context, cluster *models.ArgoCDCluster, getClusterInfoFromCache GetClusterInfoFromRedisFunc) (argocd.ClusterInfo, error) {
	ctx, cancel := context.WithTimeout(ctx, k8sResourceReadTimeout)
	defer cancel()

	redisSecret, err := a.k8sClient.CoreV1().Secrets(fmt.Sprintf("argocd-%s", cluster.InstanceID)).Get(ctx, redisPasswordSecretName, metav1.GetOptions{})
	if redisSecret == nil || err != nil {
		return argocd.ClusterInfo{}, fmt.Errorf("failed to read Redis password from secret %s for instance %s, k8s API returned an error: %w", redisPasswordSecretName, cluster.InstanceID, err)
	}

	redisPassword, ok := redisSecret.Data[RedisPasswordSecretKey]
	if !ok {
		return argocd.ClusterInfo{}, fmt.Errorf("failed to read Redis password from secret %s for instance %s, expected key missing (%s)", redisPasswordSecretName, cluster.InstanceID, RedisPasswordSecretKey)
	}

	return getClusterInfoFromCache(ctx, string(redisPassword), cluster)
}

func getClusterInfoFromRedis(ctx context.Context, password string, cluster *models.ArgoCDCluster) (argocd.ClusterInfo, error) {
	opts := &redis.Options{
		Addr:     fmt.Sprintf("argocd-redis-ha-haproxy.%s:6379", fmt.Sprintf("argocd-%s", cluster.InstanceID)),
		Password: password,
		DB:       0,
		Username: "default",
	}

	redisCacheClient := argocd.NewCacheClient(opts)
	info, err := redisCacheClient.GetClusterInfo(ctx, fmt.Sprintf("http://cluster-%s:8001", cluster.Name))
	return info, err
}

func OverrideClusterAutoscalerConfig(platformConfig common.AutoScalerConfig, clusterConfig *common.AutoScalerConfig) common.AutoScalerConfig {
	if clusterConfig != nil {
		platformConfig.ApplicationController.ResourceMaximum = clusterConfig.ApplicationController.ResourceMaximum
		platformConfig.ApplicationController.ResourceMinimum = clusterConfig.ApplicationController.ResourceMinimum
		platformConfig.RepoServer.ResourceMaximum = clusterConfig.RepoServer.ResourceMaximum
		platformConfig.RepoServer.ResourceMinimum = clusterConfig.RepoServer.ResourceMinimum
		platformConfig.RepoServer.ReplicaMaximum = clusterConfig.RepoServer.ReplicaMaximum
		platformConfig.RepoServer.ReplicaMinimum = clusterConfig.RepoServer.ReplicaMinimum
	}
	return platformConfig
}

func EnforceAppControllerMinMax(conf *common.AppControllerAutoScalingConfig, requirements corev1.ResourceRequirements) corev1.ResourceRequirements {
	return enforceResourcesMinMax(conf.ResourceMinimum, conf.ResourceMaximum, requirements)
}

func enforceResourcesMinMax(minimum, maximum common.WorkloadResourceValues, requirements corev1.ResourceRequirements) corev1.ResourceRequirements {
	newCPU := requirements.Requests.Cpu().MilliValue()
	newCPU = max(minimum.CPU.MilliValue(), newCPU)
	newCPU = min(maximum.CPU.MilliValue(), newCPU)

	newMemory := requirements.Requests.Memory().AsApproximateFloat64()
	newMemory = max(minimum.Memory.AsApproximateFloat64(), newMemory)
	newMemory = min(maximum.Memory.AsApproximateFloat64(), newMemory)
	return corev1.ResourceRequirements{
		Requests: map[corev1.ResourceName]resource.Quantity{
			corev1.ResourceCPU:    *resource.NewMilliQuantity(int64(newCPU), resource.BinarySI),
			corev1.ResourceMemory: *resource.NewQuantity(int64(newMemory), resource.BinarySI),
		},
		Limits: map[corev1.ResourceName]resource.Quantity{
			corev1.ResourceMemory: *resource.NewQuantity(int64(newMemory), resource.BinarySI),
		},
	}
}

func EnforceRepoServerMinMax(conf *common.RepoServerAutoScalingConfig, replicas int32, requirements corev1.ResourceRequirements) (int32, corev1.ResourceRequirements) {
	replicas = max(replicas, conf.ReplicaMinimum)
	replicas = min(replicas, conf.ReplicaMaximum)
	return replicas, enforceResourcesMinMax(conf.ResourceMinimum, conf.ResourceMaximum, requirements)
}
