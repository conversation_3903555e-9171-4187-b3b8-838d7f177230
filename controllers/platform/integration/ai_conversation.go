package integration

import (
	"context"
	"database/sql"
	"fmt"

	"k8s.io/client-go/rest"

	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/ai"
	"github.com/akuityio/akuity-platform/internal/services/features"
	loggingutil "github.com/akuityio/akuity-platform/internal/utils/logging"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

type aiConversationReconciler struct {
	repoSet        client.RepoSet
	db             *sql.DB
	cfg            config.AIConfig
	hostRestConfig *rest.Config
	featureSvc     features.Service
}

func NewAiConversationReconciler(featureSvc features.Service, repoSet client.RepoSet, db *sql.DB, cfg config.AIConfig, hostRestConfig *rest.Config) *aiConversationReconciler {
	return &aiConversationReconciler{
		repoSet:        repoSet,
		db:             db,
		cfg:            cfg,
		hostRestConfig: hostRestConfig,
		featureSvc:     featureSvc,
	}
}

func (r *aiConversationReconciler) ItemToID(item *models.AiConversation) string {
	return item.ID
}

func (r *aiConversationReconciler) IDColumn() string {
	return fmt.Sprintf("%s as %s", models.AiConversationTableColumns.ID, models.AiConversationColumns.ID)
}

func (r *aiConversationReconciler) LogValuesFromID(id string) []interface{} {
	return []interface{}{"ai_conversation_id", id}
}

func (r *aiConversationReconciler) LogValuesFromItem(item *models.AiConversation) []interface{} {
	return nil
}

func (r *aiConversationReconciler) Reconcile(ctx context.Context, item *models.AiConversation) error {
	srv, err := ai.NewService(r.db, r.repoSet, r.featureSvc.GetFeatureStatuses(ctx, &item.OrganizationID), item.OrganizationID, r.cfg, r.hostRestConfig, loggingutil.GetContextLogger(ctx), ai.ServiceOptionWithInstanceID(item.InstanceID.String), ai.ServiceOptionWithKargoInstanceID(item.KargoInstanceID.String))
	if err != nil {
		return err
	}

	return srv.ProcessConversation(ctx, item.ID)
}
