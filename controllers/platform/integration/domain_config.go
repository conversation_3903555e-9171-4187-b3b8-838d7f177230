package integration

import (
	"fmt"
)

type domainConfig struct {
	InstanceID             string
	CustomerDomain         string // e.g. "customer-domain.com"
	DefaultDomain          string // e.g. "cdsvcs.mydomain.com"
	Subdomain              string
	OverrideDefaultDomains bool
	UseHyphens             bool // flag to use hyphens for leftmost subdomain
}

// GenerateDomain generates a domain name for the given service type and instance
func (dc *domainConfig) GenerateDomain(serviceType string) string {
	// If customer domain is specified, use it as the base
	if dc.OverrideDefaultDomains && dc.CustomerDomain != "" {
		if dc.UseHyphens {
			// Only hyphenate the service type, don't replace dots in customer domain
			return fmt.Sprintf("%s-%s", serviceType, dc.CustomerDomain)
		}

		// Use service type as subdomain
		return fmt.Sprintf("%s.%s", serviceType, dc.CustomerDomain)
	}

	var prefix string

	if dc.OverrideDefaultDomains && dc.Subdomain != "" {
		prefix = dc.Subdomain
	} else {
		prefix = dc.InstanceID
	}

	// For default domains, always use the standard format: prefix-servicetype.defaultdomain
	// UseHyphens flag only applies to customer domains
	return fmt.Sprintf("%s-%s.%s", prefix, serviceType, dc.DefaultDomain)
}
