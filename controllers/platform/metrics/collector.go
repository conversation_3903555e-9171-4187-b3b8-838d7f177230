package metrics

import (
	"context"
	"database/sql"
	"strconv"
	"sync"
	"time"

	"github.com/go-logr/logr"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"

	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
	"github.com/akuityio/akuity-platform/models/util/status"
)

type metricsCollector struct {
	collectionInterval     time.Duration
	log                    *logr.Logger
	repoSet                client.RepoSet
	argoInstancesStatuses  map[InstanceStatus]int
	argoClustersStatuses   map[ClusterStatus]int
	kargoInstancesStatuses map[InstanceStatus]int
	kargoAgentsStatuses    map[AgentStatus]int
	refresher              *StatusRefresher
	refreshErrors          prometheus.CounterVec
	refreshHeartbeat       prometheus.HistogramVec
	lock                   sync.Mutex
}

var (
	descArgoInstanceTotal = prometheus.NewDesc(
		"argo_cd_instances_total",
		"Argo CD Instances counts by health, reconciliation and trial status",
		[]string{"health", "reconcile", "trial", "eventsSynced", "version"},
		nil,
	)

	descArgoClustersTotal = prometheus.NewDesc(
		"argo_cd_clusters_total",
		"Argo CD Clusters counts by health, reconciliation, trial, and connection status",
		[]string{"health", "reconcile", "trial", "connected", "degraded"},
		nil,
	)

	descKargoInstanceTotal = prometheus.NewDesc(
		"kargo_instances_total",
		"Kargo Instances counts by health, reconciliation and trial status",
		[]string{"health", "reconcile", "trial", "eventsSynced", "version"},
		nil,
	)

	descKargoAgentsTotal = prometheus.NewDesc(
		"kargo_agents_total",
		"Kargo Agents counts by health, reconciliation, trial, and connection status",
		[]string{"health", "reconcile", "trial", "connected", "degraded"},
		nil,
	)
)

type AggregateArgoInstanceData struct {
	Instance models.ArgoCDInstance `boil:"instance, bind"`
	Version  string                `boil:"version" json:"version,omitempty" toml:"version" yaml:"version,omitempty"`
	Trial    bool                  `boil:"trial" json:"trial,omitempty" toml:"trial" yaml:"trial,omitempty"`
}

type AggregateArgoClusterData struct {
	Cluster models.ArgoCDCluster `boil:"cluster, bind"`
	Trial   bool                 `boil:"trial" json:"trial,omitempty" toml:"trial" yaml:"trial,omitempty"`
}

type AggregateKargoInstanceData struct {
	Instance models.KargoInstance `boil:"instance, bind"`
	Version  string               `boil:"version" json:"version,omitempty" toml:"version" yaml:"version,omitempty"`
	Trial    bool                 `boil:"trial" json:"trial,omitempty" toml:"trial" yaml:"trial,omitempty"`
}

type AggregateKargoAgentData struct {
	Agent models.KargoAgent `boil:"agent, bind"`
	Trial bool              `boil:"trial" json:"trial,omitempty" toml:"trial" yaml:"trial,omitempty"`
}
type InstanceStatus struct {
	status.HealthStatusCode
	status.ReconciliationStatusCode
	trial        bool
	eventsSynced bool
	version      string
}

type ClusterStatus struct {
	status.ReconciliationStatusCode
	trial     bool
	connected bool
	healthy   bool
	degraded  bool
}

type AgentStatus struct {
	status.ReconciliationStatusCode
	trial     bool
	connected bool
	healthy   bool
	degraded  bool
}

// IsHealthy - determines if the InstanceStatus represents a healthy Argo/Kargo Instance
func (i InstanceStatus) IsHealthy() bool {
	return (i.HealthStatusCode == status.HealthStatusCodeHealthy) &&
		(i.ReconciliationStatusCode == status.ReconciliationStatusCodeSuccessful)
}

// IsHealthy - determines if the ClusterStatus represents a healthy Argo Cluster, returns true if cluster is disconnected
func (c ClusterStatus) IsHealthy() bool {
	if c.connected {
		return c.healthy && c.ReconciliationStatusCode == status.ReconciliationStatusCodeSuccessful
	}
	return true
}

// IsHealthy - determines if the AgentStatus represents a healthy Kargo Agent, returns true if agent is disconnected
func (c AgentStatus) IsHealthy() bool {
	if c.connected {
		return c.healthy && c.ReconciliationStatusCode == status.ReconciliationStatusCodeSuccessful
	}
	return true
}

func NewMetricsCollector(cfg config.PlatformControllerConfig, portalDBRawClient *sql.DB, repoSet client.RepoSet, log *logr.Logger, shard string) *metricsCollector {
	return &metricsCollector{
		collectionInterval: cfg.MetricsCollectionInterval,
		log:                log,
		repoSet:            repoSet,
		refresher:          NewStatusRefresher(cfg.Deadlines, portalDBRawClient, log, shard, context.Background()),
		refreshErrors: *promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "refresh_errors",
				Help: "Total number of metrics refresh errors",
			}, nil),
		refreshHeartbeat: *promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "refresh_loop_duration_seconds",
			Help:    "Metrics refresh loop latencies distribution",
			Buckets: prometheus.LinearBuckets(0.01, 0.05, 40),
		}, nil),
	}
}

func (c *metricsCollector) Run(ctx context.Context) error {
	ticker := time.NewTicker(c.collectionInterval)
	defer ticker.Stop()

	refresh := func() {
		startTime := time.Now()
		err := c.refreshHealthStatuses()
		c.refreshHeartbeat.WithLabelValues().Observe(time.Since(startTime).Seconds())

		if err != nil {
			c.refreshErrors.WithLabelValues().Inc()
			c.log.Error(err, "Failed to refresh Health metrics")
		}
	}
	refresh()
	for {
		select {
		case <-ctx.Done():
			break
		case <-ticker.C:
			refresh()
		}
	}
}

func (c *metricsCollector) refreshHealthStatuses() error {
	argoInstancesStatuses, err := c.refresher.RefreshArgoInstances()
	if err != nil {
		return err
	}

	argoClustersStatuses, err := c.refresher.RefreshArgoClusters()
	if err != nil {
		return err
	}

	kargoInstancesStatuses, err := c.refresher.RefreshKargoInstances()
	if err != nil {
		return err
	}

	kargoAgentsStatuses, err := c.refresher.RefreshKargoAgents()
	if err != nil {
		return err
	}

	c.lock.Lock()
	c.argoInstancesStatuses = countIDs(argoInstancesStatuses)
	c.argoClustersStatuses = countIDs(argoClustersStatuses)
	c.kargoInstancesStatuses = countIDs(kargoInstancesStatuses)
	c.kargoAgentsStatuses = countIDs(kargoAgentsStatuses)
	c.lock.Unlock()
	return nil
}

// Converts map[T][]string (map of Health status to the corresponding IDs) => map[T]int (map of Health status to number of IDs),
// using the same entries and counting the slice size (len([]string)) as a value
func countIDs[T comparable](healthStatusMapWithIDs map[T][]string) map[T]int {
	healthStatusMapWithCounters := make(map[T]int)
	for key, IDs := range healthStatusMapWithIDs {
		healthStatusMapWithCounters[key] = len(IDs)
	}
	return healthStatusMapWithCounters
}

func (c *metricsCollector) Describe(ch chan<- *prometheus.Desc) {
	ch <- descArgoInstanceTotal
	ch <- descArgoClustersTotal
	ch <- descKargoInstanceTotal
	ch <- descKargoAgentsTotal
}

func (c *metricsCollector) Collect(ch chan<- prometheus.Metric) {
	c.lock.Lock()
	argoInstancesStatuses := c.argoInstancesStatuses
	argoClustersStatuses := c.argoClustersStatuses
	kargoInstancesStatuses := c.kargoInstancesStatuses
	kargoAgentsStatuses := c.kargoAgentsStatuses
	c.lock.Unlock()

	for instancesStatus, counterValue := range argoInstancesStatuses {
		ch <- prometheus.MustNewConstMetric(descArgoInstanceTotal, prometheus.GaugeValue, float64(counterValue),
			// descArgoInstanceTotal labels: []string{"health", "reconcile", "trial", "eventsSynced", "version"}
			string(instancesStatus.HealthStatusCode),
			string(instancesStatus.ReconciliationStatusCode),
			strconv.FormatBool(instancesStatus.trial),
			strconv.FormatBool(instancesStatus.eventsSynced),
			instancesStatus.version,
		)
	}

	for clustersStatus, counterValue := range argoClustersStatuses {
		ch <- prometheus.MustNewConstMetric(descArgoClustersTotal, prometheus.GaugeValue, float64(counterValue),
			// descArgoClustersTotal labels: []string{"health", "reconcile", "trial", "connected", "degraded"}
			strconv.FormatBool(clustersStatus.healthy),
			string(clustersStatus.ReconciliationStatusCode),
			strconv.FormatBool(clustersStatus.trial),
			strconv.FormatBool(clustersStatus.connected),
			strconv.FormatBool(clustersStatus.degraded))
	}

	for instancesStatus, counterValue := range kargoInstancesStatuses {
		ch <- prometheus.MustNewConstMetric(descKargoInstanceTotal, prometheus.GaugeValue, float64(counterValue),
			// descKargoInstanceTotal labels: []string{"health", "reconcile", "trial", "eventsSynced", "version"}
			string(instancesStatus.HealthStatusCode),
			string(instancesStatus.ReconciliationStatusCode),
			strconv.FormatBool(instancesStatus.trial),
			strconv.FormatBool(instancesStatus.eventsSynced),
			instancesStatus.version,
		)
	}

	for agentStatus, counterValue := range kargoAgentsStatuses {
		ch <- prometheus.MustNewConstMetric(descKargoAgentsTotal, prometheus.GaugeValue, float64(counterValue),
			// descKargoAgentsTotal labels: []string{"health", "reconcile", "trial", "connected", "degraded"}
			strconv.FormatBool(agentStatus.healthy),
			string(agentStatus.ReconciliationStatusCode),
			strconv.FormatBool(agentStatus.trial),
			strconv.FormatBool(agentStatus.connected),
			strconv.FormatBool(agentStatus.degraded))
	}
}
