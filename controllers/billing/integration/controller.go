package integration

import (
	"context"
	"database/sql"
	"math/rand/v2"
	"time"

	"github.com/go-logr/logr"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	runtimeutil "k8s.io/apimachinery/pkg/util/runtime"

	"github.com/akuityio/akuity-platform/controllers/shared/lib"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/license"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/version"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/events"
	"github.com/akuityio/akuity-platform/models/models"
	"github.com/akuityio/akuity-platform/pkg/billing"
)

type billingController struct {
	billingController database.TableController
	watchers          []lib.CanStart
	log               *logr.Logger
	settings          ControllerSettings
}

type ControllerSettings struct {
	RepoSet            client.RepoSet
	PortalDBRawClient  *sql.DB
	PortalDBConnection string
	Log                *logr.Logger
}

func (s *ControllerSettings) GetPortalDBContext() database.DBContext {
	return database.NewPGDbContext(s.PortalDBRawClient, s.PortalDBConnection)
}

func NewBillingController(
	log *logr.Logger,
	settings ControllerSettings,
	cfg config.BillingControllerConfig,
	billingProviders map[billing.ProviderName]billing.IProvider,
) (*billingController, error) {
	featSvc := features.NewService(settings.RepoSet, settings.PortalDBRawClient, config.IsSelfHosted, config.IsSelfHostedReleaseBuild, cfg.FeatureGatesSource, license.License{}, features.WithLogger(log))

	billingReconcilerInstance := NewBillingReconciler(log, settings.RepoSet, billingProviders, settings.PortalDBRawClient, featSvc, cfg.DomainSuffix)

	var billingWatcher database.Watcher[events.Event] = database.NewWatcher[events.Event](*log, settings.GetPortalDBContext(), events.BillingChannel)

	billingErrors := promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "billing_errors",
			Help: "Total number of billing errors, per controller",
		}, []string{"controller"})

	billingEnqueueHeartbeat := promauto.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "billing_enqueue_loop_duration_seconds",
		Help:    "Billing controller enqueue latencies distribution, per controller",
		Buckets: prometheus.LinearBuckets(0.01, 0.05, 40),
	}, []string{"controller"})

	billingHeartbeat := promauto.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "billing_loop_duration_seconds",
		Help:    "Billing controller reconciliation latencies distribution, per controller",
		Buckets: prometheus.LinearBuckets(0.01, 0.05, 40),
	}, []string{"controller"})

	bc := &billingController{
		watchers: []lib.CanStart{billingWatcher},
		billingController: database.NewTableController[*models.Billing](
			settings.RepoSet.Billing(),
			*log,
			"billing",
			func(ctx context.Context) <-chan string {
				return lib.SubscribeToIds(ctx, billingWatcher, lib.EventSpecChanged, lib.GetEventId)
			},
			billingReconcilerInstance,
			billingErrors,
			billingEnqueueHeartbeat,
			billingHeartbeat,
			database.WithResyncDuration[*models.Billing](cfg.ResyncDuration),
			database.WithReconciliationTimeout[*models.Billing](cfg.ReconciliationTimeout),
			database.WithEnqueueAllDelayFunc[*models.Billing](func() time.Duration {
				// Jitter before executing enqueueAll() operation, between 0 and 20% of ResyncDuration
				return time.Duration(float64(cfg.ResyncDuration) * 0.2 * rand.Float64())
			}),
			database.WithEnqueueDelayFunc[*models.Billing](func(i, count int) time.Duration {
				// Jitter when enqueueing an item into a queue, between 0 and 1000 ms
				return time.Duration(float64(1000)*rand.Float64()) * time.Millisecond
			}),
		),
		settings: settings,
		log:      log,
	}

	return bc, nil
}

func (bc *billingController) Init(ctx context.Context) error {
	for _, watcher := range bc.watchers {
		if err := watcher.Start(ctx); err != nil {
			return err
		}
	}

	return nil
}

func (bc *billingController) Run(ctx context.Context, numWorkers int) error {
	versionInfo := version.GetVersion()
	bc.log.Info("Akuity Billing Controller Starting", "version", versionInfo.Version, "build_date", versionInfo.BuildDate)

	defer runtimeutil.HandleCrash()

	if err := bc.billingController.Start(ctx, numWorkers); err != nil {
		return err
	}

	<-ctx.Done()
	return nil
}
