package integration

import (
	"bytes"
	"context"
	"database/sql"
	"fmt"

	"github.com/go-logr/logr"
	"github.com/volatiletech/null/v8"

	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/services/organizations"
	billingutil "github.com/akuityio/akuity-platform/internal/utils/billing"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
	"github.com/akuityio/akuity-platform/pkg/billing"
)

var orgUpdateFields = []string{"spec", "max_workspaces", "max_applications", "max_instances", "max_clusters", "org_status", "plan", "max_kargo_instances", "max_kargo_stages", "max_kargo_agents"}

type billingReconciler struct {
	log              *logr.Logger
	repoSet          client.RepoSet
	billingProviders map[billing.ProviderName]billing.IProvider
	db               *sql.DB
	featSvc          features.Service
	domainSuffix     string
}

func NewBillingReconciler(
	log *logr.Logger,
	repoSet client.RepoSet,
	billingProviders map[billing.ProviderName]billing.IProvider,
	db *sql.DB,
	featSvc features.Service,
	domainSuffix string,
) *billingReconciler {
	return &billingReconciler{
		log:              log,
		repoSet:          repoSet,
		billingProviders: billingProviders,
		db:               db,
		featSvc:          featSvc,
		domainSuffix:     domainSuffix,
	}
}

func (r *billingReconciler) ItemToID(item *models.Billing) string {
	return item.ID
}

func (r *billingReconciler) IDColumn() string {
	return models.BillingTableColumns.ID
}

func (r *billingReconciler) LogValuesFromID(id string) []interface{} {
	return []interface{}{"billing_id", id}
}

func (r *billingReconciler) LogValuesFromItem(item *models.Billing) []interface{} {
	return []interface{}{}
}

// inactive billing records are not reconciled and should have minimum limits
func (r *billingReconciler) Deactivate(ctx context.Context, model *models.Billing) error {
	org, err := r.repoSet.Organizations().GetByID(ctx, model.OrganizationID)
	if err != nil {
		return err
	}

	orgUpdate, err := organizations.Deactivate(org)
	if err != nil {
		return err
	}

	if err := r.repoSet.Organizations().Update(ctx, orgUpdate, orgUpdateFields...); err != nil {
		return err
	}

	r.log.V(1).Info("customer deactivated", "billing_id", model.ID, "customer_id", model.CustomerID)
	return nil
}

func (r *billingReconciler) Reconcile(ctx context.Context, customerModel *models.Billing) error {
	if customerModel.Inactive.Bool {
		return r.Deactivate(ctx, customerModel)
	}
	provider, err := billingutil.GetProvider(customerModel.BillingAuthority, r.billingProviders)
	if err != nil {
		return err
	}
	// skipping generation check because in case a webhook event was missed the generation bump won't be noticed
	providerCustomerData, err := provider.GetCustomerDetails(ctx, customerModel.CustomerID)
	if err != nil {
		return err
	}

	// If customer was deleted in Stripe, mark them as inactive to skip reconciliation, and manual to prevent self-onboarding
	if providerCustomerData.Deleted {
		if err := r.repoSet.Billing().Update(ctx, &models.Billing{ID: customerModel.ID, Inactive: null.BoolFrom(true), Manual: null.BoolFrom(true)}, "inactive", "manual"); err != nil {
			return fmt.Errorf("failed to mark customer as inactive (%s): %w", customerModel.CustomerID, err)
		}
		r.log.V(1).Info("customer deleted in Stripe, marked as inactive", "billing_id", customerModel.ID, "customer_id", customerModel.CustomerID)
		return nil
	}

	updatedCustomerModel := customerModel.DeepCopy()
	updatedCustomerModel.Manual = null.BoolFrom(providerCustomerData.Manual)
	updatedCustomerModel.OrganizationID = providerCustomerData.OrgID
	// Only update billing email for self-onboarded customers
	if !providerCustomerData.Manual {
		updatedCustomerModel.BillingEmail = null.StringFrom(providerCustomerData.Email)
	}

	if err = updatedCustomerModel.SetMetadata(models.BillingMetadata{
		Name: providerCustomerData.Name,
	}); err != nil {
		return fmt.Errorf("failed to set billing metadata: %w", err)
	}

	equals, err := customerModel.DeepEquals(&updatedCustomerModel)
	if err != nil {
		return fmt.Errorf("failed to compare billing data: %w", err)
	}

	// If billing data has changed, update it in the database
	if !equals {
		// If this customer is manual, set the billing email in our database to null
		if updatedCustomerModel.Manual.Bool {
			updatedCustomerModel.BillingEmail = null.String{}
		}
		if err = r.repoSet.Billing().Update(ctx, &updatedCustomerModel, "manual", "customer_id", "organization_id", "billing_metadata", "billing_email"); err != nil {
			return fmt.Errorf("failed to update billing data: %w", err)
		} else {
			r.log.V(1).Info("updated billing data", "billing_id", customerModel.ID)
		}
		r.log.V(1).Info("updated billing data", "billing_id", customerModel.ID)
	}

	// 1. if no subscription data found do not update anything
	// 2. if subscription data is found but the latest invoice is not paid, do not update anything
	//     a. in case where a subscription was already active, the details of expiry and limits remain as is
	//     b. in case the user was in trial but created new unpaid subscription,
	//        do not update and keep user in trial period until subscription is paid
	if providerCustomerData.SubscriptionStatus != nil && !providerCustomerData.SubscriptionStatus.Overdue {
		// if customer previously cancelled and resubscribed, we need to reset the deletion timestamp to null
		if meta, err := customerModel.GetMetadata(); err == nil && meta.DidCancel {
			if _, err = models.ArgoCDInstances(models.ArgoCDInstanceWhere.OrganizationOwner.EQ(customerModel.OrganizationID)).UpdateAll(ctx, r.db, models.M{"deletion_timestamp": null.NullBytes}); err != nil {
				return err
			}

			// same for kargo instances
			if _, err = models.KargoInstances(models.KargoInstanceWhere.OrganizationOwner.EQ(null.StringFrom(customerModel.OrganizationID))).UpdateAll(ctx, r.db, models.M{"deletion_timestamp": null.NullBytes}); err != nil {
				return err
			}

			meta.DidCancel = false
			if err = customerModel.SetMetadata(meta); err != nil {
				return err
			}
			if err = r.repoSet.Billing().Update(ctx, customerModel, "billing_metadata"); err != nil {
				return err
			}
		}

		oldOrg, err := r.repoSet.Organizations().GetByID(ctx, customerModel.OrganizationID)
		if err != nil {
			return err
		}

		orgUpdate := &models.Organization{
			ID: customerModel.OrganizationID,
			// Plan is not updated if it's empty, so we need to set it to the old value
			Plan: oldOrg.Plan,
		}

		subscriptionContainsValidProduct := false
		if providerCustomerData.Plan != "" {
			subscriptionContainsValidProduct = true
			if providerCustomerData.Plan != oldOrg.Plan.String { // only update if plan has changed
				orgUpdate.Plan = null.StringFrom(providerCustomerData.Plan)

				instanceSvc := instances.NewServiceWithOptions(r.db, r.domainSuffix, r.featSvc)
				// DO NOT DISABLE FEATURES
				// INSTEAD KEEP SETTINGS AS IT IS BUT DISABLE THE *ACTION* MOSTLY IN CONTROLLER
				if err = instanceSvc.BumpInstancesAndClustersGeneration(ctx, oldOrg.ID); err != nil {
					return err
				}
			}
		}
		// Usage limits include _the sum of_ plan limits and limits from any addons or legacy products
		if providerCustomerData.UsageLimits != nil {
			subscriptionContainsValidProduct = true
			orgUpdate.MaxApplications = int(providerCustomerData.UsageLimits.MaxApplications)
			orgUpdate.MaxInstances = int(providerCustomerData.UsageLimits.MaxInstances)
			orgUpdate.MaxClusters = int(providerCustomerData.UsageLimits.MaxClusters)
			orgUpdate.MaxWorkspaces = int(providerCustomerData.UsageLimits.MaxWorkspaces)
			orgUpdate.MaxKargoStages = int(providerCustomerData.UsageLimits.MaxKargoStages)
			orgUpdate.MaxKargoInstances = int(providerCustomerData.UsageLimits.MaxKargoInstances)

			if providerCustomerData.UsageLimits.MaxAiCostPerMonth > 0 {
				orgSpec, err := orgUpdate.GetSpec()
				if err != nil {
					return err
				}

				orgSpec.AI.MaxCostPerMonth = &providerCustomerData.UsageLimits.MaxAiCostPerMonth

				if err = orgUpdate.SetSpec(*orgSpec); err != nil {
					return err
				}
			}
		}

		if !subscriptionContainsValidProduct {
			r.log.V(1).Info("subscription does not contain valid product", "billing_id", customerModel.ID, "organization_id", customerModel.OrganizationID)
			return nil
		}

		status, err := oldOrg.GetOrgStatus()
		if err != nil {
			return err
		}
		if err := orgUpdate.SetOrgStatus(models.OrgStatus{
			Trial:                false,
			ExpiryTime:           providerCustomerData.SubscriptionStatus.EndTime.Unix(),
			BillingUpdating:      false,
			LastEventProcessedAt: status.LastEventProcessedAt,
			State:                models.PaidCustomer,
		}); err != nil {
			return err
		}

		if orgUpdated(oldOrg, orgUpdate) {
			if err = r.repoSet.Organizations().Update(ctx, orgUpdate, orgUpdateFields...); err != nil {
				return err
			}
			r.log.Info("customer org updated", "id", customerModel.ID, "org_id", customerModel.OrganizationID, "usage_limits", providerCustomerData.UsageLimits, "plan", providerCustomerData.Plan)
		}
	} else {
		org, err := r.repoSet.Organizations().GetByID(ctx, customerModel.OrganizationID)
		if err != nil {
			return err
		}

		status, err := org.GetOrgStatus()

		oldState := status.State
		if err != nil {
			return err
		}

		// if stripe subscription is expired or does not exist then user is not paid customer anymore unless it is handled manually
		var newState models.BillingState
		if providerCustomerData.Manual {
			newState = models.PaidCustomer
		} else {
			newState = ""
		}

		if oldState != newState {
			status.State = newState

			if err = org.SetOrgStatus(*status); err != nil {
				return err
			}

			if err = r.repoSet.Organizations().Update(ctx, org, models.OrganizationColumns.OrgStatus); err != nil {
				return err
			}

			r.log.Info("customer org updated", "org_id", customerModel.OrganizationID, "state", newState)
			return nil
		}

		// Debug level log
		r.log.V(1).Info("customer not reconciled", "id", customerModel.ID, "subscription_nil", providerCustomerData.SubscriptionStatus == nil, "limits_nil", providerCustomerData.UsageLimits == nil, "overdue", providerCustomerData.SubscriptionStatus != nil && providerCustomerData.SubscriptionStatus.Overdue)
	}
	return nil
}

func orgUpdated(old, new *models.Organization) bool {
	return old.MaxWorkspaces != new.MaxWorkspaces ||
		old.MaxClusters != new.MaxClusters ||
		old.MaxApplications != new.MaxApplications ||
		old.MaxInstances != new.MaxInstances ||
		old.MaxKargoInstances != new.MaxKargoInstances ||
		old.MaxKargoStages != new.MaxKargoStages ||
		old.MaxKargoAgents != new.MaxKargoAgents ||
		!bytes.Equal(old.OrgStatus.JSON, new.OrgStatus.JSON) ||
		!bytes.Equal(old.Spec.JSON, new.Spec.JSON) ||
		old.Plan.String != new.Plan.String
}

func (r *billingReconciler) UpdateFeatures(ctx context.Context, organization *models.Organization) error {
	orgInstances, err := r.repoSet.ArgoCDInstances(models.ArgoCDInstanceWhere.OrganizationOwner.EQ(organization.ID)).ListAll(ctx)
	if err != nil {
		return err
	}

	featureStatuses := r.featSvc.GetFeatureStatusesWithOrg(ctx, organization)

	instanceSvc := instances.NewServiceWithOptions(r.db,
		r.domainSuffix,
		r.featSvc,
		instances.WithOrganizationScope(organization.ID),
		instances.WithLogger(logging.Extract(ctx)),
	)
	for _, instance := range orgInstances {
		if err := instanceSvc.DisableRestrictedFeatures(ctx, instance.ID, featureStatuses); err != nil {
			return err
		}
	}

	return nil
}
