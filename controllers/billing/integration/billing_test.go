package integration

import (
	"context"
	"encoding/base64"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/akuityio/akuity-platform/internal/license"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/utils/logging"
	modelstesting "github.com/akuityio/akuity-platform/models/client/testing"
	"github.com/akuityio/akuity-platform/models/models"
	"github.com/akuityio/akuity-platform/models/util/encryption"
	"github.com/akuityio/akuity-platform/pkg/billing"
)

var cryptKey []byte

func init() {
	var err error
	// this is a real key generated from `aws kms generate-data-key --key-spec AES_256`
	// https://awscli.amazonaws.com/v2/documentation/api/latest/reference/kms/generate-data-key.html
	if cryptKey, err = base64.StdEncoding.DecodeString("m8PTL8tiENXfaOPqop78ljrdaoloXn+w/HeTIWMUgO4="); err != nil {
		panic(err)
	}
}

func TestReconcile_BillingReconciledSuccessfully(t *testing.T) {
	err := encryption.SetCryptKey(cryptKey)
	require.NoError(t, err)
	billingProvider := &billing.FakeBillingProvider{}
	billingProvider.Customers = map[string]*billing.Customer{
		"cust_1234": {
			Name:  "customer 1",
			Email: "<EMAIL>",
			Id:    "cust_1234",
			OrgID: "org_1234",
			SubscriptionStatus: &billing.SubscriptionStatus{
				Overdue: false,
			},
			UsageLimits: &models.OrgQuota{
				MaxApplications: 100,
				MaxClusters:     2,
				MaxInstances:    10,
			},
		},
	}
	repoSet := modelstesting.NewInMemoryRepoSet()
	org := &models.Organization{ID: "org_1234", Name: "my-cluster", MaxClusters: 0, MaxInstances: 0, MaxApplications: 0}
	repoSet.OrganizationsRepo.Items[org.ID] = org

	customer := &models.Billing{
		ID:               "123",
		CustomerID:       "cust_1234",
		OrganizationID:   "org_1234",
		BillingAuthority: string(billing.StripeBillingProvider),
		Generation:       2,
	}
	repoSet.BillingRepo.Items[customer.ID] = customer

	providers := map[billing.ProviderName]billing.IProvider{
		billing.StripeBillingProvider: billingProvider,
	}

	log, err := logging.NewLogger()
	require.NoError(t, err)

	db, _, err := sqlmock.New()
	assert.NoError(t, err)
	featSvc := features.NewService(repoSet, db, false, false, features.FeatureGatesSourceEnv, license.License{}, features.WithLogger(&log))
	reconciler := NewBillingReconciler(&log, repoSet, providers, db, featSvc, "")
	require.NoError(t, reconciler.Reconcile(context.Background(), customer))

	dbOrg, err := repoSet.Organizations().GetByID(context.Background(), "org_1234")
	require.NoError(t, err)

	assert.Equal(t, dbOrg.MaxClusters, 2)
	assert.Equal(t, dbOrg.MaxInstances, 10)
	assert.Equal(t, dbOrg.MaxApplications, 100)
}
