package integration

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"strings"
	"time"

	jsonpatch "github.com/evanphx/json-patch"
	"github.com/go-logr/logr"
	"github.com/go-test/deep"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"golang.org/x/exp/maps"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"

	"github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/agent/pkg/kube"
	typev1 "github.com/akuityio/akuity-platform/controllers/addon/argocd/types/v1"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/features"
	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/internal/utils/types"
	"github.com/akuityio/akuity-platform/models/models"
)

type clusterAddonReconciler struct {
	settings ControllerSettings
	featSvc  features.Service
	cfg      config.AddonControllerConfig
}

func NewClusterAddonReconciler(
	settings ControllerSettings,
	featSvc features.Service,
	cfg config.AddonControllerConfig,
) *clusterAddonReconciler {
	return &clusterAddonReconciler{
		settings: settings,
		featSvc:  featSvc,
		cfg:      cfg,
	}
}

const (
	AppFinalizer         = "resources-finalizer.argocd.argoproj.io"
	ArgocdNS             = "argocd"
	AppRefreshAnnotation = "argocd.argoproj.io/refresh"
	AppRefreshTypeNormal = "normal"
)

func (r *clusterAddonReconciler) ItemToID(item *models.ClusterAddon) string {
	return item.ID
}

func (r *clusterAddonReconciler) IDColumn() string {
	return models.ClusterAddonTableColumns.ID
}

func (r *clusterAddonReconciler) LogValuesFromID(id string) []interface{} {
	return []interface{}{"id", id}
}

func (r *clusterAddonReconciler) LogValuesFromItem(item *models.ClusterAddon) []interface{} {
	return []interface{}{"instance_id", item.InstanceID, "addon_id", item.AddonID}
}

func (r *clusterAddonReconciler) Reconcile(ctx context.Context, clusterAddon *models.ClusterAddon) error {
	if !r.featSvc.GetFeatureStatuses(ctx, &clusterAddon.OrganizationID).GetFleetManagement().Enabled() {
		r.settings.Log.V(1).Info("Fleet management feature is disabled, skipping cluster addon reconciliation", "org", clusterAddon.OrganizationID)
		return nil
	}

	status, err := clusterAddon.GetStatus()
	if err != nil {
		return fmt.Errorf("failed to get cluster addon status: %w", err)
	}
	newStatus := status.DeepCopy()

	reconcileError := r.reconcile(ctx, clusterAddon, &newStatus)

	if !reflect.DeepEqual(status, newStatus) {
		if err := clusterAddon.SetStatus(newStatus); err != nil {
			return fmt.Errorf("failed to set cluster addon status: %w", err)
		}
		if err := r.settings.RepoSet.ClusterAddons().Update(ctx, clusterAddon, "status_info", "status_processed_generation", "status_operation"); err != nil {
			return fmt.Errorf("failed to persist cluster addon status: %w", err)
		}
	}
	// we force retry on any error for cluster addon
	// DEV: revert if this causes too much load
	if reconcileError != nil && !errorsutil.IsUserError(reconcileError) {
		return errorsutil.NewRetryableError(reconcileError, "retry syncing cluster addon")
	}
	return nil
}

func (r *clusterAddonReconciler) deleteApp(ctx context.Context, log *logr.Logger, unstructuredApp *unstructured.Unstructured, addonSpec *models.AddonSpec, status *models.ClusterAddonStatus, tenant *client.ArgoCDTenant) error {
	if !addonSpec.AppTemplate.DeletionOptions.NonCascade {
		unstructuredApp.SetFinalizers([]string{AppFinalizer})
	} else {
		unstructuredApp.SetFinalizers(nil)
	}

	_, err := tenant.ApplyCustomResource(ctx, *unstructuredApp, kube.ApplyOpts{Force: true}, true)
	if err != nil && !k8serrors.IsNotFound(err) {
		status.Conditions.SetNotEstablished(models.ClusterAddonConditionTypeSyncSuccessful, models.ClusterAddonConditionTypeSyncSuccessfulReasonSyncError, fmt.Sprintf("failed to set app finalizer %v, %v", unstructuredApp.GetName(), sanitizeError(log, err, "").Error()))
		return fmt.Errorf("failed to set app finalizers: %w", err)
	}
	if err := tenant.DeleteCustomResource(ctx, *unstructuredApp); err != nil && !k8serrors.IsNotFound(err) {
		status.Conditions.SetNotEstablished(models.ClusterAddonConditionTypeSyncSuccessful, models.ClusterAddonConditionTypeSyncSuccessfulReasonSyncError, fmt.Sprintf("failed to cleanup app %v, %v", unstructuredApp.GetName(), sanitizeError(log, err, "").Error()))
		return fmt.Errorf("failed to delete app: %w", err)
	}
	log.V(1).Info("Deleted cluster addon app", "addon_name", addonSpec.Name, "app_name", unstructuredApp.GetName())
	return nil
}

func (r *clusterAddonReconciler) reconcile(ctx context.Context, clusterAddon *models.ClusterAddon, status *models.ClusterAddonStatus) error {
	log := r.settings.Log.WithValues(r.LogValuesFromID(clusterAddon.ID)...).WithValues(r.LogValuesFromItem(clusterAddon)...)

	tenant, err := client.NewArgoCDTenant(r.settings.K8SRestConfig, log, clusterAddon.InstanceID)
	if err != nil {
		return err
	}

	clusterNames := []string{clusterAddon.ClusterName}
	existingApps, err := GetAddonUnstructuredApplications(ctx, &log, r.settings.K3sDBRawClient, clusterAddon.R.Addon, clusterNames)
	if err != nil {
		status.Conditions.SetNotEstablished(models.ClusterAddonConditionTypeSyncSuccessful, models.ClusterAddonConditionTypeSyncSuccessfulReasonSyncError, fmt.Sprintf("failed to get existing apps, %v", sanitizeError(&log, err, "").Error()))
		return err
	}

	addonSpec, err := clusterAddon.R.Addon.GetSpec()
	if err != nil {
		return err
	}

	if !clusterAddon.DeletionTimestamp.IsZero() {
		if len(existingApps) == 0 {
			if err := r.settings.RepoSet.ClusterAddons().Delete(ctx, clusterAddon.ID); err != nil {
				return errorsutil.NewRetryableError(err, "failed to delete cluster addon")
			}
			log.Info("Deleted cluster addon")
			return nil
		}
		for _, app := range existingApps {
			// delete app
			if err := r.deleteApp(ctx, &log, app, addonSpec, status, tenant); err != nil {
				return err
			}
		}

		status.Conditions.SetNotEstablished(models.ClusterAddonConditionTypeSyncSuccessful, models.ClusterAddonConditionTypeSyncSuccessfulReasonSyncError, fmt.Sprintf("Waiting for cluster to delete the apps: %s. If cluster is unavailable set non-cascade deletion to unblock deletion.", strings.Join(maps.Keys(existingApps), ",")))
		status.ProcessedGeneration = clusterAddon.Generation
		return nil
	}

	defer func() {
		appInfo, err := getAppIndividualStatus(ctx, &log, r.settings.K3sDBRawClient, clusterAddon)
		if err != nil {
			log.Error(err, "failed to get clusterAddon app info")
			return
		}
		status.ApplicationsStatus = *appInfo
	}()

	addonStatus, err := clusterAddon.R.Addon.GetStatus()
	if err != nil {
		return err
	}

	// only go through syncing apps for clusterAddon if there's a change in clusterAddon
	// or repo has a new lastSyncCommit
	// or if there are less than or more than exactly 1 app associated to the addon
	if addonSpec.Enabled && (len(existingApps) != 1 || clusterAddon.Generation != status.ProcessedGeneration || status.LastSyncCommit != addonStatus.LastSyncCommit) {

		// there's a change in clusterAddon set condition
		status.Conditions.SetNotEstablished(models.ClusterAddonConditionTypeSyncSuccessful, models.ClusterAddonConditionTypeSyncSuccessfulReasonOutOfSync, "change detected for cluster addon, syncing apps")

		cluster, err := r.settings.RepoSet.ArgoCDClusters(models.ArgoCDClusterWhere.Name.EQ(clusterAddon.ClusterName), models.ArgoCDClusterWhere.InstanceID.EQ(clusterAddon.InstanceID), models.ArgoCDClusterNoStatusManifestMod).One(ctx)
		if err != nil {
			return fmt.Errorf("failed to get addon target cluster: %w", err)
		}

		newApp, err := r.generateAppForCluster(ctx, &log, clusterAddon, clusterAddon.R.Addon, addonSpec, cluster)
		if err != nil {
			status.Conditions.SetNotEstablished(models.ClusterAddonConditionTypeSyncSuccessful, models.ClusterAddonConditionTypeSyncSuccessfulReasonSyncError, fmt.Sprintf("failed to generate app for cluster %v : %v", cluster.Name, err.Error()))
			return errorsutil.NewUserError(err)
		}

		log.V(1).Info("Existing apps for cluster addon", "app_names", maps.Keys(existingApps))

		appFound := false
		for name, unstructuredApp := range existingApps {
			// there can only be 1 or 0 apps matching the required name, all other existing apps should be cleaned up
			if name != newApp.Name {
				// delete app
				if err := r.deleteApp(ctx, &log, unstructuredApp, addonSpec, status, tenant); err != nil {
					return err
				}
				continue
			}

			unstructuredSpec, err := runtime.DefaultUnstructuredConverter.ToUnstructured(&newApp.Spec)
			if err != nil {
				return err
			}

			existingUnstructuredSpec, ok, err := unstructured.NestedMap(unstructuredApp.Object, "spec")
			if err != nil {
				return fmt.Errorf("failed to get existing app spec: %w", err)
			}
			if !ok {
				return fmt.Errorf("failed to get existing app spec: failed to extract spec")
			}

			forceDelete := false
			// we modify the unstructured app in place
			// this reduces any changes for unnecessary fields from
			// being updated for reasons like differing CR versions etc.
			if unstructuredApp.GetDeletionTimestamp() != nil {
				forceDelete = true
				// if matching app is scheduled for deletion we cannot reconcile the app
				// need to force delete the app and recreate it
				// set the finalizers to nil and unblock current deletion and recreate app
				log.V(1).Info("App set for deletion, recovering it", "app_name", unstructuredApp.GetName(), "deletion_set", unstructuredApp.GetDeletionTimestamp() != nil, "finalizers", unstructuredApp.GetFinalizers())
				unstructuredApp.SetFinalizers(nil)
			} else if !reflect.DeepEqual(existingUnstructuredSpec, unstructuredSpec) {
				// check if app spec is different
				diff := deep.Equal(existingUnstructuredSpec, unstructuredSpec)
				log.V(1).Info("App spec is different", "app_name", unstructuredApp.GetName(), "diff", diff)
				// update app
				if err := unstructured.SetNestedField(unstructuredApp.Object, unstructuredSpec, "spec"); err != nil {
					return err
				}
			}

			// update annotations and labels
			annotations := unstructuredApp.GetAnnotations()
			if annotations == nil {
				annotations = map[string]string{}
			}
			for k, v := range newApp.GetAnnotations() {
				annotations[k] = v
			}
			annotations[AppRefreshAnnotation] = AppRefreshTypeNormal
			unstructuredApp.SetAnnotations(annotations)

			labels := unstructuredApp.GetLabels()
			if labels == nil {
				labels = map[string]string{}
			}
			for k, v := range newApp.GetLabels() {
				labels[k] = v
			}
			unstructuredApp.SetLabels(labels)

			_, err = tenant.ApplyCustomResource(ctx, *unstructuredApp, kube.ApplyOpts{Force: true}, true)
			if err != nil {
				status.Conditions.SetNotEstablished(models.ClusterAddonConditionTypeSyncSuccessful, models.ClusterAddonConditionTypeSyncSuccessfulReasonSyncError, fmt.Sprintf("failed to update app %v, %v", unstructuredApp.GetName(), sanitizeError(&log, err, "").Error()))
				return fmt.Errorf("failed to update app: %w", err)
			}
			log.V(1).Info("Updated clusterAddon app", "app_name", unstructuredApp.GetName())

			if forceDelete {
				return errorsutil.NewRetryableError(fmt.Errorf("app scheduled for deletion, force delete and recreate"), "")
			}
			appFound = true
		}

		// app needs to be created
		if !appFound {
			// for first time creations the app CR structure is dependent on the version
			// available in addon controller, but this only influences the spec of the app
			unstructuredApp, err := convertAppToUnstructured(newApp)
			if err != nil {
				return err
			}
			overwrite := true
			// if explicitly set to no overwrite on conflict, set to false
			if addonSpec.AppTemplate.CreationOptions.OnConflict != models.OnConflictActionOverwrite &&
				addonSpec.AppTemplate.CreationOptions.OnConflict != "" {
				overwrite = false
			}
			_, err = tenant.ApplyCustomResource(ctx, *unstructuredApp, kube.ApplyOpts{Force: true}, false)
			if err != nil {
				if k8serrors.IsAlreadyExists(err) {
					if overwrite {
						// delete existing app
						if err := tenant.DeleteCustomResource(ctx, *unstructuredApp); err != nil {
							status.Conditions.SetNotEstablished(models.ClusterAddonConditionTypeSyncSuccessful, models.ClusterAddonConditionTypeSyncSuccessfulReasonSyncError, fmt.Sprintf("failed to remove existing conflicting app %v, %v", newApp.Name, sanitizeError(&log, err, "").Error()))
							return fmt.Errorf("failed to delete existing app: %w", err)
						}
						// create app
						_, err = tenant.ApplyCustomResource(ctx, *unstructuredApp, kube.ApplyOpts{Force: true}, false)
						if err != nil {
							status.Conditions.SetNotEstablished(models.ClusterAddonConditionTypeSyncSuccessful, models.ClusterAddonConditionTypeSyncSuccessfulReasonSyncError, fmt.Sprintf("failed to create app %v, %v", newApp.Name, sanitizeError(&log, err, "").Error()))
							return fmt.Errorf("failed to create app: %w", err)
						}
					} else {
						// if not overwrite ignore
						log.V(1).Info("App already exists, ignoring re-creation as overwrite set to false", "app_name", newApp.Name)

						status.LastSyncCommit = addonStatus.LastSyncCommit
						status.LastSyncTime = time.Now().Format(time.RFC3339)
						status.ProcessedGeneration = clusterAddon.Generation
						status.Conditions.SetEstablished(models.ClusterAddonConditionTypeSyncSuccessful)

						return nil // do not proceed with further processing as app doesn't belong to addon controller
					}
				} else {
					status.Conditions.SetNotEstablished(models.ClusterAddonConditionTypeSyncSuccessful, models.ClusterAddonConditionTypeSyncSuccessfulReasonSyncError, fmt.Sprintf("failed to create app %v, %v", newApp.Name, sanitizeError(&log, err, "").Error()))
					return fmt.Errorf("failed to create app: %w", err)
				}
			}
			log.V(1).Info("Created cluster addon app", "app_name", newApp.Name)
		}

		// once cluster addon app is all synced
		// check if operation is set, if set run operation
		if len(clusterAddon.StatusOperation.JSON) > 0 && string(clusterAddon.StatusOperation.JSON) != "{}" {
			operation, err := clusterAddon.GetStatusOperation()
			if err != nil {
				return err
			}

			// get the app again as it could've been updated in the meantime
			unstructuredApp, err := GetUnstructuredApplication(ctx, &log, r.settings.K3sDBRawClient, clusterAddon.InstanceID, newApp.Name)
			if err != nil {
				status.Conditions.SetNotEstablished(models.ClusterAddonConditionTypeSyncSuccessful, models.ClusterAddonConditionTypeSyncSuccessfulReasonOperationFailed, fmt.Sprintf("failed to get target apps, %v", sanitizeError(&log, err, "").Error()))
				return err
			}

			existingUnstructuredOp, ok, err := unstructured.NestedMap(unstructuredApp.Object, "operation")
			if err != nil {
				return fmt.Errorf("failed to get existing operation: %w", err)
			}

			op := generateOperation(operation)
			// same as updates we only touch operation field leaving other
			unstructuredOp, err := runtime.DefaultUnstructuredConverter.ToUnstructured(op)
			if err != nil {
				return err
			}

			if !ok || !reflect.DeepEqual(existingUnstructuredOp, op) {
				log.V(1).Info("Setting operation for app", "app_name", unstructuredApp.GetName())

				if err := unstructured.SetNestedField(unstructuredApp.Object, unstructuredOp, "operation"); err != nil {
					return err
				}

				_, err = tenant.ApplyCustomResource(ctx, *unstructuredApp, kube.ApplyOpts{Force: true}, true)
				if err != nil {
					status.Conditions.SetNotEstablished(models.ClusterAddonConditionTypeSyncSuccessful, models.ClusterAddonConditionTypeSyncSuccessfulReasonOperationFailed, fmt.Sprintf("failed to set operation for app %v, %v", unstructuredApp.GetName(), sanitizeError(&log, err, "").Error()))
					return fmt.Errorf("failed to set app operation for app %v: %w", unstructuredApp.GetName(), err)
				}
			}

			// once operation is applied unset the operation
			clusterAddon.StatusOperation = null.JSON{}
		}

		status.LastSyncCommit = addonStatus.LastSyncCommit
		status.LastSyncTime = time.Now().Format(time.RFC3339)
		status.Conditions.SetEstablished(models.ClusterAddonConditionTypeSyncSuccessful)
		status.ProcessedGeneration = clusterAddon.Generation
	}

	return nil
}

func (r *clusterAddonReconciler) generateAppForCluster(ctx context.Context, log *logr.Logger, clusterAddon *models.ClusterAddon, addon *models.Addon, spec *models.AddonSpec, cluster *models.ArgoCDCluster) (*typev1.Application, error) {
	project := "default"
	appName := fmt.Sprintf("%s-%s", spec.Name, cluster.Name)
	appNS := ""

	clusterSpec, err := cluster.GetSpec()
	if err != nil {
		return nil, err
	}

	repo, err := r.settings.RepoSet.AddonRepo().GetByID(ctx, addon.RepoID)
	if err != nil {
		return nil, err
	}
	repoSpec, err := repo.GetSpec()
	if err != nil {
		return nil, err
	}

	vars, err := generateTemplateVars(spec, cluster, clusterSpec)
	if err != nil {
		return nil, err
	}

	log.V(1).Info("Generating app for cluster addon", "vars", vars)

	if spec.AppTemplate.ProjectTemplate != "" {
		project, err = TemplateString(spec.AppTemplate.ProjectTemplate, vars)
		if err != nil {
			return nil, err
		}
	}

	if spec.AppTemplate.NameTemplate != "" {
		appName, err = TemplateString(spec.AppTemplate.NameTemplate, vars)
		if err != nil {
			return nil, err
		}
	}

	if spec.AppTemplate.NamespaceTemplate != "" {
		appNS, err = TemplateString(spec.AppTemplate.NamespaceTemplate, vars)
		if err != nil {
			return nil, err
		}
	}

	app := &typev1.Application{
		Spec: typev1.ApplicationSpec{
			Project: project,
			Source: &typev1.ApplicationSource{
				RepoURL:        repoSpec.RepoURL,
				Path:           fmt.Sprintf("%s/%s", DefaultAddonsDir, spec.Name),
				TargetRevision: repoSpec.Revision,
			},
			Destination: typev1.ApplicationDestination{
				Namespace: appNS,
				Name:      cluster.Name,
				Server:    "",
			},
		},
	}

	if spec.AddonType == models.AddonTypeHelm {
		helm := typev1.ApplicationSourceHelm{
			IgnoreMissingValueFiles: false,
		}

		if spec.AppTemplate.HelmOptions != nil {
			if spec.AppTemplate.HelmOptions.ReleaseNameTemplate != "" {
				releaseName, err := TemplateString(spec.AppTemplate.HelmOptions.ReleaseNameTemplate, vars)
				if err != nil {
					return nil, err
				}
				helm.ReleaseName = releaseName
			}
			helm.SkipCrds = spec.AppTemplate.HelmOptions.SkipCrds
			helm.PassCredentials = spec.AppTemplate.HelmOptions.PassCredentials
		}

		valueFiles := []string{}
		// priority order for value files
		// default addon values < env override < cluster override

		// set default values first
		valueFiles = append(valueFiles, fmt.Sprintf("/%s/%s/%s", DefaultAddonsDir, spec.Name, HelmValuesFile1))

		// set env override values if present
		if _, ok := spec.EnvOverrides[clusterSpec.Labels[DefaultEnvLabel]]; ok {
			valueFiles = append(valueFiles, fmt.Sprintf("/%s/%s/%s/%s", DefaultEnvOverrideDir, clusterSpec.Labels[DefaultEnvLabel], spec.Name, HelmValuesFile1))
		}

		// set cluster override values if present
		if _, ok := spec.ClusterOverrides[cluster.Name]; ok {
			valueFiles = append(valueFiles, fmt.Sprintf("/%s/%s/%s/%s", DefaultClusterOverrideDir, cluster.Name, spec.Name, HelmValuesFile1))
		}

		if len(valueFiles) > 0 {
			helm.ValueFiles = valueFiles
		}

		app.Spec.Source.Helm = &helm

		if _, ok := spec.EnvOverrides[clusterSpec.Labels[DefaultEnvLabel]]; ok {
			if spec.EnvOverrides[clusterSpec.Labels[DefaultEnvLabel]].HelmSource != nil && len(spec.EnvOverrides[clusterSpec.Labels[DefaultEnvLabel]].HelmSource.ChartDependencies) > 0 {
				app.Spec.Source.Path = fmt.Sprintf("%s/%s/%s", DefaultEnvOverrideDir, clusterSpec.Labels[DefaultEnvLabel], spec.Name)
			}
		}

		if _, ok := spec.ClusterOverrides[cluster.Name]; ok {
			if spec.ClusterOverrides[cluster.Name].HelmSource != nil && len(spec.ClusterOverrides[cluster.Name].HelmSource.ChartDependencies) > 0 {
				app.Spec.Source.Path = fmt.Sprintf("%s/%s/%s", DefaultClusterOverrideDir, cluster.Name, spec.Name)
			}
		}

		if app.Spec.Source.Helm.IsZero() {
			app.Spec.Source.Helm = nil
		}
	}

	if spec.AddonType == models.AddonTypeKustomize {
		// overwrite the source path for kustomize
		// prefer cluster override over env override
		// and finally if none found keep default clusterAddon path
		if _, ok := spec.ClusterOverrides[cluster.Name]; ok {
			app.Spec.Source.Path = fmt.Sprintf("%s/%s/%s", DefaultClusterOverrideDir, cluster.Name, spec.Name)
		} else if _, ok = spec.EnvOverrides[clusterSpec.Labels[DefaultEnvLabel]]; ok {
			app.Spec.Source.Path = fmt.Sprintf("%s/%s/%s", DefaultEnvOverrideDir, clusterSpec.Labels[DefaultEnvLabel], spec.Name)
		}
		if spec.AppTemplate.KustomizeOptions != nil {
			kustomize := typev1.ApplicationSourceKustomize{}
			if spec.AppTemplate.KustomizeOptions.NamePrefixTemplate != "" {
				namePrefix, err := TemplateString(spec.AppTemplate.KustomizeOptions.NamePrefixTemplate, vars)
				if err != nil {
					return nil, err
				}
				kustomize.NamePrefix = namePrefix
			}
			if spec.AppTemplate.KustomizeOptions.NameSuffixTemplate != "" {
				nameSuffix, err := TemplateString(spec.AppTemplate.KustomizeOptions.NameSuffixTemplate, vars)
				if err != nil {
					return nil, err
				}
				kustomize.NameSuffix = nameSuffix
			}
			app.Spec.Source.Kustomize = &kustomize
		}
		if app.Spec.Source.Kustomize.IsZero() {
			app.Spec.Source.Kustomize = nil
		}
	}

	if spec.AppTemplate.SyncOptions.AutoSync {
		app.Spec.SyncPolicy = &typev1.SyncPolicy{
			Automated: &typev1.SyncPolicyAutomated{
				SelfHeal: spec.AppTemplate.SyncOptions.AutoHeal,
				Prune:    spec.AppTemplate.SyncOptions.PruneResources,
			},
		}
	}

	if len(spec.AppTemplate.SyncOptions.SyncOptionsList) > 0 {
		if app.Spec.SyncPolicy == nil {
			app.Spec.SyncPolicy = &typev1.SyncPolicy{}
		}
		app.Spec.SyncPolicy.SyncOptions = spec.AppTemplate.SyncOptions.SyncOptionsList
	}

	// check if any patches are present
	if len(spec.PatchCustomizations) > 0 {
		jsonData, err := json.Marshal(app)
		if err != nil {
			return nil, err
		}
		patched := false
		for _, patch := range spec.PatchCustomizations {
			ok, err := isClusterSelected(patch.ClusterSelector, cluster)
			if err != nil {
				return nil, err
			}
			if ok {
				patchedJSON, err := jsonpatch.MergePatch(jsonData, []byte(patch.PatchJson))
				if err != nil {
					return nil, err
				}
				jsonData = patchedJSON
				patched = true
			}
		}
		if patched {
			if err := json.Unmarshal(jsonData, app); err != nil {
				return nil, err
			}
		}

	}

	// set object metadata at the end to ensure required addon info cannot be overwritten by patches
	setAppMetadata(app, metav1.ObjectMeta{
		Name:      appName,
		Namespace: ArgocdNS,
		Labels: map[string]string{
			AddonIDLabelKey:        addon.ID,
			TargetClusterKey:       cluster.Name,
			ClusterAddonIDLabelKey: clusterAddon.ID,
		},
	})

	return app, nil
}

func setAppMetadata(app *typev1.Application, metadata metav1.ObjectMeta) {
	app.Name = metadata.Name
	app.Namespace = metadata.Namespace
	for key, value := range metadata.Labels {
		if app.Labels == nil {
			app.Labels = make(map[string]string)
		}
		app.Labels[key] = value
	}
	for key, value := range metadata.Annotations {
		if app.Annotations == nil {
			app.Annotations = make(map[string]string)
		}
		app.Annotations[key] = value
	}
}

func generateOperation(operation *models.ClusterAddonStatusOperation) *typev1.Operation {
	return &typev1.Operation{
		Sync: &typev1.SyncOperation{
			Revision:    operation.Revision,
			Prune:       operation.Prune,
			SyncOptions: operation.SyncOptions,
		},
		InitiatedBy: typev1.OperationInitiator{
			Username: operation.Initiator,
		},
	}
}

type clusterVars struct {
	Name        string            `json:"name"`
	Namespace   string            `json:"namespace"`
	Labels      map[string]string `json:"labels"`
	Annotations map[string]string `json:"annotations"`
}

type addonVars struct {
	Name string `json:"name"`
	Type string `json:"type"`
}

func generateTemplateVars(addon *models.AddonSpec, cluster *models.ArgoCDCluster, clusterSpec *models.ClusterSpec) (map[string]interface{}, error) {
	clusterVals := map[string]interface{}{}
	err := types.RemarshalTo(clusterVars{
		Name:        cluster.Name,
		Namespace:   cluster.Namespace,
		Labels:      clusterSpec.Labels,
		Annotations: clusterSpec.Annotations,
	}, &clusterVals)
	if err != nil {
		return nil, err
	}

	addonVals := map[string]interface{}{}
	err = types.RemarshalTo(addonVars{
		Name: addon.Name,
		Type: string(addon.AddonType),
	}, &addonVals)
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"addon":   addonVals,
		"cluster": clusterVals,
	}, nil
}

func getAppIndividualStatus(ctx context.Context, log *logr.Logger, db boil.ContextExecutor, clusterAddon *models.ClusterAddon) (*models.IndividualApplicationStatus, error) {
	apps, err := GetAddonApplications(ctx, log, db, clusterAddon.R.Addon, []string{clusterAddon.ClusterName})
	if err != nil {
		return nil, err
	}
	status := &models.IndividualApplicationStatus{
		Errors:   []string{},
		Warnings: []string{},
	}

	appNames := maps.Keys(apps)

	if len(appNames) == 0 {
		return status, nil
	}

	if len(appNames) != 1 {
		ext := ""
		if len(appNames) > 0 {
			ext = fmt.Sprintf("Apps found [%v]", strings.Join(appNames, ","))
		}
		status.Errors = append(status.Errors, fmt.Sprintf("%v applications found for the addon while expected 1 app, please refresh addon to fix app state. %v", len(appNames), ext))
		return status, nil
	}

	app := apps[appNames[0]]

	status.ResourcesCount = len(app.Status.Resources)
	if app.Operation != nil || app.Status.OperationState != nil && app.Status.OperationState.Phase == "Running" {
		status.InProgressSync = true
	}

	if len(app.Status.Conditions) > 0 {
		for _, condition := range app.Status.Conditions {
			if isAppErrorCondition(condition) {
				status.Errors = append(status.Errors, condition.Message)
				break
			} else if isAppWarningCondition(condition) {
				status.Warnings = append(status.Warnings, condition.Message)
				break
			}
		}
	}

	status.Health = string(app.Status.Health.Status)
	status.SyncStatus = string(app.Status.Sync.Status)

	return status, nil
}

func isAppErrorCondition(condition typev1.ApplicationCondition) bool {
	return strings.HasSuffix(condition.Type, "Error")
}

func isAppWarningCondition(condition typev1.ApplicationCondition) bool {
	return strings.HasSuffix(condition.Type, "Warning")
}
