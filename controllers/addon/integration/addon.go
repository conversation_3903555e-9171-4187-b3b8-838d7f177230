package integration

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"time"

	"github.com/go-test/deep"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"k8s.io/utils/ptr"

	v1 "github.com/akuityio/akuity-platform/controllers/addon/argocd/reposerver/v1"
	"github.com/akuityio/akuity-platform/controllers/shared/keymutex"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
	ioutil "github.com/akuityio/akuity-platform/internal/utils/io"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

const (
	MaxSourceUpdateAttempts = 3
)

type addonReconciler struct {
	settings ControllerSettings
	featSvc  features.Service
	cfg      config.AddonControllerConfig

	// common shared mutex to lock on the repo for write backs
	repoMutex keymutex.KeyMutex
}

func NewAddonReconciler(
	settings ControllerSettings,
	featSvc features.Service,
	cfg config.AddonControllerConfig,
) *addonReconciler {
	return &addonReconciler{
		settings: settings,
		featSvc:  featSvc,
		cfg:      cfg,
	}
}

const (
	DefaultEnvLabel = "env"
)

func (r *addonReconciler) ItemToID(item *models.Addon) string {
	return item.ID
}

func (r *addonReconciler) IDColumn() string {
	return models.AddonTableColumns.ID
}

func (r *addonReconciler) LogValuesFromID(id string) []interface{} {
	return []interface{}{"addon_id", id}
}

func (r *addonReconciler) LogValuesFromItem(item *models.Addon) []interface{} {
	return []interface{}{"instance_id", item.InstanceID, "repo_id", item.RepoID}
}

func (r *addonReconciler) Reconcile(ctx context.Context, addon *models.Addon) error {
	if !r.featSvc.GetFeatureStatuses(ctx, &addon.OrganizationID).GetFleetManagement().Enabled() {
		r.settings.Log.V(1).Info("Fleet management feature is disabled, skipping addon reconciliation", "org", addon.OrganizationID)
		return nil
	}

	status, err := addon.GetStatus()
	if err != nil {
		return fmt.Errorf("failed to get addon status: %w", err)
	}
	newStatus := status.DeepCopy()

	reconcileError := r.reconcile(ctx, addon, &newStatus)

	if !reflect.DeepEqual(status, newStatus) {
		if err := addon.SetStatus(newStatus); err != nil {
			return fmt.Errorf("failed to set addon status: %w", err)
		}
		if err := r.settings.RepoSet.Addons().Update(ctx, addon, "status_info", "status_processed_generation", "status_operation", "status_source_update"); err != nil {
			return fmt.Errorf("failed to persist addon status: %w", err)
		}
	}
	return reconcileError
}

func (r *addonReconciler) handleSourceUpdate(ctx context.Context, addon *models.Addon, addonSpec *models.AddonSpec, status *models.AddonStatus) (bool, error) {
	log := r.settings.Log.WithValues(r.LogValuesFromID(addon.ID)...).WithValues(r.LogValuesFromItem(addon)...)
	sourceUpdate, err := addon.GetStatusSourceUpdate()
	if err != nil {
		return false, err
	}

	repoSpec, err := addon.R.Repo.GetSpec()
	if err != nil {
		return false, err
	}

	errString := ""

	if status.LastSourceUpdateStatus != nil && status.LastSourceUpdateStatus.StartTimestamp.After(sourceUpdate.StartTimestamp) {
		if status.LastSourceUpdateStatus.Cancelled {
			log.V(1).Info("Source update cancelled")
			addon.StatusSourceUpdate = null.JSON{}
			// already processed and failed
			return false, nil
		}
		if status.LastSourceUpdateStatus.CompletedTimestamp != nil && status.LastSourceUpdateStatus.CompletedTimestamp.After(sourceUpdate.StartTimestamp) {
			log.V(1).Info("Source update already processed")
			addon.StatusSourceUpdate = null.JSON{}
			// already processed
			return false, nil
		}
		if status.LastSourceUpdateStatus.Attempts >= MaxSourceUpdateAttempts {
			log.V(1).Info("Source update retry attempts exceeded")
			status.LastSourceUpdateStatus.CompletedTimestamp = ptr.To(time.Now())
			status.LastSourceUpdateStatus.Error = "source update retry attempts exceeded"
			addon.StatusSourceUpdate = null.JSON{}
			return false, nil
		}
		if sourceUpdate.Cancelled {
			log.V(1).Info("Source update cancelled")
			status.LastSourceUpdateStatus.CompletedTimestamp = ptr.To(time.Now())
			status.LastSourceUpdateStatus.Error = "source update cancelled"
			status.LastSourceUpdateStatus.Cancelled = true
			addon.StatusSourceUpdate = null.JSON{}
			return false, nil
		}
		// retry source update
		// try to acquire lock on repo, if lock cannot be acquired, reattempt in a few mins
		if !r.repoMutex.TryLock(repoSpec.RepoURL) {
			return false, errorsutil.NewRetryableError(fmt.Errorf("source updates retry sync, waiting to lock on repo"), "another source update is already in progress for same repo")
		}
		log.V(1).Info("Reattempting source update")
		status.LastSourceUpdateStatus.Attempts++
	} else {
		// new source update request, reset status
		if !r.repoMutex.TryLock(repoSpec.RepoURL) {
			return false, errorsutil.NewRetryableError(fmt.Errorf("source updates retry sync, waiting to lock on repo"), "another source update is already in progress for same repo")
		}
		log.V(1).Info("Starting new source update")
		status.LastSourceUpdateStatus = &models.SourceUpdateResult{
			StartTimestamp: time.Now(),
			Attempts:       1,
		}
		status.LastSourceUpdateStatus.Changes, err = sourceUpdate.DeepCopy()
		status.LastSourceUpdateStatus.Initiator = sourceUpdate.Initiator
		if err != nil {
			return false, err
		}
	}

	defer func() {
		r.repoMutex.Unlock(repoSpec.RepoURL)
		reattempting := ", reattempting source update in a few mins"
		if status.LastSourceUpdateStatus.Attempts >= MaxSourceUpdateAttempts {
			status.LastSourceUpdateStatus.CompletedTimestamp = ptr.To(time.Now())
			addon.StatusSourceUpdate = null.JSON{}
			reattempting = ""
		}
		if errString != "" {
			status.LastSourceUpdateStatus.Error = errString + reattempting
		}
	}()

	instanceConfig, err := r.settings.RepoSet.ArgoCDInstanceConfigs(models.ArgoCDInstanceConfigWhere.InstanceID.EQ(addon.InstanceID)).ListAll(ctx, "version")
	if err != nil {
		errString = fmt.Sprintf("failed to get instance config for instance %s", addon.InstanceID)
		return false, errorsutil.NewRetryableError(err, "re-attempting...")
	}
	if len(instanceConfig) != 1 {
		errString = fmt.Sprintf("no instance config for instance %s found", addon.InstanceID)
		return false, fmt.Errorf("failed to get instance config for instance %s", addon.InstanceID)
	}
	instanceConfigSpec, err := instanceConfig[0].GetSpec()
	if err != nil {
		errString = fmt.Sprintf("internal error while parsing instance config for instance %v", addon.InstanceID)
		return false, err
	}
	isAkp, version := misc.GetAKPVersion(instanceConfig[0].Version.String)
	if !isAkp || version < 41 {
		status.LastSourceUpdateStatus.CompletedTimestamp = ptr.To(time.Now())
		// directly set error as source cannot be updated for non-AKP argocd instance
		status.LastSourceUpdateStatus.Error = "source update supported only for AKP argocd version instances, running ak.41 or higher"
		return false, nil
	}
	repo, found, err := GetRepository(ctx, &log, r.settings.K3sDBRawClient, repoSpec.RepoURL, addon.InstanceID)
	if err != nil {
		errString = fmt.Sprintf("failed to get repo credentials for repo url %s", repoSpec.RepoURL)
		return false, err
	}
	if !found {
		errString = fmt.Sprintf("repo credentials for repo url %s not configured in argocd instance, please create the repo creds in argocd", repoSpec.RepoURL)
		return false, fmt.Errorf("repository not found for repo url %s", repoSpec.RepoURL)
	}
	if !repo.HasCredentials() {
		errString = fmt.Sprintf("repo credentials for repo url %s not configured in argocd instance, please add the repo write creds in argocd", repoSpec.RepoURL)
		return false, fmt.Errorf("repository not found for repo url %s", repoSpec.RepoURL)
	}

	closer, repoClient, err := v1.NewRepoClientV1(log, fmt.Sprintf(RepoProxyServerURL, addon.InstanceID), instanceConfig[0].Version.String, RepoProxyClientTimeout, !instanceConfigSpec.IsGitDelegateInControlPlane())
	if err != nil {
		return false, err
	}
	defer ioutil.Close(closer)

	sha, err := UpdateManifestSources(ctx, &log, repoClient, repo, repoSpec.Revision, addon.InstanceID, addonSpec, sourceUpdate)
	if err != nil {
		errString = fmt.Sprintf("failed to update manifest sources, %v", sanitizeError(&log, err, "").Error())
		return false, errorsutil.NewRetryableError(err, "re-attempting...")
	}

	addon.StatusSourceUpdate = null.JSON{}
	status.LastSourceUpdateStatus.CompletedTimestamp = ptr.To(time.Now())
	status.LastSourceUpdateStatus.CommitSha = sha
	status.LastSourceUpdateStatus.Error = ""
	return true, nil
}

func (r *addonReconciler) reconcile(ctx context.Context, addon *models.Addon, status *models.AddonStatus) error {
	spec, err := addon.GetSpec()
	if err != nil {
		return err
	}
	log := r.settings.Log.WithValues(r.LogValuesFromID(addon.ID)...).WithValues(r.LogValuesFromItem(addon)...).WithValues("addon_name", spec.Name)

	db, txBeginner := database.WithTxBeginner(r.settings.PortalDBRawClient)
	repoSet := client.NewRepoSet(db)
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()
	tx, err := txBeginner.Begin(ctx)
	if err != nil {
		return err
	}

	if !addon.DeletionTimestamp.IsZero() {
		status.Conditions.SetNotEstablished(models.AddonConditionTypeDeleteSuccessful, "", "")
		// first cleanup git
		if addon.CleanGitOnDelete.Bool {
			instanceConfig, err := r.settings.RepoSet.ArgoCDInstanceConfigs(models.ArgoCDInstanceConfigWhere.InstanceID.EQ(addon.InstanceID)).One(ctx, "version")
			if err != nil {
				if errors.Is(err, sql.ErrNoRows) {
					return fmt.Errorf("failed to get instance config for instance %s", addon.InstanceID)
				}
				return errorsutil.NewRetryableError(err, "re-attempting...")
			}
			instanceConfigSpec, err := instanceConfig.GetSpec()
			if err != nil {
				return err
			}
			closer, repoClient, err := v1.NewRepoClientV1(log, fmt.Sprintf(RepoProxyServerURL, addon.InstanceID), instanceConfig.Version.String, RepoProxyClientTimeout, !instanceConfigSpec.IsGitDelegateInControlPlane())
			if err != nil {
				return err
			}
			defer ioutil.Close(closer)
			repo, err := repoSet.AddonRepo().GetByID(ctx, addon.RepoID)
			if err != nil {
				return err
			}
			repoSpec, err := repo.GetSpec()
			if err != nil {
				return err
			}
			if err := DeleteGitAddon(ctx, &log, r.settings.K3sDBRawClient, repoClient, spec, addon.InstanceID, addon.ID, repoSpec.RepoURL, repoSpec.Revision, addon.Generation, status); err != nil {
				addon.DeletionTimestamp = null.TimeFromPtr(nil)
				addon.CleanGitOnDelete = null.BoolFrom(false)
				if err := repoSet.Addons().Update(ctx, addon, "deletion_timestamp", "clean_git_on_delete"); err != nil {
					return err
				}
				if err := tx.Commit(); err != nil {
					return err
				}
				return err
			}
		}

		// get all cluster addons
		clusterAddons, err := repoSet.ClusterAddons(models.ClusterAddonWhere.AddonID.EQ(addon.ID)).ListAll(ctx, models.ClusterAddonColumns.ID)
		if err != nil {
			return err
		}
		if len(clusterAddons) > 0 {
			for _, clusterAddon := range clusterAddons {
				clusterAddon.DeletionTimestamp = null.TimeFrom(time.Now())
				if err := repoSet.ClusterAddons().Update(ctx, clusterAddon, models.ClusterAddonColumns.DeletionTimestamp); err != nil {
					return err
				}
			}
			if err := tx.Commit(); err != nil {
				return err
			}
			return errorsutil.NewRetryableError(fmt.Errorf("addon is being deleted, %v cluster addons are being deleted", len(clusterAddons)), "addon is being deleted")
		} else {
			if err := repoSet.Addons().Delete(ctx, addon.ID); err != nil {
				return errorsutil.NewRetryableError(err, "failed to delete addon")
			}
		}
		return tx.Commit()
	}

	if !spec.Enabled {
		// do not process if addon is not enabled
		return nil
	}

	repoStatus, err := addon.R.Repo.GetStatus()
	if err != nil {
		return err
	}

	// collect target clusters and new target clusters
	// DEV: the records are all sorted by cluster name so reflect.DeepEqual can be used
	existingClusterAddons, err := repoSet.ClusterAddons(models.ClusterAddonWhere.AddonID.EQ(addon.ID), qm.OrderBy(models.ClusterAddonColumns.ClusterName)).ListAll(ctx)
	if err != nil {
		return err
	}
	existingClusterNames := []string{}
	for _, cluster := range existingClusterAddons {
		if !cluster.DeletionTimestamp.IsZero() {
			continue
		}
		existingClusterNames = append(existingClusterNames, cluster.ClusterName)
	}
	// find all clusters that match addon cluster selector
	clusters, err := r.getClusters(ctx, repoSet, spec.ClusterSelector, addon.InstanceID)
	if err != nil {
		return err
	}
	newClusterNames := []string{}
	clusterNamesMap := make(map[string]*models.ArgoCDCluster)
	for _, cluster := range clusters {
		newClusterNames = append(newClusterNames, cluster.Name)
		clusterNamesMap[cluster.Name] = cluster
	}

	log.V(1).Info("Clusters for addon", "old_cluster_names", existingClusterNames, "new_cluster_names", newClusterNames)

	// only go through syncing cluster addons if there's a change in addon
	// or repo has a new lastSyncCommit
	// or if the target cluster list changed (sorted by cluster name)
	if !reflect.DeepEqual(existingClusterNames, newClusterNames) || addon.Generation != status.ProcessedGeneration || status.LastSyncCommit != repoStatus.LastSyncCommit {
		status.Conditions.SetNotEstablished(models.AddonConditionTypeSyncSuccessful, models.AddonConditionTypeSyncSuccessfulOutOfSyncReason, "Addon refreshing...")
		var (
			requiredStatusOperation []byte
			statusOp                *models.StatusOperation
		)

		// check if addon has source update operation
		if len(addon.StatusSourceUpdate.JSON) > 0 && string(addon.StatusSourceUpdate.JSON) != "{}" {
			sourceUpdate, err := r.handleSourceUpdate(ctx, addon, spec, status)
			if err != nil {
				return err
			}
			if sourceUpdate {
				// bump repo generation to refresh addon sources
				repo, err := repoSet.AddonRepo().GetByID(ctx, addon.RepoID)
				if err != nil {
					return err
				}
				repo.Generation++
				if err := repoSet.AddonRepo().Update(ctx, repo, models.AddonRepoColumns.Generation); err != nil {
					return err
				}
				log.Info("Bumped addon repo for source update")
				return tx.Commit() // return as repo resync will re-trigger addon reconciliation
			}
		}

		// collect any user specified status operation
		if len(addon.StatusOperation.JSON) > 0 && string(addon.StatusOperation.JSON) != "{}" {
			statusOp, err = addon.GetStatusOperation()
			if err != nil {
				return err
			}
			requiredStatusOperation, err = json.Marshal(statusOp.ClusterAddonStatusOperation)
			if err != nil {
				return err
			}
		}

		for _, clusterAddon := range existingClusterAddons {
			// if existing target cluster missing in new target cluster set, delete it's addons
			if _, ok := clusterNamesMap[clusterAddon.ClusterName]; !ok {
				clusterAddon.DeletionTimestamp = null.TimeFrom(time.Now())
				if err := repoSet.ClusterAddons().Update(ctx, clusterAddon, models.ClusterAddonColumns.DeletionTimestamp); err != nil {
					return err
				}
				log.Info("Deleted cluster addon", "cluster_name", clusterAddon.ClusterName)
				continue
			}

			newSpec := models.ClusterAddonSpec{}
			existingSpec, err := clusterAddon.GetSpec()
			if err != nil {
				return err
			}
			// check if addon has diff
			if !reflect.DeepEqual(newSpec, *existingSpec) || !clusterAddon.DeletionTimestamp.IsZero() {
				diff := deep.Equal(newSpec, *existingSpec)
				log.V(1).Info("Cluster Addon spec is different", "cluster_name", clusterAddon.ClusterName, "diff", diff, "deletion_set", !clusterAddon.DeletionTimestamp.IsZero())
				if err := clusterAddon.SetSpec(newSpec); err != nil {
					return err
				}
				// reset deletion timestamp
				clusterAddon.DeletionTimestamp = null.TimeFromPtr(nil)
			}

			// check if operation changed
			if requiredStatusOperation != nil && statusOp != nil {
				selected, err := isClusterSelected(statusOp.ClusterSelector, clusterNamesMap[clusterAddon.ClusterName])
				if err != nil {
					return err
				}
				if selected && !bytes.Equal(clusterAddon.StatusOperation.JSON, requiredStatusOperation) {
					clusterAddon.StatusOperation = null.JSONFrom(requiredStatusOperation)
				}
			}

			// bump generation to refresh addon
			clusterAddon.Generation++

			if err := repoSet.ClusterAddons().Update(ctx, clusterAddon, models.ClusterAddonColumns.Generation, models.ClusterAddonColumns.Spec, models.ClusterAddonColumns.StatusOperation, models.ClusterAddonColumns.DeletionTimestamp); err != nil {
				return err
			}
			log.Info("Updated cluster addon", "cluster_name", clusterAddon.ClusterName)

			// delete cluster from list that are processed
			delete(clusterNamesMap, clusterAddon.ClusterName)
		}

		// remaining cluster addons need to be created
		for clusterName := range clusterNamesMap {
			clusterAddon := &models.ClusterAddon{
				OrganizationID: addon.OrganizationID,
				InstanceID:     addon.InstanceID,
				ClusterName:    clusterName,
				AddonID:        addon.ID,
				Generation:     1,
			}

			if requiredStatusOperation != nil && statusOp != nil {
				selected, err := isClusterSelected(statusOp.ClusterSelector, clusterNamesMap[clusterName])
				if err != nil {
					return err
				}
				if selected {
					clusterAddon.StatusOperation = null.JSONFrom(requiredStatusOperation)
				}
			}

			if err := repoSet.ClusterAddons().Create(ctx, clusterAddon); err != nil {
				return err
			}

			log.V(1).Info("Created cluster addon", "cluster_name", clusterAddon.ClusterName)
		}

		status.LastSyncCommit = repoStatus.LastSyncCommit
		status.LastSyncTime = time.Now().Format(time.RFC3339)
		status.ClusterCount = len(clusters)
		addon.StatusOperation = null.JSON{}
		status.Conditions.SetEstablished(models.AddonConditionTypeSyncSuccessful)
		status.ProcessedGeneration = addon.Generation
	}

	return tx.Commit()
}

func (r *addonReconciler) getClusters(ctx context.Context, repoSet client.RepoSet, selectors models.ClusterSelector, instanceID string, columns ...string) ([]*models.ArgoCDCluster, error) {
	filters := getAddonClusterFilters(selectors, instanceID)
	// only load required fields
	reqColumns := []string{models.ArgoCDClusterColumns.Name, models.ArgoCDClusterColumns.ID, models.ArgoCDClusterColumns.Spec}
	if len(columns) > 0 {
		reqColumns = columns
	}
	filters = append(filters, qm.Select(reqColumns...), qm.OrderBy(models.ArgoCDClusterColumns.Name))

	return repoSet.ArgoCDClusters(filters...).ListAll(ctx)
}

func getAddonClusterFilters(selectors models.ClusterSelector, instanceID string) []qm.QueryMod {
	mods := []qm.QueryMod{models.ArgoCDClusterWhere.InstanceID.EQ(instanceID)}
	positiveNameSelectors, negativeNameSelectors := models.SeparateNegativeFilters(selectors.NameFilters)
	positiveLabelSelectors, negativeLabelSelectors := models.SeparateNegativeFilters(selectors.LabelFilters)

	// DEV: had to create the in queries manually using string manipulation as the inbuilt In() function
	// wasn't working properly with coalesce

	if len(positiveNameSelectors) > 0 || len(positiveLabelSelectors) > 0 {
		// or query
		expr := []qm.QueryMod{}
		for _, s := range positiveLabelSelectors {
			if len(expr) == 0 {
				expr = append(expr, qm.Where(fmt.Sprintf("coalesce(spec -> 'labels' ->> '%s', '') in (%s)", *s.Key, toQuerySlice(s.Values))))
			} else {
				expr = append(expr, qm.Or(fmt.Sprintf("coalesce(spec -> 'labels' ->> '%s', '') in (%s)", *s.Key, toQuerySlice(s.Values))))
			}
		}
		for _, name := range positiveNameSelectors {
			if len(expr) == 0 {
				expr = append(expr, qm.Where(fmt.Sprintf("name in (%s)", toQuerySlice(name.Values))))
			} else {
				expr = append(expr, qm.Or(fmt.Sprintf("name in (%s)", toQuerySlice(name.Values))))
			}
		}
		mods = append(mods, qm.Expr(expr...))
	}

	if len(negativeLabelSelectors) > 0 {
		for _, s := range negativeLabelSelectors {
			mods = append(mods, qm.Where(fmt.Sprintf("coalesce(spec -> 'labels' ->> '%s', '') not in (%s)", *s.Key, toQuerySlice(s.Values))))
		}
	}

	if len(negativeNameSelectors) > 0 {
		for _, name := range negativeNameSelectors {
			mods = append(mods, qm.Where(fmt.Sprintf("name not in (%s)", toQuerySlice(name.Values))))
		}
	}

	return mods
}

func toQuerySlice[T any](data []T) string {
	res := ""
	for _, v := range data {
		res += fmt.Sprintf("'%v',", v)
	}
	return res[0 : len(res)-1]
}
