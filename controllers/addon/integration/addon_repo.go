package integration

import (
	"context"
	"fmt"
	"reflect"
	"time"

	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	v1 "github.com/akuityio/akuity-platform/controllers/addon/argocd/reposerver/v1"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/utils/errors"
	ioutil "github.com/akuityio/akuity-platform/internal/utils/io"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

type addonRepoReconciler struct {
	settings ControllerSettings
	featSvc  features.Service
	cfg      config.AddonControllerConfig
}

func NewAddonRepoReconciler(
	settings ControllerSettings,
	featSvc features.Service,
	cfg config.AddonControllerConfig,
) *addonRepoReconciler {
	return &addonRepoReconciler{
		settings: settings,
		featSvc:  featSvc,
		cfg:      cfg,
	}
}

const (
	RepoProxyServerURL     = "repo-proxy.argocd-%v.svc.cluster.local:9081"
	RepoProxyClientTimeout = 60
	MaxRepoRetryAttempts   = 3
)

func (r *addonRepoReconciler) ItemToID(item *models.AddonRepo) string {
	return item.ID
}

func (r *addonRepoReconciler) IDColumn() string {
	return models.AddonRepoTableColumns.ID
}

func (r *addonRepoReconciler) LogValuesFromID(id string) []interface{} {
	return []interface{}{"addon_repo_id", id}
}

func (r *addonRepoReconciler) LogValuesFromItem(item *models.AddonRepo) []interface{} {
	return []interface{}{"instance_id", item.InstanceID}
}

func (r *addonRepoReconciler) Reconcile(ctx context.Context, addonRepo *models.AddonRepo) error {
	if !r.featSvc.GetFeatureStatuses(ctx, &addonRepo.OrganizationID).GetFleetManagement().Enabled() {
		r.settings.Log.V(1).Info("Fleet management feature is disabled, skipping addon repo reconciliation", "org", addonRepo.OrganizationID)
		return nil
	}

	// if set to delete no need to monitor status
	if !addonRepo.DeletionTimestamp.IsZero() {
		done, err := r.deleteRepo(ctx, addonRepo)
		if err != nil {
			return err
		}
		if !done {
			return errors.NewRetryableError(fmt.Errorf("waiting for addons to be deleted"), "retrying...")
		}
		return nil
	}

	status, err := addonRepo.GetStatus()
	if err != nil {
		return fmt.Errorf("failed to get addon repo status: %w", err)
	}
	newStatus := status.DeepCopy()
	reconcileError := r.reconcile(ctx, addonRepo, &newStatus)

	if errors.IsIgnoredError(reconcileError) {
		reconcileError = nil
	} else if reconcileError != nil {
		// TODO(pavel): might need improvements based on retryable/sanitized errors
		if status.RetryAttempts+1 >= MaxRepoRetryAttempts {
			reconcileError = nil
		} else {
			newStatus.RetryAttempts = status.RetryAttempts + 1
		}
	}

	if reconcileError == nil {
		// if no error reset retry attempts
		newStatus.RetryAttempts = 0
		newStatus.ProcessedGeneration = addonRepo.Generation
	}

	if !reflect.DeepEqual(status, newStatus) {
		if err := addonRepo.SetStatus(newStatus); err != nil {
			return fmt.Errorf("failed to set addon repo status: %w", err)
		}
		if err := r.settings.RepoSet.AddonRepo().Update(ctx, addonRepo, "status_info", "status_processed_generation"); err != nil {
			return fmt.Errorf("failed to persist addon repo status: %w", err)
		}
	}
	return reconcileError
}

func (r *addonRepoReconciler) reconcile(ctx context.Context, addonRepo *models.AddonRepo, status *models.RepoStatus) error {
	log := r.settings.Log.WithValues(r.LogValuesFromID(addonRepo.ID)...).WithValues(r.LogValuesFromItem(addonRepo)...)
	spec, err := addonRepo.GetSpec()
	if err != nil {
		return err
	}

	repo, found, err := GetRepository(ctx, &log, r.settings.K3sDBRawClient, spec.RepoURL, addonRepo.InstanceID)
	if err != nil {
		status.Conditions.SetNotEstablished(models.AddonConditionTypeSyncSuccessful, models.AddonConditionTypeSyncNotSuccessfulRepoNotFound, sanitizeError(&log, err, "").Error())
		return err
	}
	if !found {
		status.Conditions.SetNotEstablished(models.AddonConditionTypeSyncSuccessful, models.AddonConditionTypeSyncNotSuccessfulRepoNotFound, "repository credentials not configured in argocd instance")
		return nil
	}

	if !repo.HasCredentials() {
		status.Conditions.SetNotEstablished(models.AddonConditionTypeSyncSuccessful, models.AddonConditionTypeSyncNotSuccessfulRepoNotFound, "repository write credentials not configured in argocd instance")
		return nil
	}

	instanceConfig, err := r.settings.RepoSet.ArgoCDInstanceConfigs().GetByID(ctx, addonRepo.InstanceID)
	if err != nil {
		return err
	}

	argoCDConfigMap, err := instanceConfig.GetArgoCDConfigMap()
	if err != nil {
		return err
	}
	instanceSpec, err := instanceConfig.GetSpec()
	if err != nil {
		return err
	}

	closer, repoClient, err := v1.NewRepoClientV1(log, fmt.Sprintf(RepoProxyServerURL, addonRepo.InstanceID), instanceConfig.Version.String, RepoProxyClientTimeout, !instanceSpec.IsGitDelegateInControlPlane())
	if err != nil {
		return err
	}
	defer ioutil.Close(closer)

	resolvedRevision, err := ResolveRevision(ctx, &log, repoClient, repo, spec.Revision)
	if err != nil {
		status.Conditions.SetNotEstablished(models.AddonConditionTypeSyncSuccessful, models.AddonConditionTypeSyncNotSuccessfulRevisionResolveFailed, sanitizeError(&log, errors.NewIgnoredError(err), "").Error())
		// ignore this error as we repeatedly perform this operation in every resync
		// the actual error will be passed on to the user in the status
		return errors.NewIgnoredError(err)
	}

	if resolvedRevision != status.LastSyncCommit {
		// there's a change in addon repo
		status.Conditions.SetNotEstablished(models.AddonConditionTypeSyncSuccessful, models.AddonConditionTypeSyncSuccessfulOutOfSyncReason, fmt.Sprintf("Addon repo is out of sync with revision %v", resolvedRevision))
	} else if addonRepo.Generation != status.ProcessedGeneration || !status.Conditions.IsEstablished(models.AddonConditionTypeSyncSuccessful) {
		// forced refresh requested
		status.Conditions.SetNotEstablished(models.AddonConditionTypeSyncSuccessful, models.AddonConditionTypeSyncSuccessfulOutOfSyncReason, "Addon repo refresh requested")
	} else {
		// no changes detected so no need to reconcile
		return nil
	}

	db, txBeginner := database.WithTxBeginner(r.settings.PortalDBRawClient)
	repoSet := client.NewRepoSet(db)
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()
	tx, err := txBeginner.Begin(ctx)
	if err != nil {
		return err
	}

	existingAddons, err := repoSet.Addons(models.AddonWhere.RepoID.EQ(addonRepo.ID)).ListAll(ctx)
	if err != nil {
		return err
	}
	addonSpecMap := make(map[string]*models.AddonSpec)
	for _, addon := range existingAddons {
		addonSpec, err := addon.GetSpec()
		if err != nil {
			return err
		}
		addonSpecMap[addonSpec.Name] = addonSpec
	}

	addonSpecs, err := GetAddons(ctx, &log, repoClient, repo, resolvedRevision, addonSpecMap, argoCDConfigMap.KustomizeBuildOptions)
	if err != nil {
		status.Conditions.SetNotEstablished(models.AddonConditionTypeSyncSuccessful, models.AddonConditionTypeSyncNotSuccessfulRepoReadFailed, sanitizeError(&log, err, "").Error())
		return err
	}

	addonCount := 0
	for _, existingAddon := range existingAddons {
		existingSpec, err := existingAddon.GetSpec()
		if err != nil {
			return err
		}

		// cleanup addon from db if not present in repo
		if _, ok := addonSpecs[existingSpec.Name]; !ok {
			existingAddon.DeletionTimestamp = null.TimeFrom(time.Now())
			if err := repoSet.Addons().Update(ctx, existingAddon, "deletion_timestamp"); err != nil {
				return err
			}
			continue
		}

		addonCount++
		//  update addon if spec changed
		if !reflect.DeepEqual(existingSpec.ClusterOverrides, addonSpecs[existingSpec.Name].ClusterOverrides) ||
			!reflect.DeepEqual(existingSpec.EnvOverrides, addonSpecs[existingSpec.Name].EnvOverrides) ||
			!reflect.DeepEqual(existingSpec.DefaultManifest, addonSpecs[existingSpec.Name].DefaultManifest) ||
			existingSpec.AddonType != addonSpecs[existingSpec.Name].AddonType || !existingAddon.DeletionTimestamp.IsZero() ||
			existingSpec.Checksum != addonSpecs[existingSpec.Name].Checksum {

			existingSpec.ClusterOverrides = addonSpecs[existingSpec.Name].ClusterOverrides
			existingSpec.EnvOverrides = addonSpecs[existingSpec.Name].EnvOverrides
			existingSpec.AddonType = addonSpecs[existingSpec.Name].AddonType
			existingSpec.DefaultManifest = addonSpecs[existingSpec.Name].DefaultManifest
			existingSpec.Checksum = addonSpecs[existingSpec.Name].Checksum
			if err := existingAddon.SetSpec(*existingSpec); err != nil {
				return err
			}

			// if addon was previously set to delete but isn't deleted yet
			existingAddon.DeletionTimestamp = null.TimeFromPtr(nil)

			if err := repoSet.Addons().Update(ctx, existingAddon, "spec", "deletion_timestamp"); err != nil {
				return err
			}
		}

		// delete addons that are processed
		delete(addonSpecs, existingSpec.Name)
	}

	// remaining addonSpecs are new addons
	// create them in db
	for _, addonSpec := range addonSpecs {
		addonCount++
		addon := &models.Addon{
			OrganizationID: addonRepo.OrganizationID,
			InstanceID:     addonRepo.InstanceID,
			RepoID:         addonRepo.ID,
			Generation:     1,
		}
		if err := addon.SetSpec(*addonSpec); err != nil {
			return err
		}
		if err := repoSet.Addons().Create(ctx, addon); err != nil {
			return err
		}
	}

	if err := tx.Commit(); err != nil {
		return err
	}
	status.Conditions.SetEstablished(models.AddonConditionTypeSyncSuccessful)
	status.LastSyncTime = time.Now().Format(time.RFC3339)
	status.LastSyncCommit = resolvedRevision
	status.AddonCount = addonCount
	status.ProcessedGeneration = addonRepo.Generation

	return nil
}

func (r *addonRepoReconciler) deleteRepo(ctx context.Context, addonRepo *models.AddonRepo) (bool, error) {
	db, txBeginner := database.WithTxBeginner(r.settings.PortalDBRawClient)
	repoSet := client.NewRepoSet(db)
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()
	tx, err := txBeginner.Begin(ctx)
	if err != nil {
		return false, err
	}
	spec, err := addonRepo.GetSpec()
	if err != nil {
		return false, err
	}

	addons, err := repoSet.Addons(models.AddonWhere.RepoID.EQ(addonRepo.ID)).ListAll(ctx)
	if err != nil {
		return false, err
	}

	done := false
	if len(addons) > 0 {
		for _, addon := range addons {
			if addon.DeletionTimestamp.IsZero() {
				addon.DeletionTimestamp = null.TimeFrom(time.Now())
				if err := repoSet.Addons().Update(ctx, addon, "deletion_timestamp"); err != nil {
					return false, err
				}
			}
		}
	} else {
		// all addons are cleaned up already safe to delete repo
		if err := repoSet.AddonRepo().Delete(ctx, addonRepo.ID); err != nil {
			return false, err
		}
		if err := repoSet.AddonMarketplaceInstalls().Filter(
			models.AddonMarketplaceInstallWhere.InstanceID.EQ(addonRepo.InstanceID),
			qm.Where("addon_marketplace_installs.config->>'repoURL' = ?", spec.RepoURL),
			qm.Where("addon_marketplace_installs.config->>'revision' = ?", spec.Revision),
		).DeleteAll(ctx); err != nil {
			return false, err
		}
		done = true
	}

	return done, tx.Commit()
}
