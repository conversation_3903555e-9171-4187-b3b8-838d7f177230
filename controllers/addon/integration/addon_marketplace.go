package integration

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"reflect"
	"time"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	v1 "github.com/akuityio/akuity-platform/controllers/addon/argocd/reposerver/v1"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/features"
	utilErrors "github.com/akuityio/akuity-platform/internal/utils/errors"
	ioutil "github.com/akuityio/akuity-platform/internal/utils/io"
	"github.com/akuityio/akuity-platform/models/models"
)

const (
	MaxAddonInstallAttempts = 3
)

type addonMarketplaceInstallReconciler struct {
	settings ControllerSettings
	featSvc  features.Service
	cfg      config.AddonControllerConfig
}

func NewAddonMarketplaceInstallReconciler(
	settings ControllerSettings,
	featSvc features.Service,
	cfg config.AddonControllerConfig,
) *addonMarketplaceInstallReconciler {
	return &addonMarketplaceInstallReconciler{
		settings: settings,
		featSvc:  featSvc,
		cfg:      cfg,
	}
}

func (r *addonMarketplaceInstallReconciler) ItemToID(item *models.AddonMarketplaceInstall) string {
	return item.ID
}

func (r *addonMarketplaceInstallReconciler) IDColumn() string {
	return models.AddonMarketplaceInstallTableColumns.ID
}

func (r *addonMarketplaceInstallReconciler) LogValuesFromID(id string) []interface{} {
	return []interface{}{"id", id}
}

func (r *addonMarketplaceInstallReconciler) LogValuesFromItem(item *models.AddonMarketplaceInstall) []interface{} {
	return []interface{}{"instance_id", item.InstanceID, "addon_marketplace_install_id", item.ID}
}

func (r *addonMarketplaceInstallReconciler) Reconcile(ctx context.Context, ami *models.AddonMarketplaceInstall) error {
	if !r.featSvc.GetFeatureStatuses(ctx, &ami.OrganizationID).GetFleetManagement().Enabled() {
		r.settings.Log.V(1).Info("Fleet management feature is disabled, skipping addon_marketplace_install reconciliation", "org", ami.OrganizationID)
		return nil
	}

	status, err := ami.GetStatus()
	if err != nil {
		return fmt.Errorf("failed to get addon_marketplace_install status: %w", err)
	}
	newStatus := status.DeepCopy()

	event, reconcileError := r.reconcile(ctx, ami, &newStatus)
	if reconcileError == nil {
		newStatus.EventList = append([]models.AddonEvent{event}, newStatus.EventList...)
	} else {
		if utilErrors.IsRetryable(reconcileError) && newStatus.Attemps <= MaxAddonInstallAttempts {
			newStatus.Attemps++
		} else {
			event.Message = reconcileError.Error()
			newStatus.EventList = append([]models.AddonEvent{event}, newStatus.EventList...)
			reconcileError = nil
		}
	}

	if reconcileError == nil {
		newStatus.ProcessedGeneration = ami.Generation
		newStatus.Attemps = 0
	}

	if !reflect.DeepEqual(status, newStatus) {
		if err := ami.SetStatus(newStatus); err != nil {
			return fmt.Errorf("failed to set addon_marketplace_install status: %w", err)
		}
		if err := r.settings.RepoSet.AddonMarketplaceInstalls().Update(ctx, ami, "status_info", "status_processed_generation"); err != nil {
			return fmt.Errorf("failed to persist addon_marketplace_install status: %w", err)
		}
	}

	return reconcileError
}

func (r *addonMarketplaceInstallReconciler) reconcile(ctx context.Context, ami *models.AddonMarketplaceInstall, status *models.AddonMarketplaceStatus) (models.AddonEvent, error) {
	log := r.settings.Log.WithValues(r.LogValuesFromID(ami.ID)...).WithValues(r.LogValuesFromItem(ami)...)
	eventType := models.AddonEventTypeMarketplaceInstallFailed
	for _, event := range status.EventList {
		if event.Type == models.AddonEventTypeMarketplaceInstallSucceeded || event.Type == models.AddonEventTypeMarketplaceUpdateSucceeded {
			eventType = models.AddonEventTypeMarketplaceUpdateFailed
			break
		}
	}
	event := models.AddonEvent{
		Time: time.Now(),
		Type: eventType,
	}
	amiConfig, err := ami.GetConfig()
	if err != nil {
		return event, sanitizeError(&log, err, "unable to get addon marketplace install config")
	}
	addonRepo, err := r.settings.RepoSet.AddonRepo().Filter(
		models.AddonRepoWhere.InstanceID.EQ(ami.InstanceID),
		qm.Where(fmt.Sprintf("%s ->> 'repoURL' = ?", models.AddonRepoColumns.Spec), amiConfig.RepoURL),
		qm.Where(fmt.Sprintf("%s ->> 'revision' = ?", models.AddonRepoColumns.Spec), amiConfig.Revision),
	).One(ctx)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return event, fmt.Errorf("addon repo[%s] not found", amiConfig.RepoURL)
		}
		return event, sanitizeError(&log, err, "unable to get addon repo")
	}
	instanceConfig, err := r.settings.RepoSet.ArgoCDInstanceConfigs().GetByID(ctx, ami.InstanceID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return event, fmt.Errorf("instance[%s] not found", ami.InstanceID)
		}
		return event, sanitizeError(&log, err, "unable to get instance config")
	}
	instanceSpec, err := instanceConfig.GetSpec()
	if err != nil {
		return event, sanitizeError(&log, err, "unable to get instance config")
	}
	closer, repoClient, err := v1.NewRepoClientV1(log, fmt.Sprintf(RepoProxyServerURL, ami.InstanceID), instanceConfig.Version.String, RepoProxyClientTimeout, !instanceSpec.IsGitDelegateInControlPlane())
	if err != nil {
		return event, sanitizeError(&log, err, "failed to create repo-server client")
	}
	defer ioutil.Close(closer)
	repo, found, err := GetRepository(ctx, &log, r.settings.K3sDBRawClient, amiConfig.RepoURL, ami.InstanceID)
	if err != nil {
		return event, sanitizeError(&log, err, "failed to get repository")
	}
	if !found {
		return event, fmt.Errorf("repository not found: %s", amiConfig.RepoURL)
	}
	if !repo.HasCredentials() {
		return event, fmt.Errorf("repository[%s] has no credentials", amiConfig.RepoURL)
	}
	event, err = InstallAddon(ctx, &log, repoClient, repo, ami.InstanceID, ami.ID, ami.Generation, amiConfig, status, event)
	if err != nil {
		return event, err
	}
	if addonRepo.Generation == addonRepo.StatusProcessedGeneration.Int {
		addonRepo.Generation += 1
		if err := r.settings.RepoSet.AddonRepo().Update(ctx, addonRepo, models.AddonRepoColumns.Generation); err != nil {
			log.Error(err, "unable to bump addon repo generation")
			// not critical error for addon marketplace install update
		}
	}
	return event, nil
}
