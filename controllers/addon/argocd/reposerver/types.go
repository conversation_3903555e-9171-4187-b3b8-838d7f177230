package reposerver

import (
	"context"

	"google.golang.org/grpc"

	v1 "github.com/akuityio/akuity-platform/controllers/addon/argocd/types/v1"
)

// TestRepositoryRequest is a query to test repository is valid or not and has valid access.
type TestRepositoryRequest struct {
	Repo *v1.Repository `protobuf:"bytes,1,opt,name=repo,proto3" json:"repo,omitempty"`
}

// TestRepositoryResponse represents the TestRepository response
type TestRepositoryResponse struct {
	// Request to verify the signature when generating the manifests (only for Git repositories)
	VerifiedRepository bool `protobuf:"varint,1,opt,name=verifiedRepository,proto3" json:"verifiedRepository,omitempty"`
}

// ResolveRevisionRequest
type ResolveRevisionRequest struct {
	Repo              *v1.Repository  `protobuf:"bytes,1,opt,name=repo,proto3" json:"repo,omitempty"`
	App               *v1.Application `protobuf:"bytes,2,opt,name=app,proto3" json:"app,omitempty"`
	AmbiguousRevision string          `protobuf:"bytes,3,opt,name=ambiguousRevision,proto3" json:"ambiguousRevision,omitempty"`
	SourceIndex       int64           `protobuf:"varint,4,opt,name=sourceIndex,proto3" json:"sourceIndex,omitempty"`
}

// ResolveRevisionResponse
type ResolveRevisionResponse struct {
	// returns the resolved revision
	Revision          string `protobuf:"bytes,1,opt,name=revision,proto3" json:"revision,omitempty"`
	AmbiguousRevision string `protobuf:"bytes,2,opt,name=ambiguousRevision,proto3" json:"ambiguousRevision,omitempty"`
}

type GitFilesRequest struct {
	Repo                      *v1.Repository `protobuf:"bytes,1,opt,name=repo,proto3" json:"repo,omitempty"`
	SubmoduleEnabled          bool           `protobuf:"varint,2,opt,name=submoduleEnabled,proto3" json:"submoduleEnabled,omitempty"`
	Revision                  string         `protobuf:"bytes,3,opt,name=revision,proto3" json:"revision,omitempty"`
	Path                      string         `protobuf:"bytes,4,opt,name=path,proto3" json:"path,omitempty"`
	NewGitFileGlobbingEnabled bool           `protobuf:"varint,5,opt,name=NewGitFileGlobbingEnabled,proto3" json:"NewGitFileGlobbingEnabled,omitempty"`
	NoRevisionCache           bool           `protobuf:"varint,6,opt,name=noRevisionCache,proto3" json:"noRevisionCache,omitempty"`
	VerifyCommit              bool           `protobuf:"varint,7,opt,name=verifyCommit,proto3" json:"verifyCommit,omitempty"`
}

type GitFilesResponse struct {
	// Map consisting of path of the path to its contents in bytes
	Map map[string][]byte `protobuf:"bytes,1,rep,name=map,proto3" json:"map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

type GitDirectoriesRequest struct {
	SubmoduleEnabled bool           `protobuf:"varint,2,opt,name=submoduleEnabled,proto3" json:"submoduleEnabled,omitempty"`
	Repo             *v1.Repository `protobuf:"bytes,1,opt,name=repo,proto3" json:"repo,omitempty"`
	Revision         string         `protobuf:"bytes,3,opt,name=revision,proto3" json:"revision,omitempty"`
	NoRevisionCache  bool           `protobuf:"varint,4,opt,name=noRevisionCache,proto3" json:"noRevisionCache,omitempty"`
	VerifyCommit     bool           `protobuf:"varint,5,opt,name=verifyCommit,proto3" json:"verifyCommit,omitempty"`
}

type GitDirectoriesResponse struct {
	// A set of directory paths
	Paths []string `protobuf:"bytes,1,rep,name=paths,proto3" json:"paths,omitempty"`
}

type IRepoClient interface {
	// TestRepository Returns a bool val if the repository is valid and has proper access
	TestRepository(ctx context.Context, in *TestRepositoryRequest, opts ...grpc.CallOption) (*TestRepositoryResponse, error)
	// ResolveRevision Returns a valid revision
	ResolveRevision(ctx context.Context, in *ResolveRevisionRequest, opts ...grpc.CallOption) (*ResolveRevisionResponse, error)
	// GetGitFiles returns a set of file paths and their contents for the given repo
	GetGitFiles(ctx context.Context, in *GitFilesRequest, opts ...grpc.CallOption) (*GitFilesResponse, error)
	// GetGitDirectories returns a set of directory paths for the given repo
	GetGitDirectories(ctx context.Context, in *GitDirectoriesRequest, opts ...grpc.CallOption) (*GitDirectoriesResponse, error)
	// CommitFiles commits the files to the given paths
	CommitFiles(ctx context.Context, in *CommitFilesRequest, opts ...grpc.CallOption) (*CommitFilesResponse, error)
	// GetAppDetails returns application details by given path
	GetAppDetails(ctx context.Context, in *RepoServerAppDetailsQuery, opts ...grpc.CallOption) (*RepoAppDetailsResponse, error)
	DeleteFiles(ctx context.Context, r *DeleteFilesRequest) (*DeleteFilesResponse, error)
}

// DeleteFilesRequest is the request to delete files/dirs to a repository.
type DeleteFilesRequest struct {
	// Repo contains repository information including, at minimum, the URL of the repository. Generally it will contain
	// repo credentials.
	Repo *v1.Repository `protobuf:"bytes,1,opt,name=repo,proto3" json:"repo,omitempty"`
	// TargetBranch is the branch Argo CD is committing to, i.e. the branch that will be updated.
	TargetBranch string `protobuf:"bytes,3,opt,name=targetBranch,proto3" json:"targetBranch,omitempty"`
	// CommitMessage is the commit message to use when committing changes.
	CommitMessage string `protobuf:"bytes,5,opt,name=commitMessage,proto3" json:"commitMessage,omitempty"`
	// Paths contains the paths to files and dirs that are meant to be deleted.
	Paths []string `protobuf:"bytes,6,rep,name=paths,proto3" json:"paths,omitempty"`
	// Username for commit
	Username string `protobuf:"bytes,7,opt,name=username,proto3" json:"username,omitempty"`
	// Email for commit
	Email string `protobuf:"bytes,8,opt,name=email,proto3" json:"email,omitempty"`
}

// DeleteFilesResponse is the response to the CommitFilesRequest.
type DeleteFilesResponse struct {
	CommitSha string `protobuf:"bytes,1,opt,name=commitSha,proto3" json:"commitSha,omitempty"`
}

type CommitFilesRequest struct {
	// Repo contains repository information including, at minimum, the URL of the repository. Generally it will contain
	// repo credentials.
	Repo *v1.Repository `protobuf:"bytes,1,opt,name=repo,proto3" json:"repo,omitempty"`
	// TargetBranch is the branch Argo CD is committing to, i.e. the branch that will be updated.
	TargetBranch string `protobuf:"bytes,3,opt,name=targetBranch,proto3" json:"targetBranch,omitempty"`
	// CommitMessage is the commit message to use when committing changes.
	CommitMessage string `protobuf:"bytes,5,opt,name=commitMessage,proto3" json:"commitMessage,omitempty"`
	// Paths contains the paths to write files to.
	Files []*FileDetails `protobuf:"bytes,6,rep,name=files,proto3" json:"files,omitempty"`
	// Username for commit
	Username string `protobuf:"bytes,7,opt,name=username,proto3" json:"username,omitempty"`
	// Email for commit
	Email string `protobuf:"bytes,8,opt,name=email,proto3" json:"email,omitempty"`
}

type FileDetails struct {
	// Path is the path to write the files to.
	Path string `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`
	// contains the data to write to the path.
	Data []byte `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

type CommitFilesResponse struct {
	// commitSha is the commit SHA of the files commit.
	CommitSha string `protobuf:"bytes,1,opt,name=commitSha,proto3" json:"commitSha,omitempty"`
}

// RepoAppDetailsResponse application details
type RepoAppDetailsResponse struct {
	Type      string            `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Helm      *HelmAppSpec      `protobuf:"bytes,3,opt,name=helm,proto3" json:"helm,omitempty"`
	Kustomize *KustomizeAppSpec `protobuf:"bytes,4,opt,name=kustomize,proto3" json:"kustomize,omitempty"`
	Plugin    *PluginAppSpec    `protobuf:"bytes,6,opt,name=plugin,proto3" json:"plugin,omitempty"`
}

type HelmAppSpec struct {
	Name       string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	ValueFiles []string `protobuf:"bytes,3,rep,name=valueFiles,proto3" json:"valueFiles,omitempty"`
	// the output of `helm inspect values`
	Parameters []*v1.HelmParameter `protobuf:"bytes,4,rep,name=parameters,proto3" json:"parameters,omitempty"`
	// the contents of values.yaml
	Values string `protobuf:"bytes,5,opt,name=values,proto3" json:"values,omitempty"`
}

// KustomizeAppSpec contains kustomize images
type KustomizeAppSpec struct {
	// images is a list of available images.
	Images []string `protobuf:"bytes,3,rep,name=images,proto3" json:"images,omitempty"`
}

// PluginAppSpec contains details about a plugin-type Application
type PluginAppSpec struct {
	ParametersAnnouncement []*ParameterAnnouncement `protobuf:"bytes,1,rep,name=parametersAnnouncement,proto3" json:"parametersAnnouncement,omitempty"`
}

type ParameterAnnouncement struct {
	// name is the name identifying a parameter.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// title is a human-readable text of the parameter name.
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// tooltip is a human-readable description of the parameter.
	Tooltip string `protobuf:"bytes,3,opt,name=tooltip,proto3" json:"tooltip,omitempty"`
	// required defines if this given parameter is mandatory.
	Required bool `protobuf:"varint,4,opt,name=required,proto3" json:"required,omitempty"`
	// itemType determines the primitive data type represented by the parameter. Parameters are always encoded as
	// strings, but this field lets them be interpreted as other primitive types.
	ItemType string `protobuf:"bytes,5,opt,name=itemType,proto3" json:"itemType,omitempty"`
	// collectionType is the type of value this parameter holds - either a single value (a string) or a collection
	// (array or map). If collectionType is set, only the field with that type will be used. If collectionType is not
	// set, `string` is the default. If collectionType is set to an invalid value, a validation error is thrown.
	CollectionType string `protobuf:"bytes,6,opt,name=collectionType,proto3" json:"collectionType,omitempty"`
	// string is the default value of the parameter if the parameter is a string.
	String_ string `protobuf:"bytes,7,opt,name=string,proto3" json:"string,omitempty"`
	// array is the default value of the parameter if the parameter is an array.
	Array []string `protobuf:"bytes,8,rep,name=array,proto3" json:"array,omitempty"`
	// map is the default value of the parameter if the parameter is a map.
	Map map[string]string `protobuf:"bytes,9,rep,name=map,proto3" json:"map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

// RepoServerAppDetailsQuery contains query information for app details request
type RepoServerAppDetailsQuery struct {
	Repo               *v1.Repository           `protobuf:"bytes,1,opt,name=repo,proto3" json:"repo,omitempty"`
	Source             *v1.ApplicationSource    `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	Repos              []*v1.Repository         `protobuf:"bytes,3,rep,name=repos,proto3" json:"repos,omitempty"`
	KustomizeOptions   *KustomizeOptions        `protobuf:"bytes,4,opt,name=kustomizeOptions,proto3" json:"kustomizeOptions,omitempty"`
	AppName            string                   `protobuf:"bytes,5,opt,name=appName,proto3" json:"appName,omitempty"`
	NoCache            bool                     `protobuf:"varint,6,opt,name=noCache,proto3" json:"noCache,omitempty"`
	NoRevisionCache    bool                     `protobuf:"varint,7,opt,name=noRevisionCache,proto3" json:"noRevisionCache,omitempty"`
	TrackingMethod     string                   `protobuf:"bytes,8,opt,name=trackingMethod,proto3" json:"trackingMethod,omitempty"`
	EnabledSourceTypes map[string]bool          `protobuf:"bytes,9,rep,name=enabledSourceTypes,proto3" json:"enabledSourceTypes,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	HelmOptions        *HelmOptions             `protobuf:"bytes,10,opt,name=helmOptions,proto3" json:"helmOptions,omitempty"`
	RefSources         map[string]*v1.RefTarget `protobuf:"bytes,11,rep,name=refSources,proto3" json:"refSources,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

// KustomizeOptions are options for kustomize to use when building manifests
type KustomizeOptions struct {
	// BuildOptions is a string of build parameters to use when calling `kustomize build`
	BuildOptions string `protobuf:"bytes,1,opt,name=buildOptions" json:"buildOptions,omitempty"`
	// BinaryPath holds optional path to kustomize binary
	BinaryPath string `protobuf:"bytes,2,opt,name=binaryPath" json:"binaryPath,omitempty"`
}

// HelmOptions holds helm options
type HelmOptions struct {
	ValuesFileSchemes []string `protobuf:"bytes,1,opt,name=valuesFileSchemes" json:"valuesFileSchemes"`
}
