package reposerver

import (
	"testing"

	"github.com/stretchr/testify/require"
)

func Test_EnsurePrefix(t *testing.T) {
	tests := []struct {
		name     string
		repo     string
		prefix   string
		expected string
	}{
		{
			name:     "No prefix present",
			repo:     "github.com/org/repo",
			prefix:   "ssh://",
			expected: "ssh://github.com/org/repo",
		},
		{
			name:     "Prefix already present",
			repo:     "ssh://github.com/org/repo",
			prefix:   "ssh://",
			expected: "ssh://github.com/org/repo",
		},
		{
			name:     "Empty repo URL specified",
			repo:     "",
			prefix:   "ssh://",
			expected: "ssh://",
		},
		{
			name:     "Empty prefix specified",
			repo:     "github.com/org/repo",
			prefix:   "",
			expected: "github.com/org/repo",
		},
		{
			name:     "Prefix is part of repo but not at start",
			repo:     "git@ssh://github.com/org/repo",
			prefix:   "ssh://",
			expected: "ssh://git@ssh://github.com/org/repo",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ensurePrefix(tt.repo, tt.prefix)
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestSameURL(t *testing.T) {
	tests := []struct {
		name      string
		leftRepo  string
		rightRepo string
		expected  bool
	}{
		{
			name:      "Same HTTPS URL",
			leftRepo:  "https://github.com/org/repo.git",
			rightRepo: "https://github.com/org/repo",
			expected:  true,
		},
		{
			name:      "HTTPS and SSH URL equivalent",
			leftRepo:  "**************:org/repo.git",
			rightRepo: "ssh://**************/org/repo.git",
			expected:  true,
		},
		{
			name:      "Different hosts",
			leftRepo:  "https://github.com/org/repo",
			rightRepo: "https://gitlab.com/org/repo",
			expected:  false,
		},
		{
			name:      "Case and suffix differences",
			leftRepo:  "<EMAIL>:Org/Repo.git",
			rightRepo: "https://github.com/org/repo",
			expected:  false, // not equivalent due to different schemes and users,
		},
		{
			name:      "Invalid left repo",
			leftRepo:  "not a url",
			rightRepo: "https://github.com/org/repo",
			expected:  false,
		},
		{
			name:      "Invalid right repo",
			leftRepo:  "https://github.com/org/repo",
			rightRepo: "!!!@@@",
			expected:  false,
		},
		{
			name:      "Both invalid",
			leftRepo:  "???",
			rightRepo: "!!!",
			expected:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SameURL(tt.leftRepo, tt.rightRepo)
			require.Equal(t, tt.expected, result)
		})
	}
}

func Test_NormalizeGitURl(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "HTTPS URL with .git suffix",
			input:    "https://github.com/org/repo.git",
			expected: "https://github.com/org/repo",
		},
		{
			name:     "HTTPS URL without .git suffix",
			input:    "https://github.com/org/repo",
			expected: "https://github.com/org/repo",
		},
		{
			name:     "SSH URL with git@ and colon",
			input:    "**************:org/repo.git",
			expected: "**************/org/repo",
		},
		{
			name:     "SSH URL with ssh:// prefix",
			input:    "ssh://**************/org/repo.git",
			expected: "**************/org/repo",
		},
		{
			name:     "Mixed case with spaces",
			input:    "  <EMAIL>:Org/Repo.git  ",
			expected: "**************/org/repo",
		},
		{
			name:     "Invalid URL",
			input:    "not a url",
			expected: "not%20a%20url",
		},
		{
			name:     "Empty string",
			input:    "",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := NormalizeGitURL(tt.input)
			require.Equal(t, tt.expected, result)
		})
	}
}
