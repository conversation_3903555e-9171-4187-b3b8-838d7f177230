package yaml

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"gopkg.in/yaml.v3"
)

func TestExtractYAMLValue(t *testing.T) {
	// Prepare test YAML
	baseYAML := `
database:
  host: "localhost"
  port: 5432
  credentials:
    username: admin
    password: secret
  settings:
    timeout: "30"
    enabled: true
    levels:
      - debug
      - info
  metadata:
    created_at: "2023-01-01T00:00:00Z"
    tags:
      environment: production
      type: primary
    annotations:
      k8s.io/xyz: "value1"
      example.com/app: "value2"
`

	testCases := []struct {
		name          string
		yamlContent   string
		path          string
		expectedValue string
		expectedError bool
	}{
		{
			name:          "Simple top-level extraction",
			yamlContent:   baseYAML,
			path:          "database.host",
			expectedValue: "localhost",
		},
		{
			name:          "Simple top-level extraction",
			yamlContent:   baseYAML,
			path:          "database.settings.timeout",
			expectedValue: "\"30\"",
		},
		{
			name:          "Nested credentials extraction",
			yamlContent:   baseYAML,
			path:          "database.credentials",
			expectedValue: "password: secret\nusername: admin",
		},
		{
			name:          "Array extraction",
			yamlContent:   baseYAML,
			path:          "database.settings.levels",
			expectedValue: "- debug\n- info",
		},
		{
			name:          "Nested map extraction",
			yamlContent:   baseYAML,
			path:          "database.metadata.tags",
			expectedValue: "environment: production\ntype: primary",
		},
		{
			name:          "Boolean value extraction",
			yamlContent:   baseYAML,
			path:          "database.settings.enabled",
			expectedValue: "true",
		},
		{
			name:          "Invalid path",
			yamlContent:   baseYAML,
			path:          "database.nonexistent",
			expectedError: true,
		},
		{
			name:          "Deep invalid path",
			yamlContent:   baseYAML,
			path:          "database.credentials.email",
			expectedError: true,
		},
		{
			name:          "Access key with special characters using bracket notation",
			yamlContent:   baseYAML,
			path:          `database.metadata.annotations["k8s.io/xyz"]`,
			expectedValue: `value1`,
		},
		{
			name:          "Access another key with special characters",
			yamlContent:   baseYAML,
			path:          `database.metadata.annotations["example.com/app"]`,
			expectedValue: `value2`,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := ExtractYamlPathValueFromBytes([]byte(tc.yamlContent), tc.path)

			if tc.expectedError {
				assert.Error(t, err, "Expected an error for path: %s", tc.path)
			} else {
				assert.NoError(t, err, "Unexpected error for path: %s", tc.path)
				assert.Equal(t, tc.expectedValue, result, "Extracted value mismatch for path: %s", tc.path)
			}
		})
	}
}

func TestSetYAMLValue(t *testing.T) {
	testCases := []struct {
		name          string
		initialYAML   string
		path          string
		valueToSet    interface{}
		expectedYAML  string
		expectedError bool
	}{
		{
			name: "Set simple string value",
			initialYAML: `
database:
  host: localhost
`,
			path:       "database.host",
			valueToSet: "newhost.example.com",
			expectedYAML: `database:
    host: newhost.example.com
`,
		},
		{
			name: "Set nested map",
			initialYAML: `
database:
  credentials: {}
`,
			path: "database.credentials",
			valueToSet: map[string]interface{}{
				"username": "newadmin",
				"password": "newpassword",
			},
			expectedYAML: `database:
    credentials:
        password: newpassword
        username: newadmin
`,
		},
		{
			name: "Set new nested path",
			initialYAML: `
database: {}
`,
			path:       "database.host",
			valueToSet: "localhost",
			expectedYAML: `database:
    host: localhost
`,
		},
		{
			name: "Set array value",
			initialYAML: `
settings:
  levels: []
`,
			path:       "settings.levels",
			valueToSet: []string{"debug", "info", "warn"},
			expectedYAML: `settings:
    levels:
        - debug
        - info
        - warn
`,
		},
		{
			name: "Set boolean value",
			initialYAML: `
database:
  enabled: false
`,
			path:       "database.enabled",
			valueToSet: true,
			expectedYAML: `database:
    enabled: true
`,
		},
		{
			name: "Set complex nested structure",
			initialYAML: `
services: {}
`,
			path: "services.database",
			valueToSet: map[string]interface{}{
				"host": "localhost",
				"port": 5432,
				"settings": map[string]interface{}{
					"timeout": 30,
					"retry":   3,
				},
			},
			expectedYAML: `services:
    database:
        host: localhost
        port: 5432
        settings:
            retry: 3
            timeout: 30
`,
		},
		{
			name:        "Set value in completely missing nested structure",
			initialYAML: `final: "sdfgs"`,
			path:        "services.database.credentials.username",
			valueToSet:  "admin",
			expectedYAML: `final: "sdfgs"
services:
    database:
        credentials:
            username: admin
`,
		},
		{
			name: "Set value with partially missing intermediate keys",
			initialYAML: `
app:
  settings: "test"
`,
			path:       "app.settings.logging.level",
			valueToSet: "debug",
			expectedYAML: `app:
    settings:
        logging:
            level: debug
`,
		},
		{
			name: "Overwrite nested structure with new value",
			initialYAML: `
config:
  database:
    connection: old_connection_string
`,
			path: "config.database",
			valueToSet: map[string]interface{}{
				"host": "localhost",
				"port": 5432,
			},
			expectedYAML: `config:
    database:
        host: localhost
        port: 5432
`,
		},
		{
			name: "Remove nested key and empty parent",
			initialYAML: `
services:
  database:
    credentials:
      username: admin
      password: secret
`,
			path:       "services.database.credentials.username",
			valueToSet: nil,
			expectedYAML: `services:
    database:
        credentials:
            password: secret
`,
		},
		{
			name: "Remove nested key and cascade empty parents",
			initialYAML: `
services:
  database:
    credentials:
      username: admin
`,
			path:         "services.database.credentials.username",
			valueToSet:   nil,
			expectedYAML: `{}`,
		},
		{
			name: "Do not remove parent with other children",
			initialYAML: `
services:
  database:
    credentials:
      username: admin
      email: <EMAIL>
`,
			path:       "services.database.credentials.username",
			valueToSet: nil,
			expectedYAML: `services:
    database:
        credentials:
            email: <EMAIL>
`,
		},
		{
			name: "Remove multiple levels of empty parents",
			initialYAML: `
app:
  services:
    database:
      credentials:
        username: admin
`,
			path:         "app.services.database.credentials.username",
			valueToSet:   nil,
			expectedYAML: `{}`,
		},
		{
			name: "Do not remove when parent has multiple children",
			initialYAML: `
app:
  services:
    database:
      credentials:
        username: admin
        type: primary
`,
			path:       "app.services.database.credentials.username",
			valueToSet: nil,
			expectedYAML: `app:
    services:
        database:
            credentials:
                type: primary
`,
		},
		{
			name: "Set value using bracket notation",
			initialYAML: `
metadata:
  annotations:
    key: old_value
`,
			path:       `metadata["annotations"]["key"]`,
			valueToSet: "new_value",
			expectedYAML: `metadata:
    annotations:
        key: new_value
`,
		},
		{
			name: "Set value in deeply nested bracket notation",
			initialYAML: `
spec:
  template:
    spec:
      containers:
        - name: app
          env: {}
`,
			path: `spec["template"]["spec"]["containers"]`,
			valueToSet: []map[string]interface{}{
				{
					"name": "app",
					"env": map[string]interface{}{
						"CONFIG_PATH": "/etc/config",
					},
				},
				{
					"name": "app1",
					"env":  map[string]interface{}{},
				},
			},
			expectedYAML: `spec:
    template:
        spec:
            containers:
                - name: app
                  env:
                      CONFIG_PATH: /etc/config
                - name: app1
                  env: {}
`,
		},
		{
			name: "Set value with special characters in bracket notation",
			initialYAML: `
metadata:
  annotations: {}
`,
			path:       `["metadata"]["annotations"]["k8s.io/name"]`,
			valueToSet: "my-app",
			expectedYAML: `metadata:
    annotations:
        k8s.io/name: my-app
`,
		},
		{
			name:        "Set value in an empty map using bracket notation",
			initialYAML: `config: {}`,
			path:        `config["database"]["host"]`,
			valueToSet:  "localhost",
			expectedYAML: `config:
    database:
        host: localhost
`,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Marshal the value to YAML
			valueYAML, err := yaml.Marshal(tc.valueToSet)
			assert.NoError(t, err, "Failed to marshal test value")

			// Set the YAML value
			result, err := SetYAMLPathValueFromBytes([]byte(tc.initialYAML), tc.path, valueYAML)

			if tc.expectedError {
				assert.Error(t, err, "Expected an error")
			} else {
				assert.NoError(t, err, "Unexpected error")

				// Unmarshal both expected and result to compare normalized structures
				var expectedMap, resultMap map[string]interface{}
				err = yaml.Unmarshal([]byte(tc.expectedYAML), &expectedMap)
				assert.NoError(t, err, "Failed to unmarshal expected YAML")

				err = yaml.Unmarshal(result, &resultMap)
				assert.NoError(t, err, "Failed to unmarshal result YAML")

				assert.Equal(t, expectedMap, resultMap, "YAML structures do not match")
			}
		})
	}
}

// Benchmark the extraction function
func BenchmarkExtractYAMLValue(b *testing.B) {
	yamlContent := `
database:
  host: localhost
  port: 5432
  credentials:
    username: admin
    password: secret
`

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = ExtractYamlPathValueFromBytes([]byte(yamlContent), "database.credentials.username")
	}
}

// Benchmark the set value function
func BenchmarkSetYAMLValue(b *testing.B) {
	yamlContent := `
database:
  host: localhost
  port: 5432
`

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = SetYAMLPathValueFromBytes([]byte(yamlContent), "database.credentials.username", []byte(`"admin"`))
	}
}

func TestYAMLPathValidation(t *testing.T) {
	testCases := []struct {
		name          string
		path          string
		shouldBeValid bool
		reason        string
	}{
		// Valid paths with dot notation
		{
			name:          "Simple valid path",
			path:          "metadata.annotations",
			shouldBeValid: true,
		},
		{
			name:          "Long nested valid path",
			path:          "spec.template.spec.containers.volumeMounts",
			shouldBeValid: true,
		},
		{
			name:          "Path with underscores",
			path:          "metadata.labels.app_name",
			shouldBeValid: true,
		},
		{
			name:          "Path with numbers",
			path:          "data.config2.setting1",
			shouldBeValid: true,
		},

		// Valid paths with bracket notation
		{
			name:          "Simple bracket notation",
			path:          `["metadata"]["annotations"]`,
			shouldBeValid: true,
		},
		{
			name:          "Double quotes with special characters",
			path:          `metadata.annotations["kubernetes.io/name"]`,
			shouldBeValid: true,
		},
		{
			name:          "Single quotes with special characters",
			path:          `metadata.annotations['example.com/app']`,
			shouldBeValid: true,
		},
		{
			name:          "Bracket with slashes",
			path:          `metadata.annotations["cert-manager.io/cluster-issuer"]`,
			shouldBeValid: true,
		},
		{
			name:          "Bracket with multiple special characters",
			path:          `metadata.annotations["k8s.io/last-applied-configuration"]`,
			shouldBeValid: true,
		},
		{
			name:          "Brackets with spaces in key name",
			path:          `metadata.annotations["Last Updated Date"]`,
			shouldBeValid: true,
		},
		{
			name:          "Brackets with extra whitespace",
			path:          `metadata[ "annotations" ][ "k8s.io/name" ]`,
			shouldBeValid: true,
		},

		// Mixed notation paths
		{
			name:          "Mixed dot and bracket",
			path:          `metadata.annotations["k8s.io/role"].value`,
			shouldBeValid: true,
		},
		{
			name:          "Complex mixed notation",
			path:          `spec["template"].spec.containers["env"]["CONFIG_URL"]`,
			shouldBeValid: true,
		},
		{
			name:          "Starting with bracket, ending with dot",
			path:          `["metadata"].annotations.app`,
			shouldBeValid: true,
		},
		{
			name:          "contains dashes",
			path:          `test-app.annotations.app`,
			shouldBeValid: true,
		},

		// Invalid paths
		{
			name:          "Path with consecutive dots",
			path:          "metadata..annotations",
			shouldBeValid: false,
			reason:        "consecutive dots",
		},
		{
			name:          "Path starting with dot",
			path:          ".metadata.annotations",
			shouldBeValid: false,
			reason:        "starting with dot",
		},
		{
			name:          "Path ending with dot",
			path:          "metadata.annotations.",
			shouldBeValid: false,
			reason:        "ending with dot",
		},
		{
			name:          "Unbalanced brackets",
			path:          `metadata["annotations"]"kubernetes.io/name"]`,
			shouldBeValid: false,
			reason:        "unbalanced brackets",
		},
		{
			name:          "Mismatched quotes",
			path:          `metadata["annotations]['app']`,
			shouldBeValid: false,
			reason:        "mismatched quotes",
		},
		{
			name:          "Missing quotes in bracket",
			path:          `metadata[annotations]`,
			shouldBeValid: false,
			reason:        "missing quotes",
		},
		{
			name:          "Missing data in bracket",
			path:          `[]`,
			shouldBeValid: false,
			reason:        "missing data",
		},
		{
			name:          "Missing data in bracket",
			path:          `[""]`,
			shouldBeValid: false,
			reason:        "missing data",
		},
		{
			name:          "Special characters without brackets",
			path:          "metadata.kubernetes.io/name",
			shouldBeValid: false,
			reason:        "special characters without brackets",
		},
		{
			name:          "Unclosed bracket",
			path:          `metadata["annotations`,
			shouldBeValid: false,
			reason:        "unclosed bracket",
		},
		{
			name:          "Dot after bracket without key",
			path:          `metadata["annotations"].`,
			shouldBeValid: false,
			reason:        "dot after bracket without key",
		},
		{
			name:          "Invalid characters in dot notation",
			path:          "metadata.annotations-with-dashes",
			shouldBeValid: true,
			reason:        "invalid characters in dot notation",
		},
		{
			name:          "Escaped dot in dot notation",
			path:          `metadata.kubernetes\.io`,
			shouldBeValid: false,
			reason:        "escaped characters not supported in dot notation",
		},

		// Edge cases for bracket notation
		{
			name:          "Empty key in brackets",
			path:          `metadata[""]`,
			shouldBeValid: false,
			reason:        "empty key is not allowed in bracket notation",
		},
		{
			name:          "Brackets with numbers",
			path:          `metadata["123"]`,
			shouldBeValid: true,
			reason:        "numbers are allowed in bracket notation",
		},
		{
			name:          "Path with escaped brackets in key",
			path:          `metadata["key[with]brackets"]`,
			shouldBeValid: true,
			reason:        "brackets in key content",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := ValidateYAMLPath(tc.path)
			if tc.shouldBeValid {
				assert.NoError(t, err, "Path should be valid: %s", tc.path)
			} else {
				assert.Error(t, err, "Path should be invalid (%s): %s", tc.reason, tc.path)
				assert.Contains(t, err.Error(), "invalid YAML path format", "Error should indicate invalid format")
			}
		})
	}
}

func TestParseYAMLPath(t *testing.T) {
	testCases := []struct {
		name         string
		path         string
		expectedKeys []string
		shouldError  bool
	}{
		{
			name:         "Simple dot notation",
			path:         "metadata.annotations.key",
			expectedKeys: []string{"metadata", "annotations", "key"},
			shouldError:  false,
		},
		{
			name:         "Simple bracket notation",
			path:         `["metadata"]["annotations"]["key"]`,
			expectedKeys: []string{"metadata", "annotations", "key"},
			shouldError:  false,
		},
		{
			name:         "Mixed notation",
			path:         `metadata.annotations["kubernetes.io/name"]`,
			expectedKeys: []string{"metadata", "annotations", "kubernetes.io/name"},
			shouldError:  false,
		},
		{
			name:         "Complex mixed with special characters",
			path:         `metadata["annotations"].labels["app.kubernetes.io/name"]`,
			expectedKeys: []string{"metadata", "annotations", "labels", "app.kubernetes.io/name"},
			shouldError:  false,
		},
		{
			name:         "Bracket with whitespace",
			path:         `metadata[ "annotations" ][ "key" ]`,
			expectedKeys: []string{"metadata", "annotations", "key"},
			shouldError:  false,
		},
		{
			name:         "Invalid path",
			path:         "metadata..annotations",
			expectedKeys: nil,
			shouldError:  true,
		},
		{
			name:         "Bracket with empty key",
			path:         `metadata[""]`,
			expectedKeys: nil,
			shouldError:  true,
		},
		{
			name:         "Single quotes brackets",
			path:         `metadata['annotations']['key']`,
			expectedKeys: []string{"metadata", "annotations", "key"},
			shouldError:  false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			keys, err := ParseYAMLPath(tc.path)

			if tc.shouldError {
				assert.Error(t, err, "ParseYAMLPath should error for: %s", tc.path)
			} else {
				assert.NoError(t, err, "ParseYAMLPath should not error for: %s", tc.path)
				assert.Equal(t, tc.expectedKeys, keys, "Parsed keys do not match expected for: %s", tc.path)
			}
		})
	}
}
