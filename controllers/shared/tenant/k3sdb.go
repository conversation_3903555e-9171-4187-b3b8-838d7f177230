package tenant

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"io"

	//nolint:staticcheck
	"github.com/golang/protobuf/proto"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"

	"github.com/akuityio/akuity-platform/internal/argoproj/argocd"
	kargoTypes "github.com/akuityio/akuity-platform/internal/kargo/types"
	ioutil "github.com/akuityio/akuity-platform/internal/utils/io"
	"github.com/akuityio/akuity-platform/models/models"
)

// protoEncodingPrefix holds magic prefix that kubernetes adds to all values stored in etcd
//
// https://github.com/kubernetes/apimachinery/blob/f3b1305f4010e9c68a3d36811cbc6f1af1ee186f/pkg/runtime/serializer/protobuf/protobuf.go#L45
var protoEncodingPrefix = []byte{0x6b, 0x38, 0x73, 0x00}

//nolint:staticcheck
func unmarshalProto[T proto.Unmarshaler](data []byte, res T) error {
	data = data[len(protoEncodingPrefix):]
	un := &runtime.Unknown{}
	if err := un.Unmarshal(data); err != nil {
		return err
	}
	return res.Unmarshal(un.Raw)
}

// Resource holds information about a resource and unmarshaller function
type Resource[T any] struct {
	Group        string
	Resource     string
	Unmarshal    func(data []byte) (*T, error)
	ClusterScope bool
}

var (
	ApplicationsGR = Resource[argocd.Application]{Group: "argoproj.io", Resource: "applications", Unmarshal: func(data []byte) (*argocd.Application, error) {
		var res argocd.Application
		return &res, json.Unmarshal(data, &res)
	}}
	KargoProjectsGR = Resource[kargoTypes.Project]{Group: "kargo.akuity.io", Resource: "projects", ClusterScope: true, Unmarshal: func(data []byte) (*kargoTypes.Project, error) {
		var res kargoTypes.Project
		return &res, json.Unmarshal(data, &res)
	}}
	KargoStagesGR = Resource[kargoTypes.Stage]{Group: "kargo.akuity.io", Resource: "stages", Unmarshal: func(data []byte) (*kargoTypes.Stage, error) {
		var res kargoTypes.Stage
		return &res, json.Unmarshal(data, &res)
	}}
	EventsGR = Resource[v1.Event]{Group: "", Resource: "events", Unmarshal: func(data []byte) (*v1.Event, error) {
		var res v1.Event
		return &res, unmarshalProto(data, &res)
	}}
	ConfigMapsGR = Resource[v1.ConfigMap]{Group: "", Resource: "configmaps", Unmarshal: func(data []byte) (*v1.ConfigMap, error) {
		var res v1.ConfigMap
		return &res, unmarshalProto(data, &res)
	}}
	SecretsGR = Resource[v1.Secret]{Group: "", Resource: "secrets", Unmarshal: func(data []byte) (*v1.Secret, error) {
		var res v1.Secret
		return &res, unmarshalProto(data, &res)
	}}
)

// Query holds query parameters
type Query struct {
	Select    []string
	Name      string
	Limit     int
	OrderBy   string
	Mods      []qm.QueryMod
	Namespace string
}

func (p Query) merge(other Query) Query {
	if other.Name != "" {
		p.Name = other.Name
	}
	if other.Namespace != "" {
		p.Namespace = other.Namespace
	}
	if other.Limit != 0 {
		p.Limit = other.Limit
	}
	if other.OrderBy != "" {
		p.OrderBy = other.OrderBy
	}
	if other.Mods != nil {
		p.Mods = append(p.Mods, other.Mods...)
	}
	if other.Select != nil {
		p.Select = append(p.Select, other.Select...)
	}
	return p
}

// Row holds k8s object and kine ID
type Row[T any] struct {
	Item T
	ID   int
}

func (r *Resource[T]) Count(ctx context.Context, kargo bool, db boil.ContextExecutor, instanceID string, q ...Query) (int64, error) {
	query := queryResources(
		instanceID, r.ClusterScope, kargo,
		schema.GroupResource{Group: r.Group, Resource: r.Resource},
		append(q, Query{Select: []string{"count(*)"}})...)

	var cnt int64
	if err := query.QueryRowContext(ctx, db).Scan(&cnt); err != nil {
		return 0, err
	}
	return cnt, nil
}

// Iterate executes given query and returns an iterator over rows
func (r *Resource[T]) Iterate(ctx context.Context, kargo bool, db boil.ContextExecutor, instanceID string, q ...Query) (func() (*Row[T], error), io.Closer, error) {
	query := queryResources(instanceID, r.ClusterScope, kargo, schema.GroupResource{Group: r.Group, Resource: r.Resource}, q...)
	rows, err := query.QueryContext(ctx, db)
	if err != nil {
		return nil, nil, err
	}
	return func() (*Row[T], error) {
		if !rows.Next() {
			return nil, rows.Err()
		}
		var id int
		var value []byte
		if err := rows.Scan(&id, &value); err != nil {
			return nil, err
		}
		item, err := r.Unmarshal(value)
		if err != nil {
			return nil, err
		}
		return &Row[T]{Item: *item, ID: id}, nil
	}, rows, nil
}

// One executes given query and returns a single row. If no rows are found, returns sql.ErrNoRows
func (r *Resource[T]) One(ctx context.Context, kargo bool, db boil.ContextExecutor, instanceID string, q ...Query) (*T, error) {
	iterator, closer, err := r.Iterate(ctx, kargo, db, instanceID, q...)
	if err != nil {
		return nil, err
	}
	defer ioutil.Close(closer)
	next, err := iterator()
	if err != nil {
		return nil, err
	}
	if next == nil {
		return nil, sql.ErrNoRows
	}
	return &next.Item, nil
}

// List executes given query and returns all rows
func (r *Resource[T]) List(ctx context.Context, kargo bool, db boil.ContextExecutor, instanceID string, q ...Query) ([]Row[T], error) {
	iterator, closer, err := r.Iterate(ctx, kargo, db, instanceID, q...)
	if err != nil {
		return nil, err
	}
	defer ioutil.Close(closer)
	var res []Row[T]
	for {
		next, err := iterator()
		if err != nil {
			return nil, err
		}
		if next == nil {
			break
		}

		res = append(res, *next)
	}
	return res, nil
}

func queryResources(instanceId string, clusterScope, kargo bool, gr schema.GroupResource, paramsList ...Query) *queries.Query {
	var params Query
	for _, p := range paramsList {
		params = params.merge(p)
	}

	name := params.Name
	if params.Name == "" {
		name = "%"
	}
	groupResource := gr.Resource
	if gr.Group != "" {
		groupResource = fmt.Sprintf("%s/%s", gr.Group, gr.Resource)
	}

	prefix := ""
	ns := "argocd"
	if kargo {
		prefix = "kargo_"
		ns = "kargo"
		if gr.Resource == "events" || gr.Resource == "stages" {
			ns = "%" // for kargo we need to monitor events/stages in all namespaces
		}
	}

	if params.Namespace != "" {
		ns = params.Namespace
	}

	fromQm := qm.From(fmt.Sprintf(`
(
  SELECT
    kv.id, value, kv.name, substring(kv.name, length('/registry/%[3]s/%[4]s/%[5]s')) as resource_name
  FROM
    %[1]sinstance_%[2]s.kine AS kv
    JOIN (
      SELECT
        max(mkv.id) AS id
      FROM
        %[1]sinstance_%[2]s.kine AS mkv
      WHERE
        name LIKE '/registry/%[3]s/%[4]s/%[5]s'
      GROUP BY
        mkv.name) AS maxkv ON kv.id = maxkv.id
    WHERE
      kv.deleted = 0) as lkv`, prefix, instanceId, groupResource, ns, name))

	if clusterScope {
		fromQm = qm.From(fmt.Sprintf(`
(
  SELECT
    kv.id, value, kv.name
  FROM
    %[1]sinstance_%[2]s.kine AS kv
    JOIN (
      SELECT
        max(mkv.id) AS id
      FROM
        %[1]sinstance_%[2]s.kine AS mkv
      WHERE
        name LIKE '/registry/%[3]s/%[4]s'
      GROUP BY
        mkv.name) AS maxkv ON kv.id = maxkv.id
    WHERE
      kv.deleted = 0) as lkv`, prefix, instanceId, groupResource, name))
	}

	columns := []string{"lkv.id", "lkv.value"}
	if len(params.Select) > 0 {
		columns = params.Select
	}

	mods := []qm.QueryMod{fromQm, qm.Select(columns...)}
	if params.Limit > 0 {
		mods = append(mods, qm.Limit(params.Limit))
	}
	if params.OrderBy != "" {
		mods = append(mods, qm.OrderBy(params.OrderBy))
	}
	return models.NewQuery(append(mods, params.Mods...)...)
}
