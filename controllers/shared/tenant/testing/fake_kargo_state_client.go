package testing

import (
	"context"

	"github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/akuity-platform/controllers/shared/tenant"
	"github.com/akuityio/akuity-platform/models/models"
)

type FakeKargoTenantStateClient struct{}

func (f FakeKargoTenantStateClient) GetAgentState(ctx context.Context, cluster *models.KargoAgent) (*models.KargoAgentState, error) {
	return &models.KargoAgentState{
		Status: &client.AggregatedHealthResponse{MinObservedGeneration: 1},
	}, nil
}

func (f FakeKargoTenantStateClient) GetClusterData(ctx context.Context, cluster *models.KargoAgent) (*tenant.ClusterData, error) {
	return &tenant.ClusterData{}, nil
}

func (f FakeKargoTenantStateClient) NeedK3sCNReset(ctx context.Context, instanceID string) (bool, error) {
	return false, nil
}

func (f FakeKargoTenantStateClient) GetApplicationInfo(ctx context.Context, instanceID string) (*models.ApplicationsStatus, error) {
	return &models.ApplicationsStatus{}, nil
}

func (f FakeKargoTenantStateClient) ListEventsSinceID(ctx context.Context, instanceID string, sinceID, count int) ([]tenant.Event, error) {
	return nil, nil
}

func (f FakeKargoTenantStateClient) GetStatsCount(ctx context.Context, instanceID string) (int, int, error) {
	return 0, 0, nil
}

func (f FakeKargoTenantStateClient) GetApplicationsCount(ctx context.Context, instanceID, clusterName string) (int, error) {
	return 0, nil
}
