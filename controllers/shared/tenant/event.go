package tenant

// InstanceEvent holds information about ArgoCD instance event
type InstanceEvent struct {
	InstanceID  string            `json:"instance_id"`
	ClusterName string            `json:"cluster_name"`
	Type        InstanceEventType `json:"type"`
	Shard       string            `json:"shard"`
}

const (
	InstanceEventsChannel      = "default.on_instance_changed"
	KargoInstanceEventsChannel = "default.on_kargo_instance_changed"
	AppUpdateEventsChannel     = "default.on_app_update"
)

type InstanceEventType string

const (
	InstanceEventTypeK8SEventCreated      InstanceEventType = "k8s_event_created"
	InstanceEventTypeClusterStatusChanged InstanceEventType = "cluster_status_changed"
)

type AppUpdateEvent struct {
	InstanceID     string `json:"instance_id"`
	AppName        string `json:"app_name"`
	Type           string `json:"type"`
	ClusterAddonID string `json:"cluster_addon_id"`
}
