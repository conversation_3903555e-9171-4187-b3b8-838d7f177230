package integration

import (
	"context"
	"database/sql"

	"github.com/go-logr/logr"
	"github.com/pkg/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

type eventReconciler struct {
	log          *logr.Logger
	repoSet      client.RepoSet
	db           *sql.DB
	featSvc      features.Service
	domainSuffix string
}

func NewEventReconciler(
	log *logr.Logger,
	repoSet client.RepoSet,
	db *sql.DB,
	featSvc features.Service,
	domainSuffix string,
) *eventReconciler {
	return &eventReconciler{
		log:          log,
		repoSet:      repoSet,
		db:           db,
		featSvc:      featSvc,
		domainSuffix: domainSuffix,
	}
}

func (r *eventReconciler) ItemToID(item *models.Event) string {
	return item.ID
}

func (r *eventReconciler) IDColumn() string {
	return models.EventTableColumns.ID
}

func (r *eventReconciler) LogValuesFromID(id string) []interface{} {
	return []interface{}{"event_id", id}
}

func (r *eventReconciler) LogValuesFromItem(item *models.Event) []interface{} {
	return []interface{}{"event_type", item.EventType, "org_id", item.OrganizationID}
}

func (r *eventReconciler) Reconcile(ctx context.Context, event *models.Event) error {
	db, txBeginner := database.WithTxBeginner(r.db)
	repoSet := client.NewRepoSet(db)
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()
	tx, err := txBeginner.Begin(ctx)
	if err != nil {
		return err
	}

	var users []*models.AkuityUser
	var orgNotificationCfgs []*models.OrganizationNotificationConfig
	// check if this event is for the entire platform or a specific organization
	if !event.OrganizationID.IsZero() && event.OrganizationID.String != "" {
		if !r.featSvc.GetFeatureStatuses(ctx, &event.OrganizationID.String).GetNotification().Enabled() {
			r.log.Info("notifications disabled for org", "org_id", event.OrganizationID.String)
			return nil
		}
		// the event is for a specific organization
		orgUsers, err := repoSet.OrganizationUsers().Filter(
			models.OrganizationUserWhere.OrganizationID.EQ(event.OrganizationID.String),
			qm.Load(models.OrganizationUserRels.User),
		).ListAll(ctx)
		if err != nil {
			return err
		}
		for _, orgUser := range orgUsers {
			users = append(users, orgUser.R.User)
		}

		orgNotificationCfgs, err = repoSet.
			OrganizationNotificationConfigs(
				models.OrganizationNotificationConfigWhere.OrganizationID.EQ(event.OrganizationID.String),
				models.OrganizationNotificationConfigWhere.DeliveryMethod.IN(models.EventTypeDeliveryOptions[event.EventType.String]),
			).ListAll(ctx)
		if err != nil {
			return errors.Wrap(err, "list webhook configs")
		}
	} else {
		// the event is for the entire platform
		us, err := repoSet.Users().ListAll(ctx)
		if err != nil {
			return err
		}
		users = us
	}

	var ns []*models.Notification
	// Generate events for users
	for _, user := range users {
		for _, deliveryType := range models.EventTypeDeliveryOptions[event.EventType.String] {
			shouldReceive, err := user.ShouldReceiveNotification(event, deliveryType)
			if err != nil {
				return err
			}
			if shouldReceive {
				ns = append(ns, &models.Notification{
					EventID:        event.ID,
					TargetID:       null.StringFrom(user.ID),
					DeliveryMethod: deliveryType,
				})
			}
		}
	}
	// Generate events for organization notification configs
	for _, cfg := range orgNotificationCfgs {
		for _, deliveryType := range models.EventTypeDeliveryOptions[event.EventType.String] {
			shouldReceive, err := cfg.ShouldReceiveNotification(event, deliveryType)
			if err != nil {
				return err
			}
			if shouldReceive {
				ns = append(ns, &models.Notification{
					EventID:        event.ID,
					TargetID:       null.StringFrom(cfg.ID),
					DeliveryMethod: deliveryType,
				})
			}
		}
	}

	for _, notification := range ns {
		if err := repoSet.Notifications().Create(ctx, notification); err != nil {
			return err
		}
	}

	// set event to processed
	event.StatusProcessed = true
	if err := repoSet.Events().Update(ctx, event, models.EventColumns.StatusProcessed); err != nil {
		return err
	}
	return tx.Commit()
}
