package integration

import (
	"context"
	"fmt"
	"strings"

	"github.com/go-logr/logr"
	"github.com/volatiletech/null/v8"
	"golang.org/x/mod/semver"

	agentClient "github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/akuity-platform/internal/services/notifications"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

type latestProductVersionDetails struct {
	agentClient.ComponentVersion
	LatestAkVersion int
}

func latestProductVersion(versions []agentClient.ComponentVersion) (latestProductVersionDetails, error) {
	var latest *agentClient.ComponentVersion
	// versions is expected to be sorted in descending order
	// so the first non "latest" version is the latest tagged version
	for i := range versions {
		if versions[i].Version == "latest" || strings.HasPrefix(semver.Prerelease(versions[i].Version), "-rc") {
			continue
		}
		latest = &versions[i]
		break
	}
	if latest == nil {
		return latestProductVersionDetails{}, fmt.Errorf("no latest version found")
	}
	latestAKVersion := int64(-1)
	for _, akVersion := range latest.AKVersions {
		isAk, ver := misc.GetAKPVersion(akVersion.Version)
		if isAk && ver > latestAKVersion {
			latestAKVersion = ver
		}
	}
	return latestProductVersionDetails{
		ComponentVersion: *latest,
		LatestAkVersion:  int(latestAKVersion),
	}, nil
}

func sendProductVersionUpdate(ctx context.Context, log *logr.Logger, repoSet client.RepoSet, orgID, latestVersion, productType string) error {
	event := &models.Event{
		EventType:      null.StringFrom(models.EventTypeNewProductVersion),
		OrganizationID: null.StringFrom(orgID),
	}
	metadata := models.EventMetadata{
		VersionUpdate: &models.NewProductVersion{
			Version: latestVersion,
			Product: productType,
		},
	}
	if err := event.SetMetadata(metadata); err != nil {
		return err
	}
	if err, _ := notifications.CreateEventWithDeDuplication(ctx, repoSet, event); err != nil {
		return err
	}
	log.V(1).Info("sent product version update event", "event", event)
	return nil
}
