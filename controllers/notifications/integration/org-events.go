package integration

import (
	"context"
	"database/sql"
	"fmt"
	"math"
	"strings"
	"time"

	"github.com/go-logr/logr"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"golang.org/x/mod/semver"

	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/services/kargo"
	"github.com/akuityio/akuity-platform/internal/services/notifications"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/internal/version"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

var (
	expirationDays       = map[int]bool{1: true, 3: true, 7: true}
	usageAlertThresholds = []float64{
		1.0,  // >= 100%
		0.95, // >= 95%
		0.8,  // >= 80%
	}
	usageAlertRetriggerThreshold = 30 * time.Hour * 24 // 10 days
)

type orgEventReconciler struct {
	log                 *logr.Logger
	repoSet             client.RepoSet
	db                  *sql.DB
	featSvc             features.Service
	argocdLatestVersion latestProductVersionDetails
	kargoLatestVersion  latestProductVersionDetails
	gracePeriodDuration time.Duration
}

func NewOrgEventReconciler(
	log *logr.Logger,
	repoSet client.RepoSet,
	db *sql.DB,
	featSvc features.Service,
	argocdLatestVersion latestProductVersionDetails,
	kargoLatestVersion latestProductVersionDetails,
	gracePeriodDuration time.Duration,
) *orgEventReconciler {
	return &orgEventReconciler{
		log:                 log,
		repoSet:             repoSet,
		db:                  db,
		featSvc:             featSvc,
		argocdLatestVersion: argocdLatestVersion,
		kargoLatestVersion:  kargoLatestVersion,
		gracePeriodDuration: gracePeriodDuration,
	}
}

func (r *orgEventReconciler) ItemToID(item *models.Organization) string {
	return item.ID
}

func (r *orgEventReconciler) IDColumn() string {
	return models.OrganizationTableColumns.ID
}

func (r *orgEventReconciler) LogValuesFromID(id string) []interface{} {
	return []interface{}{"org_id", id}
}

func (r *orgEventReconciler) LogValuesFromItem(_ *models.Organization) []interface{} {
	return []interface{}{}
}

func (r *orgEventReconciler) Reconcile(ctx context.Context, org *models.Organization) error {
	if !r.featSvc.GetFeatureStatusesWithOrg(ctx, org).GetNotification().Enabled() {
		r.log.Info("notifications disabled for org", "org_id", org.ID)
		return nil
	}
	db, txBeginner := database.WithTxBeginner(r.db)
	repoSet := client.NewRepoSet(db)
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()
	tx, err := txBeginner.Begin(ctx)
	if err != nil {
		return err
	}

	if err := r.reconcile(ctx, db, repoSet, org); err != nil {
		return err
	}

	metadata, err := org.GetOrgStatus()
	if err != nil {
		return err
	}
	metadata.LastEventProcessedAt = time.Now().Unix()
	if err := org.SetOrgStatus(*metadata); err != nil {
		return err
	}
	if err := repoSet.Organizations().Update(ctx, org, models.OrganizationColumns.OrgStatus); err != nil {
		return err
	}

	return tx.Commit()
}

func (r *orgEventReconciler) reconcile(ctx context.Context, db boil.ContextExecutor, repoSet client.RepoSet, org *models.Organization) error {
	expired, err := r.checkOrgLimits(ctx, db, repoSet, org)
	if err != nil {
		return err
	}
	if expired {
		// no need to process expired orgs
		return nil
	}

	if err := r.checkFreeTrials(ctx, repoSet, org); err != nil {
		return err
	}

	if err := r.checkProductVersions(ctx, db, repoSet, org); err != nil {
		return err
	}

	return nil
}

func (r *orgEventReconciler) checkFreeTrials(ctx context.Context, repoSet client.RepoSet, org *models.Organization) error {
	status, err := org.GetOrgStatus()
	if err != nil {
		return err
	}
	if !status.Trial {
		return nil
	}
	day := 24 * time.Hour
	expiryTime := time.Unix(status.ExpiryTime, 0)
	days := int(math.Floor(time.Until(expiryTime).Hours() / day.Hours()))
	if expirationDays[days] {
		metadata := models.EventMetadata{
			FreeTrialExpiring: &models.FreeTrialExpiring{
				Days: days,
			},
		}
		event := &models.Event{
			EventType:      null.StringFrom(models.EventTypeFreeTrialExpiring),
			OrganizationID: null.StringFrom(org.GetID()),
		}
		if err := event.SetMetadata(metadata); err != nil {
			return err
		}
		if err, _ := notifications.CreateEventWithDeDuplication(ctx, repoSet, event); err != nil {
			return err
		}
	}
	return nil
}

const (
	instanceVersionCheck = `
select
  %[2]s.version
from
  %[1]s
  join %[2]s on %[2]s.instance_id = %[1]s.id
where
  %[1]s.organization_owner = '%[3]s'
`
	agentVersionCheck = `
select
   count(*)
from
  %[1]s
  join  %[2]s on  %[2]s.instance_id = %[1]s.id
where
  %[1]s.organization_owner = '%[3]s' and  %[2]s.spec ->> 'targetVersion' is not NULL 
  and  %[2]s.spec ->> 'targetVersion' not like '%[4]s'; 
`
)

func (r *orgEventReconciler) checkProductVersions(ctx context.Context, db boil.ContextExecutor, repoSet client.RepoSet, org *models.Organization) error {
	// check argocd instance versions
	if err := r.checkInstanceVersions(ctx, db, repoSet, org, models.ProductArgoCD); err != nil {
		return err
	}
	// check kargo instance versions
	if err := r.checkInstanceVersions(ctx, db, repoSet, org, models.ProductKargo); err != nil {
		return err
	}
	// check agent version for argocd
	sent, err := r.checkAgentVersions(ctx, db, repoSet, org, models.ProductArgoCD)
	if err != nil {
		return err
	}
	if !sent {
		// check agent version for kargo only if agent version event was not triggered for argocd
		if _, err := r.checkAgentVersions(ctx, db, repoSet, org, models.ProductKargo); err != nil {
			return err
		}
	}
	return nil
}

func (r *orgEventReconciler) checkInstanceVersions(ctx context.Context, db boil.ContextExecutor, repoSet client.RepoSet, org *models.Organization, productType string) error {
	instanceTableName := ""
	instanceConfigTableName := ""
	latestVersion := latestProductVersionDetails{}
	switch productType {
	case models.ProductArgoCD:
		instanceTableName = "argo_cd_instance"
		instanceConfigTableName = "argo_cd_instance_config"
		latestVersion = r.argocdLatestVersion
	case models.ProductKargo:
		instanceTableName = "kargo_instance"
		instanceConfigTableName = "kargo_instance_config"
		latestVersion = r.kargoLatestVersion
	default:
		return fmt.Errorf("unknown instance product type %q", productType)

	}
	sqlRows, err := models.NewQuery(qm.SQL(fmt.Sprintf(instanceVersionCheck, instanceTableName, instanceConfigTableName, org.ID))).QueryContext(ctx, db)
	if err != nil {
		return err
	}
	defer func() { _ = sqlRows.Close() }()

	// if any instance is running old version need event
	needEvent := false
	for sqlRows.Next() {
		res := ""
		if err := sqlRows.Scan(&res); err != nil {
			return err
		}
		// ignore latest and rc versions and unstable versions
		if res == "" || res == "latest" || strings.HasPrefix(semver.Prerelease(res), "-rc") || strings.Contains(res, "unstable") {
			continue
		}
		isAk, akVersion := misc.GetAKPVersion(res)
		if isAk {
			// only compare oss version, semver cannot handle -ak.x in comparisons
			res = strings.ReplaceAll(res, fmt.Sprintf("-ak.%v", akVersion), "")
		}
		// NOTE: might need to check for ak version as well and emit event for new ak version
		// in that case use latestVersion.LatestAkVersion
		// currently only checks oss version of products
		if semver.Compare(res, latestVersion.Version) < 0 {
			needEvent = true
			break
		}
	}

	if needEvent {
		// need to close before querying again
		_ = sqlRows.Close()
		if err := sendProductVersionUpdate(ctx, r.log, repoSet, org.ID, latestVersion.Version, productType); err != nil {
			return err
		}
	}
	return nil
}

func (r *orgEventReconciler) checkAgentVersions(ctx context.Context, db boil.ContextExecutor, repoSet client.RepoSet, org *models.Organization, productType string) (bool, error) {
	instanceTableName := ""
	agentTableName := ""
	latestVersion := version.GetLatestAgentVersion()
	switch productType {
	case models.ProductArgoCD:
		instanceTableName = "argo_cd_instance"
		agentTableName = "argo_cd_cluster"
	case models.ProductKargo:
		instanceTableName = "kargo_instance"
		agentTableName = "kargo_agent"
	default:
		return false, fmt.Errorf("unknown instance product type %q", productType)

	}
	sqlRows, err := models.NewQuery(qm.SQL(fmt.Sprintf(agentVersionCheck, instanceTableName, agentTableName, org.ID, latestVersion))).QueryContext(ctx, db)
	if err != nil {
		return false, err
	}
	defer func() { _ = sqlRows.Close() }()

	// if any instance is running old version need event
	if !sqlRows.Next() {
		return false, fmt.Errorf("failed to get agent version")
	}
	count := 0
	if err := sqlRows.Scan(&count); err != nil {
		return false, err
	}

	if count > 0 {
		// need to close before querying again
		_ = sqlRows.Close()
		if err := sendProductVersionUpdate(ctx, r.log, repoSet, org.ID, latestVersion, models.ProductAgent); err != nil {
			return false, err
		}
		return true, nil
	}
	return false, nil
}

func (r *orgEventReconciler) checkOrgLimits(ctx context.Context, boil boil.ContextExecutor, repoSet client.RepoSet, org *models.Organization) (bool, error) {
	maxApps, currentUsage, expired, err := instances.GetApplicationLimits(ctx, r.gracePeriodDuration, boil, org)
	if err != nil {
		return false, err
	}
	// if expired short circuit
	if expired {
		return true, nil
	}

	if err = r.createUsageEvent(ctx, repoSet, org, models.ProductArgoCD, models.UsageTypeApps, maxApps, currentUsage); err != nil {
		return false, err
	}

	maxStages, currentStageCount, _, err := kargo.GetKargoLimits(ctx, r.gracePeriodDuration, boil, org)
	if err != nil {
		return false, err
	}

	if err = r.createUsageEvent(ctx, repoSet, org, models.ProductKargo, models.UsageTypeStages, maxStages, currentStageCount); err != nil {
		return false, err
	}
	return false, nil
}

func (r *orgEventReconciler) createUsageEvent(ctx context.Context, repoSet client.RepoSet, org *models.Organization, product, resourceType string, max, current int) error {
	// no need to check for max = 0 or < 0 as it either means infinite limit or expired org
	if max > 0 {
		usage := float64(current) / float64(max)
		for _, threshold := range usageAlertThresholds {
			if usage >= threshold {
				event := &models.Event{
					EventType:      null.StringFrom(models.EventTypeUsage),
					OrganizationID: null.StringFrom(org.ID),
				}
				metadata := models.EventMetadata{
					Usage: &models.Usage{
						Product:        product,
						UsageType:      resourceType,
						UsageThreshold: threshold,
						MaxLimit:       max,
						Usage:          current,
					},
				}
				if err := event.SetMetadata(metadata); err != nil {
					return err
				}
				if err, _ := notifications.CreateEventWithDeDuplication(ctx, repoSet, event,
					notifications.UsageEventExistsChecker(time.Now().Add(-1*usageAlertRetriggerThreshold))); err != nil {
					return err
				}
				break
			}
		}
	}
	return nil
}
