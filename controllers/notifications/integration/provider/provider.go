package provider

import (
	"context"
	"net/http"
	"time"

	"github.com/go-logr/logr"

	"github.com/akuityio/akuity-platform/controllers/notifications/integration/provider/mail"
	"github.com/akuityio/akuity-platform/controllers/notifications/integration/provider/webhook"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/notifications"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

type IProvider interface {
	SendNotification(ctx context.Context, reposet client.RepoSet, notification *models.Notification) error
}

func SetupProviders(logger *logr.Logger, cfg config.NotificationControllerConfig) (map[string]IProvider, error) {
	providers := make(map[string]IProvider)
	tmpls, err := notifications.LoadTemplates()
	if err != nil {
		return nil, err
	}
	// setup email notification provider
	if cfg.EmailProvider.Host != "" {
		providers[models.DeliveryMethodEmail] = mail.NewEmailProvider(logger, cfg, tmpls)
	}
	hc := &http.Client{
		Timeout: cfg.WebhookRequestTimeout,
	}
	providers[models.DeliveryMethodWebhook] = webhook.NewProvider(logger, hc, time.Now)

	return providers, nil
}
