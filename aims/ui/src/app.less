@font-size-base: 16px;

/* Write your global styles here, in PostCSS syntax */
@import 'antd/dist/reset.css';

// fix Tailwind CSS border styles,form Tailwind CSS's preflight
*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: theme('borderColor.DEFAULT'); /* 2 */
}

::before,
::after {
  --tw-content: '';
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: 'Satoshi-Variable';
  src: url('/fonts/Satoshi/Satoshi-Variable.woff2') format('woff2'),
    url('/fonts/Satoshi/Satoshi-Variable.woff') format('woff'),
    url('/fonts/Satoshi/Satoshi-Variable.ttf') format('truetype');
  font-weight: 300 900;
  font-display: swap;
  font-style: normal;
}

@font-face {
  font-family: 'Satoshi-VariableItalic';
  src: url('/fonts/<PERSON>shi/Satoshi-VariableItalic.woff2') format('woff2'),
    url('/fonts/Satoshi/Satoshi-VariableItalic.woff') format('woff'),
    url('/fonts/Satoshi/Satoshi-VariableItalic.ttf') format('truetype');
  font-weight: 300 900;
  font-display: swap;
  font-style: italic;
}

a {
  text-decoration: none;
  color: inherit;
}

html {
  font-size: 16px;
}

html,
body {
  height: 100%;
  min-height: 100%;
  padding: 0;
  margin: 0;
}

#root {
  width: 100%;
}

body {
  display: flex;
  flex-wrap: nowrap;
  --font: 'Satoshi-Variable';
  font-family: var(--font);

  --red: #cd0000;
  --grey: #aaaaaa;
  --argo-grey: #6d7f8b;
  --argo-grey-dark: #495763;
  --argo-grey-darker: #363c4a;
  --argo-grey-light: #dde6e9;
  --argo-grey-lighter: #f8fbfb;
  --argo-grey-background: #dde6e9;
  --akuity-grey: #444f5f;
  --light-red: #ffe4e4;
  --light-grey: #e2e2e2;
}

button {
  @apply rounded-full text-white font-bold py-1 px-3;
  background-color: var(--akuity-grey);
}

button:not(:disabled) {
  cursor: pointer;
}

// button:not(.ant-btn):hover {
//   background-color: var(--argo-grey-darker);
// }

label {
  @apply font-bold;
}

input,
textarea {
  @apply rounded-md border-gray-200 px-2 py-1 border box-border;
}

input:focus,
textarea:focus {
  outline: none;
  @apply border-akuity-200;
}

pre {
  overflow-x: scroll;
}

.bottom-divider {
  @apply border-b border-akuity-700 pb-2 mb-2;
}
.bottom-divider:last-child {
  @apply border-none pb-0 mb-0;
}

a:hover {
  color: #1677ff;
}

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

h1 {
  @apply text-3xl font-extrabold;
}

menu, ol, ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.link {
  @apply text-blue-500 cursor-pointer;
}