import { faIdCard, faMessage } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import JsonView from '@uiw/react-json-view';
import { Modal, Descriptions } from 'antd';
import moment from 'moment';

import { AuditLog } from '@/lib/apiclient/aims/v1/aims_pb';
import { isValidJSON } from '@/utils';

import { FILTER_META } from './use-audit-logs-filters';

interface AuditLogsDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  auditLog: AuditLog | null;
}

const FIELDS = [
  {
    key: 'timestamp',
    label: 'Timestamp',
    icon: FILTER_META.startTime.icon,
    getValue: (log: AuditLog) => moment(log.timestamp).format('YYYY-MM-DD HH:mm:ss')
  },
  {
    key: 'action',
    label: FILTER_META.action.label,
    icon: FILTER_META.action.icon,
    getValue: (log: AuditLog) => log.action
  },
  {
    key: 'actorId',
    label: FILTER_META.actorId.label,
    icon: FILTER_META.actorId.icon,
    getValue: (log: AuditLog) => log.actor?.id
  },
  {
    key: 'objectType',
    label: FILTER_META.objectType.label,
    icon: FILTER_META.objectType.icon,
    getValue: (log: AuditLog) => log.object?.type
  },
  {
    key: 'resourceId',
    label: 'Resource ID',
    icon: faIdCard,
    getValue: (log: AuditLog) => log.object?.id
  },
  {
    key: 'message',
    label: 'Message',
    icon: faMessage,
    getValue: (log: AuditLog) => log.details?.message
  }
];

export const AuditLogsDetailsModal = ({
  isOpen,
  onClose,
  auditLog
}: AuditLogsDetailsModalProps) => {
  if (!auditLog) return null;

  const patch = auditLog.details?.patch;

  return (
    <Modal title='Audit Log Details' open={isOpen} onCancel={onClose} footer={null} width={800}>
      <Descriptions bordered column={1}>
        {FIELDS.map(({ key, label, icon, getValue }) => (
          <Descriptions.Item key={key} label={label}>
            <span className='flex items-center gap-2'>
              <FontAwesomeIcon icon={icon} />
              {getValue(auditLog)}
            </span>
          </Descriptions.Item>
        ))}

        <Descriptions.Item label='Changes'>
          {patch && isValidJSON(patch) ? <JsonView value={JSON.parse(patch)} /> : patch}
        </Descriptions.Item>
      </Descriptions>
    </Modal>
  );
};
