import { Select } from 'antd';
import { useState } from 'react';

import { Filter } from '@/lib/components/shared/filter';

import { useInternalAuditLogsFilters } from '../use-audit-logs-filters';

export const ActorFilter = ({ close }: { close?: () => void }) => {
  const { filters, setFilters, removeFilters } = useInternalAuditLogsFilters();
  const [actors, setActors] = useState<string[]>(filters.actorId || []);

  const onClear = () => {
    removeFilters(['actorId']);
    setActors([]);
    close?.();
  };

  const onApply = () => {
    setFilters({ ...filters, actorId: actors });
    close?.();
  };

  return (
    <Filter onApplyFilter={onApply} onClearFilter={onClear} close={close}>
      <Select
        mode='tags'
        style={{ width: '100%' }}
        placeholder='Type actor and press enter'
        value={actors}
        onChange={setActors}
        onBlur={() => {}}
        onKeyDown={(e) => {
          if (e.key === 'Enter') {
            e.preventDefault();
          }
        }}
      />
    </Filter>
  );
};
