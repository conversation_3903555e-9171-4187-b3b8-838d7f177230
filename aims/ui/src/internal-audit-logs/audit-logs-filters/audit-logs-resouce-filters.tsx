import { Checkbox } from 'antd';
import { useForm } from 'react-hook-form';

import { Filter } from '@/lib/components/shared/filter';
import { FieldContainer } from '@/lib/components/shared/forms';

import { objectTypeOptions, useInternalAuditLogsFilters } from '../use-audit-logs-filters';

export const ResourceTypeFilter = ({ close }: { close?: () => void }) => {
  const { filters, setFilters, removeFilters } = useInternalAuditLogsFilters();

  const { control, handleSubmit } = useForm({
    values: filters
  });

  const onClear = () => {
    removeFilters(['objectType']);
    close?.();
  };

  const onApply = handleSubmit((filters) => {
    setFilters(filters);
    close?.();
  });

  return (
    <Filter onApplyFilter={onApply} onClearFilter={onClear} close={close}>
      <FieldContainer
        control={control}
        name='objectType'
        defaultValue={filters.objectType || []}
        label='Resource Type'
      >
        {({ field }) => (
          <Checkbox.Group
            value={field.value || []}
            onChange={field.onChange}
            options={objectTypeOptions}
          />
        )}
      </FieldContainer>
    </Filter>
  );
};
