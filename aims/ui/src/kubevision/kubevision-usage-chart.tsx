import { Timestamp } from '@bufbuild/protobuf';
import {
  BarElement,
  CategoryScale,
  Chart as ChartJS,
  ChartData,
  ChartOptions,
  Legend,
  LinearScale,
  Title,
  Tooltip
} from 'chart.js';
import moment from 'moment';
import { useMemo } from 'react';
import { Bar } from 'react-chartjs-2';

import { AIUsage, KubeVisionUsage } from '@/lib/apiclient/organization/v1/organization_pb';
import { capitalize } from '@/utils';

import { useChartHover } from './chart-hover-context';

interface KubevisionUsageChartProps {
  data: KubeVisionUsage[];
  metric: keyof Omit<KubeVisionUsage, 'timestamp'>;
  startTime: number;
  endTime: number;
}

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

export const KubevisionUsageChart = ({
  data,
  metric,
  startTime,
  endTime
}: KubevisionUsageChartProps) => {
  const { hoveredDate, setHoveredDate } = useChartHover();

  const groupDataByWeek = (data: KubeVisionUsage[], startTime: number, endTime: number) => {
    const sortedData = [...data].sort(
      (a, b) => moment(a.timestamp?.toDate()).valueOf() - moment(b.timestamp?.toDate()).valueOf()
    );

    const startDate = moment(startTime).startOf('isoWeek');
    const endDate = moment(endTime).endOf('isoWeek');

    const weeklyDataMap = new Map<string, KubeVisionUsage[]>();

    // Initialize all weeks
    const currentWeek = startDate.clone();
    while (currentWeek.isSameOrBefore(endDate)) {
      const weekKey = currentWeek.format('YYYY-[W]WW');
      weeklyDataMap.set(weekKey, []);
      currentWeek.add(1, 'week');
    }

    // Group data into weeks if needed
    for (const entry of sortedData) {
      const entryDate = moment(entry.timestamp?.toDate());
      const weekKey = entryDate.format('YYYY-[W]WW');

      if (weeklyDataMap.has(weekKey)) {
        weeklyDataMap.get(weekKey)?.push(entry);
      }
    }

    const weeklyAverages: KubeVisionUsage[] = [];
    weeklyDataMap.forEach((weekData, weekKey) => {
      if (weekData.length === 0) {
        // If no data for the week, create empty entry
        weeklyAverages.push(
          new KubeVisionUsage({
            timestamp: Timestamp.fromDate(moment(weekKey, 'YYYY-[W]WW').toDate()),
            instanceCount: 0,
            clusterCount: 0,
            apiResourceCount: 0,
            objectCount: 0,
            nodeCount: 0,
            podCount: 0,
            containerCount: 0,
            aiUsage: {
              cost: 0.0,
              inputTokens: 0,
              cachedInputTokens: 0,
              outputTokens: 0
            }
          })
        );
      } else {
        const avgEntry = new KubeVisionUsage({
          timestamp: Timestamp.fromDate(moment(weekKey, 'YYYY-[W]WW').toDate()),
          instanceCount: Math.round(
            weekData.reduce((sum, d) => sum + d.instanceCount, 0) / weekData.length
          ),
          clusterCount: Math.round(
            weekData.reduce((sum, d) => sum + d.clusterCount, 0) / weekData.length
          ),
          apiResourceCount: Math.round(
            weekData.reduce((sum, d) => sum + d.apiResourceCount, 0) / weekData.length
          ),
          objectCount: Math.round(
            weekData.reduce((sum, d) => sum + d.objectCount, 0) / weekData.length
          ),
          nodeCount: Math.round(
            weekData.reduce((sum, d) => sum + d.nodeCount, 0) / weekData.length
          ),
          podCount: Math.round(weekData.reduce((sum, d) => sum + d.podCount, 0) / weekData.length),
          containerCount: Math.round(
            weekData.reduce((sum, d) => sum + d.containerCount, 0) / weekData.length
          ),
          aiUsage: new AIUsage({
            cost: weekData.reduce((sum, d) => sum + d.aiUsage?.cost, 0.0) / weekData.length,
            inputTokens: Math.round(
              weekData.reduce((sum, d) => sum + d.aiUsage?.inputTokens, 0) / weekData.length
            ),
            cachedInputTokens: Math.round(
              weekData.reduce((sum, d) => sum + d.aiUsage?.cachedInputTokens, 0) / weekData.length
            ),
            outputTokens: Math.round(
              weekData.reduce((sum, d) => sum + d.aiUsage?.outputTokens, 0) / weekData.length
            )
          })
        });
        weeklyAverages.push(avgEntry);
      }
    });

    return weeklyAverages;
  };

  const chartData = useMemo((): ChartData<'bar'> => {
    const timeRangeInDays = moment(endTime).diff(moment(startTime), 'days');

    // Use weekly grouping for ranges >= 3 months
    const shouldGroupByWeek = timeRangeInDays >= 89;

    let processedData: KubeVisionUsage[];
    let dateFormat: string;

    if (shouldGroupByWeek) {
      processedData = groupDataByWeek(data, startTime, endTime);
      dateFormat = 'YYYY-[W]WW';
    } else {
      const sortedData = [...data].sort(
        (a, b) => moment(a.timestamp?.toDate()).valueOf() - moment(b.timestamp?.toDate()).valueOf()
      );

      const startDate = moment(startTime);
      const endDate = moment(endTime);

      const dataMap = new Map(
        sortedData.map((d) => [moment(d.timestamp?.toDate()).format('YYYY-MM-DD'), d])
      );

      const allDates: KubeVisionUsage[] = [];
      const currentDate = startDate.clone();

      while (currentDate.isSameOrBefore(endDate)) {
        const dateStr = currentDate.format('YYYY-MM-DD');
        const existingData = dataMap.get(dateStr);

        allDates.push(
          existingData ||
            new KubeVisionUsage({
              timestamp: Timestamp.fromDate(currentDate.toDate()),
              instanceCount: 0,
              clusterCount: 0,
              apiResourceCount: 0,
              objectCount: 0,
              nodeCount: 0,
              podCount: 0,
              containerCount: 0,
              aiUsage: new AIUsage({
                cost: 0,
                inputTokens: 0,
                cachedInputTokens: 0,
                outputTokens: 0
              })
            })
        );

        currentDate.add(1, 'day');
      }

      processedData = allDates;
      dateFormat = 'YYYY-MM-DD';
    }

    return {
      labels: processedData.map((d) => moment(d.timestamp?.toDate()).format(dateFormat)),
      datasets: [
        {
          label: metric
            .replace(/([A-Z])/g, ' $1')
            .replace('Count', '')
            .trim()
            .toUpperCase(),
          data: processedData.map((d) => {
            if (metric === 'aiUsage') {
              return d.aiUsage?.cost || 0;
            }
            return d[metric] as number;
          }),
          backgroundColor: (context) => {
            const label = chartData.labels[context.dataIndex];
            return label === hoveredDate ? 'rgb(199, 241, 255)' : 'rgb(147, 203, 255)';
          },
          hoverBackgroundColor: 'rgb(199, 241, 255)'
        }
      ]
    };
  }, [data, metric, startTime, endTime, hoveredDate]);

  const options: ChartOptions<'bar'> = {
    responsive: true,
    maintainAspectRatio: false,
    onHover: (event, elements) => {
      if (elements && elements.length > 0) {
        const dataIndex = elements[0].index;
        const date = chartData.labels[dataIndex];
        setHoveredDate(date as string);
      } else {
        setHoveredDate(null);
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          precision: 0
        },
        afterDataLimits: (scale) => {
          const maxValue = Math.max(...(scale.chart.data.datasets[0].data as number[]));
          scale.max = maxValue + maxValue * 0.05; // Add 5% padding on the top area
        }
      },
      x: {
        grid: {
          display: false
        },
        ticks: {
          maxTicksLimit: 5,
          autoSkip: true
        }
      }
    },
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        callbacks: {
          afterTitle: (tooltipItems) => {
            const timeRangeInDays = moment(endTime).diff(moment(startTime), 'days');
            const shouldGroupByWeek = timeRangeInDays >= 89;

            if (shouldGroupByWeek) {
              const date = moment(tooltipItems[0].label, 'YYYY-[W]WW');
              const startOfWeek = date.startOf('isoWeek').format('YYYY-MM-DD');
              const endOfWeek = date.endOf('isoWeek').format('YYYY-MM-DD');
              return `(${startOfWeek} - ${endOfWeek})`;
            }
            return '';
          },
          label: (context) => {
            return `${capitalize(context.dataset.label)}: ${context.parsed.y}`;
          }
        }
      }
    }
  };

  return (
    <div style={{ height: '280px', width: '100%' }}>
      <Bar data={chartData} options={options} />
    </div>
  );
};
