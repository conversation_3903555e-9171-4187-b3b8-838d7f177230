import { Timestamp } from '@bufbuild/protobuf';
import { Flex, Row, Select } from 'antd';
import generatePicker from 'antd/es/date-picker/generatePicker';
import moment, { Moment } from 'moment';
import momentGenerateConfig from 'rc-picker/lib/generate/moment';
import { useState } from 'react';

import { useGetKubeVisionUsage } from '@/hook/api';
import {
  AIUsage,
  GetKubeVisionUsageResponse,
  KubeVisionUsage
} from '@/lib/apiclient/organization/v1/organization_pb';
import { Loading } from '@/lib/components/loading';

import { ChartHoverProvider } from './chart-hover-context';
import { KubevisionUsageChart } from './kubevision-usage-chart';

type Props = {
  orgId: string;
};

type KubeVisonUsageData = {
  timestamp: string;
  instanceCount: number;
  clusterCount: number;
  apiResourceCount: number;
  objectCount: number;
  nodeCount: number;
  podCount: number;
  containerCount: number;
  aiUsage?: AIUsage;
};

const DatePicker = generatePicker<Moment>(momentGenerateConfig);

export const KubeVisionUsageContainer = ({ orgId }: Props) => {
  const [timeRange, setTimeRange] = useState(() => {
    const now = moment();
    return {
      startTime: now.clone().subtract(7, 'days').valueOf(),
      endTime: now.valueOf()
    };
  });

  const [selectedTimeRange, setSelectedTimeRange] = useState<string>('7d');
  const { data, isFetching } = useGetKubeVisionUsage(
    orgId,
    timeRange.startTime ? new Date(timeRange.startTime).toISOString() : undefined,
    timeRange.endTime ? new Date(timeRange.endTime).toISOString() : undefined,
    {
      select: (data) =>
        ({
          ...data,
          usage: data.usage?.map((item) => ({
            ...item,
            timestamp:
              item.timestamp instanceof Timestamp
                ? item.timestamp
                : Timestamp.fromDate(new Date(item.timestamp))
          }))
        }) as GetKubeVisionUsageResponse,
      staleTime: 3000
    }
  );

  const handleFilterChange = (newTimeRange: Partial<typeof timeRange>) => {
    setTimeRange((prev) => ({
      ...prev,
      ...newTimeRange
    }));
  };

  const metrics: Array<keyof Omit<KubeVisionUsage, 'timestamp'>> = [
    'instanceCount',
    'clusterCount',
    'apiResourceCount',
    'objectCount',
    'nodeCount',
    'podCount',
    'containerCount',
    'aiUsage'
  ] as const;

  const onTimeRangeSelect = (key: string) => {
    const now = moment();
    let startTime: number;
    const endTime = now.valueOf();

    switch (key) {
      case '1d':
        startTime = now.clone().subtract(1, 'days').valueOf();
        break;
      case '7d':
        startTime = now.clone().subtract(7, 'days').valueOf();
        break;
      case '14d':
        startTime = now.clone().subtract(14, 'days').valueOf();
        break;
      case '1m':
        startTime = now.clone().subtract(1, 'month').valueOf();
        break;
      case '3m':
        startTime = now.clone().subtract(3, 'months').valueOf();
        break;
      case '6m':
        startTime = now.clone().subtract(6, 'months').valueOf();
        break;
      case '1y':
        startTime = now.clone().subtract(1, 'year').valueOf();
        break;
      default:
        return;
    }

    handleFilterChange({
      startTime,
      endTime
    });
  };

  const SingleDayMetrics = ({ data }: { data: KubeVisonUsageData[] | undefined }) => {
    if (!data) return null;
    const convertedData = data.map((item) => ({
      ...item,
      timestamp: item.timestamp.toString()
    }));
    const latestData = convertedData[convertedData.length - 1] || {
      instanceCount: 0,
      clusterCount: 0,
      apiResourceCount: 0,
      objectCount: 0,
      nodeCount: 0,
      podCount: 0,
      containerCount: 0,
      aiUsage: new AIUsage({
        cost: 0,
        inputTokens: 0,
        cachedInputTokens: 0,
        outputTokens: 0
      })
    };

    const MetricCard = ({ label, value }: { label: string; value: number }) => (
      <Flex
        className='px-4 py-8 border rounded-lg shadow-md bg-white'
        vertical
        justify='center'
        align='center'
      >
        <div className='text-gray-600 text-sm font-semibold mb-2'>{label}</div>
        <div className='text-3xl font-bold text-[#93cbff]'>{value.toLocaleString()}</div>
      </Flex>
    );

    return (
      <div className='grid grid-cols-4 gap-4'>
        <MetricCard label='Instances' value={latestData.instanceCount} />
        <MetricCard label='Clusters' value={latestData.clusterCount} />
        <MetricCard label='API Resources' value={latestData.apiResourceCount} />
        <MetricCard label='Objects' value={latestData.objectCount} />
        <MetricCard label='Nodes' value={latestData.nodeCount} />
        <MetricCard label='Pods' value={latestData.podCount} />
        <MetricCard label='Containers' value={latestData.containerCount} />
        <MetricCard label='AI Cost' value={latestData.aiUsage.cost} />
      </div>
    );
  };

  return isFetching ? (
    <Flex justify={'center'} className='w-full mt-20' style={{ color: '#44505f' }}>
      <Loading />
    </Flex>
  ) : (
    <>
      <Row
        justify='space-between'
        align='middle'
        className='w-auto mb-3 p-4 rounded-lg bg-[#fafafa]'
      >
        <Flex justify='start' align='center'>
          <strong className='mr-2 h-fit text-center'>Time Range:</strong>

          <Select
            popupMatchSelectWidth={false}
            value={selectedTimeRange}
            style={{ width: 140 }}
            options={[
              { value: '1d', label: 'Past 1 day' },
              { value: '7d', label: 'Past 1 week' },
              { value: '14d', label: 'Past 2 weeks' },
              { value: '1m', label: 'Past 1 month' },
              { value: '3m', label: 'Past 3 months' },
              { value: '6m', label: 'Past 6 months' },
              { value: '1y', label: 'Past 1 year' },
              { value: 'custom', label: 'Custom' }
            ]}
            onChange={(value) => {
              setSelectedTimeRange(value);
              onTimeRangeSelect(value);
            }}
          />

          {selectedTimeRange === 'custom' && (
            <DatePicker.RangePicker
              showTime
              className='ml-2'
              value={[moment(timeRange.startTime), moment(timeRange.endTime)]}
              onChange={(dates) => {
                if (dates?.[0] && dates?.[1]) {
                  setTimeRange({
                    startTime: dates[0].valueOf(),
                    endTime: dates[1].valueOf()
                  });
                }
              }}
            />
          )}
        </Flex>

        <Flex justify='center' align='center'>
          <Flex className='items-center mr-5' justify='start'>
            <div className='ml-4 text-sm text-gray-500'>
              <strong>From: </strong>
              {moment(timeRange.startTime).format('YYYY-MM-DD')}
              <span className='mr-1'></span>
              <strong>To: </strong>
              {timeRange.endTime ? moment(timeRange.endTime).format('YYYY-MM-DD') : 'N/A'}
            </div>
          </Flex>
        </Flex>
      </Row>

      {selectedTimeRange === '1d' ? (
        <SingleDayMetrics
          data={data?.usage?.map((item) => ({
            ...item,
            timestamp: item.timestamp.toString()
          }))}
        />
      ) : (
        <ChartHoverProvider>
          <div className='grid gap-4 mt-4 grid-cols-2'>
            {metrics.map((metric) => (
              <div key={metric} className='p-4 border rounded-lg shadow-md'>
                <div className='text-gray-600 text-sm font-semibold mb-4 text-center'>
                  {metric === 'instanceCount' && 'Instances'}
                  {metric === 'clusterCount' && 'Clusters'}
                  {metric === 'apiResourceCount' && 'API Resources'}
                  {metric === 'objectCount' && 'Objects'}
                  {metric === 'nodeCount' && 'Nodes'}
                  {metric === 'podCount' && 'Pods'}
                  {metric === 'containerCount' && 'Containers'}
                  {metric === 'aiUsage' && 'AI Usage'}
                </div>
                <KubevisionUsageChart
                  data={(data?.usage as KubeVisionUsage[]) || []}
                  metric={metric}
                  startTime={timeRange.startTime}
                  endTime={timeRange.endTime}
                />
              </div>
            ))}
          </div>
        </ChartHoverProvider>
      )}
    </>
  );
};
