import { createContext, useContext, useState } from 'react';

type ChartHoverContextType = {
  hoveredDate: string | null;
  setHoveredDate: (date: string | null) => void;
};

const ChartHoverContext = createContext<ChartHoverContextType>({
  hoveredDate: null,
  setHoveredDate: () => {}
});

export const ChartHoverProvider = ({ children }: { children: React.ReactNode }) => {
  const [hoveredDate, setHoveredDate] = useState<string | null>(null);

  return (
    <ChartHoverContext.Provider value={{ hoveredDate, setHoveredDate }}>
      {children}
    </ChartHoverContext.Provider>
  );
};

export const useChartHover = () => useContext(ChartHoverContext);
