import { faDownload } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Modal, Alert, notification } from 'antd';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { ModalComponentProps } from '@/context/modal-context';
import { AuditForm } from '@/lib/components/shared/audit-form';
import { zodAuditSchema } from '@/utils';

type DownloadManifestModalProps = ModalComponentProps & {
  instanceId: string;
  clusterId: string;
  clusterName: string;
  type: 'argocd' | 'kargo';
};

const schema = z.object({
  audit: zodAuditSchema
});

export const DownloadManifestModal = ({
  hide,
  visible,
  instanceId,
  clusterId,
  clusterName,
  type
}: DownloadManifestModalProps) => {
  const form = useForm({
    defaultValues: {
      audit: {
        actor: '',
        reason: ''
      }
    },
    resolver: zodResolver(schema)
  });

  const onSubmit = form.handleSubmit(async (data) => {
    try {
      const auditParams = new URLSearchParams({
        'audit.actor': data.audit.actor,
        'audit.reason': data.audit.reason
      });

      const endpoint =
        type === 'kargo'
          ? `/api/v1/aims/kargo/instances/${instanceId}/agents/${clusterId}/manifests?${auditParams}`
          : `/api/v1/aims/instances/${instanceId}/clusters/${clusterId}/manifests?${auditParams}`;

      const link = document.createElement('a');
      link.href = endpoint;
      link.download = `akuity-manifests-${clusterName}.yaml`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      hide();
    } catch {
      notification.error({
        message: 'Error',
        description: 'Failed to download manifest'
      });
    }
  });

  return (
    <Modal
      open={visible}
      onCancel={hide}
      title={`Download ${type === 'kargo' ? 'Kargo Agent' : 'ArgoCD Cluster'} Manifest`}
      footer={[
        <Button key='cancel' onClick={hide}>
          Cancel
        </Button>,
        <Button
          key='download'
          type='primary'
          icon={<FontAwesomeIcon icon={faDownload} />}
          onClick={onSubmit}
        >
          Download Manifest
        </Button>
      ]}
    >
      <div className='w-full py-3'>
        <Alert
          type='info'
          showIcon
          message='Manifest Download'
          description={`You are about to download the agent manifest for ${type === 'kargo' ? 'Kargo agent' : 'ArgoCD cluster'} "${clusterName}". This action will be logged for audit purposes.`}
          className='mb-4'
        />

        <div className='bg-slate-50 rounded-md p-5'>
          <AuditForm control={form.control} />
        </div>
      </div>
    </Modal>
  );
};
