import { TenantPhase } from '@/lib/apiclient/types/status/health/v1/health_pb';
import { RawAuditFilters } from '@/types';

export interface ClusterFilter {
  nameLike?: string;
  agentStatus?: TenantPhase[];
  agentVersion?: string[];
  argocdVersion?: string[];
  /** @format int64 */
  limit?: string;
  /** @format int64 */
  offset?: string;
  excludeAgentVersion?: string;
  outdatedManifest?: boolean;
  namespace?: string[];
  namespaceScoped?: boolean;
  labels?: Record<string, string>;
  needReapply?: boolean;
  excludeDirectCluster?: boolean;
}

export const defaultClusterFilters: ClusterFilter = {
  limit: '10',
  offset: '0',
  agentVersion: [],
  argocdVersion: [],
  nameLike: '',
  agentStatus: [],
  excludeAgentVersion: ''
};

export const defaultAuditFilters: RawAuditFilters = {
  limit: 50,
  offset: 0,
  actor_id: [],
  action: [],
  start_time: null,
  end_time: null,
  actor_type: []
};
