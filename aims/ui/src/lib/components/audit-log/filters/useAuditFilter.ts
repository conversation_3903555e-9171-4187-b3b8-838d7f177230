import { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { faClock } from '@fortawesome/free-solid-svg-icons/faClock';
import { faCogs } from '@fortawesome/free-solid-svg-icons/faCogs';
import { faUser } from '@fortawesome/free-solid-svg-icons/faUser';
import { URLSearchParamsInit } from 'react-router-dom';

import type { AuditFilterAction, RawAuditFilters, TFilterScopes } from '@/types';

import { useActionFilter } from './action';
import { useActorFilter } from './actor';
import { useObjectFilter } from './object';
import { auditObjectMapper, toObjectType } from './object/utils';
import { useTimeFilter } from './time';
import { auditFilterKeys } from './utils';

export const useAuditFilter = (
  activeFilters: RawAuditFilters,
  search?: URLSearchParams,
  setSearch?: (
    nextInit: URLSearchParamsInit,
    navigateOptions?: {
      replace?: boolean;
    }
  ) => void,
  filterScopes?: TFilterScopes
) => {
  const clearSearchItems = (items: Array<string>) => {
    if (!setSearch) return;
    const newSearch = new URLSearchParams(search);

    for (const item of items) {
      newSearch.delete(item);
    }
    setSearch(newSearch);
  };

  const timeFilter = useTimeFilter({
    onClearFilter() {
      clearSearchItems([auditFilterKeys.startTime, auditFilterKeys.endTime]);
    },
    initialValues: {
      startTime: activeFilters?.start_time ? new Date(activeFilters.start_time) : null,
      endTime: activeFilters?.end_time ? new Date(activeFilters.end_time) : null
    }
  });

  const actionFilter = useActionFilter({
    onClearFilter() {
      clearSearchItems([auditFilterKeys.action]);
    },
    initialValue: activeFilters?.action as AuditFilterAction[]
  });

  const actorFilter = useActorFilter({
    onClearFilter() {
      clearSearchItems([auditFilterKeys.actorId, auditFilterKeys.actorType]);
    },
    initialValue: activeFilters?.actor_id
  });

  const objectFilter = useObjectFilter({
    onClearFilter() {
      clearSearchItems([
        `${auditFilterKeys.k8sResource}.enabled`,
        `${auditFilterKeys.argocdApplication}.enabled`,
        `${auditFilterKeys.argocdCluster}.enabled`,
        `${auditFilterKeys.argocdInstance}.enabled`,
        `${auditFilterKeys.argocdProject}.enabled`,
        `${auditFilterKeys.member}.enabled`,
        `${auditFilterKeys.organizationInvite}.enabled`,
        `${auditFilterKeys.kargoInstance}.enabled`,
        `${auditFilterKeys.kargoAgent}.enabled`,
        `${auditFilterKeys.kargoPromotion}.enabled`,
        `${auditFilterKeys.kargoFreight}.enabled`,
        `${auditFilterKeys.customRoles}.enabled`,
        `${auditFilterKeys.notificationCfg}.enabled`,
        `${auditFilterKeys.apiKeys}.enabled`,
        `${auditFilterKeys.addons}.enabled`,
        `${auditFilterKeys.addonRepos}.enabled`,
        `${auditFilterKeys.addonMarketplaceInstall}.enabled`
      ]);
    },
    initialValue: toObjectType(activeFilters)
  });

  const appliedFilters: Array<{
    key: string;
    value: string[];
    icon?: IconDefinition;
    onClear: () => void;
  }> = [];

  if (activeFilters.start_time && activeFilters.end_time) {
    appliedFilters.push({
      key: 'timerange',
      value: [`${activeFilters.start_time} - ${activeFilters.end_time}`],
      icon: faClock,
      onClear: () => {
        timeFilter.onClearFilter();
      }
    });
  }

  if (activeFilters.action.length) {
    appliedFilters.push({
      key: 'action',
      value: activeFilters.action,
      icon: faCogs,
      onClear: () => {
        actionFilter.onClearFilter();
      }
    });
  }

  if (activeFilters.actor_id.length) {
    appliedFilters.push({
      key: 'actor',
      value: activeFilters.actor_id,
      icon: faUser,
      onClear: () => {
        actorFilter.onClearFilter();
      }
    });
  }

  let resource: keyof Pick<
    RawAuditFilters,
    | 'k8s_resource'
    | 'argocd_application'
    | 'argocd_cluster'
    | 'argocd_instance'
    | 'argocd_project'
    | 'member'
    | 'organization_invite'
    | 'kargo_instance'
    | 'kargo_agent'
    | 'kargo_promotion'
    | 'kargo_freight'
    | 'custom_roles'
    | 'notification_cfg'
    | 'api_keys'
    | 'addons'
    | 'addon_repos'
    | 'addon_marketplace_install'
  > = 'k8s_resource';
  let resourceFilter = false;
  if (activeFilters.k8s_resource?.enabled) {
    resourceFilter = true;
    resource = 'k8s_resource';
  } else if (activeFilters.argocd_application?.enabled) {
    resourceFilter = true;
    resource = 'argocd_application';
  } else if (activeFilters.argocd_cluster?.enabled) {
    resourceFilter = true;
    resource = 'argocd_cluster';
  } else if (activeFilters.argocd_instance?.enabled && filterScopes !== 'instance') {
    resourceFilter = true;
    resource = 'argocd_instance';
  } else if (activeFilters.argocd_project?.enabled) {
    resourceFilter = true;
    resource = 'argocd_project';
  } else if (activeFilters.member?.enabled) {
    resourceFilter = true;
    resource = 'member';
  } else if (activeFilters.organization_invite?.enabled) {
    resourceFilter = true;
    resource = 'organization_invite';
  } else if (activeFilters.kargo_instance?.enabled && filterScopes !== 'kargo_instance') {
    resourceFilter = true;
    resource = 'kargo_instance';
  } else if (activeFilters.kargo_agent?.enabled) {
    resourceFilter = true;
    resource = 'kargo_agent';
  } else if (activeFilters.kargo_promotion?.enabled) {
    resourceFilter = true;
    resource = 'kargo_promotion';
  } else if (activeFilters.kargo_freight?.enabled) {
    resourceFilter = true;
    resource = 'kargo_freight';
  } else if (activeFilters?.custom_roles?.enabled) {
    resourceFilter = true;
    resource = 'custom_roles';
  } else if (activeFilters?.notification_cfg?.enabled) {
    resourceFilter = true;
    resource = 'notification_cfg';
  } else if (activeFilters?.api_keys?.enabled) {
    resourceFilter = true;
    resource = 'api_keys';
  } else if (activeFilters?.addons?.enabled) {
    resourceFilter = true;
    resource = 'addons';
  } else if (activeFilters?.addon_repos?.enabled) {
    resourceFilter = true;
    resource = 'addon_repos';
  } else if (activeFilters?.addon_marketplace_install?.enabled) {
    resourceFilter = true;
    resource = 'addon_marketplace_install';
  }

  if (resourceFilter) {
    const tokens = [];
    if (resource) {
      tokens.push(`type ${auditObjectMapper[resource]}`);
    }

    if (activeFilters[resource]?.objectName?.length) {
      tokens.push(`name ${activeFilters[resource].objectName[0]}`);
    }

    if (
      activeFilters[resource]?.objectParentName?.length &&
      // because when audit is filter scoped, object parent for these values are fixed
      (resource === 'argocd_cluster' ||
        resource === 'argocd_application' ||
        resource === 'argocd_project') &&
      filterScopes !== 'instance'
    ) {
      tokens.push(`parent ${activeFilters[resource].objectParentName[0]}`);
    }

    if (activeFilters[resource]?.objectGroup?.length) {
      tokens.push(`group ${activeFilters[resource].objectGroup[0]}`);
    }

    if (activeFilters[resource]?.objectKind?.length) {
      tokens.push(`kind ${activeFilters[resource].objectKind[0]}`);
    }

    appliedFilters.push({
      key: 'object',
      value: [tokens.join(' AND ')],
      onClear: () => {
        objectFilter.onClearFilter();
      }
    });
  }

  return {
    timeFilter,
    actionFilter,
    actorFilter,
    objectFilter,
    appliedFilters: appliedFilters
  };
};
