import { faPlus, faTimesCircle } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { AutoComplete, Button, Flex } from 'antd';
import { useEffect, useState } from 'react';

import { Filter, FilterProps } from '@/lib/components/shared/filter';
import type { AuditFilterAction } from '@/types';

export type ActionFilterProps = Pick<FilterProps, 'onApplyFilter'> &
  ReturnType<typeof useActionFilter> & { availableActions: string[] };

export const useActionFilter = ({
  onClearFilter,
  initialValue
}: {
  onClearFilter: () => void;
  initialValue?: AuditFilterAction[];
}) => {
  const [actions, setActions] = useState<Array<string>>(initialValue || []);

  useEffect(() => {
    setActions(initialValue || []);
  }, [initialValue]);

  const onPushAction = (a: string) => {
    const action = a.toLowerCase();
    if (actions.find((_action) => _action === action)) {
      setActions(actions.filter((_action) => _action !== action));
      return;
    }

    setActions([...actions, action]);
  };

  const onRemoveAction = (a: string) => {
    const action = a.toLowerCase();
    setActions(actions.filter((_action) => _action !== action));
  };

  const _onClearFilter = () => {
    setActions([]);
    onClearFilter();
  };

  return {
    actions,
    onPushAction,
    onClearFilter: _onClearFilter,
    onRemoveAction,
    raw: {
      action: actions
    }
  };
};

export const ActionFilter = ({
  onApplyFilter,
  actions,
  onClearFilter,
  onPushAction,
  onRemoveAction,
  availableActions
}: ActionFilterProps) => {
  const [input, setInput] = useState('');
  return (
    <Filter onApplyFilter={onApplyFilter} onClearFilter={onClearFilter}>
      <Flex className='items-center mb-2'>
        <AutoComplete
          placeholder='Action'
          onChange={(val) => setInput(val)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              onPushAction(input);
              setInput('');
            }
          }}
          onSelect={(val) => {
            onPushAction(val);
            setInput('');
          }}
          value={input}
          className='mr-2 w-full'
          options={availableActions.map((action) => ({ value: action }))}
        />
        <Button
          type='primary'
          onClick={() => {
            onPushAction(input);
            setInput('');
          }}
        >
          <FontAwesomeIcon icon={faPlus} />
        </Button>
      </Flex>
      {actions.map((action) => (
        <Flex
          className='items-center w-full p-2 mb-1 rounded bg-gray-100 font-semibold'
          key={action}
        >
          {action}{' '}
          <FontAwesomeIcon
            icon={faTimesCircle}
            onClick={() => onRemoveAction(action)}
            className='ml-auto cursor-pointer text-blue-500 hover:text-blue-600'
          />
        </Flex>
      ))}
    </Filter>
  );
};
