import { PlainMessage } from '@bufbuild/protobuf';
import { zodResolver } from '@hookform/resolvers/zod';
import { <PERSON>ert, Button, Modal, Typography } from 'antd';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { useDecrementInstance } from '@/hook/api';
import { Audit } from '@/lib/apiclient/aims/v1/aims_pb';
import { Instance } from '@/lib/apiclient/argocd/v1/argocd_pb';
import { AuditForm } from '@/lib/components/shared/audit-form';
import { zodAuditSchema } from '@/utils';

const schema = z.object({
  audit: zodAuditSchema
});

type GenerationForm = z.infer<typeof schema>;

export const GenerationModal = (props: {
  visible: boolean;
  onClose: (confirmed: boolean, e: React.MouseEvent<HTMLElement, MouseEvent>) => void;
  inst: Instance;
}) => {
  const { visible, onClose, inst } = props;
  const [err, setErr] = useState<boolean>(false);
  const [done, setDone] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  const decrementMutation = useDecrementInstance();

  const { control, handleSubmit } = useForm<GenerationForm>({
    resolver: zodResolver(schema),
    defaultValues: {
      audit: {
        actor: '',
        reason: ''
      }
    }
  });

  const onConfirm = handleSubmit((data) => {
    setErr(false);
    setDone(false);
    setLoading(true);
    decrementMutation.mutate(
      {
        instanceId: inst.id,
        audit: data.audit as PlainMessage<Audit>
      },
      {
        onSuccess: () => {
          setDone(true);
          setLoading(false);
        },
        onError: () => {
          setErr(true);
          setLoading(false);
        }
      }
    );
  });

  return (
    <Modal
      visible={visible}
      title='Decrement Observed Generation'
      onOk={onConfirm}
      onCancel={(e) => onClose(false, e)}
      footer={[
        <Button key='back' onClick={(e) => onClose(false, e)}>
          Cancel
        </Button>,
        <Button
          key='submit'
          type='primary'
          loading={loading}
          onClick={(e) => {
            if (done) {
              onClose(false, e);
            } else {
              onConfirm();
            }
          }}
        >
          {done ? 'Okay' : 'Confirm'}
        </Button>
      ]}
    >
      <div className='text-md font-semibold mb-2'>
        Are you sure you want to decrement the observed generation of instance
        <Typography.Text code>
          {inst.name} ({inst.id})
        </Typography.Text>
        ?
      </div>
      <div>
        This will decrement the observed generations of both the instance, and all associated
        clusters.
      </div>
      <AuditForm control={control} />
      <div className='mt-2'>
        <Alert
          message='Note'
          description='Decrementing the observed generation of an instance will trigger a re-apply, without pruning.'
          type='info'
          showIcon
        />
      </div>
      {err && (
        <Alert
          message='Error'
          className='mt-2'
          description='There was an error processing your request.'
          type='error'
          showIcon
        />
      )}
      {done && (
        <Alert
          className='mt-2'
          message='Success'
          description='The generation was successfully updated.'
          type='success'
          showIcon
        />
      )}
    </Modal>
  );
};
