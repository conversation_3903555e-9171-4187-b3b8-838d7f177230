import { useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useDebounce } from 'use-debounce';

export type FilterState = {
  fuzz?: string;
  startFrom?: string;
};

export const clusterFilters = {
  toSearch: (state: FilterState) => {
    const query = new URLSearchParams();

    if (state.fuzz) {
      query.set('fuzz', state.fuzz);
    }

    if (state.startFrom) {
      query.set('startFrom', state.startFrom);
    }

    return query.toString();
  },
  toState: (query: string) => {
    const state: FilterState = {};
    const params = new URLSearchParams(query);

    const fuzz = params.get('fuzz');
    if (fuzz) {
      state.fuzz = fuzz;
    }

    const startFrom = params.get('startFrom');
    if (startFrom) {
      state.startFrom = startFrom;
    }

    return state;
  }
};

export const useClusterFilters = () => {
  const [search, setSearch] = useSearchParams();

  const filter = useMemo(() => clusterFilters.toState(search.toString()), [search]);

  const [debounceFilter] = useDebounce(filter, 500);

  return {
    filter,
    debounceFilter,
    setSearch
  };
};
