import { faForward, faWarning, faDownload } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import JsonView from '@uiw/react-json-view';
import { Table, DatePicker, Input, Select, Button, Row, Col } from 'antd';
import moment from 'moment';

import { useModal } from '@/hook';
import { useGetInstanceClusters } from '@/hook/api';
import { HealthState } from '@/lib/components/shared/health-state';

import { ClusterFilter } from '../apiclient/aims/v1/aims_pb';
import { Cluster } from '../apiclient/argocd/v1/argocd_pb';

import { DownloadManifestModal } from './download-manifest-modal';
import { clusterFilters, useClusterFilters } from './filters';
import { MaintenanceModeModal } from './maintenance-mode-modal';

export const ClustersTable = (props: { instanceId: string }) => {
  const { filter, debounceFilter, setSearch } = useClusterFilters();
  const { data } = useGetInstanceClusters(
    props.instanceId,
    new ClusterFilter({
      fuzz: debounceFilter?.fuzz,
      timeFrom: debounceFilter?.startFrom
    })
  );

  const { show } = useModal();

  return (
    <div className='w-full'>
      <Row gutter={[16, 16]} align='middle' className='mb-5'>
        <Col>
          <Button
            icon={<FontAwesomeIcon icon={faForward} />}
            type='primary'
            onClick={() =>
              show((modalProps) => (
                <MaintenanceModeModal {...modalProps} instanceId={props.instanceId} />
              ))
            }
          >
            Maintenance Mode
          </Button>
        </Col>
        <Col flex='auto'>
          <Input
            value={filter?.fuzz || ''}
            onChange={(e) =>
              setSearch(clusterFilters.toSearch({ ...filter, fuzz: e.target.value }))
            }
            placeholder='Search by cluster name or id or namespace'
          />
        </Col>
        <Col>
          <Select
            placeholder='Created'
            value={filter?.startFrom}
            labelRender={(props) => (props.value ? moment().from(props.value) : 'Created')}
            style={{ width: 180 }}
            options={[
              {
                label: 'in 1 Hour',
                value: moment().subtract(1, 'hour').toISOString()
              },
              {
                label: 'in 1 Day',
                value: moment().subtract(1, 'day').toISOString()
              },
              {
                label: 'in 1 Week',
                value: moment().subtract(1, 'week').toISOString()
              },
              {
                label: 'in 1 Month',
                value: moment().subtract(1, 'month').toISOString()
              },
              {
                label: 'Clear',
                value: ''
              }
            ]}
            onChange={(value) =>
              setSearch(clusterFilters.toSearch({ ...filter, startFrom: value }))
            }
          />
        </Col>
        <Col>
          <DatePicker
            onChange={(date) =>
              setSearch(
                clusterFilters.toSearch({ ...filter, startFrom: date ? date.toISOString() : null })
              )
            }
          />
        </Col>
      </Row>

      <div className='text-sm font-semibold mb-4'>CLUSTERS</div>
      <Table
        dataSource={data || []}
        rowKey={(c: Cluster) => `${c?.id}-${c?.name}`}
        className='w-full'
        pagination={{ defaultPageSize: 100 }}
      >
        <Table.Column
          key='id'
          title='Id / Name'
          render={(value: Cluster) => (
            <>
              <b>{value?.name}</b>
              <div className='text-gray-400'>{value?.id}</div>
            </>
          )}
        />
        <Table.Column
          title='HEALTH'
          render={(c: Cluster) => (
            <div>
              {c?.data?.maintenanceMode && (
                <span className='text-xs text-red-500'>
                  <FontAwesomeIcon icon={faWarning} /> Maintenance Mode
                </span>
              )}
              <HealthState health={c.healthStatus} deletionTimestamp={c.deleteTime} />
            </div>
          )}
        />
        <Table.Column title='NAMESPACE' dataIndex='namespace' key='namespace' />
        <Table.Column
          title='DESCRIPTION'
          dataIndex='description'
          key='description'
          ellipsis
          render={(val) => val || '-'}
        />

        <Table.Column
          title='Agent Version'
          dataIndex={['agentState', 'version']}
          key='agentVersion'
          render={(val) => val || '-'}
        />
        <Table.Column
          title='ArgoCD Version'
          dataIndex={['agentState', 'argoCdVersion']}
          key='argoCdVersion'
          render={(val) => val || '-'}
        />
        <Table.Column
          title='DATA'
          dataIndex='data'
          key='data'
          render={(d) => <JsonView value={d} collapsed />}
        />
        <Table.Column
          title='CREDENTIAL ROTATION'
          dataIndex='credentialRotation'
          key='credentialRotation'
          render={(allowed) => (allowed ? 'Allowed' : 'Not Allowed')}
        />
        <Table.Column
          title='MANIFEST'
          width={120}
          render={(_, cluster: Cluster) => (
            <Button
              type='link'
              size='small'
              icon={<FontAwesomeIcon icon={faDownload} />}
              onClick={() =>
                show((modalProps) => (
                  <DownloadManifestModal
                    {...modalProps}
                    instanceId={props.instanceId}
                    clusterId={cluster.id}
                    clusterName={cluster.name}
                    type='argocd'
                  />
                ))
              }
            >
              Download
            </Button>
          )}
        />
      </Table>
    </div>
  );
};
