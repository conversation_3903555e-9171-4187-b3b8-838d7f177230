import {
  faBoxOpen,
  faServer,
  faDiagramProject,
  faMoneyCheckDollar,
  faDoorOpen,
  faCalendar,
  faPeopleGroup,
  faBell,
  IconDefinition,
  faList
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Menu } from 'antd';
import { useEffect, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';

const { SubMenu } = Menu;

interface MenuItem {
  key: string;
  label: string;
  icon?: IconDefinition;
  children?: MenuItem[];
}

const menuItems: MenuItem[] = [
  {
    key: 'product',
    label: 'Product',
    icon: faBoxOpen,
    children: [
      {
        key: 'argocd',
        label: 'Argo CD',
        icon: faServer,
        children: [
          { key: '/instances/paid', label: 'Paid Instances' },
          { key: '/instances/unpaid', label: 'Unpaid Instances' }
        ]
      },
      {
        key: 'kargo',
        label: 'Kargo',
        icon: faDiagramProject,
        children: [
          { key: '/kargo/instances/paid', label: 'Paid Instances' },
          { key: '/kargo/instances/unpaid', label: 'Unpaid Instances' }
        ]
      }
    ]
  },
  {
    key: 'billing',
    label: 'Billing Management',
    icon: faMoneyCheckDollar,
    children: [
      { key: '/billing', label: 'Billing', icon: faMoneyCheckDollar },
      { key: '/onboarding', label: 'Onboarding', icon: faDoorOpen },
      { key: '/trial', label: 'Update Trial', icon: faCalendar }
    ]
  },
  { key: '/organizations', label: 'Organizations', icon: faPeopleGroup },
  { key: '/notifications', label: 'Notifications', icon: faBell },
  { key: '/audit-logs', label: 'Internal Audit Logs', icon: faList }
];

export const Sidebar = () => {
  const location = useLocation();
  const [openKeys, setOpenKeys] = useState<string[]>([]);

  useEffect(() => {
    const findActiveMenu = (items: MenuItem[], path: string): string[] => {
      for (const item of items) {
        if (item.key === '/instances/paid' && path === '/') return [item.key];
        if (item.key === path) return [item.key];
        if (item.children) {
          const found = findActiveMenu(item.children, path);
          if (found.length) return [item.key, ...found];
        }
      }
      return [];
    };

    setOpenKeys(findActiveMenu(menuItems, location.pathname));
  }, []);

  const handleOpenChange = (keys: string[]) => {
    setOpenKeys(keys);
  };

  return (
    <div className='bg-black h-full w-80 p-4 overflow-y-auto'>
      <div className='flex items-center justify-center mb-6'>
        <img src='/images/aims-logo.png' className='w-16' alt='AIMS Logo' />
        <div className='text-4xl font-black ml-2 text-white'>aims</div>
      </div>
      <div className='text-gray-600 text-xs text-center mb-8 font-bold'>
        Akuity Internal <br /> Management System
      </div>

      <Menu
        mode='inline'
        theme='dark'
        selectedKeys={[
          menuItems.find((item) => location.pathname.startsWith(item.key))?.key || location.pathname
        ]}
        openKeys={openKeys}
        onOpenChange={handleOpenChange}
        className='text-white bg-black'
      >
        {menuItems.map((item) =>
          item.children ? (
            <SubMenu key={item.key} title={item.label} icon={<FontAwesomeIcon icon={item.icon} />}>
              {item.children.map((subItem) =>
                subItem.children ? (
                  <SubMenu
                    key={subItem.key}
                    title={subItem.label}
                    icon={<FontAwesomeIcon icon={subItem.icon} />}
                  >
                    {subItem.children.map((subSubItem) => (
                      <Menu.Item key={subSubItem.key}>
                        <Link to={subSubItem.key}>{subSubItem.label}</Link>
                      </Menu.Item>
                    ))}
                  </SubMenu>
                ) : (
                  <Menu.Item key={subItem.key} icon={<FontAwesomeIcon icon={subItem.icon} />}>
                    <Link to={subItem.key}>{subItem.label}</Link>
                  </Menu.Item>
                )
              )}
            </SubMenu>
          ) : (
            <Menu.Item key={item.key} icon={<FontAwesomeIcon icon={item.icon} />}>
              <Link to={item.key}>{item.label}</Link>
            </Menu.Item>
          )
        )}
      </Menu>
    </div>
  );
};
