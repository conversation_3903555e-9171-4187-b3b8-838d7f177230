import { PlainMessage, Timestamp } from '@bufbuild/protobuf';
import { faGhost } from '@fortawesome/free-solid-svg-icons/faGhost';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Tooltip } from 'antd';
import classNames from 'classnames';

import { Status, StatusCode } from '@/lib/apiclient/types/status/health/v1/health_pb';
import {
  HealthV1Status,
  HealthV1StatusCode,
  ReconciliationV1Status,
  ReconciliationV1StatusCode
} from '@/types';

import { IconPropsForHealth } from './heath-icon';

export const humananifyStatusCode = (
  code:
    | HealthV1StatusCode
    | ReconciliationV1StatusCode
    // TODO: remove string when we migrate cluster status code type to HealthV1StatusCode
    | string
    | StatusCode
) => {
  switch (code) {
    case HealthV1StatusCode.STATUS_CODE_HEALTHY:
    case StatusCode.HEALTHY:
      return 'Healthy';
    case ReconciliationV1StatusCode.STATUS_CODE_SUCCESSFUL:
      return 'Success';
    case ReconciliationV1StatusCode.STATUS_CODE_FAILED:
      return 'Failed';
    case HealthV1StatusCode.STATUS_CODE_PROGRESSING:
    case StatusCode.PROGRESSING:
      return 'Progressing';
    case HealthV1StatusCode.STATUS_CODE_UNKNOWN:
    case StatusCode.UNKNOWN:
      return 'Unknown';
    case HealthV1StatusCode.STATUS_CODE_DEGRADED:
    case StatusCode.DEGRADED:
      return 'Degraded';
    case HealthV1StatusCode.STATUS_CODE_UNSPECIFIED:
    case StatusCode.UNSPECIFIED:
      return 'Unspecified';
  }

  return 'Unknown';
};

export const HealthState = (props: {
  // TODO: use HealthV1Status type
  health: HealthV1Status | { code: string; message: string } | Status | ReconciliationV1Status;
  deletionTimestamp: string | Timestamp | PlainMessage<Timestamp>;
  className?: string;
  iconOnly?: boolean;
}) => {
  return (
    <div className={classNames(props.className)}>
      <div className='flex items-center space-x-1'>
        {props.deletionTimestamp ? (
          <>
            <FontAwesomeIcon icon={faGhost} className='mr-2 text-gray-300' />
            {!props.iconOnly && <span>Deleting...</span>}
          </>
        ) : (
          <>
            <div className='mr-2'>
              <Tooltip title={props.health.message}>
                <FontAwesomeIcon {...IconPropsForHealth(props.health.code)} />
              </Tooltip>
            </div>
            {!props.iconOnly && (
              <span title={props.health.message}>{humananifyStatusCode(props.health.code)}</span>
            )}
          </>
        )}
      </div>
    </div>
  );
};
