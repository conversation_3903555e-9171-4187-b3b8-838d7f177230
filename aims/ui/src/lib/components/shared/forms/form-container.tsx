import { type IconDefinition } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Space } from 'antd';
import classNames from 'classnames';
import type { ReactNode } from 'react';
import React from 'react';

export interface FormContainerProps {
  name?: string;
  className?: string;
  icon?: IconDefinition;
  label?: string | React.ReactNode;
  description?: string;
  error?: string;
  children: ReactNode;
  noBorder?: boolean;
  labelHtmlFor?: string;
  wrapperClassName?: string;
  errorClassName?: string;
  optional?: boolean;
  note?: string | React.ReactNode;
  direction?: 'vertical' | 'horizontal';
}

export const FormContainer = ({
  name,
  className,
  icon,
  label,
  error,
  children,
  noBorder,
  labelHtmlFor,
  wrapperClassName,
  errorClassName,
  optional,
  description,
  note,
  direction = 'vertical'
}: FormContainerProps) => {
  return (
    <div className={classNames('mb-4 w-full', wrapperClassName)}>
      <Space direction={direction} className='w-full'>
        {label && (
          <label
            className='flex w-fit items-center gap-1'
            htmlFor={labelHtmlFor || name}
            onClick={(e) => {
              if (e.detail === 1) {
                (
                  e.currentTarget?.parentElement?.nextSibling?.lastChild?.lastChild as HTMLElement
                )?.focus();
              }
            }}
          >
            {label}
            {description && (
              <span className='text-sm font-normal text-gray-400'> {description}</span>
            )}{' '}
            {Boolean(optional) && (
              <span className='text-xs font-normal text-gray-400'>(optional)</span>
            )}
          </label>
        )}
        <div
          className={`flex w-full rounded-md box-border items-center ${
            noBorder ? '' : 'border border-akuity-100 focus-within:border-blue-500'
          } ${className}`}
        >
          {icon && <FontAwesomeIcon icon={icon} className='ml-4 mr-2 text-akuity-400' />}
          {children}
        </div>
      </Space>
      {error && (
        <p className={`text-red-600 text-sm italic mt-2 ${errorClassName}`} role='alert'>
          {error}
        </p>
      )}

      {!error && Boolean(note) && (
        <span className='text-xs font-light text-gray-400 italic'>{note}</span>
      )}
    </div>
  );
};
