import type { InputProps } from 'antd';
import React from 'react';
import {
  FieldPath,
  FieldValues,
  useC<PERSON>roller,
  UseControllerProps,
  UseControllerReturn
} from 'react-hook-form';

import { FormContainer, FormContainerProps } from './form-container';

export type AriaProps = Pick<InputProps, 'aria-invalid'>;

export const getAriaProps = ({ invalid }: { invalid?: boolean }): AriaProps => ({
  ['aria-invalid']: invalid ? 'true' : 'false'
});
// Prefer not to use FieldContainer disabled prop unless you are absolutely sure that you need it.
// Check documentation to see if you need it: https://react-hook-form.com/docs/usecontroller
// Using it here causes console errors, see: https://github.com/orgs/react-hook-form/discussions/10964#discussioncomment-7087442
interface Props<
  TFieldValues extends FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> extends UseControllerProps<TFieldValues, TName>,
    Omit<FormContainerProps, 'children' | 'name'> {
  children: (
    props: UseControllerReturn<TFieldValues, TName> & { ariaProps?: AriaProps }
  ) => React.ReactNode;
  isPaywalled?: boolean;
}

export const FieldContainer = <
  TFieldValues extends FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  children,
  ...props
}: Props<TFieldValues, TName>) => {
  const controller = useController(props);

  const ariaProps = getAriaProps({
    invalid: Boolean(controller?.fieldState?.error?.message)
  });

  return (
    <FormContainer noBorder {...props} error={controller.fieldState.error?.message || props.error}>
      {children({ ...controller, ariaProps })}
    </FormContainer>
  );
};
