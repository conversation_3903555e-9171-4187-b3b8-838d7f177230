import { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { faCircleNotch } from '@fortawesome/free-solid-svg-icons/faCircleNotch';
import { faHeart } from '@fortawesome/free-solid-svg-icons/faHeart';
import { faHeartBroken } from '@fortawesome/free-solid-svg-icons/faHeartBroken';
import { faQuestionCircle } from '@fortawesome/free-solid-svg-icons/faQuestionCircle';

import { StatusCode } from '@/lib/apiclient/types/status/health/v1/health_pb';
import { HealthV1StatusCode, ReconciliationV1StatusCode } from '@/types';

export const IconPropsForHealth = (
  code: string | StatusCode
): { icon: IconDefinition; spin?: boolean; className: string } => {
  switch (code) {
    case HealthV1StatusCode.STATUS_CODE_HEALTHY:
    case StatusCode.HEALTHY:
    case ReconciliationV1StatusCode.STATUS_CODE_SUCCESSFUL:
      return {
        icon: faHeart,
        className: 'text-green-500'
      };
    case HealthV1StatusCode.STATUS_CODE_PROGRESSING:
    case StatusCode.PROGRESSING:
      return {
        icon: faCircleNotch,
        className: 'text-blue-500',
        spin: true
      };
    case HealthV1StatusCode.STATUS_CODE_DEGRADED:
    case StatusCode.DEGRADED:
    case ReconciliationV1StatusCode.STATUS_CODE_FAILED:
      return {
        icon: faHeartBroken,
        className: 'text-orange-500'
      };
    default:
      return {
        icon: faQuestionCircle,
        className: 'text-gray-500'
      };
  }
};
