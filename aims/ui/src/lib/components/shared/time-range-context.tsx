import { Button, Radio, Space } from 'antd';
import generatePicker from 'antd/es/date-picker/generatePicker';
import type { Moment } from 'moment';
import moment from 'moment';
import momentGenerateConfig from 'rc-picker/lib/generate/moment';
import { useState, useEffect } from 'react';

import { TimeRangeOption } from '@/types';
import { determineTimeRange, getTimeRangeDates } from '@/utils';

const AntdDatePicker = generatePicker<Moment>(momentGenerateConfig);
const { RangePicker } = AntdDatePicker;

interface TimeRangeFilterContentProps {
  startTime?: string;
  endTime?: string;
  onApply: (times: { startTime?: string; endTime?: string }) => void;
  onClear: () => void;
}

export const TimeRangeFilterContent = ({
  startTime,
  endTime,
  onApply,
  onClear
}: TimeRangeFilterContentProps) => {
  const [timeRange, setTimeRange] = useState<TimeRangeOption>(() =>
    determineTimeRange(startTime, endTime)
  );

  const [customDates, setCustomDates] = useState<[Moment, Moment] | null>(() => {
    if (startTime && endTime) {
      return [moment(startTime), moment(endTime)];
    }
    return null;
  });

  useEffect(() => {
    const determinedRange = determineTimeRange(startTime, endTime);
    setTimeRange(determinedRange);
    if (startTime && endTime) {
      setCustomDates([moment(startTime), moment(endTime)]);
    } else {
      setCustomDates(null);
    }
  }, [startTime, endTime]);

  const handleApplyClick = () => {
    let newStartTime: string | undefined;
    let newEndTime: string | undefined;

    if (timeRange === 'custom') {
      if (customDates?.[0] && customDates?.[1]) {
        newStartTime = customDates[0].startOf('day').toISOString();
        newEndTime = customDates[1].endOf('day').toISOString();
      }
    } else {
      const dates = getTimeRangeDates(timeRange);
      if (dates) {
        newStartTime = dates[0].toISOString();
        newEndTime = dates[1].toISOString();
      }
    }
    onApply({ startTime: newStartTime, endTime: newEndTime });
  };

  const handleTimeRangeChange = (value: TimeRangeOption) => {
    setTimeRange(value);
    if (value !== 'custom') {
      const dates = getTimeRangeDates(value);
      setCustomDates(dates);
    }
  };

  return (
    <div className='p-5' style={{ width: '280px' }}>
      <div className='space-y-4'>
        <Radio.Group value={timeRange} onChange={(e) => handleTimeRangeChange(e.target.value)}>
          <Space direction='vertical'>
            <Radio value='7-days'>Last 7 days</Radio>
            <Radio value='1-month'>Last month</Radio>
            <Radio value='90-days'>Last 90 days</Radio>
            <Radio value='custom'>Custom range</Radio>
          </Space>
        </Radio.Group>
        {timeRange === 'custom' && (
          <RangePicker
            showTime
            value={customDates}
            onChange={(dates) => setCustomDates(dates as [Moment, Moment] | null)}
            className='w-full mt-2'
          />
        )}
      </div>
      <div className='flex justify-end space-x-2 pt-4 mt-2 border-t'>
        <Button onClick={onClear} disabled={!startTime && !endTime}>
          Clear
        </Button>
        <Button type='primary' onClick={handleApplyClick}>
          Apply
        </Button>
      </div>
    </div>
  );
};
