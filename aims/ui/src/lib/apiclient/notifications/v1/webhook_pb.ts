// @generated by protoc-gen-es v1.2.0 with parameter "target=ts"
// @generated from file notifications/v1/webhook.proto (package akuity.notifications.webhook.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3, protoInt64, Timestamp } from "@bufbuild/protobuf";

/**
 * `WebhookEventType` defines the types of webhook events.
 *
 * @generated from enum akuity.notifications.webhook.v1.WebhookEventType
 */
export enum WebhookEventType {
  /**
   * Unspecified event type.
   *
   * @generated from enum value: WEBHOOK_EVENT_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * Ping event type.
   *
   * @generated from enum value: WEBHOOK_EVENT_TYPE_PING = 1;
   */
  PING = 1,

  /**
   * Audit log event type.
   *
   * @generated from enum value: WEBHOOK_EVENT_TYPE_AUDIT = 2;
   */
  AUDIT = 2,

  /**
   * Usage alert event type.
   *
   * @generated from enum value: WEBHOOK_EVENT_TYPE_USAGE = 3;
   */
  USAGE = 3,
}
// Retrieve enum metadata with: proto3.getEnumType(WebhookEventType)
proto3.util.setEnumType(WebhookEventType, "akuity.notifications.webhook.v1.WebhookEventType", [
  { no: 0, name: "WEBHOOK_EVENT_TYPE_UNSPECIFIED" },
  { no: 1, name: "WEBHOOK_EVENT_TYPE_PING" },
  { no: 2, name: "WEBHOOK_EVENT_TYPE_AUDIT" },
  { no: 3, name: "WEBHOOK_EVENT_TYPE_USAGE" },
]);

/**
 * `WebhookEventPayload` is the payload sent to the webhook endpoint.
 *
 * @generated from message akuity.notifications.webhook.v1.WebhookEventPayload
 */
export class WebhookEventPayload extends Message<WebhookEventPayload> {
  /**
   * The time the event was triggered.
   *
   * @generated from field: google.protobuf.Timestamp event_time = 1;
   */
  eventTime?: Timestamp;

  /**
   * The type of event.
   *
   * @generated from field: akuity.notifications.webhook.v1.WebhookEventType event_type = 2;
   */
  eventType = WebhookEventType.UNSPECIFIED;

  /**
   * The organization ID.
   *
   * @generated from field: string organization_id = 3;
   */
  organizationId = "";

  /**
   * The event ID.
   *
   * @generated from field: string event_id = 4;
   */
  eventId = "";

  /**
   * The event metadata.
   *
   * @generated from oneof akuity.notifications.webhook.v1.WebhookEventPayload.metadata
   */
  metadata: {
    /**
     * Metadata for a ping event.
     *
     * @generated from field: akuity.notifications.webhook.v1.PingEvent ping_event = 5;
     */
    value: PingEvent;
    case: "pingEvent";
  } | {
    /**
     * Metadata for a usage update event.
     *
     * @generated from field: akuity.notifications.webhook.v1.UsageUpdateEvent usage_event = 6;
     */
    value: UsageUpdateEvent;
    case: "usageEvent";
  } | {
    /**
     * Metadata for an audit event.
     *
     * @generated from field: akuity.notifications.webhook.v1.AuditEvent audit_event = 7;
     */
    value: AuditEvent;
    case: "auditEvent";
  } | { case: undefined; value?: undefined } = { case: undefined };

  constructor(data?: PartialMessage<WebhookEventPayload>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.notifications.webhook.v1.WebhookEventPayload";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "event_time", kind: "message", T: Timestamp },
    { no: 2, name: "event_type", kind: "enum", T: proto3.getEnumType(WebhookEventType) },
    { no: 3, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "event_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "ping_event", kind: "message", T: PingEvent, oneof: "metadata" },
    { no: 6, name: "usage_event", kind: "message", T: UsageUpdateEvent, oneof: "metadata" },
    { no: 7, name: "audit_event", kind: "message", T: AuditEvent, oneof: "metadata" },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WebhookEventPayload {
    return new WebhookEventPayload().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WebhookEventPayload {
    return new WebhookEventPayload().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WebhookEventPayload {
    return new WebhookEventPayload().fromJsonString(jsonString, options);
  }

  static equals(a: WebhookEventPayload | PlainMessage<WebhookEventPayload> | undefined, b: WebhookEventPayload | PlainMessage<WebhookEventPayload> | undefined): boolean {
    return proto3.util.equals(WebhookEventPayload, a, b);
  }
}

/**
 * `PingEvent` contains metadata for a ping event.
 *
 * @generated from message akuity.notifications.webhook.v1.PingEvent
 */
export class PingEvent extends Message<PingEvent> {
  /**
   * NotificationConfigID is the organization notification config ID that triggered the ping
   *
   * @generated from field: string notification_config_id = 1;
   */
  notificationConfigId = "";

  constructor(data?: PartialMessage<PingEvent>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.notifications.webhook.v1.PingEvent";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "notification_config_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PingEvent {
    return new PingEvent().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PingEvent {
    return new PingEvent().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PingEvent {
    return new PingEvent().fromJsonString(jsonString, options);
  }

  static equals(a: PingEvent | PlainMessage<PingEvent> | undefined, b: PingEvent | PlainMessage<PingEvent> | undefined): boolean {
    return proto3.util.equals(PingEvent, a, b);
  }
}

/**
 * @generated from message akuity.notifications.webhook.v1.UsageUpdateEvent
 */
export class UsageUpdateEvent extends Message<UsageUpdateEvent> {
  /**
   * The product associated with the usage update. E.g. "ArgoCD", "Kargo", "Akuity Agent".
   *
   * @generated from field: string product = 1;
   */
  product = "";

  /**
   * The type of usage. E.g. "applications", "projects".
   *
   * @generated from field: string usage_type = 2;
   */
  usageType = "";

  /**
   * The usage threshold.
   *
   * @generated from field: double usage_threshold = 3;
   */
  usageThreshold = 0;

  /**
   * The maximum limit.
   *
   * @generated from field: int64 max_limit = 4;
   */
  maxLimit = protoInt64.zero;

  /**
   * The current usage.
   *
   * @generated from field: int64 usage = 5;
   */
  usage = protoInt64.zero;

  constructor(data?: PartialMessage<UsageUpdateEvent>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.notifications.webhook.v1.UsageUpdateEvent";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "product", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "usage_type", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "usage_threshold", kind: "scalar", T: 1 /* ScalarType.DOUBLE */ },
    { no: 4, name: "max_limit", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 5, name: "usage", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UsageUpdateEvent {
    return new UsageUpdateEvent().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UsageUpdateEvent {
    return new UsageUpdateEvent().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UsageUpdateEvent {
    return new UsageUpdateEvent().fromJsonString(jsonString, options);
  }

  static equals(a: UsageUpdateEvent | PlainMessage<UsageUpdateEvent> | undefined, b: UsageUpdateEvent | PlainMessage<UsageUpdateEvent> | undefined): boolean {
    return proto3.util.equals(UsageUpdateEvent, a, b);
  }
}

/**
 * `AuditEvent` contains metadata for an audit event.
 *
 * @generated from message akuity.notifications.webhook.v1.AuditEvent
 */
export class AuditEvent extends Message<AuditEvent> {
  /**
   * The time the event was triggered.
   *
   * @generated from field: string timestamp = 1;
   */
  timestamp = "";

  /**
   * The action that was performed.
   *
   * @generated from field: string action = 2;
   */
  action = "";

  /**
   * The actor that triggered the event.
   *
   * @generated from field: akuity.notifications.webhook.v1.AuditEvent.EventAuditActor actor = 3;
   */
  actor?: AuditEvent_EventAuditActor;

  /**
   * The object that was acted upon.
   *
   * @generated from field: akuity.notifications.webhook.v1.AuditEvent.EventAuditObject object = 4;
   */
  object?: AuditEvent_EventAuditObject;

  /**
   * The details of the event.
   *
   * @generated from field: akuity.notifications.webhook.v1.AuditEvent.EventAuditDetails details = 5;
   */
  details?: AuditEvent_EventAuditDetails;

  constructor(data?: PartialMessage<AuditEvent>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.notifications.webhook.v1.AuditEvent";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "timestamp", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "action", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "actor", kind: "message", T: AuditEvent_EventAuditActor },
    { no: 4, name: "object", kind: "message", T: AuditEvent_EventAuditObject },
    { no: 5, name: "details", kind: "message", T: AuditEvent_EventAuditDetails },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AuditEvent {
    return new AuditEvent().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AuditEvent {
    return new AuditEvent().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AuditEvent {
    return new AuditEvent().fromJsonString(jsonString, options);
  }

  static equals(a: AuditEvent | PlainMessage<AuditEvent> | undefined, b: AuditEvent | PlainMessage<AuditEvent> | undefined): boolean {
    return proto3.util.equals(AuditEvent, a, b);
  }
}

/**
 * @generated from message akuity.notifications.webhook.v1.AuditEvent.EventAuditActor
 */
export class AuditEvent_EventAuditActor extends Message<AuditEvent_EventAuditActor> {
  /**
   * The type of actor.
   *
   * @generated from field: string type = 1;
   */
  type = "";

  /**
   * The ID of the actor.
   *
   * @generated from field: string id = 2;
   */
  id = "";

  /**
   * The IP address of the actor.
   *
   * @generated from field: optional string ip = 3;
   */
  ip?: string;

  constructor(data?: PartialMessage<AuditEvent_EventAuditActor>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.notifications.webhook.v1.AuditEvent.EventAuditActor";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "type", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "ip", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AuditEvent_EventAuditActor {
    return new AuditEvent_EventAuditActor().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AuditEvent_EventAuditActor {
    return new AuditEvent_EventAuditActor().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AuditEvent_EventAuditActor {
    return new AuditEvent_EventAuditActor().fromJsonString(jsonString, options);
  }

  static equals(a: AuditEvent_EventAuditActor | PlainMessage<AuditEvent_EventAuditActor> | undefined, b: AuditEvent_EventAuditActor | PlainMessage<AuditEvent_EventAuditActor> | undefined): boolean {
    return proto3.util.equals(AuditEvent_EventAuditActor, a, b);
  }
}

/**
 * @generated from message akuity.notifications.webhook.v1.AuditEvent.EventAuditObject
 */
export class AuditEvent_EventAuditObject extends Message<AuditEvent_EventAuditObject> {
  /**
   * The type of object. E.g. "team_member", "team", "custom_role", "kargo_instance", etc.
   *
   * @generated from field: string type = 1;
   */
  type = "";

  /**
   * The ID of the object.
   *
   * @generated from field: akuity.notifications.webhook.v1.AuditEvent.EventAuditObject.EventAuditObjId id = 2;
   */
  id?: AuditEvent_EventAuditObject_EventAuditObjId;

  /**
   * The parent ID of the object.
   *
   * @generated from field: akuity.notifications.webhook.v1.AuditEvent.EventAuditObject.EventAuditParentId parent_id = 3;
   */
  parentId?: AuditEvent_EventAuditObject_EventAuditParentId;

  constructor(data?: PartialMessage<AuditEvent_EventAuditObject>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.notifications.webhook.v1.AuditEvent.EventAuditObject";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "type", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "id", kind: "message", T: AuditEvent_EventAuditObject_EventAuditObjId },
    { no: 3, name: "parent_id", kind: "message", T: AuditEvent_EventAuditObject_EventAuditParentId },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AuditEvent_EventAuditObject {
    return new AuditEvent_EventAuditObject().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AuditEvent_EventAuditObject {
    return new AuditEvent_EventAuditObject().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AuditEvent_EventAuditObject {
    return new AuditEvent_EventAuditObject().fromJsonString(jsonString, options);
  }

  static equals(a: AuditEvent_EventAuditObject | PlainMessage<AuditEvent_EventAuditObject> | undefined, b: AuditEvent_EventAuditObject | PlainMessage<AuditEvent_EventAuditObject> | undefined): boolean {
    return proto3.util.equals(AuditEvent_EventAuditObject, a, b);
  }
}

/**
 * @generated from message akuity.notifications.webhook.v1.AuditEvent.EventAuditObject.EventAuditObjId
 */
export class AuditEvent_EventAuditObject_EventAuditObjId extends Message<AuditEvent_EventAuditObject_EventAuditObjId> {
  /**
   * The name of the object.
   *
   * @generated from field: string name = 1;
   */
  name = "";

  /**
   * The kind of object.
   *
   * @generated from field: string kind = 2;
   */
  kind = "";

  /**
   * The group of the object.
   *
   * @generated from field: string group = 3;
   */
  group = "";

  constructor(data?: PartialMessage<AuditEvent_EventAuditObject_EventAuditObjId>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.notifications.webhook.v1.AuditEvent.EventAuditObject.EventAuditObjId";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "kind", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "group", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AuditEvent_EventAuditObject_EventAuditObjId {
    return new AuditEvent_EventAuditObject_EventAuditObjId().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AuditEvent_EventAuditObject_EventAuditObjId {
    return new AuditEvent_EventAuditObject_EventAuditObjId().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AuditEvent_EventAuditObject_EventAuditObjId {
    return new AuditEvent_EventAuditObject_EventAuditObjId().fromJsonString(jsonString, options);
  }

  static equals(a: AuditEvent_EventAuditObject_EventAuditObjId | PlainMessage<AuditEvent_EventAuditObject_EventAuditObjId> | undefined, b: AuditEvent_EventAuditObject_EventAuditObjId | PlainMessage<AuditEvent_EventAuditObject_EventAuditObjId> | undefined): boolean {
    return proto3.util.equals(AuditEvent_EventAuditObject_EventAuditObjId, a, b);
  }
}

/**
 * @generated from message akuity.notifications.webhook.v1.AuditEvent.EventAuditObject.EventAuditParentId
 */
export class AuditEvent_EventAuditObject_EventAuditParentId extends Message<AuditEvent_EventAuditObject_EventAuditParentId> {
  /**
   * The name of the parent.
   *
   * @generated from field: string name = 1;
   */
  name = "";

  /**
   * The name of the parent object's parent (if present).
   *
   * @generated from field: string parent_name = 2;
   */
  parentName = "";

  /**
   * The name of the application.
   *
   * @generated from field: string application_name = 3;
   */
  applicationName = "";

  constructor(data?: PartialMessage<AuditEvent_EventAuditObject_EventAuditParentId>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.notifications.webhook.v1.AuditEvent.EventAuditObject.EventAuditParentId";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "parent_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "application_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AuditEvent_EventAuditObject_EventAuditParentId {
    return new AuditEvent_EventAuditObject_EventAuditParentId().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AuditEvent_EventAuditObject_EventAuditParentId {
    return new AuditEvent_EventAuditObject_EventAuditParentId().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AuditEvent_EventAuditObject_EventAuditParentId {
    return new AuditEvent_EventAuditObject_EventAuditParentId().fromJsonString(jsonString, options);
  }

  static equals(a: AuditEvent_EventAuditObject_EventAuditParentId | PlainMessage<AuditEvent_EventAuditObject_EventAuditParentId> | undefined, b: AuditEvent_EventAuditObject_EventAuditParentId | PlainMessage<AuditEvent_EventAuditObject_EventAuditParentId> | undefined): boolean {
    return proto3.util.equals(AuditEvent_EventAuditObject_EventAuditParentId, a, b);
  }
}

/**
 * @generated from message akuity.notifications.webhook.v1.AuditEvent.EventAuditDetails
 */
export class AuditEvent_EventAuditDetails extends Message<AuditEvent_EventAuditDetails> {
  /**
   * The message associated with the event.
   *
   * @generated from field: string message = 1;
   */
  message = "";

  /**
   * The patch associated with the event.
   *
   * @generated from field: string patch = 2;
   */
  patch = "";

  /**
   * The action type.
   *
   * @generated from field: string action_type = 3;
   */
  actionType = "";

  constructor(data?: PartialMessage<AuditEvent_EventAuditDetails>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.notifications.webhook.v1.AuditEvent.EventAuditDetails";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "message", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "patch", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "action_type", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AuditEvent_EventAuditDetails {
    return new AuditEvent_EventAuditDetails().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AuditEvent_EventAuditDetails {
    return new AuditEvent_EventAuditDetails().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AuditEvent_EventAuditDetails {
    return new AuditEvent_EventAuditDetails().fromJsonString(jsonString, options);
  }

  static equals(a: AuditEvent_EventAuditDetails | PlainMessage<AuditEvent_EventAuditDetails> | undefined, b: AuditEvent_EventAuditDetails | PlainMessage<AuditEvent_EventAuditDetails> | undefined): boolean {
    return proto3.util.equals(AuditEvent_EventAuditDetails, a, b);
  }
}

