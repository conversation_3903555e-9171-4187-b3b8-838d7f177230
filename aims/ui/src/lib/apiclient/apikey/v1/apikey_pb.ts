// @generated by protoc-gen-es v1.2.0 with parameter "target=ts"
// @generated from file apikey/v1/apikey.proto (package akuity.apikey.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3, Timestamp } from "@bufbuild/protobuf";
import { Permissions } from "../../accesscontrol/v1/accesscontrol_pb.js";

/**
 * @generated from message akuity.apikey.v1.GetWorkspaceAPIKeyRequest
 */
export class GetWorkspaceAPIKeyRequest extends Message<GetWorkspaceAPIKeyRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string workspace_id = 2;
   */
  workspaceId = "";

  /**
   * @generated from field: string id = 3;
   */
  id = "";

  constructor(data?: PartialMessage<GetWorkspaceAPIKeyRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.apikey.v1.GetWorkspaceAPIKeyRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetWorkspaceAPIKeyRequest {
    return new GetWorkspaceAPIKeyRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetWorkspaceAPIKeyRequest {
    return new GetWorkspaceAPIKeyRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetWorkspaceAPIKeyRequest {
    return new GetWorkspaceAPIKeyRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetWorkspaceAPIKeyRequest | PlainMessage<GetWorkspaceAPIKeyRequest> | undefined, b: GetWorkspaceAPIKeyRequest | PlainMessage<GetWorkspaceAPIKeyRequest> | undefined): boolean {
    return proto3.util.equals(GetWorkspaceAPIKeyRequest, a, b);
  }
}

/**
 * @generated from message akuity.apikey.v1.GetWorkspaceAPIKeyResponse
 */
export class GetWorkspaceAPIKeyResponse extends Message<GetWorkspaceAPIKeyResponse> {
  /**
   * @generated from field: akuity.apikey.v1.APIKey api_key = 1;
   */
  apiKey?: APIKey;

  constructor(data?: PartialMessage<GetWorkspaceAPIKeyResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.apikey.v1.GetWorkspaceAPIKeyResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "api_key", kind: "message", T: APIKey },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetWorkspaceAPIKeyResponse {
    return new GetWorkspaceAPIKeyResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetWorkspaceAPIKeyResponse {
    return new GetWorkspaceAPIKeyResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetWorkspaceAPIKeyResponse {
    return new GetWorkspaceAPIKeyResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetWorkspaceAPIKeyResponse | PlainMessage<GetWorkspaceAPIKeyResponse> | undefined, b: GetWorkspaceAPIKeyResponse | PlainMessage<GetWorkspaceAPIKeyResponse> | undefined): boolean {
    return proto3.util.equals(GetWorkspaceAPIKeyResponse, a, b);
  }
}

/**
 * @generated from message akuity.apikey.v1.DeleteWorkspaceAPIKeyRequest
 */
export class DeleteWorkspaceAPIKeyRequest extends Message<DeleteWorkspaceAPIKeyRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string workspace_id = 2;
   */
  workspaceId = "";

  /**
   * @generated from field: string id = 3;
   */
  id = "";

  constructor(data?: PartialMessage<DeleteWorkspaceAPIKeyRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.apikey.v1.DeleteWorkspaceAPIKeyRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteWorkspaceAPIKeyRequest {
    return new DeleteWorkspaceAPIKeyRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteWorkspaceAPIKeyRequest {
    return new DeleteWorkspaceAPIKeyRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteWorkspaceAPIKeyRequest {
    return new DeleteWorkspaceAPIKeyRequest().fromJsonString(jsonString, options);
  }

  static equals(a: DeleteWorkspaceAPIKeyRequest | PlainMessage<DeleteWorkspaceAPIKeyRequest> | undefined, b: DeleteWorkspaceAPIKeyRequest | PlainMessage<DeleteWorkspaceAPIKeyRequest> | undefined): boolean {
    return proto3.util.equals(DeleteWorkspaceAPIKeyRequest, a, b);
  }
}

/**
 * explicitly empty 
 *
 * @generated from message akuity.apikey.v1.DeleteWorkspaceAPIKeyResponse
 */
export class DeleteWorkspaceAPIKeyResponse extends Message<DeleteWorkspaceAPIKeyResponse> {
  constructor(data?: PartialMessage<DeleteWorkspaceAPIKeyResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.apikey.v1.DeleteWorkspaceAPIKeyResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteWorkspaceAPIKeyResponse {
    return new DeleteWorkspaceAPIKeyResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteWorkspaceAPIKeyResponse {
    return new DeleteWorkspaceAPIKeyResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteWorkspaceAPIKeyResponse {
    return new DeleteWorkspaceAPIKeyResponse().fromJsonString(jsonString, options);
  }

  static equals(a: DeleteWorkspaceAPIKeyResponse | PlainMessage<DeleteWorkspaceAPIKeyResponse> | undefined, b: DeleteWorkspaceAPIKeyResponse | PlainMessage<DeleteWorkspaceAPIKeyResponse> | undefined): boolean {
    return proto3.util.equals(DeleteWorkspaceAPIKeyResponse, a, b);
  }
}

/**
 * @generated from message akuity.apikey.v1.RegenerateWorkspaceAPIKeySecretRequest
 */
export class RegenerateWorkspaceAPIKeySecretRequest extends Message<RegenerateWorkspaceAPIKeySecretRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string workspace_id = 2;
   */
  workspaceId = "";

  /**
   * @generated from field: string id = 3;
   */
  id = "";

  /**
   * @generated from field: string expiry = 4;
   */
  expiry = "";

  constructor(data?: PartialMessage<RegenerateWorkspaceAPIKeySecretRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.apikey.v1.RegenerateWorkspaceAPIKeySecretRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "expiry", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RegenerateWorkspaceAPIKeySecretRequest {
    return new RegenerateWorkspaceAPIKeySecretRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RegenerateWorkspaceAPIKeySecretRequest {
    return new RegenerateWorkspaceAPIKeySecretRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RegenerateWorkspaceAPIKeySecretRequest {
    return new RegenerateWorkspaceAPIKeySecretRequest().fromJsonString(jsonString, options);
  }

  static equals(a: RegenerateWorkspaceAPIKeySecretRequest | PlainMessage<RegenerateWorkspaceAPIKeySecretRequest> | undefined, b: RegenerateWorkspaceAPIKeySecretRequest | PlainMessage<RegenerateWorkspaceAPIKeySecretRequest> | undefined): boolean {
    return proto3.util.equals(RegenerateWorkspaceAPIKeySecretRequest, a, b);
  }
}

/**
 * @generated from message akuity.apikey.v1.RegenerateWorkspaceAPIKeySecretResponse
 */
export class RegenerateWorkspaceAPIKeySecretResponse extends Message<RegenerateWorkspaceAPIKeySecretResponse> {
  /**
   * @generated from field: akuity.apikey.v1.APIKey api_key = 1;
   */
  apiKey?: APIKey;

  constructor(data?: PartialMessage<RegenerateWorkspaceAPIKeySecretResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.apikey.v1.RegenerateWorkspaceAPIKeySecretResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "api_key", kind: "message", T: APIKey },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RegenerateWorkspaceAPIKeySecretResponse {
    return new RegenerateWorkspaceAPIKeySecretResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RegenerateWorkspaceAPIKeySecretResponse {
    return new RegenerateWorkspaceAPIKeySecretResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RegenerateWorkspaceAPIKeySecretResponse {
    return new RegenerateWorkspaceAPIKeySecretResponse().fromJsonString(jsonString, options);
  }

  static equals(a: RegenerateWorkspaceAPIKeySecretResponse | PlainMessage<RegenerateWorkspaceAPIKeySecretResponse> | undefined, b: RegenerateWorkspaceAPIKeySecretResponse | PlainMessage<RegenerateWorkspaceAPIKeySecretResponse> | undefined): boolean {
    return proto3.util.equals(RegenerateWorkspaceAPIKeySecretResponse, a, b);
  }
}

/**
 * @generated from message akuity.apikey.v1.GetAPIKeyRequest
 */
export class GetAPIKeyRequest extends Message<GetAPIKeyRequest> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  constructor(data?: PartialMessage<GetAPIKeyRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.apikey.v1.GetAPIKeyRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAPIKeyRequest {
    return new GetAPIKeyRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAPIKeyRequest {
    return new GetAPIKeyRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAPIKeyRequest {
    return new GetAPIKeyRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetAPIKeyRequest | PlainMessage<GetAPIKeyRequest> | undefined, b: GetAPIKeyRequest | PlainMessage<GetAPIKeyRequest> | undefined): boolean {
    return proto3.util.equals(GetAPIKeyRequest, a, b);
  }
}

/**
 * @generated from message akuity.apikey.v1.GetAPIKeyResponse
 */
export class GetAPIKeyResponse extends Message<GetAPIKeyResponse> {
  /**
   * @generated from field: akuity.apikey.v1.APIKey api_key = 1;
   */
  apiKey?: APIKey;

  constructor(data?: PartialMessage<GetAPIKeyResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.apikey.v1.GetAPIKeyResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "api_key", kind: "message", T: APIKey },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAPIKeyResponse {
    return new GetAPIKeyResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAPIKeyResponse {
    return new GetAPIKeyResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAPIKeyResponse {
    return new GetAPIKeyResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetAPIKeyResponse | PlainMessage<GetAPIKeyResponse> | undefined, b: GetAPIKeyResponse | PlainMessage<GetAPIKeyResponse> | undefined): boolean {
    return proto3.util.equals(GetAPIKeyResponse, a, b);
  }
}

/**
 * @generated from message akuity.apikey.v1.DeleteAPIKeyRequest
 */
export class DeleteAPIKeyRequest extends Message<DeleteAPIKeyRequest> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  constructor(data?: PartialMessage<DeleteAPIKeyRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.apikey.v1.DeleteAPIKeyRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteAPIKeyRequest {
    return new DeleteAPIKeyRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteAPIKeyRequest {
    return new DeleteAPIKeyRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteAPIKeyRequest {
    return new DeleteAPIKeyRequest().fromJsonString(jsonString, options);
  }

  static equals(a: DeleteAPIKeyRequest | PlainMessage<DeleteAPIKeyRequest> | undefined, b: DeleteAPIKeyRequest | PlainMessage<DeleteAPIKeyRequest> | undefined): boolean {
    return proto3.util.equals(DeleteAPIKeyRequest, a, b);
  }
}

/**
 * explicitly empty 
 *
 * @generated from message akuity.apikey.v1.DeleteAPIKeyResponse
 */
export class DeleteAPIKeyResponse extends Message<DeleteAPIKeyResponse> {
  constructor(data?: PartialMessage<DeleteAPIKeyResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.apikey.v1.DeleteAPIKeyResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteAPIKeyResponse {
    return new DeleteAPIKeyResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteAPIKeyResponse {
    return new DeleteAPIKeyResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteAPIKeyResponse {
    return new DeleteAPIKeyResponse().fromJsonString(jsonString, options);
  }

  static equals(a: DeleteAPIKeyResponse | PlainMessage<DeleteAPIKeyResponse> | undefined, b: DeleteAPIKeyResponse | PlainMessage<DeleteAPIKeyResponse> | undefined): boolean {
    return proto3.util.equals(DeleteAPIKeyResponse, a, b);
  }
}

/**
 * @generated from message akuity.apikey.v1.RegenerateAPIKeySecretRequest
 */
export class RegenerateAPIKeySecretRequest extends Message<RegenerateAPIKeySecretRequest> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  /**
   * @generated from field: string expiry = 2;
   */
  expiry = "";

  constructor(data?: PartialMessage<RegenerateAPIKeySecretRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.apikey.v1.RegenerateAPIKeySecretRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "expiry", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RegenerateAPIKeySecretRequest {
    return new RegenerateAPIKeySecretRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RegenerateAPIKeySecretRequest {
    return new RegenerateAPIKeySecretRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RegenerateAPIKeySecretRequest {
    return new RegenerateAPIKeySecretRequest().fromJsonString(jsonString, options);
  }

  static equals(a: RegenerateAPIKeySecretRequest | PlainMessage<RegenerateAPIKeySecretRequest> | undefined, b: RegenerateAPIKeySecretRequest | PlainMessage<RegenerateAPIKeySecretRequest> | undefined): boolean {
    return proto3.util.equals(RegenerateAPIKeySecretRequest, a, b);
  }
}

/**
 * @generated from message akuity.apikey.v1.RegenerateAPIKeySecretResponse
 */
export class RegenerateAPIKeySecretResponse extends Message<RegenerateAPIKeySecretResponse> {
  /**
   * @generated from field: akuity.apikey.v1.APIKey api_key = 1;
   */
  apiKey?: APIKey;

  constructor(data?: PartialMessage<RegenerateAPIKeySecretResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.apikey.v1.RegenerateAPIKeySecretResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "api_key", kind: "message", T: APIKey },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RegenerateAPIKeySecretResponse {
    return new RegenerateAPIKeySecretResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RegenerateAPIKeySecretResponse {
    return new RegenerateAPIKeySecretResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RegenerateAPIKeySecretResponse {
    return new RegenerateAPIKeySecretResponse().fromJsonString(jsonString, options);
  }

  static equals(a: RegenerateAPIKeySecretResponse | PlainMessage<RegenerateAPIKeySecretResponse> | undefined, b: RegenerateAPIKeySecretResponse | PlainMessage<RegenerateAPIKeySecretResponse> | undefined): boolean {
    return proto3.util.equals(RegenerateAPIKeySecretResponse, a, b);
  }
}

/**
 * @generated from message akuity.apikey.v1.APIKey
 */
export class APIKey extends Message<APIKey> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  /**
   * @generated from field: string description = 2;
   */
  description = "";

  /**
   * @generated from field: optional string secret = 3;
   */
  secret?: string;

  /**
   * @generated from field: string organization_id = 4;
   */
  organizationId = "";

  /**
   * @generated from field: akuity.accesscontrol.v1.Permissions permissions = 5;
   */
  permissions?: Permissions;

  /**
   * @generated from field: google.protobuf.Timestamp create_time = 6;
   */
  createTime?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp expire_time = 7;
   */
  expireTime?: Timestamp;

  constructor(data?: PartialMessage<APIKey>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.apikey.v1.APIKey";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "description", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "secret", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 4, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "permissions", kind: "message", T: Permissions },
    { no: 6, name: "create_time", kind: "message", T: Timestamp },
    { no: 7, name: "expire_time", kind: "message", T: Timestamp },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): APIKey {
    return new APIKey().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): APIKey {
    return new APIKey().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): APIKey {
    return new APIKey().fromJsonString(jsonString, options);
  }

  static equals(a: APIKey | PlainMessage<APIKey> | undefined, b: APIKey | PlainMessage<APIKey> | undefined): boolean {
    return proto3.util.equals(APIKey, a, b);
  }
}

