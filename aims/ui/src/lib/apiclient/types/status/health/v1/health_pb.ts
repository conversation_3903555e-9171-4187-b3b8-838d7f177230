// @generated by protoc-gen-es v1.2.0 with parameter "target=ts"
// @generated from file types/status/health/v1/health.proto (package akuity.types.status.health.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3, protoInt64 } from "@bufbuild/protobuf";

/**
 * @generated from enum akuity.types.status.health.v1.StatusCode
 */
export enum StatusCode {
  /**
   * @generated from enum value: STATUS_CODE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: STATUS_CODE_HEALTHY = 1;
   */
  HEALTHY = 1,

  /**
   * @generated from enum value: STATUS_CODE_PROGRESSING = 2;
   */
  PROGRESSING = 2,

  /**
   * @generated from enum value: STATUS_CODE_DEGRADED = 3;
   */
  DEGRADED = 3,

  /**
   * @generated from enum value: STATUS_CODE_UNKNOWN = 4;
   */
  UNKNOWN = 4,
}
// Retrieve enum metadata with: proto3.getEnumType(StatusCode)
proto3.util.setEnumType(StatusCode, "akuity.types.status.health.v1.StatusCode", [
  { no: 0, name: "STATUS_CODE_UNSPECIFIED" },
  { no: 1, name: "STATUS_CODE_HEALTHY" },
  { no: 2, name: "STATUS_CODE_PROGRESSING" },
  { no: 3, name: "STATUS_CODE_DEGRADED" },
  { no: 4, name: "STATUS_CODE_UNKNOWN" },
]);

/**
 * @generated from enum akuity.types.status.health.v1.TenantPhase
 */
export enum TenantPhase {
  /**
   * @generated from enum value: TENANT_PHASE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: TENANT_PHASE_HEALTHY = 1;
   */
  HEALTHY = 1,

  /**
   * @generated from enum value: TENANT_PHASE_PROGRESSING = 2;
   */
  PROGRESSING = 2,

  /**
   * @generated from enum value: TENANT_PHASE_DEGRADED = 3;
   */
  DEGRADED = 3,

  /**
   * @generated from enum value: TENANT_PHASE_UNKNOWN = 4;
   */
  UNKNOWN = 4,
}
// Retrieve enum metadata with: proto3.getEnumType(TenantPhase)
proto3.util.setEnumType(TenantPhase, "akuity.types.status.health.v1.TenantPhase", [
  { no: 0, name: "TENANT_PHASE_UNSPECIFIED" },
  { no: 1, name: "TENANT_PHASE_HEALTHY" },
  { no: 2, name: "TENANT_PHASE_PROGRESSING" },
  { no: 3, name: "TENANT_PHASE_DEGRADED" },
  { no: 4, name: "TENANT_PHASE_UNKNOWN" },
]);

/**
 * @generated from message akuity.types.status.health.v1.Status
 */
export class Status extends Message<Status> {
  /**
   * @generated from field: akuity.types.status.health.v1.StatusCode code = 1;
   */
  code = StatusCode.UNSPECIFIED;

  /**
   * @generated from field: string message = 2;
   */
  message = "";

  constructor(data?: PartialMessage<Status>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.types.status.health.v1.Status";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "code", kind: "enum", T: proto3.getEnumType(StatusCode) },
    { no: 2, name: "message", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Status {
    return new Status().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Status {
    return new Status().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Status {
    return new Status().fromJsonString(jsonString, options);
  }

  static equals(a: Status | PlainMessage<Status> | undefined, b: Status | PlainMessage<Status> | undefined): boolean {
    return proto3.util.equals(Status, a, b);
  }
}

/**
 * @generated from message akuity.types.status.health.v1.AgentHealthStatus
 */
export class AgentHealthStatus extends Message<AgentHealthStatus> {
  /**
   * @generated from field: uint64 observed_generation = 1;
   */
  observedGeneration = protoInt64.zero;

  /**
   * @generated from field: akuity.types.status.health.v1.TenantPhase status = 2;
   */
  status = TenantPhase.UNSPECIFIED;

  /**
   * @generated from field: string message = 3;
   */
  message = "";

  constructor(data?: PartialMessage<AgentHealthStatus>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.types.status.health.v1.AgentHealthStatus";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "observed_generation", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 2, name: "status", kind: "enum", T: proto3.getEnumType(TenantPhase) },
    { no: 3, name: "message", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AgentHealthStatus {
    return new AgentHealthStatus().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AgentHealthStatus {
    return new AgentHealthStatus().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AgentHealthStatus {
    return new AgentHealthStatus().fromJsonString(jsonString, options);
  }

  static equals(a: AgentHealthStatus | PlainMessage<AgentHealthStatus> | undefined, b: AgentHealthStatus | PlainMessage<AgentHealthStatus> | undefined): boolean {
    return proto3.util.equals(AgentHealthStatus, a, b);
  }
}

/**
 * @generated from message akuity.types.status.health.v1.AgentAggregatedHealthResponse
 */
export class AgentAggregatedHealthResponse extends Message<AgentAggregatedHealthResponse> {
  /**
   * @generated from field: uint64 min_observed_generation = 1;
   */
  minObservedGeneration = protoInt64.zero;

  /**
   * @generated from field: map<string, akuity.types.status.health.v1.AgentHealthStatus> healthy = 2;
   */
  healthy: { [key: string]: AgentHealthStatus } = {};

  /**
   * @generated from field: map<string, akuity.types.status.health.v1.AgentHealthStatus> progressing = 3;
   */
  progressing: { [key: string]: AgentHealthStatus } = {};

  /**
   * @generated from field: map<string, akuity.types.status.health.v1.AgentHealthStatus> degraded = 4;
   */
  degraded: { [key: string]: AgentHealthStatus } = {};

  /**
   * @generated from field: map<string, akuity.types.status.health.v1.AgentHealthStatus> unknown = 5;
   */
  unknown: { [key: string]: AgentHealthStatus } = {};

  /**
   * @generated from field: akuity.types.status.health.v1.TenantPhase priority_status = 6;
   */
  priorityStatus = TenantPhase.UNSPECIFIED;

  constructor(data?: PartialMessage<AgentAggregatedHealthResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.types.status.health.v1.AgentAggregatedHealthResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "min_observed_generation", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 2, name: "healthy", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "message", T: AgentHealthStatus} },
    { no: 3, name: "progressing", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "message", T: AgentHealthStatus} },
    { no: 4, name: "degraded", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "message", T: AgentHealthStatus} },
    { no: 5, name: "unknown", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "message", T: AgentHealthStatus} },
    { no: 6, name: "priority_status", kind: "enum", T: proto3.getEnumType(TenantPhase) },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AgentAggregatedHealthResponse {
    return new AgentAggregatedHealthResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AgentAggregatedHealthResponse {
    return new AgentAggregatedHealthResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AgentAggregatedHealthResponse {
    return new AgentAggregatedHealthResponse().fromJsonString(jsonString, options);
  }

  static equals(a: AgentAggregatedHealthResponse | PlainMessage<AgentAggregatedHealthResponse> | undefined, b: AgentAggregatedHealthResponse | PlainMessage<AgentAggregatedHealthResponse> | undefined): boolean {
    return proto3.util.equals(AgentAggregatedHealthResponse, a, b);
  }
}

