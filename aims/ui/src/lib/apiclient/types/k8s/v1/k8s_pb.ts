// @generated by protoc-gen-es v1.2.0 with parameter "target=ts"
// @generated from file types/k8s/v1/k8s.proto (package akuity.types.k8s.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from enum akuity.types.k8s.v1.DeprecatedInfoSeverity
 */
export enum DeprecatedInfoSeverity {
  /**
   * @generated from enum value: DEPRECATED_INFO_SEVERITY_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: DEPRECATED_INFO_SEVERITY_LOW = 1;
   */
  LOW = 1,

  /**
   * @generated from enum value: DEPRECATED_INFO_SEVERITY_MEDIUM = 2;
   */
  MEDIUM = 2,

  /**
   * @generated from enum value: DEPRECATED_INFO_SEVERITY_HIGH = 3;
   */
  HIGH = 3,

  /**
   * @generated from enum value: DEPRECATED_INFO_SEVERITY_CRITICAL = 4;
   */
  CRITICAL = 4,
}
// Retrieve enum metadata with: proto3.getEnumType(DeprecatedInfoSeverity)
proto3.util.setEnumType(DeprecatedInfoSeverity, "akuity.types.k8s.v1.DeprecatedInfoSeverity", [
  { no: 0, name: "DEPRECATED_INFO_SEVERITY_UNSPECIFIED" },
  { no: 1, name: "DEPRECATED_INFO_SEVERITY_LOW" },
  { no: 2, name: "DEPRECATED_INFO_SEVERITY_MEDIUM" },
  { no: 3, name: "DEPRECATED_INFO_SEVERITY_HIGH" },
  { no: 4, name: "DEPRECATED_INFO_SEVERITY_CRITICAL" },
]);

/**
 * @generated from enum akuity.types.k8s.v1.ResourceCategory
 */
export enum ResourceCategory {
  /**
   * @generated from enum value: RESOURCE_CATEGORY_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: RESOURCE_CATEGORY_WORKLOADS = 1;
   */
  WORKLOADS = 1,

  /**
   * @generated from enum value: RESOURCE_CATEGORY_NETWORKING = 2;
   */
  NETWORKING = 2,

  /**
   * @generated from enum value: RESOURCE_CATEGORY_STORAGE = 3;
   */
  STORAGE = 3,

  /**
   * @generated from enum value: RESOURCE_CATEGORY_CONFIGURATION = 4;
   */
  CONFIGURATION = 4,

  /**
   * @generated from enum value: RESOURCE_CATEGORY_RESOURCE_POLICY = 5;
   */
  RESOURCE_POLICY = 5,

  /**
   * @generated from enum value: RESOURCE_CATEGORY_RBAC = 6;
   */
  RBAC = 6,

  /**
   * @generated from enum value: RESOURCE_CATEGORY_CLUSTER_MANAGEMENT = 7;
   */
  CLUSTER_MANAGEMENT = 7,

  /**
   * @generated from enum value: RESOURCE_CATEGORY_ADMISSION = 8;
   */
  ADMISSION = 8,

  /**
   * @generated from enum value: RESOURCE_CATEGORY_CUSTOM_RESOURCE = 9;
   */
  CUSTOM_RESOURCE = 9,

  /**
   * @generated from enum value: RESOURCE_CATEGORY_OTHERS = 10;
   */
  OTHERS = 10,
}
// Retrieve enum metadata with: proto3.getEnumType(ResourceCategory)
proto3.util.setEnumType(ResourceCategory, "akuity.types.k8s.v1.ResourceCategory", [
  { no: 0, name: "RESOURCE_CATEGORY_UNSPECIFIED" },
  { no: 1, name: "RESOURCE_CATEGORY_WORKLOADS" },
  { no: 2, name: "RESOURCE_CATEGORY_NETWORKING" },
  { no: 3, name: "RESOURCE_CATEGORY_STORAGE" },
  { no: 4, name: "RESOURCE_CATEGORY_CONFIGURATION" },
  { no: 5, name: "RESOURCE_CATEGORY_RESOURCE_POLICY" },
  { no: 6, name: "RESOURCE_CATEGORY_RBAC" },
  { no: 7, name: "RESOURCE_CATEGORY_CLUSTER_MANAGEMENT" },
  { no: 8, name: "RESOURCE_CATEGORY_ADMISSION" },
  { no: 9, name: "RESOURCE_CATEGORY_CUSTOM_RESOURCE" },
  { no: 10, name: "RESOURCE_CATEGORY_OTHERS" },
]);

/**
 * @generated from message akuity.types.k8s.v1.ResourceType
 */
export class ResourceType extends Message<ResourceType> {
  /**
   * @generated from field: akuity.types.k8s.v1.GroupVersionKind group_version_kind = 1;
   */
  groupVersionKind?: GroupVersionKind;

  /**
   * @generated from field: repeated akuity.types.k8s.v1.ColumnInfo columns = 2;
   */
  columns: ColumnInfo[] = [];

  /**
   * @generated from field: optional akuity.types.k8s.v1.ResourceCategory category = 3;
   */
  category?: ResourceCategory;

  /**
   * @generated from field: optional akuity.types.k8s.v1.DeprecatedInfo deprecated_info = 4;
   */
  deprecatedInfo?: DeprecatedInfo;

  /**
   * @generated from field: bool cluster_scoped = 5;
   */
  clusterScoped = false;

  constructor(data?: PartialMessage<ResourceType>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.types.k8s.v1.ResourceType";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "group_version_kind", kind: "message", T: GroupVersionKind },
    { no: 2, name: "columns", kind: "message", T: ColumnInfo, repeated: true },
    { no: 3, name: "category", kind: "enum", T: proto3.getEnumType(ResourceCategory), opt: true },
    { no: 4, name: "deprecated_info", kind: "message", T: DeprecatedInfo, opt: true },
    { no: 5, name: "cluster_scoped", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ResourceType {
    return new ResourceType().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ResourceType {
    return new ResourceType().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ResourceType {
    return new ResourceType().fromJsonString(jsonString, options);
  }

  static equals(a: ResourceType | PlainMessage<ResourceType> | undefined, b: ResourceType | PlainMessage<ResourceType> | undefined): boolean {
    return proto3.util.equals(ResourceType, a, b);
  }
}

/**
 * @generated from message akuity.types.k8s.v1.DeprecatedInfo
 */
export class DeprecatedInfo extends Message<DeprecatedInfo> {
  /**
   * @generated from field: bool deprecated = 1;
   */
  deprecated = false;

  /**
   * @generated from field: string message = 2;
   */
  message = "";

  /**
   * @generated from field: optional string deprecated_in = 3;
   */
  deprecatedIn?: string;

  /**
   * @generated from field: optional string unavailable_in = 4;
   */
  unavailableIn?: string;

  /**
   * @generated from field: optional akuity.types.k8s.v1.GroupVersionKind migrate_to = 5;
   */
  migrateTo?: GroupVersionKind;

  /**
   * @generated from field: optional string cluster_id = 6;
   */
  clusterId?: string;

  /**
   * @generated from field: optional akuity.types.k8s.v1.GroupVersionKind group_version_kind = 7;
   */
  groupVersionKind?: GroupVersionKind;

  /**
   * @generated from field: optional uint32 resource_count = 8;
   */
  resourceCount?: number;

  /**
   * @generated from field: optional string kubernetes_version = 9;
   */
  kubernetesVersion?: string;

  /**
   * @generated from field: optional string instance_id = 10;
   */
  instanceId?: string;

  /**
   * @generated from field: akuity.types.k8s.v1.DeprecatedInfoSeverity severity = 11;
   */
  severity = DeprecatedInfoSeverity.UNSPECIFIED;

  constructor(data?: PartialMessage<DeprecatedInfo>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.types.k8s.v1.DeprecatedInfo";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "deprecated", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 2, name: "message", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "deprecated_in", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 4, name: "unavailable_in", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 5, name: "migrate_to", kind: "message", T: GroupVersionKind, opt: true },
    { no: 6, name: "cluster_id", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 7, name: "group_version_kind", kind: "message", T: GroupVersionKind, opt: true },
    { no: 8, name: "resource_count", kind: "scalar", T: 13 /* ScalarType.UINT32 */, opt: true },
    { no: 9, name: "kubernetes_version", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 10, name: "instance_id", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 11, name: "severity", kind: "enum", T: proto3.getEnumType(DeprecatedInfoSeverity) },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeprecatedInfo {
    return new DeprecatedInfo().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeprecatedInfo {
    return new DeprecatedInfo().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeprecatedInfo {
    return new DeprecatedInfo().fromJsonString(jsonString, options);
  }

  static equals(a: DeprecatedInfo | PlainMessage<DeprecatedInfo> | undefined, b: DeprecatedInfo | PlainMessage<DeprecatedInfo> | undefined): boolean {
    return proto3.util.equals(DeprecatedInfo, a, b);
  }
}

/**
 * @generated from message akuity.types.k8s.v1.GroupVersionKind
 */
export class GroupVersionKind extends Message<GroupVersionKind> {
  /**
   * @generated from field: string group = 1;
   */
  group = "";

  /**
   * @generated from field: string version = 2;
   */
  version = "";

  /**
   * @generated from field: string kind = 3;
   */
  kind = "";

  constructor(data?: PartialMessage<GroupVersionKind>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.types.k8s.v1.GroupVersionKind";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "group", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "version", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "kind", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GroupVersionKind {
    return new GroupVersionKind().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GroupVersionKind {
    return new GroupVersionKind().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GroupVersionKind {
    return new GroupVersionKind().fromJsonString(jsonString, options);
  }

  static equals(a: GroupVersionKind | PlainMessage<GroupVersionKind> | undefined, b: GroupVersionKind | PlainMessage<GroupVersionKind> | undefined): boolean {
    return proto3.util.equals(GroupVersionKind, a, b);
  }
}

/**
 * @generated from message akuity.types.k8s.v1.ColumnInfo
 */
export class ColumnInfo extends Message<ColumnInfo> {
  /**
   * @generated from field: string name = 1;
   */
  name = "";

  /**
   * @generated from field: string title = 2;
   */
  title = "";

  constructor(data?: PartialMessage<ColumnInfo>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.types.k8s.v1.ColumnInfo";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "title", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ColumnInfo {
    return new ColumnInfo().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ColumnInfo {
    return new ColumnInfo().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ColumnInfo {
    return new ColumnInfo().fromJsonString(jsonString, options);
  }

  static equals(a: ColumnInfo | PlainMessage<ColumnInfo> | undefined, b: ColumnInfo | PlainMessage<ColumnInfo> | undefined): boolean {
    return proto3.util.equals(ColumnInfo, a, b);
  }
}

