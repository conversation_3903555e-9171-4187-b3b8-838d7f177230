// @generated by protoc-gen-es v1.2.0 with parameter "target=ts"
// @generated from file types/features/v1/features.proto (package akuity.types.features.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3, protoInt64 } from "@bufbuild/protobuf";

/**
 * @generated from enum akuity.types.features.v1.FeatureStatus
 */
export enum FeatureStatus {
  /**
   * @generated from enum value: FEATURE_STATUS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: FEATURE_STATUS_NOT_AVAILABLE = 1;
   */
  NOT_AVAILABLE = 1,

  /**
   * @generated from enum value: FEATURE_STATUS_ENABLED = 2;
   */
  ENABLED = 2,

  /**
   * @generated from enum value: FEATURE_STATUS_DISABLED = 3;
   */
  DISABLED = 3,
}
// Retrieve enum metadata with: proto3.getEnumType(FeatureStatus)
proto3.util.setEnumType(FeatureStatus, "akuity.types.features.v1.FeatureStatus", [
  { no: 0, name: "FEATURE_STATUS_UNSPECIFIED" },
  { no: 1, name: "FEATURE_STATUS_NOT_AVAILABLE" },
  { no: 2, name: "FEATURE_STATUS_ENABLED" },
  { no: 3, name: "FEATURE_STATUS_DISABLED" },
]);

/**
 * @generated from message akuity.types.features.v1.SystemFeatureGates
 */
export class SystemFeatureGates extends Message<SystemFeatureGates> {
  /**
   * @generated from field: bool sso = 1;
   */
  sso = false;

  /**
   * @generated from field: bool kargo = 2;
   */
  kargo = false;

  /**
   * @generated from field: bool autoscaler = 3;
   */
  autoscaler = false;

  /**
   * @generated from field: bool k3s_proxy_informers = 4;
   */
  k3sProxyInformers = false;

  /**
   * @generated from field: bool ai_assistant_stats = 5;
   */
  aiAssistantStats = false;

  /**
   * @generated from field: bool agent_permissions = 6;
   */
  agentPermissions = false;

  /**
   * @generated from field: bool team = 7;
   */
  team = false;

  /**
   * @generated from field: bool self_serve_cancel = 8;
   */
  selfServeCancel = false;

  /**
   * @generated from field: bool k3s_cert_cn_reset = 9;
   */
  k3sCertCnReset = false;

  /**
   * @generated from field: bool notification = 10;
   */
  notification = false;

  /**
   * @generated from field: bool multi_cluster_k8s_dashboard = 11;
   */
  multiClusterK8sDashboard = false;

  /**
   * @generated from field: bool cluster_autoscaler = 12;
   */
  clusterAutoscaler = false;

  /**
   * @generated from field: bool fleet_management = 13;
   */
  fleetManagement = false;

  /**
   * @generated from field: bool ai_support_engineer = 14;
   */
  aiSupportEngineer = false;

  /**
   * @generated from field: bool secret_management = 15;
   */
  secretManagement = false;

  /**
   * @generated from field: bool kargo_enterprise = 16;
   */
  kargoEnterprise = false;

  constructor(data?: PartialMessage<SystemFeatureGates>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.types.features.v1.SystemFeatureGates";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "sso", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 2, name: "kargo", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 3, name: "autoscaler", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 4, name: "k3s_proxy_informers", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 5, name: "ai_assistant_stats", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 6, name: "agent_permissions", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 7, name: "team", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 8, name: "self_serve_cancel", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 9, name: "k3s_cert_cn_reset", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 10, name: "notification", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 11, name: "multi_cluster_k8s_dashboard", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 12, name: "cluster_autoscaler", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 13, name: "fleet_management", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 14, name: "ai_support_engineer", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 15, name: "secret_management", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 16, name: "kargo_enterprise", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SystemFeatureGates {
    return new SystemFeatureGates().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SystemFeatureGates {
    return new SystemFeatureGates().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SystemFeatureGates {
    return new SystemFeatureGates().fromJsonString(jsonString, options);
  }

  static equals(a: SystemFeatureGates | PlainMessage<SystemFeatureGates> | undefined, b: SystemFeatureGates | PlainMessage<SystemFeatureGates> | undefined): boolean {
    return proto3.util.equals(SystemFeatureGates, a, b);
  }
}

/**
 * @generated from message akuity.types.features.v1.OrganizationFeatureGates
 */
export class OrganizationFeatureGates extends Message<OrganizationFeatureGates> {
  /**
   * @generated from field: optional bool sso = 1;
   */
  sso?: boolean;

  /**
   * @generated from field: repeated string shards = 2;
   */
  shards: string[] = [];

  /**
   * @generated from field: optional bool kargo = 3;
   */
  kargo?: boolean;

  /**
   * @generated from field: optional bool k3s_proxy_informers = 4;
   */
  k3sProxyInformers?: boolean;

  /**
   * @generated from field: optional bool team = 5;
   */
  team?: boolean;

  /**
   * @generated from field: optional bool audit_record_export = 6;
   */
  auditRecordExport?: boolean;

  /**
   * @generated from field: optional bool workspaces = 7;
   */
  workspaces?: boolean;

  /**
   * @generated from field: optional bool custom_roles = 8;
   */
  customRoles?: boolean;

  /**
   * @generated from field: optional bool scoped_api_keys = 9;
   */
  scopedApiKeys?: boolean;

  /**
   * @generated from field: optional bool argocd_sso = 10;
   */
  argocdSso?: boolean;

  /**
   * @generated from field: optional bool argocd_ha_control_plane = 11;
   */
  argocdHaControlPlane?: boolean;

  /**
   * @generated from field: optional bool akuity_argocd_extensions = 12;
   */
  akuityArgocdExtensions?: boolean;

  /**
   * @generated from field: optional bool app_of_apps = 13;
   */
  appOfApps?: boolean;

  /**
   * @generated from field: optional bool application_set_controller = 14;
   */
  applicationSetController?: boolean;

  /**
   * @generated from field: optional bool argocd_custom_subdomain = 15;
   */
  argocdCustomSubdomain?: boolean;

  /**
   * @generated from field: optional bool argocd_custom_domain = 16;
   */
  argocdCustomDomain?: boolean;

  /**
   * @generated from field: optional bool argocd_flexible_architecture = 17;
   */
  argocdFlexibleArchitecture?: boolean;

  /**
   * @generated from field: optional bool argocd_agent_state_replication = 18;
   */
  argocdAgentStateReplication?: boolean;

  /**
   * @generated from field: optional bool argocd_deep_links = 19;
   */
  argocdDeepLinks?: boolean;

  /**
   * @generated from field: optional bool argocd_custom_styles = 20;
   */
  argocdCustomStyles?: boolean;

  /**
   * @generated from field: optional bool config_management_plugins = 21;
   */
  configManagementPlugins?: boolean;

  /**
   * @generated from field: optional bool audit_archive = 22;
   */
  auditArchive?: boolean;

  /**
   * @generated from field: optional bool multi_cluster_k8s_dashboard = 23;
   */
  multiClusterK8sDashboard?: boolean;

  /**
   * @generated from field: optional bool argocd_cluster_integration = 24;
   */
  argocdClusterIntegration?: boolean;

  /**
   * @generated from field: optional bool notification = 25;
   */
  notification?: boolean;

  /**
   * @generated from field: optional bool cluster_autoscaler = 26;
   */
  clusterAutoscaler?: boolean;

  /**
   * @generated from field: optional bool fleet_management = 27;
   */
  fleetManagement?: boolean;

  /**
   * @generated from field: optional bool pgpool = 28;
   */
  pgpool?: boolean;

  /**
   * @generated from field: optional bool ai_support_engineer = 29;
   */
  aiSupportEngineer?: boolean;

  /**
   * @generated from field: optional bool pgbouncer = 30;
   */
  pgbouncer?: boolean;

  /**
   * @generated from field: optional bool multi_factor_auth = 31;
   */
  multiFactorAuth?: boolean;

  /**
   * @generated from field: optional bool kargo_analysis_logs = 32;
   */
  kargoAnalysisLogs?: boolean;

  /**
   * @generated from field: optional bool kargo_enterprise = 33;
   */
  kargoEnterprise?: boolean;

  /**
   * @generated from field: optional bool oidc_map = 34;
   */
  oidcMap?: boolean;

  /**
   * @generated from field: optional bool eks_addon = 35;
   */
  eksAddon?: boolean;

  /**
   * @generated from field: optional bool k3s_traffic_reduction = 36;
   */
  k3sTrafficReduction?: boolean;

  /**
   * @generated from field: optional bool redis_traffic_reduction = 37;
   */
  redisTrafficReduction?: boolean;

  constructor(data?: PartialMessage<OrganizationFeatureGates>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.types.features.v1.OrganizationFeatureGates";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "sso", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 2, name: "shards", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 3, name: "kargo", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 4, name: "k3s_proxy_informers", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 5, name: "team", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 6, name: "audit_record_export", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 7, name: "workspaces", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 8, name: "custom_roles", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 9, name: "scoped_api_keys", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 10, name: "argocd_sso", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 11, name: "argocd_ha_control_plane", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 12, name: "akuity_argocd_extensions", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 13, name: "app_of_apps", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 14, name: "application_set_controller", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 15, name: "argocd_custom_subdomain", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 16, name: "argocd_custom_domain", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 17, name: "argocd_flexible_architecture", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 18, name: "argocd_agent_state_replication", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 19, name: "argocd_deep_links", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 20, name: "argocd_custom_styles", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 21, name: "config_management_plugins", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 22, name: "audit_archive", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 23, name: "multi_cluster_k8s_dashboard", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 24, name: "argocd_cluster_integration", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 25, name: "notification", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 26, name: "cluster_autoscaler", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 27, name: "fleet_management", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 28, name: "pgpool", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 29, name: "ai_support_engineer", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 30, name: "pgbouncer", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 31, name: "multi_factor_auth", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 32, name: "kargo_analysis_logs", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 33, name: "kargo_enterprise", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 34, name: "oidc_map", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 35, name: "eks_addon", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 36, name: "k3s_traffic_reduction", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 37, name: "redis_traffic_reduction", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OrganizationFeatureGates {
    return new OrganizationFeatureGates().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OrganizationFeatureGates {
    return new OrganizationFeatureGates().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OrganizationFeatureGates {
    return new OrganizationFeatureGates().fromJsonString(jsonString, options);
  }

  static equals(a: OrganizationFeatureGates | PlainMessage<OrganizationFeatureGates> | undefined, b: OrganizationFeatureGates | PlainMessage<OrganizationFeatureGates> | undefined): boolean {
    return proto3.util.equals(OrganizationFeatureGates, a, b);
  }
}

/**
 * @generated from message akuity.types.features.v1.FeatureStatuses
 */
export class FeatureStatuses extends Message<FeatureStatuses> {
  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus sso = 1;
   */
  sso = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus kargo = 2;
   */
  kargo = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus autoscaler = 3;
   */
  autoscaler = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus k3s_proxy_informers = 4;
   */
  k3sProxyInformers = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus ai_assistant_stats = 5;
   */
  aiAssistantStats = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus agent_permissions = 6;
   */
  agentPermissions = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus team = 7;
   */
  team = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus self_serve_cancel = 8;
   */
  selfServeCancel = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus k3s_cert_cn_reset = 9;
   */
  k3sCertCnReset = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus notification = 10;
   */
  notification = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus multi_cluster_k8s_dashboard = 11;
   */
  multiClusterK8sDashboard = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus cluster_autoscaler = 12;
   */
  clusterAutoscaler = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus fleet_management = 13;
   */
  fleetManagement = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus ai_support_engineer = 14;
   */
  aiSupportEngineer = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus secret_management = 15;
   */
  secretManagement = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: repeated string shards = 16;
   */
  shards: string[] = [];

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus audit_record_export = 17;
   */
  auditRecordExport = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus workspaces = 18;
   */
  workspaces = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus custom_roles = 19;
   */
  customRoles = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus scoped_api_keys = 20;
   */
  scopedApiKeys = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus argocd_sso = 21;
   */
  argocdSso = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus argocd_ha_control_plane = 22;
   */
  argocdHaControlPlane = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus akuity_argocd_extensions = 23;
   */
  akuityArgocdExtensions = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus app_of_apps = 24;
   */
  appOfApps = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus application_set_controller = 25;
   */
  applicationSetController = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus argocd_custom_subdomain = 26;
   */
  argocdCustomSubdomain = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus argocd_custom_domain = 27;
   */
  argocdCustomDomain = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus argocd_flexible_architecture = 28;
   */
  argocdFlexibleArchitecture = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus argocd_agent_state_replication = 29;
   */
  argocdAgentStateReplication = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus argocd_deep_links = 30;
   */
  argocdDeepLinks = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus argocd_custom_styles = 31;
   */
  argocdCustomStyles = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus config_management_plugins = 32;
   */
  configManagementPlugins = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus audit_archive = 33;
   */
  auditArchive = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus argocd_cluster_integration = 34;
   */
  argocdClusterIntegration = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus pgpool = 35;
   */
  pgpool = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus pgbouncer = 36;
   */
  pgbouncer = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus multi_factor_auth = 37;
   */
  multiFactorAuth = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus kargo_analysis_logs = 38;
   */
  kargoAnalysisLogs = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus kargo_enterprise = 39;
   */
  kargoEnterprise = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus oidc_map = 40;
   */
  oidcMap = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus eks_addon = 41;
   */
  eksAddon = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus k3s_traffic_reduction = 42;
   */
  k3sTrafficReduction = FeatureStatus.UNSPECIFIED;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatus redis_traffic_reduction = 43;
   */
  redisTrafficReduction = FeatureStatus.UNSPECIFIED;

  constructor(data?: PartialMessage<FeatureStatuses>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.types.features.v1.FeatureStatuses";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "sso", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 2, name: "kargo", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 3, name: "autoscaler", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 4, name: "k3s_proxy_informers", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 5, name: "ai_assistant_stats", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 6, name: "agent_permissions", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 7, name: "team", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 8, name: "self_serve_cancel", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 9, name: "k3s_cert_cn_reset", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 10, name: "notification", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 11, name: "multi_cluster_k8s_dashboard", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 12, name: "cluster_autoscaler", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 13, name: "fleet_management", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 14, name: "ai_support_engineer", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 15, name: "secret_management", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 16, name: "shards", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 17, name: "audit_record_export", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 18, name: "workspaces", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 19, name: "custom_roles", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 20, name: "scoped_api_keys", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 21, name: "argocd_sso", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 22, name: "argocd_ha_control_plane", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 23, name: "akuity_argocd_extensions", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 24, name: "app_of_apps", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 25, name: "application_set_controller", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 26, name: "argocd_custom_subdomain", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 27, name: "argocd_custom_domain", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 28, name: "argocd_flexible_architecture", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 29, name: "argocd_agent_state_replication", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 30, name: "argocd_deep_links", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 31, name: "argocd_custom_styles", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 32, name: "config_management_plugins", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 33, name: "audit_archive", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 34, name: "argocd_cluster_integration", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 35, name: "pgpool", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 36, name: "pgbouncer", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 37, name: "multi_factor_auth", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 38, name: "kargo_analysis_logs", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 39, name: "kargo_enterprise", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 40, name: "oidc_map", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 41, name: "eks_addon", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 42, name: "k3s_traffic_reduction", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
    { no: 43, name: "redis_traffic_reduction", kind: "enum", T: proto3.getEnumType(FeatureStatus) },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FeatureStatuses {
    return new FeatureStatuses().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FeatureStatuses {
    return new FeatureStatuses().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FeatureStatuses {
    return new FeatureStatuses().fromJsonString(jsonString, options);
  }

  static equals(a: FeatureStatuses | PlainMessage<FeatureStatuses> | undefined, b: FeatureStatuses | PlainMessage<FeatureStatuses> | undefined): boolean {
    return proto3.util.equals(FeatureStatuses, a, b);
  }
}

/**
 * OrganizationQuota is a quota for the given organization.
 * NOTE: It is encouraged to define quota fields in `double` or `int64` to
 * avoid type cast failure.
 *
 * @generated from message akuity.types.features.v1.OrganizationQuota
 */
export class OrganizationQuota extends Message<OrganizationQuota> {
  /**
   * @generated from field: int64 max_instances = 1;
   */
  maxInstances = protoInt64.zero;

  /**
   * @generated from field: int64 max_clusters = 2;
   */
  maxClusters = protoInt64.zero;

  /**
   * @generated from field: int64 max_applications = 3;
   */
  maxApplications = protoInt64.zero;

  /**
   * @generated from field: int64 max_kargo_instances = 4;
   */
  maxKargoInstances = protoInt64.zero;

  /**
   * Deprecated: we use stages now
   *
   * @generated from field: int64 max_kargo_projects = 5;
   */
  maxKargoProjects = protoInt64.zero;

  /**
   * @generated from field: int64 max_kargo_agents = 6;
   */
  maxKargoAgents = protoInt64.zero;

  /**
   * @generated from field: int64 audit_record_months = 7;
   */
  auditRecordMonths = protoInt64.zero;

  /**
   * @generated from field: int64 audit_record_archive_months = 8;
   */
  auditRecordArchiveMonths = protoInt64.zero;

  /**
   * @generated from field: int64 max_org_members = 9;
   */
  maxOrgMembers = protoInt64.zero;

  /**
   * @generated from field: int64 max_workspaces = 10;
   */
  maxWorkspaces = protoInt64.zero;

  /**
   * @generated from field: int64 max_kargo_stages = 11;
   */
  maxKargoStages = protoInt64.zero;

  /**
   * @generated from field: double max_ai_cost_per_month = 12;
   */
  maxAiCostPerMonth = 0;

  constructor(data?: PartialMessage<OrganizationQuota>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.types.features.v1.OrganizationQuota";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "max_instances", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 2, name: "max_clusters", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 3, name: "max_applications", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 4, name: "max_kargo_instances", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 5, name: "max_kargo_projects", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 6, name: "max_kargo_agents", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 7, name: "audit_record_months", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 8, name: "audit_record_archive_months", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 9, name: "max_org_members", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 10, name: "max_workspaces", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 11, name: "max_kargo_stages", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 12, name: "max_ai_cost_per_month", kind: "scalar", T: 1 /* ScalarType.DOUBLE */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OrganizationQuota {
    return new OrganizationQuota().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OrganizationQuota {
    return new OrganizationQuota().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OrganizationQuota {
    return new OrganizationQuota().fromJsonString(jsonString, options);
  }

  static equals(a: OrganizationQuota | PlainMessage<OrganizationQuota> | undefined, b: OrganizationQuota | PlainMessage<OrganizationQuota> | undefined): boolean {
    return proto3.util.equals(OrganizationQuota, a, b);
  }
}

/**
 * OrganizationUsage is the usage of resources for the given organization.
 *
 * @generated from message akuity.types.features.v1.OrganizationUsage
 */
export class OrganizationUsage extends Message<OrganizationUsage> {
  /**
   * @generated from field: int64 current_instances = 1;
   */
  currentInstances = protoInt64.zero;

  /**
   * @generated from field: int64 current_clusters = 2;
   */
  currentClusters = protoInt64.zero;

  /**
   * @generated from field: int64 current_applications = 3;
   */
  currentApplications = protoInt64.zero;

  /**
   * @generated from field: int64 current_kargo_instances = 4;
   */
  currentKargoInstances = protoInt64.zero;

  /**
   * Deprecated: we use stages now
   *
   * @generated from field: int64 current_kargo_projects = 5;
   */
  currentKargoProjects = protoInt64.zero;

  /**
   * @generated from field: int64 current_kargo_agents = 6;
   */
  currentKargoAgents = protoInt64.zero;

  /**
   * @generated from field: int64 current_org_members = 7;
   */
  currentOrgMembers = protoInt64.zero;

  /**
   * @generated from field: int64 current_workspaces = 8;
   */
  currentWorkspaces = protoInt64.zero;

  /**
   * @generated from field: int64 current_kargo_stages = 9;
   */
  currentKargoStages = protoInt64.zero;

  /**
   * @generated from field: int64 current_ai_input_tokens_per_month = 10;
   */
  currentAiInputTokensPerMonth = protoInt64.zero;

  /**
   * @generated from field: int64 current_ai_input_cached_tokens_per_month = 11;
   */
  currentAiInputCachedTokensPerMonth = protoInt64.zero;

  /**
   * @generated from field: int64 current_ai_output_tokens_per_month = 12;
   */
  currentAiOutputTokensPerMonth = protoInt64.zero;

  /**
   * @generated from field: double current_ai_cost_per_month = 13;
   */
  currentAiCostPerMonth = 0;

  constructor(data?: PartialMessage<OrganizationUsage>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.types.features.v1.OrganizationUsage";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "current_instances", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 2, name: "current_clusters", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 3, name: "current_applications", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 4, name: "current_kargo_instances", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 5, name: "current_kargo_projects", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 6, name: "current_kargo_agents", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 7, name: "current_org_members", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 8, name: "current_workspaces", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 9, name: "current_kargo_stages", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 10, name: "current_ai_input_tokens_per_month", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 11, name: "current_ai_input_cached_tokens_per_month", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 12, name: "current_ai_output_tokens_per_month", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 13, name: "current_ai_cost_per_month", kind: "scalar", T: 1 /* ScalarType.DOUBLE */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OrganizationUsage {
    return new OrganizationUsage().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OrganizationUsage {
    return new OrganizationUsage().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OrganizationUsage {
    return new OrganizationUsage().fromJsonString(jsonString, options);
  }

  static equals(a: OrganizationUsage | PlainMessage<OrganizationUsage> | undefined, b: OrganizationUsage | PlainMessage<OrganizationUsage> | undefined): boolean {
    return proto3.util.equals(OrganizationUsage, a, b);
  }
}

