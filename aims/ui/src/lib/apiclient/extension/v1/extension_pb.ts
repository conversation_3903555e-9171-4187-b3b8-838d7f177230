// @generated by protoc-gen-es v1.2.0 with parameter "target=ts"
// @generated from file extension/v1/extension.proto (package akuity.extension.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3, protoInt64 } from "@bufbuild/protobuf";
import { AuditFilters, AuditLog } from "../../organization/v1/organization_pb.js";
import { AkuityIntelligenceExtension, ApplicationSetExtension, CrossplaneExtension, KubeVisionConfig, SyncOperationEvent, SyncOperationField, SyncOperationFilter, SyncOperationGroupField, SyncOperationStat } from "../../argocd/v1/argocd_pb.js";
import { GroupByInterval } from "../../types/misc/v1/misc_pb.js";
import { AkuityIntelligence } from "../../kargo/v1/kargo_pb.js";
import { FeatureStatuses } from "../../types/features/v1/features_pb.js";

/**
 * @generated from message akuity.extension.v1.ListAuditRecordForKargoProjectsRequest
 */
export class ListAuditRecordForKargoProjectsRequest extends Message<ListAuditRecordForKargoProjectsRequest> {
  /**
   * @generated from field: akuity.organization.v1.AuditFilters filters = 1;
   */
  filters?: AuditFilters;

  /**
   * @generated from field: string project_name = 2;
   */
  projectName = "";

  constructor(data?: PartialMessage<ListAuditRecordForKargoProjectsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.extension.v1.ListAuditRecordForKargoProjectsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "filters", kind: "message", T: AuditFilters },
    { no: 2, name: "project_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListAuditRecordForKargoProjectsRequest {
    return new ListAuditRecordForKargoProjectsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListAuditRecordForKargoProjectsRequest {
    return new ListAuditRecordForKargoProjectsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListAuditRecordForKargoProjectsRequest {
    return new ListAuditRecordForKargoProjectsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ListAuditRecordForKargoProjectsRequest | PlainMessage<ListAuditRecordForKargoProjectsRequest> | undefined, b: ListAuditRecordForKargoProjectsRequest | PlainMessage<ListAuditRecordForKargoProjectsRequest> | undefined): boolean {
    return proto3.util.equals(ListAuditRecordForKargoProjectsRequest, a, b);
  }
}

/**
 * @generated from message akuity.extension.v1.ListAuditRecordForKargoProjectsResponse
 */
export class ListAuditRecordForKargoProjectsResponse extends Message<ListAuditRecordForKargoProjectsResponse> {
  /**
   * @generated from field: repeated akuity.organization.v1.AuditLog items = 1;
   */
  items: AuditLog[] = [];

  /**
   * @generated from field: uint32 total_count = 2;
   */
  totalCount = 0;

  constructor(data?: PartialMessage<ListAuditRecordForKargoProjectsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.extension.v1.ListAuditRecordForKargoProjectsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "items", kind: "message", T: AuditLog, repeated: true },
    { no: 2, name: "total_count", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListAuditRecordForKargoProjectsResponse {
    return new ListAuditRecordForKargoProjectsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListAuditRecordForKargoProjectsResponse {
    return new ListAuditRecordForKargoProjectsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListAuditRecordForKargoProjectsResponse {
    return new ListAuditRecordForKargoProjectsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListAuditRecordForKargoProjectsResponse | PlainMessage<ListAuditRecordForKargoProjectsResponse> | undefined, b: ListAuditRecordForKargoProjectsResponse | PlainMessage<ListAuditRecordForKargoProjectsResponse> | undefined): boolean {
    return proto3.util.equals(ListAuditRecordForKargoProjectsResponse, a, b);
  }
}

/**
 * @generated from message akuity.extension.v1.ListAuditRecordForApplicationRequest
 */
export class ListAuditRecordForApplicationRequest extends Message<ListAuditRecordForApplicationRequest> {
  /**
   * @generated from field: akuity.organization.v1.AuditFilters filters = 1;
   */
  filters?: AuditFilters;

  constructor(data?: PartialMessage<ListAuditRecordForApplicationRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.extension.v1.ListAuditRecordForApplicationRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "filters", kind: "message", T: AuditFilters },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListAuditRecordForApplicationRequest {
    return new ListAuditRecordForApplicationRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListAuditRecordForApplicationRequest {
    return new ListAuditRecordForApplicationRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListAuditRecordForApplicationRequest {
    return new ListAuditRecordForApplicationRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ListAuditRecordForApplicationRequest | PlainMessage<ListAuditRecordForApplicationRequest> | undefined, b: ListAuditRecordForApplicationRequest | PlainMessage<ListAuditRecordForApplicationRequest> | undefined): boolean {
    return proto3.util.equals(ListAuditRecordForApplicationRequest, a, b);
  }
}

/**
 * @generated from message akuity.extension.v1.ListAuditRecordForApplicationResponse
 */
export class ListAuditRecordForApplicationResponse extends Message<ListAuditRecordForApplicationResponse> {
  /**
   * @generated from field: repeated akuity.organization.v1.AuditLog items = 1;
   */
  items: AuditLog[] = [];

  /**
   * @generated from field: uint32 total_count = 2;
   */
  totalCount = 0;

  constructor(data?: PartialMessage<ListAuditRecordForApplicationResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.extension.v1.ListAuditRecordForApplicationResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "items", kind: "message", T: AuditLog, repeated: true },
    { no: 2, name: "total_count", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListAuditRecordForApplicationResponse {
    return new ListAuditRecordForApplicationResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListAuditRecordForApplicationResponse {
    return new ListAuditRecordForApplicationResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListAuditRecordForApplicationResponse {
    return new ListAuditRecordForApplicationResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListAuditRecordForApplicationResponse | PlainMessage<ListAuditRecordForApplicationResponse> | undefined, b: ListAuditRecordForApplicationResponse | PlainMessage<ListAuditRecordForApplicationResponse> | undefined): boolean {
    return proto3.util.equals(ListAuditRecordForApplicationResponse, a, b);
  }
}

/**
 * @generated from message akuity.extension.v1.GetSyncOperationsStatsForApplicationRequest
 */
export class GetSyncOperationsStatsForApplicationRequest extends Message<GetSyncOperationsStatsForApplicationRequest> {
  /**
   * @generated from field: akuity.argocd.v1.SyncOperationFilter filter = 1;
   */
  filter?: SyncOperationFilter;

  /**
   * @generated from field: akuity.types.misc.v1.GroupByInterval interval = 2;
   */
  interval = GroupByInterval.UNSPECIFIED;

  /**
   * @generated from field: akuity.argocd.v1.SyncOperationGroupField group_by_field = 3;
   */
  groupByField = SyncOperationGroupField.UNSPECIFIED;

  constructor(data?: PartialMessage<GetSyncOperationsStatsForApplicationRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.extension.v1.GetSyncOperationsStatsForApplicationRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "filter", kind: "message", T: SyncOperationFilter },
    { no: 2, name: "interval", kind: "enum", T: proto3.getEnumType(GroupByInterval) },
    { no: 3, name: "group_by_field", kind: "enum", T: proto3.getEnumType(SyncOperationGroupField) },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetSyncOperationsStatsForApplicationRequest {
    return new GetSyncOperationsStatsForApplicationRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetSyncOperationsStatsForApplicationRequest {
    return new GetSyncOperationsStatsForApplicationRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetSyncOperationsStatsForApplicationRequest {
    return new GetSyncOperationsStatsForApplicationRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetSyncOperationsStatsForApplicationRequest | PlainMessage<GetSyncOperationsStatsForApplicationRequest> | undefined, b: GetSyncOperationsStatsForApplicationRequest | PlainMessage<GetSyncOperationsStatsForApplicationRequest> | undefined): boolean {
    return proto3.util.equals(GetSyncOperationsStatsForApplicationRequest, a, b);
  }
}

/**
 * @generated from message akuity.extension.v1.GetSyncOperationsStatsForApplicationResponse
 */
export class GetSyncOperationsStatsForApplicationResponse extends Message<GetSyncOperationsStatsForApplicationResponse> {
  /**
   * @generated from field: repeated akuity.argocd.v1.SyncOperationStat sync_operation_stats = 1;
   */
  syncOperationStats: SyncOperationStat[] = [];

  constructor(data?: PartialMessage<GetSyncOperationsStatsForApplicationResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.extension.v1.GetSyncOperationsStatsForApplicationResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "sync_operation_stats", kind: "message", T: SyncOperationStat, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetSyncOperationsStatsForApplicationResponse {
    return new GetSyncOperationsStatsForApplicationResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetSyncOperationsStatsForApplicationResponse {
    return new GetSyncOperationsStatsForApplicationResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetSyncOperationsStatsForApplicationResponse {
    return new GetSyncOperationsStatsForApplicationResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetSyncOperationsStatsForApplicationResponse | PlainMessage<GetSyncOperationsStatsForApplicationResponse> | undefined, b: GetSyncOperationsStatsForApplicationResponse | PlainMessage<GetSyncOperationsStatsForApplicationResponse> | undefined): boolean {
    return proto3.util.equals(GetSyncOperationsStatsForApplicationResponse, a, b);
  }
}

/**
 * @generated from message akuity.extension.v1.GetSyncOperationsEventsForApplicationRequest
 */
export class GetSyncOperationsEventsForApplicationRequest extends Message<GetSyncOperationsEventsForApplicationRequest> {
  /**
   * @generated from field: akuity.argocd.v1.SyncOperationFilter filter = 1;
   */
  filter?: SyncOperationFilter;

  /**
   * @generated from field: optional int64 limit = 2;
   */
  limit?: bigint;

  /**
   * @generated from field: optional int64 offset = 3;
   */
  offset?: bigint;

  /**
   * @generated from field: akuity.argocd.v1.SyncOperationField field = 4;
   */
  field = SyncOperationField.UNSPECIFIED;

  /**
   * @generated from field: string field_like = 6;
   */
  fieldLike = "";

  constructor(data?: PartialMessage<GetSyncOperationsEventsForApplicationRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.extension.v1.GetSyncOperationsEventsForApplicationRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "filter", kind: "message", T: SyncOperationFilter },
    { no: 2, name: "limit", kind: "scalar", T: 3 /* ScalarType.INT64 */, opt: true },
    { no: 3, name: "offset", kind: "scalar", T: 3 /* ScalarType.INT64 */, opt: true },
    { no: 4, name: "field", kind: "enum", T: proto3.getEnumType(SyncOperationField) },
    { no: 6, name: "field_like", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetSyncOperationsEventsForApplicationRequest {
    return new GetSyncOperationsEventsForApplicationRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetSyncOperationsEventsForApplicationRequest {
    return new GetSyncOperationsEventsForApplicationRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetSyncOperationsEventsForApplicationRequest {
    return new GetSyncOperationsEventsForApplicationRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetSyncOperationsEventsForApplicationRequest | PlainMessage<GetSyncOperationsEventsForApplicationRequest> | undefined, b: GetSyncOperationsEventsForApplicationRequest | PlainMessage<GetSyncOperationsEventsForApplicationRequest> | undefined): boolean {
    return proto3.util.equals(GetSyncOperationsEventsForApplicationRequest, a, b);
  }
}

/**
 * @generated from message akuity.extension.v1.GetSyncOperationsEventsForApplicationResponse
 */
export class GetSyncOperationsEventsForApplicationResponse extends Message<GetSyncOperationsEventsForApplicationResponse> {
  /**
   * @generated from field: repeated akuity.argocd.v1.SyncOperationEvent sync_operation_events = 1;
   */
  syncOperationEvents: SyncOperationEvent[] = [];

  /**
   * @generated from field: int64 count = 2;
   */
  count = protoInt64.zero;

  /**
   * @generated from field: repeated string field_result = 3;
   */
  fieldResult: string[] = [];

  constructor(data?: PartialMessage<GetSyncOperationsEventsForApplicationResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.extension.v1.GetSyncOperationsEventsForApplicationResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "sync_operation_events", kind: "message", T: SyncOperationEvent, repeated: true },
    { no: 2, name: "count", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 3, name: "field_result", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetSyncOperationsEventsForApplicationResponse {
    return new GetSyncOperationsEventsForApplicationResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetSyncOperationsEventsForApplicationResponse {
    return new GetSyncOperationsEventsForApplicationResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetSyncOperationsEventsForApplicationResponse {
    return new GetSyncOperationsEventsForApplicationResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetSyncOperationsEventsForApplicationResponse | PlainMessage<GetSyncOperationsEventsForApplicationResponse> | undefined, b: GetSyncOperationsEventsForApplicationResponse | PlainMessage<GetSyncOperationsEventsForApplicationResponse> | undefined): boolean {
    return proto3.util.equals(GetSyncOperationsEventsForApplicationResponse, a, b);
  }
}

/**
 * @generated from message akuity.extension.v1.GetExtensionSettingsRequest
 */
export class GetExtensionSettingsRequest extends Message<GetExtensionSettingsRequest> {
  constructor(data?: PartialMessage<GetExtensionSettingsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.extension.v1.GetExtensionSettingsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetExtensionSettingsRequest {
    return new GetExtensionSettingsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetExtensionSettingsRequest {
    return new GetExtensionSettingsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetExtensionSettingsRequest {
    return new GetExtensionSettingsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetExtensionSettingsRequest | PlainMessage<GetExtensionSettingsRequest> | undefined, b: GetExtensionSettingsRequest | PlainMessage<GetExtensionSettingsRequest> | undefined): boolean {
    return proto3.util.equals(GetExtensionSettingsRequest, a, b);
  }
}

/**
 * @generated from message akuity.extension.v1.GetExtensionSettingsResponse
 */
export class GetExtensionSettingsResponse extends Message<GetExtensionSettingsResponse> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string instance_id = 2;
   */
  instanceId = "";

  /**
   * @generated from field: akuity.kargo.v1.AkuityIntelligence akuity_intelligence = 3;
   */
  akuityIntelligence?: AkuityIntelligence;

  constructor(data?: PartialMessage<GetExtensionSettingsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.extension.v1.GetExtensionSettingsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "instance_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "akuity_intelligence", kind: "message", T: AkuityIntelligence },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetExtensionSettingsResponse {
    return new GetExtensionSettingsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetExtensionSettingsResponse {
    return new GetExtensionSettingsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetExtensionSettingsResponse {
    return new GetExtensionSettingsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetExtensionSettingsResponse | PlainMessage<GetExtensionSettingsResponse> | undefined, b: GetExtensionSettingsResponse | PlainMessage<GetExtensionSettingsResponse> | undefined): boolean {
    return proto3.util.equals(GetExtensionSettingsResponse, a, b);
  }
}

/**
 * @generated from message akuity.extension.v1.Settings
 */
export class Settings extends Message<Settings> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string instance_id = 2;
   */
  instanceId = "";

  /**
   * @generated from field: string instance_version = 3;
   */
  instanceVersion = "";

  /**
   * @generated from field: bool audit_extension_enabled = 4;
   */
  auditExtensionEnabled = false;

  /**
   * @generated from field: bool sync_history_extension_enabled = 5;
   */
  syncHistoryExtensionEnabled = false;

  /**
   * @generated from field: bool assistant_extension_enabled = 6;
   */
  assistantExtensionEnabled = false;

  /**
   * @generated from field: akuity.argocd.v1.CrossplaneExtension crossplane_extension = 7;
   */
  crossplaneExtension?: CrossplaneExtension;

  /**
   * @generated from field: akuity.argocd.v1.AkuityIntelligenceExtension akuity_intelligence_extension = 8;
   */
  akuityIntelligenceExtension?: AkuityIntelligenceExtension;

  /**
   * @generated from field: akuity.extension.v1.Config config = 11;
   */
  config?: Config;

  /**
   * @generated from field: akuity.argocd.v1.KubeVisionConfig kube_vision_config = 12;
   */
  kubeVisionConfig?: KubeVisionConfig;

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatuses feature_statuses = 14;
   */
  featureStatuses?: FeatureStatuses;

  /**
   * @generated from field: akuity.argocd.v1.ApplicationSetExtension application_set_extension = 15;
   */
  applicationSetExtension?: ApplicationSetExtension;

  constructor(data?: PartialMessage<Settings>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.extension.v1.Settings";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "instance_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "instance_version", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "audit_extension_enabled", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 5, name: "sync_history_extension_enabled", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 6, name: "assistant_extension_enabled", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 7, name: "crossplane_extension", kind: "message", T: CrossplaneExtension },
    { no: 8, name: "akuity_intelligence_extension", kind: "message", T: AkuityIntelligenceExtension },
    { no: 11, name: "config", kind: "message", T: Config },
    { no: 12, name: "kube_vision_config", kind: "message", T: KubeVisionConfig },
    { no: 14, name: "feature_statuses", kind: "message", T: FeatureStatuses },
    { no: 15, name: "application_set_extension", kind: "message", T: ApplicationSetExtension },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Settings {
    return new Settings().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Settings {
    return new Settings().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Settings {
    return new Settings().fromJsonString(jsonString, options);
  }

  static equals(a: Settings | PlainMessage<Settings> | undefined, b: Settings | PlainMessage<Settings> | undefined): boolean {
    return proto3.util.equals(Settings, a, b);
  }
}

/**
 * @generated from message akuity.extension.v1.KargoSettings
 */
export class KargoSettings extends Message<KargoSettings> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string instance_id = 2;
   */
  instanceId = "";

  /**
   * @generated from field: akuity.types.features.v1.FeatureStatuses feature_statuses = 3;
   */
  featureStatuses?: FeatureStatuses;

  /**
   * @generated from field: akuity.kargo.v1.AkuityIntelligence akuity_intelligence = 4;
   */
  akuityIntelligence?: AkuityIntelligence;

  constructor(data?: PartialMessage<KargoSettings>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.extension.v1.KargoSettings";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "instance_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "feature_statuses", kind: "message", T: FeatureStatuses },
    { no: 4, name: "akuity_intelligence", kind: "message", T: AkuityIntelligence },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): KargoSettings {
    return new KargoSettings().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): KargoSettings {
    return new KargoSettings().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): KargoSettings {
    return new KargoSettings().fromJsonString(jsonString, options);
  }

  static equals(a: KargoSettings | PlainMessage<KargoSettings> | undefined, b: KargoSettings | PlainMessage<KargoSettings> | undefined): boolean {
    return proto3.util.equals(KargoSettings, a, b);
  }
}

/**
 * @generated from message akuity.extension.v1.Config
 */
export class Config extends Message<Config> {
  /**
   * @generated from field: string env = 1;
   */
  env = "";

  /**
   * @generated from field: string argocd_extension_sentry_dsn = 2;
   */
  argocdExtensionSentryDsn = "";

  constructor(data?: PartialMessage<Config>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.extension.v1.Config";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "env", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "argocd_extension_sentry_dsn", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Config {
    return new Config().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Config {
    return new Config().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Config {
    return new Config().fromJsonString(jsonString, options);
  }

  static equals(a: Config | PlainMessage<Config> | undefined, b: Config | PlainMessage<Config> | undefined): boolean {
    return proto3.util.equals(Config, a, b);
  }
}

/**
 * @generated from message akuity.extension.v1.GetKargoAnalysisLogsRequest
 */
export class GetKargoAnalysisLogsRequest extends Message<GetKargoAnalysisLogsRequest> {
  /**
   * @generated from field: string project_name = 1;
   */
  projectName = "";

  /**
   * @generated from field: string analysis_run = 2;
   */
  analysisRun = "";

  /**
   * @generated from field: string container_name = 3;
   */
  containerName = "";

  constructor(data?: PartialMessage<GetKargoAnalysisLogsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.extension.v1.GetKargoAnalysisLogsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "project_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "analysis_run", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "container_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetKargoAnalysisLogsRequest {
    return new GetKargoAnalysisLogsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetKargoAnalysisLogsRequest {
    return new GetKargoAnalysisLogsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetKargoAnalysisLogsRequest {
    return new GetKargoAnalysisLogsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetKargoAnalysisLogsRequest | PlainMessage<GetKargoAnalysisLogsRequest> | undefined, b: GetKargoAnalysisLogsRequest | PlainMessage<GetKargoAnalysisLogsRequest> | undefined): boolean {
    return proto3.util.equals(GetKargoAnalysisLogsRequest, a, b);
  }
}

