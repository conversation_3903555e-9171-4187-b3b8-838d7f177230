// @generated by protoc-gen-es v1.2.0 with parameter "target=ts"
// @generated from file customrole/v1/customrole.proto (package akuity.customrole.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * Request and response messages
 *
 * @generated from message akuity.customrole.v1.CreateCustomRoleRequest
 */
export class CreateCustomRoleRequest extends Message<CreateCustomRoleRequest> {
  /**
   * @generated from field: string name = 1;
   */
  name = "";

  /**
   * @generated from field: string description = 2;
   */
  description = "";

  /**
   * @generated from field: string policy = 3;
   */
  policy = "";

  /**
   * @generated from field: string organization_id = 4;
   */
  organizationId = "";

  constructor(data?: PartialMessage<CreateCustomRoleRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.customrole.v1.CreateCustomRoleRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "description", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "policy", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateCustomRoleRequest {
    return new CreateCustomRoleRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateCustomRoleRequest {
    return new CreateCustomRoleRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateCustomRoleRequest {
    return new CreateCustomRoleRequest().fromJsonString(jsonString, options);
  }

  static equals(a: CreateCustomRoleRequest | PlainMessage<CreateCustomRoleRequest> | undefined, b: CreateCustomRoleRequest | PlainMessage<CreateCustomRoleRequest> | undefined): boolean {
    return proto3.util.equals(CreateCustomRoleRequest, a, b);
  }
}

/**
 * @generated from message akuity.customrole.v1.CreateCustomRoleResponse
 */
export class CreateCustomRoleResponse extends Message<CreateCustomRoleResponse> {
  /**
   * @generated from field: akuity.customrole.v1.CustomRole custom_role = 1;
   */
  customRole?: CustomRole;

  constructor(data?: PartialMessage<CreateCustomRoleResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.customrole.v1.CreateCustomRoleResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "custom_role", kind: "message", T: CustomRole },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateCustomRoleResponse {
    return new CreateCustomRoleResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateCustomRoleResponse {
    return new CreateCustomRoleResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateCustomRoleResponse {
    return new CreateCustomRoleResponse().fromJsonString(jsonString, options);
  }

  static equals(a: CreateCustomRoleResponse | PlainMessage<CreateCustomRoleResponse> | undefined, b: CreateCustomRoleResponse | PlainMessage<CreateCustomRoleResponse> | undefined): boolean {
    return proto3.util.equals(CreateCustomRoleResponse, a, b);
  }
}

/**
 * @generated from message akuity.customrole.v1.UpdateCustomRoleRequest
 */
export class UpdateCustomRoleRequest extends Message<UpdateCustomRoleRequest> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  /**
   * @generated from field: string name = 2;
   */
  name = "";

  /**
   * @generated from field: string description = 3;
   */
  description = "";

  /**
   * @generated from field: string policy = 4;
   */
  policy = "";

  /**
   * @generated from field: string organization_id = 5;
   */
  organizationId = "";

  constructor(data?: PartialMessage<UpdateCustomRoleRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.customrole.v1.UpdateCustomRoleRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "description", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "policy", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateCustomRoleRequest {
    return new UpdateCustomRoleRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateCustomRoleRequest {
    return new UpdateCustomRoleRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateCustomRoleRequest {
    return new UpdateCustomRoleRequest().fromJsonString(jsonString, options);
  }

  static equals(a: UpdateCustomRoleRequest | PlainMessage<UpdateCustomRoleRequest> | undefined, b: UpdateCustomRoleRequest | PlainMessage<UpdateCustomRoleRequest> | undefined): boolean {
    return proto3.util.equals(UpdateCustomRoleRequest, a, b);
  }
}

/**
 * @generated from message akuity.customrole.v1.UpdateCustomRoleResponse
 */
export class UpdateCustomRoleResponse extends Message<UpdateCustomRoleResponse> {
  /**
   * @generated from field: akuity.customrole.v1.CustomRole custom_role = 1;
   */
  customRole?: CustomRole;

  constructor(data?: PartialMessage<UpdateCustomRoleResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.customrole.v1.UpdateCustomRoleResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "custom_role", kind: "message", T: CustomRole },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateCustomRoleResponse {
    return new UpdateCustomRoleResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateCustomRoleResponse {
    return new UpdateCustomRoleResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateCustomRoleResponse {
    return new UpdateCustomRoleResponse().fromJsonString(jsonString, options);
  }

  static equals(a: UpdateCustomRoleResponse | PlainMessage<UpdateCustomRoleResponse> | undefined, b: UpdateCustomRoleResponse | PlainMessage<UpdateCustomRoleResponse> | undefined): boolean {
    return proto3.util.equals(UpdateCustomRoleResponse, a, b);
  }
}

/**
 * @generated from message akuity.customrole.v1.GetCustomRoleRequest
 */
export class GetCustomRoleRequest extends Message<GetCustomRoleRequest> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  constructor(data?: PartialMessage<GetCustomRoleRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.customrole.v1.GetCustomRoleRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetCustomRoleRequest {
    return new GetCustomRoleRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetCustomRoleRequest {
    return new GetCustomRoleRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetCustomRoleRequest {
    return new GetCustomRoleRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetCustomRoleRequest | PlainMessage<GetCustomRoleRequest> | undefined, b: GetCustomRoleRequest | PlainMessage<GetCustomRoleRequest> | undefined): boolean {
    return proto3.util.equals(GetCustomRoleRequest, a, b);
  }
}

/**
 * @generated from message akuity.customrole.v1.GetCustomRoleResponse
 */
export class GetCustomRoleResponse extends Message<GetCustomRoleResponse> {
  /**
   * @generated from field: akuity.customrole.v1.CustomRole custom_role = 1;
   */
  customRole?: CustomRole;

  constructor(data?: PartialMessage<GetCustomRoleResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.customrole.v1.GetCustomRoleResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "custom_role", kind: "message", T: CustomRole },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetCustomRoleResponse {
    return new GetCustomRoleResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetCustomRoleResponse {
    return new GetCustomRoleResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetCustomRoleResponse {
    return new GetCustomRoleResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetCustomRoleResponse | PlainMessage<GetCustomRoleResponse> | undefined, b: GetCustomRoleResponse | PlainMessage<GetCustomRoleResponse> | undefined): boolean {
    return proto3.util.equals(GetCustomRoleResponse, a, b);
  }
}

/**
 * @generated from message akuity.customrole.v1.ListCustomRolesRequest
 */
export class ListCustomRolesRequest extends Message<ListCustomRolesRequest> {
  /**
   * @generated from field: string organization_id = 3;
   */
  organizationId = "";

  constructor(data?: PartialMessage<ListCustomRolesRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.customrole.v1.ListCustomRolesRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 3, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListCustomRolesRequest {
    return new ListCustomRolesRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListCustomRolesRequest {
    return new ListCustomRolesRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListCustomRolesRequest {
    return new ListCustomRolesRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ListCustomRolesRequest | PlainMessage<ListCustomRolesRequest> | undefined, b: ListCustomRolesRequest | PlainMessage<ListCustomRolesRequest> | undefined): boolean {
    return proto3.util.equals(ListCustomRolesRequest, a, b);
  }
}

/**
 * @generated from message akuity.customrole.v1.ListCustomRolesResponse
 */
export class ListCustomRolesResponse extends Message<ListCustomRolesResponse> {
  /**
   * @generated from field: repeated akuity.customrole.v1.CustomRole custom_roles = 1;
   */
  customRoles: CustomRole[] = [];

  constructor(data?: PartialMessage<ListCustomRolesResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.customrole.v1.ListCustomRolesResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "custom_roles", kind: "message", T: CustomRole, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListCustomRolesResponse {
    return new ListCustomRolesResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListCustomRolesResponse {
    return new ListCustomRolesResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListCustomRolesResponse {
    return new ListCustomRolesResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListCustomRolesResponse | PlainMessage<ListCustomRolesResponse> | undefined, b: ListCustomRolesResponse | PlainMessage<ListCustomRolesResponse> | undefined): boolean {
    return proto3.util.equals(ListCustomRolesResponse, a, b);
  }
}

/**
 * @generated from message akuity.customrole.v1.DeleteCustomRoleRequest
 */
export class DeleteCustomRoleRequest extends Message<DeleteCustomRoleRequest> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  constructor(data?: PartialMessage<DeleteCustomRoleRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.customrole.v1.DeleteCustomRoleRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteCustomRoleRequest {
    return new DeleteCustomRoleRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteCustomRoleRequest {
    return new DeleteCustomRoleRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteCustomRoleRequest {
    return new DeleteCustomRoleRequest().fromJsonString(jsonString, options);
  }

  static equals(a: DeleteCustomRoleRequest | PlainMessage<DeleteCustomRoleRequest> | undefined, b: DeleteCustomRoleRequest | PlainMessage<DeleteCustomRoleRequest> | undefined): boolean {
    return proto3.util.equals(DeleteCustomRoleRequest, a, b);
  }
}

/**
 * This message is intentionally left empty.
 *
 * @generated from message akuity.customrole.v1.DeleteCustomRoleResponse
 */
export class DeleteCustomRoleResponse extends Message<DeleteCustomRoleResponse> {
  constructor(data?: PartialMessage<DeleteCustomRoleResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.customrole.v1.DeleteCustomRoleResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteCustomRoleResponse {
    return new DeleteCustomRoleResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteCustomRoleResponse {
    return new DeleteCustomRoleResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteCustomRoleResponse {
    return new DeleteCustomRoleResponse().fromJsonString(jsonString, options);
  }

  static equals(a: DeleteCustomRoleResponse | PlainMessage<DeleteCustomRoleResponse> | undefined, b: DeleteCustomRoleResponse | PlainMessage<DeleteCustomRoleResponse> | undefined): boolean {
    return proto3.util.equals(DeleteCustomRoleResponse, a, b);
  }
}

/**
 * @generated from message akuity.customrole.v1.CustomRole
 */
export class CustomRole extends Message<CustomRole> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  /**
   * @generated from field: string name = 2;
   */
  name = "";

  /**
   * @generated from field: string description = 3;
   */
  description = "";

  /**
   * @generated from field: string policy = 4;
   */
  policy = "";

  /**
   * @generated from field: string organization_id = 5;
   */
  organizationId = "";

  constructor(data?: PartialMessage<CustomRole>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.customrole.v1.CustomRole";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "description", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "policy", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CustomRole {
    return new CustomRole().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CustomRole {
    return new CustomRole().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CustomRole {
    return new CustomRole().fromJsonString(jsonString, options);
  }

  static equals(a: CustomRole | PlainMessage<CustomRole> | undefined, b: CustomRole | PlainMessage<CustomRole> | undefined): boolean {
    return proto3.util.equals(CustomRole, a, b);
  }
}

