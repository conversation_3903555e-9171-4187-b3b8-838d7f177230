// @generated by protoc-gen-es v1.2.0 with parameter "target=ts"
// @generated from file accesscontrol/v1/accesscontrol.proto (package akuity.accesscontrol.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from message akuity.accesscontrol.v1.Permissions
 */
export class Permissions extends Message<Permissions> {
  /**
   * @generated from field: repeated string actions = 1;
   */
  actions: string[] = [];

  /**
   * @generated from field: repeated string roles = 2;
   */
  roles: string[] = [];

  /**
   * @generated from field: repeated string custom_roles = 3;
   */
  customRoles: string[] = [];

  constructor(data?: PartialMessage<Permissions>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.accesscontrol.v1.Permissions";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "actions", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 2, name: "roles", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 3, name: "custom_roles", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Permissions {
    return new Permissions().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Permissions {
    return new Permissions().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Permissions {
    return new Permissions().fromJsonString(jsonString, options);
  }

  static equals(a: Permissions | PlainMessage<Permissions> | undefined, b: Permissions | PlainMessage<Permissions> | undefined): boolean {
    return proto3.util.equals(Permissions, a, b);
  }
}

