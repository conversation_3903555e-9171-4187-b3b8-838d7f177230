// @generated by protoc-gen-es v1.2.0 with parameter "target=ts"
// @generated from file kargo/v1/kargo.proto (package akuity.kargo.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3, protoInt64, Struct, Timestamp } from "@bufbuild/protobuf";
import { GroupByInterval } from "../../types/misc/v1/misc_pb.js";
import { AgentAggregatedHealthResponse, Status, StatusCode } from "../../types/status/health/v1/health_pb.js";
import { AgentUpdateStatus, Status as Status$1 } from "../../types/status/reconciliation/v1/reconciliation_pb.js";
import { EventType } from "../../types/events/v1/events_pb.js";
import { Type } from "../../types/id/v1/id_pb.js";

/**
 * @generated from enum akuity.kargo.v1.PromotionField
 */
export enum PromotionField {
  /**
   * @generated from enum value: PROMOTION_FIELD_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: PROMOTION_FIELD_STAGES = 1;
   */
  STAGES = 1,

  /**
   * @generated from enum value: PROMOTION_FIELD_PROJECTS = 2;
   */
  PROJECTS = 2,

  /**
   * @generated from enum value: PROMOTION_FIELD_INITIATORS = 3;
   */
  INITIATORS = 3,

  /**
   * @generated from enum value: PROMOTION_FIELD_PROMOTIONS = 4;
   */
  PROMOTIONS = 4,

  /**
   * @generated from enum value: PROMOTION_FIELD_INSTANCE_NAMES = 5;
   */
  INSTANCE_NAMES = 5,
}
// Retrieve enum metadata with: proto3.getEnumType(PromotionField)
proto3.util.setEnumType(PromotionField, "akuity.kargo.v1.PromotionField", [
  { no: 0, name: "PROMOTION_FIELD_UNSPECIFIED" },
  { no: 1, name: "PROMOTION_FIELD_STAGES" },
  { no: 2, name: "PROMOTION_FIELD_PROJECTS" },
  { no: 3, name: "PROMOTION_FIELD_INITIATORS" },
  { no: 4, name: "PROMOTION_FIELD_PROMOTIONS" },
  { no: 5, name: "PROMOTION_FIELD_INSTANCE_NAMES" },
]);

/**
 * @generated from enum akuity.kargo.v1.PromotionGroupField
 */
export enum PromotionGroupField {
  /**
   * @generated from enum value: PROMOTION_GROUP_FIELD_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: PROMOTION_GROUP_FIELD_STAGES = 1;
   */
  STAGES = 1,

  /**
   * @generated from enum value: PROMOTION_GROUP_FIELD_PROJECTS = 2;
   */
  PROJECTS = 2,

  /**
   * @generated from enum value: PROMOTION_GROUP_FIELD_INITIATORS = 3;
   */
  INITIATORS = 3,

  /**
   * @generated from enum value: PROMOTION_GROUP_FIELD_INSTANCE_NAMES = 4;
   */
  INSTANCE_NAMES = 4,

  /**
   * @generated from enum value: PROMOTION_GROUP_FIELD_STATUS = 5;
   */
  STATUS = 5,
}
// Retrieve enum metadata with: proto3.getEnumType(PromotionGroupField)
proto3.util.setEnumType(PromotionGroupField, "akuity.kargo.v1.PromotionGroupField", [
  { no: 0, name: "PROMOTION_GROUP_FIELD_UNSPECIFIED" },
  { no: 1, name: "PROMOTION_GROUP_FIELD_STAGES" },
  { no: 2, name: "PROMOTION_GROUP_FIELD_PROJECTS" },
  { no: 3, name: "PROMOTION_GROUP_FIELD_INITIATORS" },
  { no: 4, name: "PROMOTION_GROUP_FIELD_INSTANCE_NAMES" },
  { no: 5, name: "PROMOTION_GROUP_FIELD_STATUS" },
]);

/**
 * @generated from enum akuity.kargo.v1.KargoAgentSize
 */
export enum KargoAgentSize {
  /**
   * @generated from enum value: KARGO_AGENT_SIZE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: KARGO_AGENT_SIZE_SMALL = 1;
   */
  SMALL = 1,

  /**
   * @generated from enum value: KARGO_AGENT_SIZE_MEDIUM = 2;
   */
  MEDIUM = 2,

  /**
   * @generated from enum value: KARGO_AGENT_SIZE_LARGE = 3;
   */
  LARGE = 3,
}
// Retrieve enum metadata with: proto3.getEnumType(KargoAgentSize)
proto3.util.setEnumType(KargoAgentSize, "akuity.kargo.v1.KargoAgentSize", [
  { no: 0, name: "KARGO_AGENT_SIZE_UNSPECIFIED" },
  { no: 1, name: "KARGO_AGENT_SIZE_SMALL" },
  { no: 2, name: "KARGO_AGENT_SIZE_MEDIUM" },
  { no: 3, name: "KARGO_AGENT_SIZE_LARGE" },
]);

/**
 * @generated from enum akuity.kargo.v1.PruneResourceType
 */
export enum PruneResourceType {
  /**
   * @generated from enum value: PRUNE_RESOURCE_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: PRUNE_RESOURCE_TYPE_ALL = 1;
   */
  ALL = 1,

  /**
   * @generated from enum value: PRUNE_RESOURCE_TYPE_AGENTS = 2;
   */
  AGENTS = 2,

  /**
   * @generated from enum value: PRUNE_RESOURCE_TYPE_PROJECTS = 3;
   */
  PROJECTS = 3,

  /**
   * @generated from enum value: PRUNE_RESOURCE_TYPE_WAREHOUSES = 4;
   */
  WAREHOUSES = 4,

  /**
   * @generated from enum value: PRUNE_RESOURCE_TYPE_STAGES = 5;
   */
  STAGES = 5,

  /**
   * @generated from enum value: PRUNE_RESOURCE_TYPE_ANALYSIS_TEMPLATES = 6;
   */
  ANALYSIS_TEMPLATES = 6,

  /**
   * @generated from enum value: PRUNE_RESOURCE_TYPE_PROMOTION_TASKS = 7;
   */
  PROMOTION_TASKS = 7,

  /**
   * @generated from enum value: PRUNE_RESOURCE_TYPE_CLUSTER_PROMOTION_TASKS = 8;
   */
  CLUSTER_PROMOTION_TASKS = 8,

  /**
   * @generated from enum value: PRUNE_RESOURCE_TYPE_REPO_CREDENTIALS = 9;
   */
  REPO_CREDENTIALS = 9,
}
// Retrieve enum metadata with: proto3.getEnumType(PruneResourceType)
proto3.util.setEnumType(PruneResourceType, "akuity.kargo.v1.PruneResourceType", [
  { no: 0, name: "PRUNE_RESOURCE_TYPE_UNSPECIFIED" },
  { no: 1, name: "PRUNE_RESOURCE_TYPE_ALL" },
  { no: 2, name: "PRUNE_RESOURCE_TYPE_AGENTS" },
  { no: 3, name: "PRUNE_RESOURCE_TYPE_PROJECTS" },
  { no: 4, name: "PRUNE_RESOURCE_TYPE_WAREHOUSES" },
  { no: 5, name: "PRUNE_RESOURCE_TYPE_STAGES" },
  { no: 6, name: "PRUNE_RESOURCE_TYPE_ANALYSIS_TEMPLATES" },
  { no: 7, name: "PRUNE_RESOURCE_TYPE_PROMOTION_TASKS" },
  { no: 8, name: "PRUNE_RESOURCE_TYPE_CLUSTER_PROMOTION_TASKS" },
  { no: 9, name: "PRUNE_RESOURCE_TYPE_REPO_CREDENTIALS" },
]);

/**
 * for lead time and recovery time graphs
 *
 * @generated from message akuity.kargo.v1.GetStageSpecificStatsRequest
 */
export class GetStageSpecificStatsRequest extends Message<GetStageSpecificStatsRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: akuity.kargo.v1.PromotionFilter filter = 2;
   */
  filter?: PromotionFilter;

  /**
   * @generated from field: string workspace_id = 3;
   */
  workspaceId = "";

  constructor(data?: PartialMessage<GetStageSpecificStatsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.GetStageSpecificStatsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "filter", kind: "message", T: PromotionFilter },
    { no: 3, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetStageSpecificStatsRequest {
    return new GetStageSpecificStatsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetStageSpecificStatsRequest {
    return new GetStageSpecificStatsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetStageSpecificStatsRequest {
    return new GetStageSpecificStatsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetStageSpecificStatsRequest | PlainMessage<GetStageSpecificStatsRequest> | undefined, b: GetStageSpecificStatsRequest | PlainMessage<GetStageSpecificStatsRequest> | undefined): boolean {
    return proto3.util.equals(GetStageSpecificStatsRequest, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.GetStageSpecificStatsResponse
 */
export class GetStageSpecificStatsResponse extends Message<GetStageSpecificStatsResponse> {
  /**
   * @generated from field: repeated akuity.kargo.v1.LeadTimeData lead_time_data = 1;
   */
  leadTimeData: LeadTimeData[] = [];

  /**
   * @generated from field: repeated akuity.kargo.v1.RecoveryTimeData recovery_time_data = 2;
   */
  recoveryTimeData: RecoveryTimeData[] = [];

  constructor(data?: PartialMessage<GetStageSpecificStatsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.GetStageSpecificStatsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "lead_time_data", kind: "message", T: LeadTimeData, repeated: true },
    { no: 2, name: "recovery_time_data", kind: "message", T: RecoveryTimeData, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetStageSpecificStatsResponse {
    return new GetStageSpecificStatsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetStageSpecificStatsResponse {
    return new GetStageSpecificStatsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetStageSpecificStatsResponse {
    return new GetStageSpecificStatsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetStageSpecificStatsResponse | PlainMessage<GetStageSpecificStatsResponse> | undefined, b: GetStageSpecificStatsResponse | PlainMessage<GetStageSpecificStatsResponse> | undefined): boolean {
    return proto3.util.equals(GetStageSpecificStatsResponse, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.LeadTimeData
 */
export class LeadTimeData extends Message<LeadTimeData> {
  /**
   * @generated from field: string promotion_end_time = 2;
   */
  promotionEndTime = "";

  /**
   * @generated from field: string freight_creation_time = 3;
   */
  freightCreationTime = "";

  /**
   * @generated from field: float lead_time = 4;
   */
  leadTime = 0;

  /**
   * @generated from field: string stage_name = 5;
   */
  stageName = "";

  constructor(data?: PartialMessage<LeadTimeData>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.LeadTimeData";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 2, name: "promotion_end_time", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "freight_creation_time", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "lead_time", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 5, name: "stage_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LeadTimeData {
    return new LeadTimeData().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LeadTimeData {
    return new LeadTimeData().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LeadTimeData {
    return new LeadTimeData().fromJsonString(jsonString, options);
  }

  static equals(a: LeadTimeData | PlainMessage<LeadTimeData> | undefined, b: LeadTimeData | PlainMessage<LeadTimeData> | undefined): boolean {
    return proto3.util.equals(LeadTimeData, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.RecoveryTimeData
 */
export class RecoveryTimeData extends Message<RecoveryTimeData> {
  /**
   * @generated from field: string phase = 1;
   */
  phase = "";

  /**
   * @generated from field: string phase_change_time = 2;
   */
  phaseChangeTime = "";

  /**
   * @generated from field: string stage_name = 3;
   */
  stageName = "";

  constructor(data?: PartialMessage<RecoveryTimeData>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.RecoveryTimeData";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "phase", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "phase_change_time", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "stage_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RecoveryTimeData {
    return new RecoveryTimeData().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RecoveryTimeData {
    return new RecoveryTimeData().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RecoveryTimeData {
    return new RecoveryTimeData().fromJsonString(jsonString, options);
  }

  static equals(a: RecoveryTimeData | PlainMessage<RecoveryTimeData> | undefined, b: RecoveryTimeData | PlainMessage<RecoveryTimeData> | undefined): boolean {
    return proto3.util.equals(RecoveryTimeData, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.GetPromotionStatsRequest
 */
export class GetPromotionStatsRequest extends Message<GetPromotionStatsRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: akuity.kargo.v1.PromotionFilter filter = 2;
   */
  filter?: PromotionFilter;

  /**
   * @generated from field: akuity.types.misc.v1.GroupByInterval interval = 3;
   */
  interval = GroupByInterval.UNSPECIFIED;

  /**
   * can either set group_by_field or group_by_label_field(has more preference)
   *
   * @generated from field: akuity.kargo.v1.PromotionGroupField group_by_field = 4;
   */
  groupByField = PromotionGroupField.UNSPECIFIED;

  /**
   * @generated from field: string workspace_id = 5;
   */
  workspaceId = "";

  constructor(data?: PartialMessage<GetPromotionStatsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.GetPromotionStatsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "filter", kind: "message", T: PromotionFilter },
    { no: 3, name: "interval", kind: "enum", T: proto3.getEnumType(GroupByInterval) },
    { no: 4, name: "group_by_field", kind: "enum", T: proto3.getEnumType(PromotionGroupField) },
    { no: 5, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetPromotionStatsRequest {
    return new GetPromotionStatsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetPromotionStatsRequest {
    return new GetPromotionStatsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetPromotionStatsRequest {
    return new GetPromotionStatsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetPromotionStatsRequest | PlainMessage<GetPromotionStatsRequest> | undefined, b: GetPromotionStatsRequest | PlainMessage<GetPromotionStatsRequest> | undefined): boolean {
    return proto3.util.equals(GetPromotionStatsRequest, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.GetPromotionEventsRequest
 */
export class GetPromotionEventsRequest extends Message<GetPromotionEventsRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: akuity.kargo.v1.PromotionFilter filter = 2;
   */
  filter?: PromotionFilter;

  /**
   * @generated from field: optional int64 limit = 3;
   */
  limit?: bigint;

  /**
   * @generated from field: optional int64 offset = 4;
   */
  offset?: bigint;

  /**
   * @generated from field: akuity.kargo.v1.PromotionField field = 5;
   */
  field = PromotionField.UNSPECIFIED;

  /**
   * @generated from field: string field_like = 6;
   */
  fieldLike = "";

  /**
   * @generated from field: string workspace_id = 7;
   */
  workspaceId = "";

  constructor(data?: PartialMessage<GetPromotionEventsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.GetPromotionEventsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "filter", kind: "message", T: PromotionFilter },
    { no: 3, name: "limit", kind: "scalar", T: 3 /* ScalarType.INT64 */, opt: true },
    { no: 4, name: "offset", kind: "scalar", T: 3 /* ScalarType.INT64 */, opt: true },
    { no: 5, name: "field", kind: "enum", T: proto3.getEnumType(PromotionField) },
    { no: 6, name: "field_like", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 7, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetPromotionEventsRequest {
    return new GetPromotionEventsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetPromotionEventsRequest {
    return new GetPromotionEventsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetPromotionEventsRequest {
    return new GetPromotionEventsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetPromotionEventsRequest | PlainMessage<GetPromotionEventsRequest> | undefined, b: GetPromotionEventsRequest | PlainMessage<GetPromotionEventsRequest> | undefined): boolean {
    return proto3.util.equals(GetPromotionEventsRequest, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.PromotionFilter
 */
export class PromotionFilter extends Message<PromotionFilter> {
  /**
   * @generated from field: string start_time = 1;
   */
  startTime = "";

  /**
   * @generated from field: optional string end_time = 2;
   */
  endTime?: string;

  /**
   * stage_name is a list of stages to be filtered. Stage name allows `*` to match multiple stages (e.g. 'test-*')
   *
   * @generated from field: repeated string stage_name = 3;
   */
  stageName: string[] = [];

  /**
   * @generated from field: repeated string projects = 4;
   */
  projects: string[] = [];

  /**
   * @generated from field: repeated string promotion_name = 6;
   */
  promotionName: string[] = [];

  /**
   * @generated from field: repeated string instance_id = 7;
   */
  instanceId: string[] = [];

  /**
   * @generated from field: repeated string initiated_by = 8;
   */
  initiatedBy: string[] = [];

  /**
   * @generated from field: repeated string instance_names = 9;
   */
  instanceNames: string[] = [];

  constructor(data?: PartialMessage<PromotionFilter>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.PromotionFilter";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "start_time", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "end_time", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 3, name: "stage_name", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 4, name: "projects", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 6, name: "promotion_name", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 7, name: "instance_id", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 8, name: "initiated_by", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 9, name: "instance_names", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PromotionFilter {
    return new PromotionFilter().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PromotionFilter {
    return new PromotionFilter().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PromotionFilter {
    return new PromotionFilter().fromJsonString(jsonString, options);
  }

  static equals(a: PromotionFilter | PlainMessage<PromotionFilter> | undefined, b: PromotionFilter | PlainMessage<PromotionFilter> | undefined): boolean {
    return proto3.util.equals(PromotionFilter, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.PromotionStat
 */
export class PromotionStat extends Message<PromotionStat> {
  /**
   * @generated from field: string interval_start = 1;
   */
  intervalStart = "";

  /**
   * @generated from field: map<string, uint32> count_map = 2;
   */
  countMap: { [key: string]: number } = {};

  /**
   * @generated from field: map<string, float> average_map = 3;
   */
  averageMap: { [key: string]: number } = {};

  constructor(data?: PartialMessage<PromotionStat>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.PromotionStat";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "interval_start", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "count_map", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "scalar", T: 13 /* ScalarType.UINT32 */} },
    { no: 3, name: "average_map", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "scalar", T: 2 /* ScalarType.FLOAT */} },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PromotionStat {
    return new PromotionStat().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PromotionStat {
    return new PromotionStat().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PromotionStat {
    return new PromotionStat().fromJsonString(jsonString, options);
  }

  static equals(a: PromotionStat | PlainMessage<PromotionStat> | undefined, b: PromotionStat | PlainMessage<PromotionStat> | undefined): boolean {
    return proto3.util.equals(PromotionStat, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.PromotionEvent
 */
export class PromotionEvent extends Message<PromotionEvent> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  /**
   * @generated from field: string instance_id = 2;
   */
  instanceId = "";

  /**
   * @generated from field: string promotion_name = 3;
   */
  promotionName = "";

  /**
   * @generated from field: string start_time = 4;
   */
  startTime = "";

  /**
   * @generated from field: string end_time = 5;
   */
  endTime = "";

  /**
   * @generated from field: string result_phase = 6;
   */
  resultPhase = "";

  /**
   * @generated from field: string result_message = 7;
   */
  resultMessage = "";

  /**
   * @generated from field: akuity.kargo.v1.PromotionEventDetails details = 8;
   */
  details?: PromotionEventDetails;

  constructor(data?: PartialMessage<PromotionEvent>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.PromotionEvent";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "instance_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "promotion_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "start_time", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "end_time", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 6, name: "result_phase", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 7, name: "result_message", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 8, name: "details", kind: "message", T: PromotionEventDetails },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PromotionEvent {
    return new PromotionEvent().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PromotionEvent {
    return new PromotionEvent().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PromotionEvent {
    return new PromotionEvent().fromJsonString(jsonString, options);
  }

  static equals(a: PromotionEvent | PlainMessage<PromotionEvent> | undefined, b: PromotionEvent | PlainMessage<PromotionEvent> | undefined): boolean {
    return proto3.util.equals(PromotionEvent, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.PromotionEventDetails
 */
export class PromotionEventDetails extends Message<PromotionEventDetails> {
  /**
   * @generated from field: string project = 1;
   */
  project = "";

  /**
   * @generated from field: string stage = 2;
   */
  stage = "";

  /**
   * @generated from field: akuity.kargo.v1.OperationInitiator initiated_by = 3;
   */
  initiatedBy?: OperationInitiator;

  /**
   * @generated from field: string promotion_status = 4;
   */
  promotionStatus = "";

  /**
   * @generated from field: string freight_name = 5;
   */
  freightName = "";

  /**
   * @generated from field: string freight_alias = 6;
   */
  freightAlias = "";

  /**
   * @generated from field: string freight_creation_time = 7;
   */
  freightCreationTime = "";

  /**
   * @generated from field: string verification_start_time = 8;
   */
  verificationStartTime = "";

  /**
   * @generated from field: string verification_end_time = 9;
   */
  verificationEndTime = "";

  /**
   * @generated from field: string verification_status = 10;
   */
  verificationStatus = "";

  /**
   * @generated from field: google.protobuf.Struct miscellaneous = 11;
   */
  miscellaneous?: Struct;

  constructor(data?: PartialMessage<PromotionEventDetails>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.PromotionEventDetails";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "project", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "stage", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "initiated_by", kind: "message", T: OperationInitiator },
    { no: 4, name: "promotion_status", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "freight_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 6, name: "freight_alias", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 7, name: "freight_creation_time", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 8, name: "verification_start_time", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 9, name: "verification_end_time", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 10, name: "verification_status", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 11, name: "miscellaneous", kind: "message", T: Struct },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PromotionEventDetails {
    return new PromotionEventDetails().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PromotionEventDetails {
    return new PromotionEventDetails().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PromotionEventDetails {
    return new PromotionEventDetails().fromJsonString(jsonString, options);
  }

  static equals(a: PromotionEventDetails | PlainMessage<PromotionEventDetails> | undefined, b: PromotionEventDetails | PlainMessage<PromotionEventDetails> | undefined): boolean {
    return proto3.util.equals(PromotionEventDetails, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.OperationInitiator
 */
export class OperationInitiator extends Message<OperationInitiator> {
  /**
   * @generated from field: string username = 1;
   */
  username = "";

  /**
   * @generated from field: bool automated = 2;
   */
  automated = false;

  constructor(data?: PartialMessage<OperationInitiator>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.OperationInitiator";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "username", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "automated", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OperationInitiator {
    return new OperationInitiator().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OperationInitiator {
    return new OperationInitiator().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OperationInitiator {
    return new OperationInitiator().fromJsonString(jsonString, options);
  }

  static equals(a: OperationInitiator | PlainMessage<OperationInitiator> | undefined, b: OperationInitiator | PlainMessage<OperationInitiator> | undefined): boolean {
    return proto3.util.equals(OperationInitiator, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.GetPromotionStatsResponse
 */
export class GetPromotionStatsResponse extends Message<GetPromotionStatsResponse> {
  /**
   * @generated from field: repeated akuity.kargo.v1.PromotionStat promotion_stats = 1;
   */
  promotionStats: PromotionStat[] = [];

  constructor(data?: PartialMessage<GetPromotionStatsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.GetPromotionStatsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "promotion_stats", kind: "message", T: PromotionStat, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetPromotionStatsResponse {
    return new GetPromotionStatsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetPromotionStatsResponse {
    return new GetPromotionStatsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetPromotionStatsResponse {
    return new GetPromotionStatsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetPromotionStatsResponse | PlainMessage<GetPromotionStatsResponse> | undefined, b: GetPromotionStatsResponse | PlainMessage<GetPromotionStatsResponse> | undefined): boolean {
    return proto3.util.equals(GetPromotionStatsResponse, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.GetPromotionEventsResponse
 */
export class GetPromotionEventsResponse extends Message<GetPromotionEventsResponse> {
  /**
   * @generated from field: repeated akuity.kargo.v1.PromotionEvent promotion_events = 1;
   */
  promotionEvents: PromotionEvent[] = [];

  /**
   * @generated from field: int64 count = 2;
   */
  count = protoInt64.zero;

  /**
   * @generated from field: repeated string field_result = 3;
   */
  fieldResult: string[] = [];

  constructor(data?: PartialMessage<GetPromotionEventsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.GetPromotionEventsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "promotion_events", kind: "message", T: PromotionEvent, repeated: true },
    { no: 2, name: "count", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 3, name: "field_result", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetPromotionEventsResponse {
    return new GetPromotionEventsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetPromotionEventsResponse {
    return new GetPromotionEventsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetPromotionEventsResponse {
    return new GetPromotionEventsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetPromotionEventsResponse | PlainMessage<GetPromotionEventsResponse> | undefined, b: GetPromotionEventsResponse | PlainMessage<GetPromotionEventsResponse> | undefined): boolean {
    return proto3.util.equals(GetPromotionEventsResponse, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.UpdateInstanceAgentVersionRequest
 */
export class UpdateInstanceAgentVersionRequest extends Message<UpdateInstanceAgentVersionRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string instance_id = 2;
   */
  instanceId = "";

  /**
   * @generated from field: repeated string agent_names = 3;
   */
  agentNames: string[] = [];

  /**
   * @generated from field: string new_version = 4;
   */
  newVersion = "";

  /**
   * @generated from field: string workspace_id = 5;
   */
  workspaceId = "";

  constructor(data?: PartialMessage<UpdateInstanceAgentVersionRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.UpdateInstanceAgentVersionRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "instance_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "agent_names", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 4, name: "new_version", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateInstanceAgentVersionRequest {
    return new UpdateInstanceAgentVersionRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateInstanceAgentVersionRequest {
    return new UpdateInstanceAgentVersionRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateInstanceAgentVersionRequest {
    return new UpdateInstanceAgentVersionRequest().fromJsonString(jsonString, options);
  }

  static equals(a: UpdateInstanceAgentVersionRequest | PlainMessage<UpdateInstanceAgentVersionRequest> | undefined, b: UpdateInstanceAgentVersionRequest | PlainMessage<UpdateInstanceAgentVersionRequest> | undefined): boolean {
    return proto3.util.equals(UpdateInstanceAgentVersionRequest, a, b);
  }
}

/**
 * explicitly empty 
 *
 * @generated from message akuity.kargo.v1.UpdateInstanceAgentVersionResponse
 */
export class UpdateInstanceAgentVersionResponse extends Message<UpdateInstanceAgentVersionResponse> {
  constructor(data?: PartialMessage<UpdateInstanceAgentVersionResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.UpdateInstanceAgentVersionResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateInstanceAgentVersionResponse {
    return new UpdateInstanceAgentVersionResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateInstanceAgentVersionResponse {
    return new UpdateInstanceAgentVersionResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateInstanceAgentVersionResponse {
    return new UpdateInstanceAgentVersionResponse().fromJsonString(jsonString, options);
  }

  static equals(a: UpdateInstanceAgentVersionResponse | PlainMessage<UpdateInstanceAgentVersionResponse> | undefined, b: UpdateInstanceAgentVersionResponse | PlainMessage<UpdateInstanceAgentVersionResponse> | undefined): boolean {
    return proto3.util.equals(UpdateInstanceAgentVersionResponse, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.RotateInstanceAgentCredentialsRequest
 */
export class RotateInstanceAgentCredentialsRequest extends Message<RotateInstanceAgentCredentialsRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string instance_id = 2;
   */
  instanceId = "";

  /**
   * @generated from field: repeated string agent_names = 3;
   */
  agentNames: string[] = [];

  /**
   * @generated from field: string workspace_id = 4;
   */
  workspaceId = "";

  constructor(data?: PartialMessage<RotateInstanceAgentCredentialsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.RotateInstanceAgentCredentialsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "instance_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "agent_names", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 4, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RotateInstanceAgentCredentialsRequest {
    return new RotateInstanceAgentCredentialsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RotateInstanceAgentCredentialsRequest {
    return new RotateInstanceAgentCredentialsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RotateInstanceAgentCredentialsRequest {
    return new RotateInstanceAgentCredentialsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: RotateInstanceAgentCredentialsRequest | PlainMessage<RotateInstanceAgentCredentialsRequest> | undefined, b: RotateInstanceAgentCredentialsRequest | PlainMessage<RotateInstanceAgentCredentialsRequest> | undefined): boolean {
    return proto3.util.equals(RotateInstanceAgentCredentialsRequest, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.RotateInstanceAgentCredentialsResponse
 */
export class RotateInstanceAgentCredentialsResponse extends Message<RotateInstanceAgentCredentialsResponse> {
  /**
   * @generated from field: repeated string skipped_agents = 1;
   */
  skippedAgents: string[] = [];

  constructor(data?: PartialMessage<RotateInstanceAgentCredentialsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.RotateInstanceAgentCredentialsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "skipped_agents", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RotateInstanceAgentCredentialsResponse {
    return new RotateInstanceAgentCredentialsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RotateInstanceAgentCredentialsResponse {
    return new RotateInstanceAgentCredentialsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RotateInstanceAgentCredentialsResponse {
    return new RotateInstanceAgentCredentialsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: RotateInstanceAgentCredentialsResponse | PlainMessage<RotateInstanceAgentCredentialsResponse> | undefined, b: RotateInstanceAgentCredentialsResponse | PlainMessage<RotateInstanceAgentCredentialsResponse> | undefined): boolean {
    return proto3.util.equals(RotateInstanceAgentCredentialsResponse, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.DeleteInstanceAgentRequest
 */
export class DeleteInstanceAgentRequest extends Message<DeleteInstanceAgentRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string instance_id = 2;
   */
  instanceId = "";

  /**
   * @generated from field: string id = 3;
   */
  id = "";

  /**
   * @generated from field: string workspace_id = 4;
   */
  workspaceId = "";

  constructor(data?: PartialMessage<DeleteInstanceAgentRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.DeleteInstanceAgentRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "instance_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteInstanceAgentRequest {
    return new DeleteInstanceAgentRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteInstanceAgentRequest {
    return new DeleteInstanceAgentRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteInstanceAgentRequest {
    return new DeleteInstanceAgentRequest().fromJsonString(jsonString, options);
  }

  static equals(a: DeleteInstanceAgentRequest | PlainMessage<DeleteInstanceAgentRequest> | undefined, b: DeleteInstanceAgentRequest | PlainMessage<DeleteInstanceAgentRequest> | undefined): boolean {
    return proto3.util.equals(DeleteInstanceAgentRequest, a, b);
  }
}

/**
 * explicitly empty 
 *
 * @generated from message akuity.kargo.v1.DeleteInstanceAgentResponse
 */
export class DeleteInstanceAgentResponse extends Message<DeleteInstanceAgentResponse> {
  constructor(data?: PartialMessage<DeleteInstanceAgentResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.DeleteInstanceAgentResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteInstanceAgentResponse {
    return new DeleteInstanceAgentResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteInstanceAgentResponse {
    return new DeleteInstanceAgentResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteInstanceAgentResponse {
    return new DeleteInstanceAgentResponse().fromJsonString(jsonString, options);
  }

  static equals(a: DeleteInstanceAgentResponse | PlainMessage<DeleteInstanceAgentResponse> | undefined, b: DeleteInstanceAgentResponse | PlainMessage<DeleteInstanceAgentResponse> | undefined): boolean {
    return proto3.util.equals(DeleteInstanceAgentResponse, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.DeleteInstanceRequest
 */
export class DeleteInstanceRequest extends Message<DeleteInstanceRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string id = 2;
   */
  id = "";

  /**
   * @generated from field: string workspace_id = 3;
   */
  workspaceId = "";

  constructor(data?: PartialMessage<DeleteInstanceRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.DeleteInstanceRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteInstanceRequest {
    return new DeleteInstanceRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteInstanceRequest {
    return new DeleteInstanceRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteInstanceRequest {
    return new DeleteInstanceRequest().fromJsonString(jsonString, options);
  }

  static equals(a: DeleteInstanceRequest | PlainMessage<DeleteInstanceRequest> | undefined, b: DeleteInstanceRequest | PlainMessage<DeleteInstanceRequest> | undefined): boolean {
    return proto3.util.equals(DeleteInstanceRequest, a, b);
  }
}

/**
 * explicitly empty 
 *
 * @generated from message akuity.kargo.v1.DeleteInstanceResponse
 */
export class DeleteInstanceResponse extends Message<DeleteInstanceResponse> {
  constructor(data?: PartialMessage<DeleteInstanceResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.DeleteInstanceResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteInstanceResponse {
    return new DeleteInstanceResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteInstanceResponse {
    return new DeleteInstanceResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteInstanceResponse {
    return new DeleteInstanceResponse().fromJsonString(jsonString, options);
  }

  static equals(a: DeleteInstanceResponse | PlainMessage<DeleteInstanceResponse> | undefined, b: DeleteInstanceResponse | PlainMessage<DeleteInstanceResponse> | undefined): boolean {
    return proto3.util.equals(DeleteInstanceResponse, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.ListKargoInstancesRequest
 */
export class ListKargoInstancesRequest extends Message<ListKargoInstancesRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string workspace_id = 2;
   */
  workspaceId = "";

  constructor(data?: PartialMessage<ListKargoInstancesRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.ListKargoInstancesRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListKargoInstancesRequest {
    return new ListKargoInstancesRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListKargoInstancesRequest {
    return new ListKargoInstancesRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListKargoInstancesRequest {
    return new ListKargoInstancesRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ListKargoInstancesRequest | PlainMessage<ListKargoInstancesRequest> | undefined, b: ListKargoInstancesRequest | PlainMessage<ListKargoInstancesRequest> | undefined): boolean {
    return proto3.util.equals(ListKargoInstancesRequest, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.ListKargoInstancesResponse
 */
export class ListKargoInstancesResponse extends Message<ListKargoInstancesResponse> {
  /**
   * @generated from field: repeated akuity.kargo.v1.KargoInstance instances = 1;
   */
  instances: KargoInstance[] = [];

  constructor(data?: PartialMessage<ListKargoInstancesResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.ListKargoInstancesResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "instances", kind: "message", T: KargoInstance, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListKargoInstancesResponse {
    return new ListKargoInstancesResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListKargoInstancesResponse {
    return new ListKargoInstancesResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListKargoInstancesResponse {
    return new ListKargoInstancesResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListKargoInstancesResponse | PlainMessage<ListKargoInstancesResponse> | undefined, b: ListKargoInstancesResponse | PlainMessage<ListKargoInstancesResponse> | undefined): boolean {
    return proto3.util.equals(ListKargoInstancesResponse, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.CreateKargoInstanceRequest
 */
export class CreateKargoInstanceRequest extends Message<CreateKargoInstanceRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string name = 2;
   */
  name = "";

  /**
   * @generated from field: string version = 3;
   */
  version = "";

  /**
   * @generated from field: optional string description = 4;
   */
  description?: string;

  /**
   * @generated from field: string workspace_id = 5;
   */
  workspaceId = "";

  constructor(data?: PartialMessage<CreateKargoInstanceRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.CreateKargoInstanceRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "version", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "description", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 5, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateKargoInstanceRequest {
    return new CreateKargoInstanceRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateKargoInstanceRequest {
    return new CreateKargoInstanceRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateKargoInstanceRequest {
    return new CreateKargoInstanceRequest().fromJsonString(jsonString, options);
  }

  static equals(a: CreateKargoInstanceRequest | PlainMessage<CreateKargoInstanceRequest> | undefined, b: CreateKargoInstanceRequest | PlainMessage<CreateKargoInstanceRequest> | undefined): boolean {
    return proto3.util.equals(CreateKargoInstanceRequest, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.CreateKargoInstanceResponse
 */
export class CreateKargoInstanceResponse extends Message<CreateKargoInstanceResponse> {
  /**
   * @generated from field: akuity.kargo.v1.KargoInstance instance = 1;
   */
  instance?: KargoInstance;

  constructor(data?: PartialMessage<CreateKargoInstanceResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.CreateKargoInstanceResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "instance", kind: "message", T: KargoInstance },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateKargoInstanceResponse {
    return new CreateKargoInstanceResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateKargoInstanceResponse {
    return new CreateKargoInstanceResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateKargoInstanceResponse {
    return new CreateKargoInstanceResponse().fromJsonString(jsonString, options);
  }

  static equals(a: CreateKargoInstanceResponse | PlainMessage<CreateKargoInstanceResponse> | undefined, b: CreateKargoInstanceResponse | PlainMessage<CreateKargoInstanceResponse> | undefined): boolean {
    return proto3.util.equals(CreateKargoInstanceResponse, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.GetKargoInstanceRequest
 */
export class GetKargoInstanceRequest extends Message<GetKargoInstanceRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string name = 2;
   */
  name = "";

  /**
   * @generated from field: string workspace_id = 3;
   */
  workspaceId = "";

  constructor(data?: PartialMessage<GetKargoInstanceRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.GetKargoInstanceRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetKargoInstanceRequest {
    return new GetKargoInstanceRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetKargoInstanceRequest {
    return new GetKargoInstanceRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetKargoInstanceRequest {
    return new GetKargoInstanceRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetKargoInstanceRequest | PlainMessage<GetKargoInstanceRequest> | undefined, b: GetKargoInstanceRequest | PlainMessage<GetKargoInstanceRequest> | undefined): boolean {
    return proto3.util.equals(GetKargoInstanceRequest, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.CertificateStatus
 */
export class CertificateStatus extends Message<CertificateStatus> {
  /**
   * @generated from field: bool is_cname_set = 1;
   */
  isCnameSet = false;

  /**
   * @generated from field: bool is_issued = 2;
   */
  isIssued = false;

  /**
   * @generated from field: string message = 3;
   */
  message = "";

  constructor(data?: PartialMessage<CertificateStatus>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.CertificateStatus";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "is_cname_set", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 2, name: "is_issued", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 3, name: "message", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CertificateStatus {
    return new CertificateStatus().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CertificateStatus {
    return new CertificateStatus().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CertificateStatus {
    return new CertificateStatus().fromJsonString(jsonString, options);
  }

  static equals(a: CertificateStatus | PlainMessage<CertificateStatus> | undefined, b: CertificateStatus | PlainMessage<CertificateStatus> | undefined): boolean {
    return proto3.util.equals(CertificateStatus, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.KargoInstance
 */
export class KargoInstance extends Message<KargoInstance> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  /**
   * @generated from field: string name = 2;
   */
  name = "";

  /**
   * @generated from field: string description = 3;
   */
  description = "";

  /**
   * @generated from field: akuity.kargo.v1.KargoInstanceSpec spec = 4;
   */
  spec?: KargoInstanceSpec;

  /**
   * @generated from field: string hostname = 5;
   */
  hostname = "";

  /**
   * @generated from field: uint32 generation = 6;
   */
  generation = 0;

  /**
   * @generated from field: akuity.types.status.health.v1.Status health_status = 7;
   */
  healthStatus?: Status;

  /**
   * @generated from field: akuity.types.status.reconciliation.v1.Status reconciliation_status = 8;
   */
  reconciliationStatus?: Status$1;

  /**
   * @generated from field: optional google.protobuf.Timestamp delete_time = 9;
   */
  deleteTime?: Timestamp;

  /**
   * @generated from field: string owner_organization_name = 10;
   */
  ownerOrganizationName = "";

  /**
   * @generated from field: string version = 11;
   */
  version = "";

  /**
   * @generated from field: optional akuity.kargo.v1.KargoControllerCM controller_cm = 12;
   */
  controllerCm?: KargoControllerCM;

  /**
   * @generated from field: optional akuity.kargo.v1.KargoWebhookCM webhook_cm = 13;
   */
  webhookCm?: KargoWebhookCM;

  /**
   * @generated from field: optional akuity.kargo.v1.KargoApiCM api_cm = 14;
   */
  apiCm?: KargoApiCM;

  /**
   * @generated from field: optional akuity.kargo.v1.KargoApiSecret api_secret = 15;
   */
  apiSecret?: KargoApiSecret;

  /**
   * @generated from field: optional akuity.kargo.v1.KargoOidcConfig oidc_config = 16;
   */
  oidcConfig?: KargoOidcConfig;

  /**
   * @generated from field: string subdomain = 17;
   */
  subdomain = "";

  /**
   * @generated from field: string workspace_id = 18;
   */
  workspaceId = "";

  /**
   * @generated from field: optional akuity.kargo.v1.KargoMiscellaneousSecrets miscellaneous_secrets = 19;
   */
  miscellaneousSecrets?: KargoMiscellaneousSecrets;

  /**
   * @generated from field: string fqdn = 20;
   */
  fqdn = "";

  /**
   * @generated from field: optional akuity.kargo.v1.CertificateStatus certificate_status = 21;
   */
  certificateStatus?: CertificateStatus;

  /**
   * @generated from field: optional bool unsupported_version = 22;
   */
  unsupportedVersion?: boolean;

  constructor(data?: PartialMessage<KargoInstance>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.KargoInstance";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "description", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "spec", kind: "message", T: KargoInstanceSpec },
    { no: 5, name: "hostname", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 6, name: "generation", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 7, name: "health_status", kind: "message", T: Status },
    { no: 8, name: "reconciliation_status", kind: "message", T: Status$1 },
    { no: 9, name: "delete_time", kind: "message", T: Timestamp, opt: true },
    { no: 10, name: "owner_organization_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 11, name: "version", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 12, name: "controller_cm", kind: "message", T: KargoControllerCM, opt: true },
    { no: 13, name: "webhook_cm", kind: "message", T: KargoWebhookCM, opt: true },
    { no: 14, name: "api_cm", kind: "message", T: KargoApiCM, opt: true },
    { no: 15, name: "api_secret", kind: "message", T: KargoApiSecret, opt: true },
    { no: 16, name: "oidc_config", kind: "message", T: KargoOidcConfig, opt: true },
    { no: 17, name: "subdomain", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 18, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 19, name: "miscellaneous_secrets", kind: "message", T: KargoMiscellaneousSecrets, opt: true },
    { no: 20, name: "fqdn", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 21, name: "certificate_status", kind: "message", T: CertificateStatus, opt: true },
    { no: 22, name: "unsupported_version", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): KargoInstance {
    return new KargoInstance().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): KargoInstance {
    return new KargoInstance().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): KargoInstance {
    return new KargoInstance().fromJsonString(jsonString, options);
  }

  static equals(a: KargoInstance | PlainMessage<KargoInstance> | undefined, b: KargoInstance | PlainMessage<KargoInstance> | undefined): boolean {
    return proto3.util.equals(KargoInstance, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.KargoIPAllowListEntry
 */
export class KargoIPAllowListEntry extends Message<KargoIPAllowListEntry> {
  /**
   * @generated from field: string ip = 1;
   */
  ip = "";

  /**
   * @generated from field: string description = 2;
   */
  description = "";

  constructor(data?: PartialMessage<KargoIPAllowListEntry>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.KargoIPAllowListEntry";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "ip", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "description", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): KargoIPAllowListEntry {
    return new KargoIPAllowListEntry().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): KargoIPAllowListEntry {
    return new KargoIPAllowListEntry().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): KargoIPAllowListEntry {
    return new KargoIPAllowListEntry().fromJsonString(jsonString, options);
  }

  static equals(a: KargoIPAllowListEntry | PlainMessage<KargoIPAllowListEntry> | undefined, b: KargoIPAllowListEntry | PlainMessage<KargoIPAllowListEntry> | undefined): boolean {
    return proto3.util.equals(KargoIPAllowListEntry, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.KargoAgentCustomization
 */
export class KargoAgentCustomization extends Message<KargoAgentCustomization> {
  /**
   * @generated from field: bool auto_upgrade_disabled = 1;
   */
  autoUpgradeDisabled = false;

  /**
   * @generated from field: google.protobuf.Struct kustomization = 2;
   */
  kustomization?: Struct;

  constructor(data?: PartialMessage<KargoAgentCustomization>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.KargoAgentCustomization";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "auto_upgrade_disabled", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 2, name: "kustomization", kind: "message", T: Struct },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): KargoAgentCustomization {
    return new KargoAgentCustomization().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): KargoAgentCustomization {
    return new KargoAgentCustomization().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): KargoAgentCustomization {
    return new KargoAgentCustomization().fromJsonString(jsonString, options);
  }

  static equals(a: KargoAgentCustomization | PlainMessage<KargoAgentCustomization> | undefined, b: KargoAgentCustomization | PlainMessage<KargoAgentCustomization> | undefined): boolean {
    return proto3.util.equals(KargoAgentCustomization, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.KargoInstanceSpec
 */
export class KargoInstanceSpec extends Message<KargoInstanceSpec> {
  /**
   * @generated from field: bool backend_ip_allow_list_enabled = 2;
   */
  backendIpAllowListEnabled = false;

  /**
   * @generated from field: repeated akuity.kargo.v1.KargoIPAllowListEntry ip_allow_list = 1;
   */
  ipAllowList: KargoIPAllowListEntry[] = [];

  /**
   * @generated from field: akuity.kargo.v1.KargoAgentCustomization agent_customization_defaults = 3;
   */
  agentCustomizationDefaults?: KargoAgentCustomization;

  /**
   * @generated from field: string default_shard_agent = 4;
   */
  defaultShardAgent = "";

  /**
   * @generated from field: repeated string global_credentials_ns = 5;
   */
  globalCredentialsNs: string[] = [];

  /**
   * @generated from field: repeated string global_service_account_ns = 6;
   */
  globalServiceAccountNs: string[] = [];

  /**
   * @generated from field: akuity.kargo.v1.AkuityIntelligence akuity_intelligence = 7;
   */
  akuityIntelligence?: AkuityIntelligence;

  constructor(data?: PartialMessage<KargoInstanceSpec>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.KargoInstanceSpec";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 2, name: "backend_ip_allow_list_enabled", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 1, name: "ip_allow_list", kind: "message", T: KargoIPAllowListEntry, repeated: true },
    { no: 3, name: "agent_customization_defaults", kind: "message", T: KargoAgentCustomization },
    { no: 4, name: "default_shard_agent", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "global_credentials_ns", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 6, name: "global_service_account_ns", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 7, name: "akuity_intelligence", kind: "message", T: AkuityIntelligence },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): KargoInstanceSpec {
    return new KargoInstanceSpec().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): KargoInstanceSpec {
    return new KargoInstanceSpec().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): KargoInstanceSpec {
    return new KargoInstanceSpec().fromJsonString(jsonString, options);
  }

  static equals(a: KargoInstanceSpec | PlainMessage<KargoInstanceSpec> | undefined, b: KargoInstanceSpec | PlainMessage<KargoInstanceSpec> | undefined): boolean {
    return proto3.util.equals(KargoInstanceSpec, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.AkuityIntelligence
 */
export class AkuityIntelligence extends Message<AkuityIntelligence> {
  /**
   * @generated from field: bool ai_support_engineer_enabled = 1;
   */
  aiSupportEngineerEnabled = false;

  /**
   * @generated from field: bool enabled = 2;
   */
  enabled = false;

  /**
   * @generated from field: repeated string allowed_usernames = 3;
   */
  allowedUsernames: string[] = [];

  /**
   * @generated from field: repeated string allowed_groups = 4;
   */
  allowedGroups: string[] = [];

  /**
   * @generated from field: string model_version = 5;
   */
  modelVersion = "";

  constructor(data?: PartialMessage<AkuityIntelligence>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.AkuityIntelligence";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "ai_support_engineer_enabled", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 2, name: "enabled", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 3, name: "allowed_usernames", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 4, name: "allowed_groups", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 5, name: "model_version", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AkuityIntelligence {
    return new AkuityIntelligence().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AkuityIntelligence {
    return new AkuityIntelligence().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AkuityIntelligence {
    return new AkuityIntelligence().fromJsonString(jsonString, options);
  }

  static equals(a: AkuityIntelligence | PlainMessage<AkuityIntelligence> | undefined, b: AkuityIntelligence | PlainMessage<AkuityIntelligence> | undefined): boolean {
    return proto3.util.equals(AkuityIntelligence, a, b);
  }
}

/**
 * nothing to store for now
 *
 * @generated from message akuity.kargo.v1.KargoControllerCM
 */
export class KargoControllerCM extends Message<KargoControllerCM> {
  constructor(data?: PartialMessage<KargoControllerCM>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.KargoControllerCM";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): KargoControllerCM {
    return new KargoControllerCM().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): KargoControllerCM {
    return new KargoControllerCM().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): KargoControllerCM {
    return new KargoControllerCM().fromJsonString(jsonString, options);
  }

  static equals(a: KargoControllerCM | PlainMessage<KargoControllerCM> | undefined, b: KargoControllerCM | PlainMessage<KargoControllerCM> | undefined): boolean {
    return proto3.util.equals(KargoControllerCM, a, b);
  }
}

/**
 * nothing to store for now
 *
 * @generated from message akuity.kargo.v1.KargoWebhookCM
 */
export class KargoWebhookCM extends Message<KargoWebhookCM> {
  constructor(data?: PartialMessage<KargoWebhookCM>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.KargoWebhookCM";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): KargoWebhookCM {
    return new KargoWebhookCM().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): KargoWebhookCM {
    return new KargoWebhookCM().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): KargoWebhookCM {
    return new KargoWebhookCM().fromJsonString(jsonString, options);
  }

  static equals(a: KargoWebhookCM | PlainMessage<KargoWebhookCM> | undefined, b: KargoWebhookCM | PlainMessage<KargoWebhookCM> | undefined): boolean {
    return proto3.util.equals(KargoWebhookCM, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.KargoApiCM
 */
export class KargoApiCM extends Message<KargoApiCM> {
  /**
   * @generated from field: bool admin_account_enabled = 1;
   */
  adminAccountEnabled = false;

  /**
   * @generated from field: string admin_account_token_ttl = 4;
   */
  adminAccountTokenTtl = "";

  constructor(data?: PartialMessage<KargoApiCM>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.KargoApiCM";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "admin_account_enabled", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 4, name: "admin_account_token_ttl", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): KargoApiCM {
    return new KargoApiCM().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): KargoApiCM {
    return new KargoApiCM().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): KargoApiCM {
    return new KargoApiCM().fromJsonString(jsonString, options);
  }

  static equals(a: KargoApiCM | PlainMessage<KargoApiCM> | undefined, b: KargoApiCM | PlainMessage<KargoApiCM> | undefined): boolean {
    return proto3.util.equals(KargoApiCM, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.KargoApiSecret
 */
export class KargoApiSecret extends Message<KargoApiSecret> {
  /**
   * @generated from field: string admin_account_password_hash = 1;
   */
  adminAccountPasswordHash = "";

  constructor(data?: PartialMessage<KargoApiSecret>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.KargoApiSecret";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "admin_account_password_hash", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): KargoApiSecret {
    return new KargoApiSecret().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): KargoApiSecret {
    return new KargoApiSecret().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): KargoApiSecret {
    return new KargoApiSecret().fromJsonString(jsonString, options);
  }

  static equals(a: KargoApiSecret | PlainMessage<KargoApiSecret> | undefined, b: KargoApiSecret | PlainMessage<KargoApiSecret> | undefined): boolean {
    return proto3.util.equals(KargoApiSecret, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.KargoPredefinedAccountClaimValue
 */
export class KargoPredefinedAccountClaimValue extends Message<KargoPredefinedAccountClaimValue> {
  /**
   * @generated from field: repeated string values = 1;
   */
  values: string[] = [];

  constructor(data?: PartialMessage<KargoPredefinedAccountClaimValue>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.KargoPredefinedAccountClaimValue";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "values", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): KargoPredefinedAccountClaimValue {
    return new KargoPredefinedAccountClaimValue().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): KargoPredefinedAccountClaimValue {
    return new KargoPredefinedAccountClaimValue().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): KargoPredefinedAccountClaimValue {
    return new KargoPredefinedAccountClaimValue().fromJsonString(jsonString, options);
  }

  static equals(a: KargoPredefinedAccountClaimValue | PlainMessage<KargoPredefinedAccountClaimValue> | undefined, b: KargoPredefinedAccountClaimValue | PlainMessage<KargoPredefinedAccountClaimValue> | undefined): boolean {
    return proto3.util.equals(KargoPredefinedAccountClaimValue, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.KargoPredefinedAccountData
 */
export class KargoPredefinedAccountData extends Message<KargoPredefinedAccountData> {
  /**
   * Use claims instead;
   *
   * @generated from field: repeated string email = 1 [deprecated = true];
   * @deprecated
   */
  email: string[] = [];

  /**
   * Use claims instead;
   *
   * @generated from field: repeated string sub = 2 [deprecated = true];
   * @deprecated
   */
  sub: string[] = [];

  /**
   * Use claims instead;
   *
   * @generated from field: repeated string groups = 3 [deprecated = true];
   * @deprecated
   */
  groups: string[] = [];

  /**
   * @generated from field: map<string, akuity.kargo.v1.KargoPredefinedAccountClaimValue> claims = 4;
   */
  claims: { [key: string]: KargoPredefinedAccountClaimValue } = {};

  constructor(data?: PartialMessage<KargoPredefinedAccountData>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.KargoPredefinedAccountData";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "email", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 2, name: "sub", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 3, name: "groups", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 4, name: "claims", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "message", T: KargoPredefinedAccountClaimValue} },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): KargoPredefinedAccountData {
    return new KargoPredefinedAccountData().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): KargoPredefinedAccountData {
    return new KargoPredefinedAccountData().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): KargoPredefinedAccountData {
    return new KargoPredefinedAccountData().fromJsonString(jsonString, options);
  }

  static equals(a: KargoPredefinedAccountData | PlainMessage<KargoPredefinedAccountData> | undefined, b: KargoPredefinedAccountData | PlainMessage<KargoPredefinedAccountData> | undefined): boolean {
    return proto3.util.equals(KargoPredefinedAccountData, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.KargoMiscellaneousSecrets
 */
export class KargoMiscellaneousSecrets extends Message<KargoMiscellaneousSecrets> {
  /**
   * @generated from field: optional akuity.kargo.v1.DataDogRolloutsSecret datadog_rollouts_secret = 1;
   */
  datadogRolloutsSecret?: DataDogRolloutsSecret;

  /**
   * @generated from field: optional akuity.kargo.v1.NewRelicRolloutsSecret newrelic_rollouts_secret = 2;
   */
  newrelicRolloutsSecret?: NewRelicRolloutsSecret;

  /**
   * @generated from field: optional akuity.kargo.v1.InfluxDbRolloutsSecret influxdb_rollouts_secret = 3;
   */
  influxdbRolloutsSecret?: InfluxDbRolloutsSecret;

  constructor(data?: PartialMessage<KargoMiscellaneousSecrets>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.KargoMiscellaneousSecrets";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "datadog_rollouts_secret", kind: "message", T: DataDogRolloutsSecret, opt: true },
    { no: 2, name: "newrelic_rollouts_secret", kind: "message", T: NewRelicRolloutsSecret, opt: true },
    { no: 3, name: "influxdb_rollouts_secret", kind: "message", T: InfluxDbRolloutsSecret, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): KargoMiscellaneousSecrets {
    return new KargoMiscellaneousSecrets().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): KargoMiscellaneousSecrets {
    return new KargoMiscellaneousSecrets().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): KargoMiscellaneousSecrets {
    return new KargoMiscellaneousSecrets().fromJsonString(jsonString, options);
  }

  static equals(a: KargoMiscellaneousSecrets | PlainMessage<KargoMiscellaneousSecrets> | undefined, b: KargoMiscellaneousSecrets | PlainMessage<KargoMiscellaneousSecrets> | undefined): boolean {
    return proto3.util.equals(KargoMiscellaneousSecrets, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.DataDogRolloutsSecret
 */
export class DataDogRolloutsSecret extends Message<DataDogRolloutsSecret> {
  /**
   * @generated from field: string address = 1;
   */
  address = "";

  /**
   * @generated from field: string api_key = 2;
   */
  apiKey = "";

  /**
   * @generated from field: string app_key = 3;
   */
  appKey = "";

  constructor(data?: PartialMessage<DataDogRolloutsSecret>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.DataDogRolloutsSecret";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "address", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "api_key", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "app_key", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DataDogRolloutsSecret {
    return new DataDogRolloutsSecret().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DataDogRolloutsSecret {
    return new DataDogRolloutsSecret().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DataDogRolloutsSecret {
    return new DataDogRolloutsSecret().fromJsonString(jsonString, options);
  }

  static equals(a: DataDogRolloutsSecret | PlainMessage<DataDogRolloutsSecret> | undefined, b: DataDogRolloutsSecret | PlainMessage<DataDogRolloutsSecret> | undefined): boolean {
    return proto3.util.equals(DataDogRolloutsSecret, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.NewRelicRolloutsSecret
 */
export class NewRelicRolloutsSecret extends Message<NewRelicRolloutsSecret> {
  /**
   * @generated from field: string personal_api_key = 1;
   */
  personalApiKey = "";

  /**
   * @generated from field: string account_id = 2;
   */
  accountId = "";

  /**
   * @generated from field: string region = 3;
   */
  region = "";

  /**
   * @generated from field: string base_url_rest = 4;
   */
  baseUrlRest = "";

  /**
   * @generated from field: string base_url_nerdgraph = 5;
   */
  baseUrlNerdgraph = "";

  constructor(data?: PartialMessage<NewRelicRolloutsSecret>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.NewRelicRolloutsSecret";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "personal_api_key", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "account_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "region", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "base_url_rest", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "base_url_nerdgraph", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): NewRelicRolloutsSecret {
    return new NewRelicRolloutsSecret().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): NewRelicRolloutsSecret {
    return new NewRelicRolloutsSecret().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): NewRelicRolloutsSecret {
    return new NewRelicRolloutsSecret().fromJsonString(jsonString, options);
  }

  static equals(a: NewRelicRolloutsSecret | PlainMessage<NewRelicRolloutsSecret> | undefined, b: NewRelicRolloutsSecret | PlainMessage<NewRelicRolloutsSecret> | undefined): boolean {
    return proto3.util.equals(NewRelicRolloutsSecret, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.InfluxDbRolloutsSecret
 */
export class InfluxDbRolloutsSecret extends Message<InfluxDbRolloutsSecret> {
  /**
   * @generated from field: string influxdb_address = 1;
   */
  influxdbAddress = "";

  /**
   * @generated from field: string auth_token = 2;
   */
  authToken = "";

  /**
   * @generated from field: string org = 3;
   */
  org = "";

  constructor(data?: PartialMessage<InfluxDbRolloutsSecret>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.InfluxDbRolloutsSecret";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "influxdb_address", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "auth_token", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "org", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InfluxDbRolloutsSecret {
    return new InfluxDbRolloutsSecret().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InfluxDbRolloutsSecret {
    return new InfluxDbRolloutsSecret().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InfluxDbRolloutsSecret {
    return new InfluxDbRolloutsSecret().fromJsonString(jsonString, options);
  }

  static equals(a: InfluxDbRolloutsSecret | PlainMessage<InfluxDbRolloutsSecret> | undefined, b: InfluxDbRolloutsSecret | PlainMessage<InfluxDbRolloutsSecret> | undefined): boolean {
    return proto3.util.equals(InfluxDbRolloutsSecret, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.KargoOidcConfig
 */
export class KargoOidcConfig extends Message<KargoOidcConfig> {
  /**
   * @generated from field: bool enabled = 1;
   */
  enabled = false;

  /**
   * @generated from field: bool dex_enabled = 2;
   */
  dexEnabled = false;

  /**
   * @generated from field: string dex_config = 3;
   */
  dexConfig = "";

  /**
   * @generated from field: map<string, akuity.kargo.v1.KargoOidcConfig.Value> dex_config_secret = 4;
   */
  dexConfigSecret: { [key: string]: KargoOidcConfig_Value } = {};

  /**
   * @generated from field: string issuer_url = 5;
   */
  issuerUrl = "";

  /**
   * @generated from field: string client_id = 6;
   */
  clientId = "";

  /**
   * @generated from field: string cli_client_id = 7;
   */
  cliClientId = "";

  /**
   * @generated from field: akuity.kargo.v1.KargoPredefinedAccountData admin_account = 8;
   */
  adminAccount?: KargoPredefinedAccountData;

  /**
   * @generated from field: akuity.kargo.v1.KargoPredefinedAccountData viewer_account = 9;
   */
  viewerAccount?: KargoPredefinedAccountData;

  /**
   * @generated from field: repeated string additional_scopes = 10;
   */
  additionalScopes: string[] = [];

  /**
   * @generated from field: akuity.kargo.v1.KargoPredefinedAccountData user_account = 11;
   */
  userAccount?: KargoPredefinedAccountData;

  constructor(data?: PartialMessage<KargoOidcConfig>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.KargoOidcConfig";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "enabled", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 2, name: "dex_enabled", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 3, name: "dex_config", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "dex_config_secret", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "message", T: KargoOidcConfig_Value} },
    { no: 5, name: "issuer_url", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 6, name: "client_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 7, name: "cli_client_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 8, name: "admin_account", kind: "message", T: KargoPredefinedAccountData },
    { no: 9, name: "viewer_account", kind: "message", T: KargoPredefinedAccountData },
    { no: 10, name: "additional_scopes", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 11, name: "user_account", kind: "message", T: KargoPredefinedAccountData },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): KargoOidcConfig {
    return new KargoOidcConfig().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): KargoOidcConfig {
    return new KargoOidcConfig().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): KargoOidcConfig {
    return new KargoOidcConfig().fromJsonString(jsonString, options);
  }

  static equals(a: KargoOidcConfig | PlainMessage<KargoOidcConfig> | undefined, b: KargoOidcConfig | PlainMessage<KargoOidcConfig> | undefined): boolean {
    return proto3.util.equals(KargoOidcConfig, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.KargoOidcConfig.Value
 */
export class KargoOidcConfig_Value extends Message<KargoOidcConfig_Value> {
  /**
   * @generated from field: optional string value = 1;
   */
  value?: string;

  constructor(data?: PartialMessage<KargoOidcConfig_Value>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.KargoOidcConfig.Value";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "value", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): KargoOidcConfig_Value {
    return new KargoOidcConfig_Value().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): KargoOidcConfig_Value {
    return new KargoOidcConfig_Value().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): KargoOidcConfig_Value {
    return new KargoOidcConfig_Value().fromJsonString(jsonString, options);
  }

  static equals(a: KargoOidcConfig_Value | PlainMessage<KargoOidcConfig_Value> | undefined, b: KargoOidcConfig_Value | PlainMessage<KargoOidcConfig_Value> | undefined): boolean {
    return proto3.util.equals(KargoOidcConfig_Value, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.WatchKargoInstancesRequest
 */
export class WatchKargoInstancesRequest extends Message<WatchKargoInstancesRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: optional string instance_id = 2;
   */
  instanceId?: string;

  /**
   * @generated from field: string workspace_id = 3;
   */
  workspaceId = "";

  constructor(data?: PartialMessage<WatchKargoInstancesRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.WatchKargoInstancesRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "instance_id", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 3, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WatchKargoInstancesRequest {
    return new WatchKargoInstancesRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WatchKargoInstancesRequest {
    return new WatchKargoInstancesRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WatchKargoInstancesRequest {
    return new WatchKargoInstancesRequest().fromJsonString(jsonString, options);
  }

  static equals(a: WatchKargoInstancesRequest | PlainMessage<WatchKargoInstancesRequest> | undefined, b: WatchKargoInstancesRequest | PlainMessage<WatchKargoInstancesRequest> | undefined): boolean {
    return proto3.util.equals(WatchKargoInstancesRequest, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.GetKargoInstanceResponse
 */
export class GetKargoInstanceResponse extends Message<GetKargoInstanceResponse> {
  /**
   * @generated from field: akuity.kargo.v1.KargoInstance instance = 1;
   */
  instance?: KargoInstance;

  constructor(data?: PartialMessage<GetKargoInstanceResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.GetKargoInstanceResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "instance", kind: "message", T: KargoInstance },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetKargoInstanceResponse {
    return new GetKargoInstanceResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetKargoInstanceResponse {
    return new GetKargoInstanceResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetKargoInstanceResponse {
    return new GetKargoInstanceResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetKargoInstanceResponse | PlainMessage<GetKargoInstanceResponse> | undefined, b: GetKargoInstanceResponse | PlainMessage<GetKargoInstanceResponse> | undefined): boolean {
    return proto3.util.equals(GetKargoInstanceResponse, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.WatchKargoInstancesResponse
 */
export class WatchKargoInstancesResponse extends Message<WatchKargoInstancesResponse> {
  /**
   * @generated from field: akuity.kargo.v1.KargoInstance item = 1;
   */
  item?: KargoInstance;

  /**
   * @generated from field: akuity.types.events.v1.EventType type = 2;
   */
  type = EventType.UNSPECIFIED;

  constructor(data?: PartialMessage<WatchKargoInstancesResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.WatchKargoInstancesResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "item", kind: "message", T: KargoInstance },
    { no: 2, name: "type", kind: "enum", T: proto3.getEnumType(EventType) },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WatchKargoInstancesResponse {
    return new WatchKargoInstancesResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WatchKargoInstancesResponse {
    return new WatchKargoInstancesResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WatchKargoInstancesResponse {
    return new WatchKargoInstancesResponse().fromJsonString(jsonString, options);
  }

  static equals(a: WatchKargoInstancesResponse | PlainMessage<WatchKargoInstancesResponse> | undefined, b: WatchKargoInstancesResponse | PlainMessage<WatchKargoInstancesResponse> | undefined): boolean {
    return proto3.util.equals(WatchKargoInstancesResponse, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.PatchKargoInstanceRequest
 */
export class PatchKargoInstanceRequest extends Message<PatchKargoInstanceRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string id = 2;
   */
  id = "";

  /**
   * @generated from field: google.protobuf.Struct patch = 3;
   */
  patch?: Struct;

  /**
   * @generated from field: string workspace_id = 4;
   */
  workspaceId = "";

  constructor(data?: PartialMessage<PatchKargoInstanceRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.PatchKargoInstanceRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "patch", kind: "message", T: Struct },
    { no: 4, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PatchKargoInstanceRequest {
    return new PatchKargoInstanceRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PatchKargoInstanceRequest {
    return new PatchKargoInstanceRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PatchKargoInstanceRequest {
    return new PatchKargoInstanceRequest().fromJsonString(jsonString, options);
  }

  static equals(a: PatchKargoInstanceRequest | PlainMessage<PatchKargoInstanceRequest> | undefined, b: PatchKargoInstanceRequest | PlainMessage<PatchKargoInstanceRequest> | undefined): boolean {
    return proto3.util.equals(PatchKargoInstanceRequest, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.PatchKargoInstanceResponse
 */
export class PatchKargoInstanceResponse extends Message<PatchKargoInstanceResponse> {
  /**
   * @generated from field: akuity.kargo.v1.KargoInstance instance = 1;
   */
  instance?: KargoInstance;

  constructor(data?: PartialMessage<PatchKargoInstanceResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.PatchKargoInstanceResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "instance", kind: "message", T: KargoInstance },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PatchKargoInstanceResponse {
    return new PatchKargoInstanceResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PatchKargoInstanceResponse {
    return new PatchKargoInstanceResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PatchKargoInstanceResponse {
    return new PatchKargoInstanceResponse().fromJsonString(jsonString, options);
  }

  static equals(a: PatchKargoInstanceResponse | PlainMessage<PatchKargoInstanceResponse> | undefined, b: PatchKargoInstanceResponse | PlainMessage<PatchKargoInstanceResponse> | undefined): boolean {
    return proto3.util.equals(PatchKargoInstanceResponse, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.UpdateKargoInstanceWorkspaceRequest
 */
export class UpdateKargoInstanceWorkspaceRequest extends Message<UpdateKargoInstanceWorkspaceRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string id = 2;
   */
  id = "";

  /**
   * @generated from field: string workspace_id = 3;
   */
  workspaceId = "";

  /**
   * @generated from field: string new_workspace_id = 4;
   */
  newWorkspaceId = "";

  constructor(data?: PartialMessage<UpdateKargoInstanceWorkspaceRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.UpdateKargoInstanceWorkspaceRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "new_workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateKargoInstanceWorkspaceRequest {
    return new UpdateKargoInstanceWorkspaceRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateKargoInstanceWorkspaceRequest {
    return new UpdateKargoInstanceWorkspaceRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateKargoInstanceWorkspaceRequest {
    return new UpdateKargoInstanceWorkspaceRequest().fromJsonString(jsonString, options);
  }

  static equals(a: UpdateKargoInstanceWorkspaceRequest | PlainMessage<UpdateKargoInstanceWorkspaceRequest> | undefined, b: UpdateKargoInstanceWorkspaceRequest | PlainMessage<UpdateKargoInstanceWorkspaceRequest> | undefined): boolean {
    return proto3.util.equals(UpdateKargoInstanceWorkspaceRequest, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.UpdateKargoInstanceWorkspaceResponse
 */
export class UpdateKargoInstanceWorkspaceResponse extends Message<UpdateKargoInstanceWorkspaceResponse> {
  /**
   * @generated from field: akuity.kargo.v1.KargoInstance instance = 1;
   */
  instance?: KargoInstance;

  constructor(data?: PartialMessage<UpdateKargoInstanceWorkspaceResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.UpdateKargoInstanceWorkspaceResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "instance", kind: "message", T: KargoInstance },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateKargoInstanceWorkspaceResponse {
    return new UpdateKargoInstanceWorkspaceResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateKargoInstanceWorkspaceResponse {
    return new UpdateKargoInstanceWorkspaceResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateKargoInstanceWorkspaceResponse {
    return new UpdateKargoInstanceWorkspaceResponse().fromJsonString(jsonString, options);
  }

  static equals(a: UpdateKargoInstanceWorkspaceResponse | PlainMessage<UpdateKargoInstanceWorkspaceResponse> | undefined, b: UpdateKargoInstanceWorkspaceResponse | PlainMessage<UpdateKargoInstanceWorkspaceResponse> | undefined): boolean {
    return proto3.util.equals(UpdateKargoInstanceWorkspaceResponse, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.KargoAgent
 */
export class KargoAgent extends Message<KargoAgent> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  /**
   * @generated from field: string name = 2;
   */
  name = "";

  /**
   * Use data.namespace instead
   *
   * @generated from field: string namespace = 4 [deprecated = true];
   * @deprecated
   */
  namespace = "";

  /**
   * @generated from field: string description = 5;
   */
  description = "";

  /**
   * @generated from field: akuity.kargo.v1.KargoAgentData data = 6;
   */
  data?: KargoAgentData;

  /**
   * @generated from field: optional google.protobuf.Timestamp delete_time = 7;
   */
  deleteTime?: Timestamp;

  /**
   * @generated from field: akuity.types.status.health.v1.Status health_status = 8;
   */
  healthStatus?: Status;

  /**
   * @generated from field: akuity.types.status.reconciliation.v1.Status reconciliation_status = 9;
   */
  reconciliationStatus?: Status$1;

  /**
   * @generated from field: optional akuity.kargo.v1.KargoAgentState agent_state = 10;
   */
  agentState?: KargoAgentState;

  /**
   * @generated from field: optional uint64 readonly_settings_changed_generation = 11;
   */
  readonlySettingsChangedGeneration?: bigint;

  /**
   * @generated from field: optional uint64 observed_generation = 12;
   */
  observedGeneration?: bigint;

  constructor(data?: PartialMessage<KargoAgent>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.KargoAgent";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "namespace", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "description", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 6, name: "data", kind: "message", T: KargoAgentData },
    { no: 7, name: "delete_time", kind: "message", T: Timestamp, opt: true },
    { no: 8, name: "health_status", kind: "message", T: Status },
    { no: 9, name: "reconciliation_status", kind: "message", T: Status$1 },
    { no: 10, name: "agent_state", kind: "message", T: KargoAgentState, opt: true },
    { no: 11, name: "readonly_settings_changed_generation", kind: "scalar", T: 4 /* ScalarType.UINT64 */, opt: true },
    { no: 12, name: "observed_generation", kind: "scalar", T: 4 /* ScalarType.UINT64 */, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): KargoAgent {
    return new KargoAgent().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): KargoAgent {
    return new KargoAgent().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): KargoAgent {
    return new KargoAgent().fromJsonString(jsonString, options);
  }

  static equals(a: KargoAgent | PlainMessage<KargoAgent> | undefined, b: KargoAgent | PlainMessage<KargoAgent> | undefined): boolean {
    return proto3.util.equals(KargoAgent, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.KargoAgentState
 */
export class KargoAgentState extends Message<KargoAgentState> {
  /**
   * @generated from field: string version = 1;
   */
  version = "";

  /**
   * @generated from field: string kargo_version = 2;
   */
  kargoVersion = "";

  /**
   * @generated from field: optional google.protobuf.Timestamp observe_time = 3;
   */
  observeTime?: Timestamp;

  /**
   * @generated from field: optional akuity.types.status.health.v1.AgentAggregatedHealthResponse status = 4;
   */
  status?: AgentAggregatedHealthResponse;

  /**
   * @generated from field: repeated string agent_ids = 5;
   */
  agentIds: string[] = [];

  /**
   * @generated from field: uint64 last_user_applied_generation = 6;
   */
  lastUserAppliedGeneration = protoInt64.zero;

  /**
   * @generated from field: optional akuity.types.status.reconciliation.v1.AgentUpdateStatus update_status = 7;
   */
  updateStatus?: AgentUpdateStatus;

  constructor(data?: PartialMessage<KargoAgentState>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.KargoAgentState";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "version", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "kargo_version", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "observe_time", kind: "message", T: Timestamp, opt: true },
    { no: 4, name: "status", kind: "message", T: AgentAggregatedHealthResponse, opt: true },
    { no: 5, name: "agent_ids", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 6, name: "last_user_applied_generation", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 7, name: "update_status", kind: "enum", T: proto3.getEnumType(AgentUpdateStatus), opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): KargoAgentState {
    return new KargoAgentState().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): KargoAgentState {
    return new KargoAgentState().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): KargoAgentState {
    return new KargoAgentState().fromJsonString(jsonString, options);
  }

  static equals(a: KargoAgentState | PlainMessage<KargoAgentState> | undefined, b: KargoAgentState | PlainMessage<KargoAgentState> | undefined): boolean {
    return proto3.util.equals(KargoAgentState, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.ListKargoInstanceAgentsRequest
 */
export class ListKargoInstanceAgentsRequest extends Message<ListKargoInstanceAgentsRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string instance_id = 2;
   */
  instanceId = "";

  /**
   * @generated from field: optional akuity.kargo.v1.KargoAgentFilter filter = 3;
   */
  filter?: KargoAgentFilter;

  /**
   * @generated from field: string workspace_id = 4;
   */
  workspaceId = "";

  constructor(data?: PartialMessage<ListKargoInstanceAgentsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.ListKargoInstanceAgentsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "instance_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "filter", kind: "message", T: KargoAgentFilter, opt: true },
    { no: 4, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListKargoInstanceAgentsRequest {
    return new ListKargoInstanceAgentsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListKargoInstanceAgentsRequest {
    return new ListKargoInstanceAgentsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListKargoInstanceAgentsRequest {
    return new ListKargoInstanceAgentsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ListKargoInstanceAgentsRequest | PlainMessage<ListKargoInstanceAgentsRequest> | undefined, b: ListKargoInstanceAgentsRequest | PlainMessage<ListKargoInstanceAgentsRequest> | undefined): boolean {
    return proto3.util.equals(ListKargoInstanceAgentsRequest, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.WatchKargoInstanceAgentsRequest
 */
export class WatchKargoInstanceAgentsRequest extends Message<WatchKargoInstanceAgentsRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string instance_id = 2;
   */
  instanceId = "";

  /**
   * @generated from field: optional string agent_id = 3;
   */
  agentId?: string;

  /**
   * @generated from field: optional string min_agent_name = 4;
   */
  minAgentName?: string;

  /**
   * @generated from field: optional string max_agent_name = 5;
   */
  maxAgentName?: string;

  /**
   * @generated from field: optional akuity.kargo.v1.KargoAgentFilter filter = 6;
   */
  filter?: KargoAgentFilter;

  /**
   * @generated from field: string workspace_id = 7;
   */
  workspaceId = "";

  constructor(data?: PartialMessage<WatchKargoInstanceAgentsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.WatchKargoInstanceAgentsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "instance_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "agent_id", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 4, name: "min_agent_name", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 5, name: "max_agent_name", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 6, name: "filter", kind: "message", T: KargoAgentFilter, opt: true },
    { no: 7, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WatchKargoInstanceAgentsRequest {
    return new WatchKargoInstanceAgentsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WatchKargoInstanceAgentsRequest {
    return new WatchKargoInstanceAgentsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WatchKargoInstanceAgentsRequest {
    return new WatchKargoInstanceAgentsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: WatchKargoInstanceAgentsRequest | PlainMessage<WatchKargoInstanceAgentsRequest> | undefined, b: WatchKargoInstanceAgentsRequest | PlainMessage<WatchKargoInstanceAgentsRequest> | undefined): boolean {
    return proto3.util.equals(WatchKargoInstanceAgentsRequest, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.KargoAgentFilter
 */
export class KargoAgentFilter extends Message<KargoAgentFilter> {
  /**
   * @generated from field: optional string name_like = 1;
   */
  nameLike?: string;

  /**
   * @generated from field: repeated akuity.types.status.health.v1.StatusCode agent_status = 2;
   */
  agentStatus: StatusCode[] = [];

  /**
   * @generated from field: repeated string agent_version = 3;
   */
  agentVersion: string[] = [];

  /**
   * @generated from field: repeated string kargo_version = 4;
   */
  kargoVersion: string[] = [];

  /**
   * @generated from field: optional int64 limit = 5;
   */
  limit?: bigint;

  /**
   * @generated from field: optional int64 offset = 6;
   */
  offset?: bigint;

  /**
   * @generated from field: optional string exclude_agent_version = 7;
   */
  excludeAgentVersion?: string;

  /**
   * @generated from field: optional bool outdated_manifest = 8;
   */
  outdatedManifest?: boolean;

  /**
   * @generated from field: repeated string namespace = 9;
   */
  namespace: string[] = [];

  /**
   * @generated from field: map<string, string> labels = 10;
   */
  labels: { [key: string]: string } = {};

  /**
   * @generated from field: repeated string remote_argocd_ids = 11;
   */
  remoteArgocdIds: string[] = [];

  /**
   * @generated from field: optional bool self_managed = 12;
   */
  selfManaged?: boolean;

  /**
   * @generated from field: optional bool need_reapply = 13;
   */
  needReapply?: boolean;

  constructor(data?: PartialMessage<KargoAgentFilter>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.KargoAgentFilter";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "name_like", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 2, name: "agent_status", kind: "enum", T: proto3.getEnumType(StatusCode), repeated: true },
    { no: 3, name: "agent_version", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 4, name: "kargo_version", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 5, name: "limit", kind: "scalar", T: 3 /* ScalarType.INT64 */, opt: true },
    { no: 6, name: "offset", kind: "scalar", T: 3 /* ScalarType.INT64 */, opt: true },
    { no: 7, name: "exclude_agent_version", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 8, name: "outdated_manifest", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 9, name: "namespace", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 10, name: "labels", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "scalar", T: 9 /* ScalarType.STRING */} },
    { no: 11, name: "remote_argocd_ids", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 12, name: "self_managed", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 13, name: "need_reapply", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): KargoAgentFilter {
    return new KargoAgentFilter().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): KargoAgentFilter {
    return new KargoAgentFilter().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): KargoAgentFilter {
    return new KargoAgentFilter().fromJsonString(jsonString, options);
  }

  static equals(a: KargoAgentFilter | PlainMessage<KargoAgentFilter> | undefined, b: KargoAgentFilter | PlainMessage<KargoAgentFilter> | undefined): boolean {
    return proto3.util.equals(KargoAgentFilter, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.CreateKargoInstanceAgentRequest
 */
export class CreateKargoInstanceAgentRequest extends Message<CreateKargoInstanceAgentRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string instance_id = 2;
   */
  instanceId = "";

  /**
   * @generated from field: string name = 3;
   */
  name = "";

  /**
   * Use data.namespace instead
   *
   * @generated from field: string namespace = 4 [deprecated = true];
   * @deprecated
   */
  namespace = "";

  /**
   * @generated from field: string description = 5;
   */
  description = "";

  /**
   * @generated from field: akuity.kargo.v1.KargoAgentData data = 6;
   */
  data?: KargoAgentData;

  /**
   * Use UpdateKargoInstanceAgent endpoint instead
   *
   * @generated from field: bool upsert = 7 [deprecated = true];
   * @deprecated
   */
  upsert = false;

  /**
   * @generated from field: string workspace_id = 8;
   */
  workspaceId = "";

  constructor(data?: PartialMessage<CreateKargoInstanceAgentRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.CreateKargoInstanceAgentRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "instance_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "namespace", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "description", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 6, name: "data", kind: "message", T: KargoAgentData },
    { no: 7, name: "upsert", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 8, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateKargoInstanceAgentRequest {
    return new CreateKargoInstanceAgentRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateKargoInstanceAgentRequest {
    return new CreateKargoInstanceAgentRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateKargoInstanceAgentRequest {
    return new CreateKargoInstanceAgentRequest().fromJsonString(jsonString, options);
  }

  static equals(a: CreateKargoInstanceAgentRequest | PlainMessage<CreateKargoInstanceAgentRequest> | undefined, b: CreateKargoInstanceAgentRequest | PlainMessage<CreateKargoInstanceAgentRequest> | undefined): boolean {
    return proto3.util.equals(CreateKargoInstanceAgentRequest, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.KargoAgentData
 */
export class KargoAgentData extends Message<KargoAgentData> {
  /**
   * @generated from field: akuity.kargo.v1.KargoAgentSize size = 1;
   */
  size = KargoAgentSize.UNSPECIFIED;

  /**
   * @generated from field: map<string, string> labels = 2;
   */
  labels: { [key: string]: string } = {};

  /**
   * @generated from field: map<string, string> annotations = 3;
   */
  annotations: { [key: string]: string } = {};

  /**
   * @generated from field: optional bool auto_upgrade_disabled = 4;
   */
  autoUpgradeDisabled?: boolean;

  /**
   * @generated from field: string target_version = 5;
   */
  targetVersion = "";

  /**
   * @generated from field: google.protobuf.Struct kustomization = 6;
   */
  kustomization?: Struct;

  /**
   * @generated from field: string remote_argocd = 7;
   */
  remoteArgocd = "";

  /**
   * @generated from field: bool akuity_managed = 8;
   */
  akuityManaged = false;

  /**
   * @generated from field: string namespace = 9;
   */
  namespace = "";

  /**
   * @generated from field: string argocd_namespace = 10;
   */
  argocdNamespace = "";

  /**
   * @generated from field: string self_managed_argocd_url = 11;
   */
  selfManagedArgocdUrl = "";

  constructor(data?: PartialMessage<KargoAgentData>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.KargoAgentData";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "size", kind: "enum", T: proto3.getEnumType(KargoAgentSize) },
    { no: 2, name: "labels", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "scalar", T: 9 /* ScalarType.STRING */} },
    { no: 3, name: "annotations", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "scalar", T: 9 /* ScalarType.STRING */} },
    { no: 4, name: "auto_upgrade_disabled", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 5, name: "target_version", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 6, name: "kustomization", kind: "message", T: Struct },
    { no: 7, name: "remote_argocd", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 8, name: "akuity_managed", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 9, name: "namespace", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 10, name: "argocd_namespace", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 11, name: "self_managed_argocd_url", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): KargoAgentData {
    return new KargoAgentData().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): KargoAgentData {
    return new KargoAgentData().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): KargoAgentData {
    return new KargoAgentData().fromJsonString(jsonString, options);
  }

  static equals(a: KargoAgentData | PlainMessage<KargoAgentData> | undefined, b: KargoAgentData | PlainMessage<KargoAgentData> | undefined): boolean {
    return proto3.util.equals(KargoAgentData, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.GetInstanceAgentCommandRequest
 */
export class GetInstanceAgentCommandRequest extends Message<GetInstanceAgentCommandRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string instance_id = 2;
   */
  instanceId = "";

  /**
   * @generated from field: string id = 3;
   */
  id = "";

  /**
   * @generated from field: string location_origin = 4;
   */
  locationOrigin = "";

  /**
   * @generated from field: string type = 6;
   */
  type = "";

  /**
   * @generated from field: optional bool skip_namespace = 7;
   */
  skipNamespace?: boolean;

  /**
   * @generated from field: string workspace_id = 8;
   */
  workspaceId = "";

  constructor(data?: PartialMessage<GetInstanceAgentCommandRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.GetInstanceAgentCommandRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "instance_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "location_origin", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 6, name: "type", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 7, name: "skip_namespace", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 8, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetInstanceAgentCommandRequest {
    return new GetInstanceAgentCommandRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetInstanceAgentCommandRequest {
    return new GetInstanceAgentCommandRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetInstanceAgentCommandRequest {
    return new GetInstanceAgentCommandRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetInstanceAgentCommandRequest | PlainMessage<GetInstanceAgentCommandRequest> | undefined, b: GetInstanceAgentCommandRequest | PlainMessage<GetInstanceAgentCommandRequest> | undefined): boolean {
    return proto3.util.equals(GetInstanceAgentCommandRequest, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.GetInstanceAgentCommandResponse
 */
export class GetInstanceAgentCommandResponse extends Message<GetInstanceAgentCommandResponse> {
  /**
   * @generated from field: string command = 1;
   */
  command = "";

  constructor(data?: PartialMessage<GetInstanceAgentCommandResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.GetInstanceAgentCommandResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "command", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetInstanceAgentCommandResponse {
    return new GetInstanceAgentCommandResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetInstanceAgentCommandResponse {
    return new GetInstanceAgentCommandResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetInstanceAgentCommandResponse {
    return new GetInstanceAgentCommandResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetInstanceAgentCommandResponse | PlainMessage<GetInstanceAgentCommandResponse> | undefined, b: GetInstanceAgentCommandResponse | PlainMessage<GetInstanceAgentCommandResponse> | undefined): boolean {
    return proto3.util.equals(GetInstanceAgentCommandResponse, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.ListKargoInstanceAgentsResponse
 */
export class ListKargoInstanceAgentsResponse extends Message<ListKargoInstanceAgentsResponse> {
  /**
   * @generated from field: repeated akuity.kargo.v1.KargoAgent agents = 1;
   */
  agents: KargoAgent[] = [];

  /**
   * @generated from field: int64 total_count = 2;
   */
  totalCount = protoInt64.zero;

  constructor(data?: PartialMessage<ListKargoInstanceAgentsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.ListKargoInstanceAgentsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "agents", kind: "message", T: KargoAgent, repeated: true },
    { no: 2, name: "total_count", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListKargoInstanceAgentsResponse {
    return new ListKargoInstanceAgentsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListKargoInstanceAgentsResponse {
    return new ListKargoInstanceAgentsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListKargoInstanceAgentsResponse {
    return new ListKargoInstanceAgentsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListKargoInstanceAgentsResponse | PlainMessage<ListKargoInstanceAgentsResponse> | undefined, b: ListKargoInstanceAgentsResponse | PlainMessage<ListKargoInstanceAgentsResponse> | undefined): boolean {
    return proto3.util.equals(ListKargoInstanceAgentsResponse, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.WatchKargoInstanceAgentsResponse
 */
export class WatchKargoInstanceAgentsResponse extends Message<WatchKargoInstanceAgentsResponse> {
  /**
   * @generated from field: akuity.kargo.v1.KargoAgent item = 1;
   */
  item?: KargoAgent;

  /**
   * @generated from field: akuity.types.events.v1.EventType type = 2;
   */
  type = EventType.UNSPECIFIED;

  constructor(data?: PartialMessage<WatchKargoInstanceAgentsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.WatchKargoInstanceAgentsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "item", kind: "message", T: KargoAgent },
    { no: 2, name: "type", kind: "enum", T: proto3.getEnumType(EventType) },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WatchKargoInstanceAgentsResponse {
    return new WatchKargoInstanceAgentsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WatchKargoInstanceAgentsResponse {
    return new WatchKargoInstanceAgentsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WatchKargoInstanceAgentsResponse {
    return new WatchKargoInstanceAgentsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: WatchKargoInstanceAgentsResponse | PlainMessage<WatchKargoInstanceAgentsResponse> | undefined, b: WatchKargoInstanceAgentsResponse | PlainMessage<WatchKargoInstanceAgentsResponse> | undefined): boolean {
    return proto3.util.equals(WatchKargoInstanceAgentsResponse, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.CreateKargoInstanceAgentResponse
 */
export class CreateKargoInstanceAgentResponse extends Message<CreateKargoInstanceAgentResponse> {
  /**
   * @generated from field: akuity.kargo.v1.KargoAgent agent = 1;
   */
  agent?: KargoAgent;

  constructor(data?: PartialMessage<CreateKargoInstanceAgentResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.CreateKargoInstanceAgentResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "agent", kind: "message", T: KargoAgent },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateKargoInstanceAgentResponse {
    return new CreateKargoInstanceAgentResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateKargoInstanceAgentResponse {
    return new CreateKargoInstanceAgentResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateKargoInstanceAgentResponse {
    return new CreateKargoInstanceAgentResponse().fromJsonString(jsonString, options);
  }

  static equals(a: CreateKargoInstanceAgentResponse | PlainMessage<CreateKargoInstanceAgentResponse> | undefined, b: CreateKargoInstanceAgentResponse | PlainMessage<CreateKargoInstanceAgentResponse> | undefined): boolean {
    return proto3.util.equals(CreateKargoInstanceAgentResponse, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.GetKargoInstanceAgentRequest
 */
export class GetKargoInstanceAgentRequest extends Message<GetKargoInstanceAgentRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string instance_id = 2;
   */
  instanceId = "";

  /**
   * @generated from field: string id = 3;
   */
  id = "";

  /**
   * @generated from field: string workspace_id = 4;
   */
  workspaceId = "";

  constructor(data?: PartialMessage<GetKargoInstanceAgentRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.GetKargoInstanceAgentRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "instance_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetKargoInstanceAgentRequest {
    return new GetKargoInstanceAgentRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetKargoInstanceAgentRequest {
    return new GetKargoInstanceAgentRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetKargoInstanceAgentRequest {
    return new GetKargoInstanceAgentRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetKargoInstanceAgentRequest | PlainMessage<GetKargoInstanceAgentRequest> | undefined, b: GetKargoInstanceAgentRequest | PlainMessage<GetKargoInstanceAgentRequest> | undefined): boolean {
    return proto3.util.equals(GetKargoInstanceAgentRequest, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.GetKargoInstanceAgentResponse
 */
export class GetKargoInstanceAgentResponse extends Message<GetKargoInstanceAgentResponse> {
  /**
   * @generated from field: akuity.kargo.v1.KargoAgent agent = 1;
   */
  agent?: KargoAgent;

  constructor(data?: PartialMessage<GetKargoInstanceAgentResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.GetKargoInstanceAgentResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "agent", kind: "message", T: KargoAgent },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetKargoInstanceAgentResponse {
    return new GetKargoInstanceAgentResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetKargoInstanceAgentResponse {
    return new GetKargoInstanceAgentResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetKargoInstanceAgentResponse {
    return new GetKargoInstanceAgentResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetKargoInstanceAgentResponse | PlainMessage<GetKargoInstanceAgentResponse> | undefined, b: GetKargoInstanceAgentResponse | PlainMessage<GetKargoInstanceAgentResponse> | undefined): boolean {
    return proto3.util.equals(GetKargoInstanceAgentResponse, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.UpdateKargoInstanceAgentRequest
 */
export class UpdateKargoInstanceAgentRequest extends Message<UpdateKargoInstanceAgentRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string instance_id = 2;
   */
  instanceId = "";

  /**
   * @generated from field: string id = 3;
   */
  id = "";

  /**
   * @generated from field: string description = 4;
   */
  description = "";

  /**
   * @generated from field: akuity.kargo.v1.KargoAgentData data = 5;
   */
  data?: KargoAgentData;

  /**
   * @generated from field: string workspace_id = 6;
   */
  workspaceId = "";

  constructor(data?: PartialMessage<UpdateKargoInstanceAgentRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.UpdateKargoInstanceAgentRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "instance_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "description", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "data", kind: "message", T: KargoAgentData },
    { no: 6, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateKargoInstanceAgentRequest {
    return new UpdateKargoInstanceAgentRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateKargoInstanceAgentRequest {
    return new UpdateKargoInstanceAgentRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateKargoInstanceAgentRequest {
    return new UpdateKargoInstanceAgentRequest().fromJsonString(jsonString, options);
  }

  static equals(a: UpdateKargoInstanceAgentRequest | PlainMessage<UpdateKargoInstanceAgentRequest> | undefined, b: UpdateKargoInstanceAgentRequest | PlainMessage<UpdateKargoInstanceAgentRequest> | undefined): boolean {
    return proto3.util.equals(UpdateKargoInstanceAgentRequest, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.UpdateKargoInstanceAgentResponse
 */
export class UpdateKargoInstanceAgentResponse extends Message<UpdateKargoInstanceAgentResponse> {
  /**
   * @generated from field: akuity.kargo.v1.KargoAgent agent = 1;
   */
  agent?: KargoAgent;

  constructor(data?: PartialMessage<UpdateKargoInstanceAgentResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.UpdateKargoInstanceAgentResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "agent", kind: "message", T: KargoAgent },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateKargoInstanceAgentResponse {
    return new UpdateKargoInstanceAgentResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateKargoInstanceAgentResponse {
    return new UpdateKargoInstanceAgentResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateKargoInstanceAgentResponse {
    return new UpdateKargoInstanceAgentResponse().fromJsonString(jsonString, options);
  }

  static equals(a: UpdateKargoInstanceAgentResponse | PlainMessage<UpdateKargoInstanceAgentResponse> | undefined, b: UpdateKargoInstanceAgentResponse | PlainMessage<UpdateKargoInstanceAgentResponse> | undefined): boolean {
    return proto3.util.equals(UpdateKargoInstanceAgentResponse, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.GetKargoInstanceAgentManifestsRequest
 */
export class GetKargoInstanceAgentManifestsRequest extends Message<GetKargoInstanceAgentManifestsRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string instance_id = 2;
   */
  instanceId = "";

  /**
   * @generated from field: string id = 3;
   */
  id = "";

  /**
   * @generated from field: optional bool skip_namespace = 4;
   */
  skipNamespace?: boolean;

  /**
   * @generated from field: string workspace_id = 5;
   */
  workspaceId = "";

  constructor(data?: PartialMessage<GetKargoInstanceAgentManifestsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.GetKargoInstanceAgentManifestsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "instance_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "skip_namespace", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 5, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetKargoInstanceAgentManifestsRequest {
    return new GetKargoInstanceAgentManifestsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetKargoInstanceAgentManifestsRequest {
    return new GetKargoInstanceAgentManifestsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetKargoInstanceAgentManifestsRequest {
    return new GetKargoInstanceAgentManifestsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetKargoInstanceAgentManifestsRequest | PlainMessage<GetKargoInstanceAgentManifestsRequest> | undefined, b: GetKargoInstanceAgentManifestsRequest | PlainMessage<GetKargoInstanceAgentManifestsRequest> | undefined): boolean {
    return proto3.util.equals(GetKargoInstanceAgentManifestsRequest, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.UpdateKargoInstanceAgentsRequest
 */
export class UpdateKargoInstanceAgentsRequest extends Message<UpdateKargoInstanceAgentsRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string instance_id = 2;
   */
  instanceId = "";

  /**
   * @generated from field: akuity.kargo.v1.KargoAgentCustomization agent_customizations = 3;
   */
  agentCustomizations?: KargoAgentCustomization;

  /**
   * @generated from field: string workspace_id = 4;
   */
  workspaceId = "";

  constructor(data?: PartialMessage<UpdateKargoInstanceAgentsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.UpdateKargoInstanceAgentsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "instance_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "agent_customizations", kind: "message", T: KargoAgentCustomization },
    { no: 4, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateKargoInstanceAgentsRequest {
    return new UpdateKargoInstanceAgentsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateKargoInstanceAgentsRequest {
    return new UpdateKargoInstanceAgentsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateKargoInstanceAgentsRequest {
    return new UpdateKargoInstanceAgentsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: UpdateKargoInstanceAgentsRequest | PlainMessage<UpdateKargoInstanceAgentsRequest> | undefined, b: UpdateKargoInstanceAgentsRequest | PlainMessage<UpdateKargoInstanceAgentsRequest> | undefined): boolean {
    return proto3.util.equals(UpdateKargoInstanceAgentsRequest, a, b);
  }
}

/**
 * empty 
 *
 * @generated from message akuity.kargo.v1.UpdateKargoInstanceAgentsResponse
 */
export class UpdateKargoInstanceAgentsResponse extends Message<UpdateKargoInstanceAgentsResponse> {
  constructor(data?: PartialMessage<UpdateKargoInstanceAgentsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.UpdateKargoInstanceAgentsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateKargoInstanceAgentsResponse {
    return new UpdateKargoInstanceAgentsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateKargoInstanceAgentsResponse {
    return new UpdateKargoInstanceAgentsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateKargoInstanceAgentsResponse {
    return new UpdateKargoInstanceAgentsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: UpdateKargoInstanceAgentsResponse | PlainMessage<UpdateKargoInstanceAgentsResponse> | undefined, b: UpdateKargoInstanceAgentsResponse | PlainMessage<UpdateKargoInstanceAgentsResponse> | undefined): boolean {
    return proto3.util.equals(UpdateKargoInstanceAgentsResponse, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.ApplyKargoInstanceRequest
 */
export class ApplyKargoInstanceRequest extends Message<ApplyKargoInstanceRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string id = 2;
   */
  id = "";

  /**
   * @generated from field: akuity.types.id.v1.Type id_type = 3;
   */
  idType = Type.UNSPECIFIED;

  /**
   * @generated from field: string workspace_id = 4;
   */
  workspaceId = "";

  /**
   * @generated from field: google.protobuf.Struct kargo = 5;
   */
  kargo?: Struct;

  /**
   * @generated from field: repeated google.protobuf.Struct agents = 6;
   */
  agents: Struct[] = [];

  /**
   * @generated from field: repeated akuity.kargo.v1.PruneResourceType prune_resource_types = 7;
   */
  pruneResourceTypes: PruneResourceType[] = [];

  /**
   * @generated from field: google.protobuf.Struct kargo_configmap = 8;
   */
  kargoConfigmap?: Struct;

  /**
   * @generated from field: google.protobuf.Struct kargo_secret = 9;
   */
  kargoSecret?: Struct;

  /**
   * @generated from field: repeated google.protobuf.Struct projects = 10;
   */
  projects: Struct[] = [];

  /**
   * @generated from field: repeated google.protobuf.Struct warehouses = 11;
   */
  warehouses: Struct[] = [];

  /**
   * @generated from field: repeated google.protobuf.Struct stages = 12;
   */
  stages: Struct[] = [];

  /**
   * @generated from field: repeated google.protobuf.Struct analysis_templates = 13;
   */
  analysisTemplates: Struct[] = [];

  /**
   * @generated from field: repeated google.protobuf.Struct promotion_tasks = 14;
   */
  promotionTasks: Struct[] = [];

  /**
   * @generated from field: repeated google.protobuf.Struct cluster_promotion_tasks = 15;
   */
  clusterPromotionTasks: Struct[] = [];

  /**
   * @generated from field: repeated google.protobuf.Struct repo_credentials = 16;
   */
  repoCredentials: Struct[] = [];

  constructor(data?: PartialMessage<ApplyKargoInstanceRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.ApplyKargoInstanceRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "id_type", kind: "enum", T: proto3.getEnumType(Type) },
    { no: 4, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "kargo", kind: "message", T: Struct },
    { no: 6, name: "agents", kind: "message", T: Struct, repeated: true },
    { no: 7, name: "prune_resource_types", kind: "enum", T: proto3.getEnumType(PruneResourceType), repeated: true },
    { no: 8, name: "kargo_configmap", kind: "message", T: Struct },
    { no: 9, name: "kargo_secret", kind: "message", T: Struct },
    { no: 10, name: "projects", kind: "message", T: Struct, repeated: true },
    { no: 11, name: "warehouses", kind: "message", T: Struct, repeated: true },
    { no: 12, name: "stages", kind: "message", T: Struct, repeated: true },
    { no: 13, name: "analysis_templates", kind: "message", T: Struct, repeated: true },
    { no: 14, name: "promotion_tasks", kind: "message", T: Struct, repeated: true },
    { no: 15, name: "cluster_promotion_tasks", kind: "message", T: Struct, repeated: true },
    { no: 16, name: "repo_credentials", kind: "message", T: Struct, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ApplyKargoInstanceRequest {
    return new ApplyKargoInstanceRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ApplyKargoInstanceRequest {
    return new ApplyKargoInstanceRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ApplyKargoInstanceRequest {
    return new ApplyKargoInstanceRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ApplyKargoInstanceRequest | PlainMessage<ApplyKargoInstanceRequest> | undefined, b: ApplyKargoInstanceRequest | PlainMessage<ApplyKargoInstanceRequest> | undefined): boolean {
    return proto3.util.equals(ApplyKargoInstanceRequest, a, b);
  }
}

/**
 * empty 
 *
 * @generated from message akuity.kargo.v1.ApplyKargoInstanceResponse
 */
export class ApplyKargoInstanceResponse extends Message<ApplyKargoInstanceResponse> {
  constructor(data?: PartialMessage<ApplyKargoInstanceResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.ApplyKargoInstanceResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ApplyKargoInstanceResponse {
    return new ApplyKargoInstanceResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ApplyKargoInstanceResponse {
    return new ApplyKargoInstanceResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ApplyKargoInstanceResponse {
    return new ApplyKargoInstanceResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ApplyKargoInstanceResponse | PlainMessage<ApplyKargoInstanceResponse> | undefined, b: ApplyKargoInstanceResponse | PlainMessage<ApplyKargoInstanceResponse> | undefined): boolean {
    return proto3.util.equals(ApplyKargoInstanceResponse, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.ExportKargoInstanceRequest
 */
export class ExportKargoInstanceRequest extends Message<ExportKargoInstanceRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string id = 2;
   */
  id = "";

  /**
   * @generated from field: string workspace_id = 3;
   */
  workspaceId = "";

  constructor(data?: PartialMessage<ExportKargoInstanceRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.ExportKargoInstanceRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ExportKargoInstanceRequest {
    return new ExportKargoInstanceRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ExportKargoInstanceRequest {
    return new ExportKargoInstanceRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ExportKargoInstanceRequest {
    return new ExportKargoInstanceRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ExportKargoInstanceRequest | PlainMessage<ExportKargoInstanceRequest> | undefined, b: ExportKargoInstanceRequest | PlainMessage<ExportKargoInstanceRequest> | undefined): boolean {
    return proto3.util.equals(ExportKargoInstanceRequest, a, b);
  }
}

/**
 * @generated from message akuity.kargo.v1.ExportKargoInstanceResponse
 */
export class ExportKargoInstanceResponse extends Message<ExportKargoInstanceResponse> {
  /**
   * @generated from field: google.protobuf.Struct kargo = 1;
   */
  kargo?: Struct;

  /**
   * @generated from field: repeated google.protobuf.Struct agents = 2;
   */
  agents: Struct[] = [];

  /**
   * @generated from field: google.protobuf.Struct kargo_configmap = 3;
   */
  kargoConfigmap?: Struct;

  /**
   * @generated from field: repeated google.protobuf.Struct projects = 4;
   */
  projects: Struct[] = [];

  /**
   * @generated from field: repeated google.protobuf.Struct warehouses = 5;
   */
  warehouses: Struct[] = [];

  /**
   * @generated from field: repeated google.protobuf.Struct stages = 6;
   */
  stages: Struct[] = [];

  /**
   * @generated from field: repeated google.protobuf.Struct analysis_templates = 7;
   */
  analysisTemplates: Struct[] = [];

  /**
   * @generated from field: repeated google.protobuf.Struct promotion_tasks = 8;
   */
  promotionTasks: Struct[] = [];

  /**
   * @generated from field: repeated google.protobuf.Struct cluster_promotion_tasks = 9;
   */
  clusterPromotionTasks: Struct[] = [];

  constructor(data?: PartialMessage<ExportKargoInstanceResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.kargo.v1.ExportKargoInstanceResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "kargo", kind: "message", T: Struct },
    { no: 2, name: "agents", kind: "message", T: Struct, repeated: true },
    { no: 3, name: "kargo_configmap", kind: "message", T: Struct },
    { no: 4, name: "projects", kind: "message", T: Struct, repeated: true },
    { no: 5, name: "warehouses", kind: "message", T: Struct, repeated: true },
    { no: 6, name: "stages", kind: "message", T: Struct, repeated: true },
    { no: 7, name: "analysis_templates", kind: "message", T: Struct, repeated: true },
    { no: 8, name: "promotion_tasks", kind: "message", T: Struct, repeated: true },
    { no: 9, name: "cluster_promotion_tasks", kind: "message", T: Struct, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ExportKargoInstanceResponse {
    return new ExportKargoInstanceResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ExportKargoInstanceResponse {
    return new ExportKargoInstanceResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ExportKargoInstanceResponse {
    return new ExportKargoInstanceResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ExportKargoInstanceResponse | PlainMessage<ExportKargoInstanceResponse> | undefined, b: ExportKargoInstanceResponse | PlainMessage<ExportKargoInstanceResponse> | undefined): boolean {
    return proto3.util.equals(ExportKargoInstanceResponse, a, b);
  }
}

