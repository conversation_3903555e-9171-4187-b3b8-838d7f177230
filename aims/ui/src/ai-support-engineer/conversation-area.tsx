import { faExclamationTriangle } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { AIConversation } from '@/lib/apiclient/organization/v1/organization_pb';

import { MessageBubble } from './message-bubble';
interface ConversationAreaProps {
  conversation: AIConversation;
}

export const ConversationArea = ({ conversation }: ConversationAreaProps) => {
  return (
    <div className='flex-1 relative min-h-0'>
      {conversation.feedbacks && conversation.feedbacks.length > 0 && (
        <div className='flex flex-col p-4 gap-2'>
          <div className='text-lg font-bold'>Feedbacks</div>
          <div>
            {conversation.feedbacks.map((feedback, index) => (
              <div key={index}>
                {index + 1}. {feedback}
              </div>
            ))}
          </div>
        </div>
      )}
      <div className='overflow-y-auto h-full w-full p-4'>
        <div className='text-lg font-bold'>Conversation</div>
        {conversation.messages?.length > 0 ? (
          conversation.messages.map((item) => <MessageBubble key={item.id} message={item} />)
        ) : (
          <div className='text-center text-gray-400'>No messages yet</div>
        )}

        {conversation.processingError && (
          <div className='p-3 rounded-lg message-bubble space-x-2'>
            <FontAwesomeIcon className='text-red-500' icon={faExclamationTriangle} />
            <span className='text-red-500'>{conversation.processingError}</span>
          </div>
        )}
      </div>
    </div>
  );
};
