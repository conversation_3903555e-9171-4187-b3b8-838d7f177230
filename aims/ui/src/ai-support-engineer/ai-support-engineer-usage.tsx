import { Layout, Tabs } from 'antd';
import { useState } from 'react';

import { useListAIConversations, useGetAIConversation } from '@/hook/api/organizations-queries';
import { KubeVisionUsageContainer } from '@/kubevision/kubevision-usage';
import { BasicOrganization } from '@/lib/apiclient/aims/v1/aims_pb';

import { ConversationArea } from './conversation-area';
import { HistoryPanel } from './history-panel';
import { InstanceRunbooks } from './instance-runbooks';

type AiSupportEngineerUsageProp = {
  organization: BasicOrganization;
};

export const AiSupportEngineerUsage = ({ organization }: AiSupportEngineerUsageProp) => {
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);

  const { data: conversationsData, isLoading: isConversationsLoading } = useListAIConversations(
    organization.id
  );

  const { data: conversationData } = useGetAIConversation(
    organization.id,
    currentConversationId || '',
    {
      enabled: !!organization.id && !!currentConversationId
    }
  );

  const conversations = conversationsData?.conversations;
  const currentConversation = conversationData?.conversation;

  const items = [
    {
      key: 'conversation-history',
      label: 'Conversation History',
      children: (
        <Layout className='rounded-lg border shadow-md h-[calc(100vh-360px)]'>
          <Layout.Sider width={400} className='rounded-l-lg overflow-hidden bg-[#ffffff]'>
            <HistoryPanel
              historyChats={conversations}
              onSelectChat={(conversationId) => setCurrentConversationId(conversationId)}
              isLoading={isConversationsLoading}
            />
          </Layout.Sider>
          <Layout.Content className='overflow-auto'>
            {currentConversation && <ConversationArea conversation={currentConversation} />}
          </Layout.Content>
        </Layout>
      )
    },
    {
      key: 'runbooks',
      label: 'Runbooks',
      children: <InstanceRunbooks organization={organization} />
    },
    {
      key: 'usage',
      label: 'Intelligence Usage',
      children: <KubeVisionUsageContainer orgId={organization.id} />
    }
  ];

  return <Tabs items={items} />;
};
