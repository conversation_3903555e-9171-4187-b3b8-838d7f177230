import { PlainMessage } from '@bufbuild/protobuf';
import { faBook } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Layout } from 'antd';
import { useMemo, useState } from 'react';

import { useListArgoInstances } from '@/hook/api/instance-queries';
import { BasicOrganization, ListArgoInstancesRequest } from '@/lib/apiclient/aims/v1/aims_pb';
import { Instance, Runbook } from '@/lib/apiclient/argocd/v1/argocd_pb';

type RunbookItem = PlainMessage<Runbook> & {
  instanceId: string;
};

type InstanceRunbooksProps = {
  organization: BasicOrganization;
};

export const InstanceRunbooks = ({ organization }: InstanceRunbooksProps) => {
  const filters = new ListArgoInstancesRequest({
    filter: {
      organizationId: organization.id
    }
  });
  const { data: instances } = useListArgoInstances(filters);
  const instanceMap = useMemo(() => {
    return instances?.reduce(
      (acc, instance) => {
        acc[instance.instance?.id ?? ''] = instance.instance;
        return acc;
      },
      {} as Record<string, Instance>
    );
  }, [instances]);
  const runbooks: RunbookItem[] = useMemo(() => {
    return (
      instances?.flatMap(
        (instance) =>
          instance.instance?.spec?.kubeVisionConfig?.aiConfig?.runbooks?.map((runbook) => ({
            ...runbook,
            instanceId: instance.instance?.id ?? ''
          })) ?? []
      ) ?? []
    );
  }, [instances]);

  const [selectedRunbook, setSelectedRunbook] = useState<RunbookItem | null>(null);

  return (
    <Layout className='rounded-lg border shadow-md h-[calc(100vh-360px)]'>
      <Layout.Sider width={400} className='rounded-l-lg overflow-hidden bg-[#ffffff]'>
        <div>
          {runbooks.map((runbook, idx) => (
            <div
              key={idx}
              className={`py-2 px-4 hover:bg-zinc-100 cursor-pointer flex flex-row items-center
                    ${runbook.instanceId === selectedRunbook?.instanceId && runbook.name === selectedRunbook?.name ? 'bg-zinc-100' : ''}`}
              onClick={() => {
                setSelectedRunbook(runbook);
              }}
            >
              <div className='mr-4 flex items-center justify-center'>
                <FontAwesomeIcon icon={faBook} className='text-gray-500 w-[24px] h-[24px]' />
              </div>
              <div className='flex-1 flex justify-between'>
                <div>{runbook.name}</div>
                <div className='text-sm text-gray-500'>
                  <a href={`/instances/settings/${runbook.instanceId}`}>
                    {instanceMap[runbook.instanceId]?.name}
                  </a>
                </div>
              </div>
            </div>
          ))}
          {runbooks.length === 0 && (
            <div className='text-sm text-gray-500 px-4 mb-2'>No runbooks found</div>
          )}
        </div>
      </Layout.Sider>
      <Layout.Content className='overflow-auto'>
        {selectedRunbook ? (
          <div className='p-2'>
            <div className='text-lg font-bold mb-2'>Name: </div>
            <div className='font-semibold mb-4'>{selectedRunbook.name}</div>

            <div className='text-lg font-bold mb-2'>Content: </div>
            <div className='text-sm'>
              <pre>
                <code>{selectedRunbook.content}</code>
              </pre>
            </div>
          </div>
        ) : (
          <div className='p-2'>No runbook selected</div>
        )}
      </Layout.Content>
    </Layout>
  );
};
