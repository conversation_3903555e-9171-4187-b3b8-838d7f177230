import { Timestamp } from '@bufbuild/protobuf';
import { faThumbsUp, faThumbsDown } from '@fortawesome/free-regular-svg-icons';
import { faMinusSquare, faPlusSquare, faSpinner } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Collapse, Button, Steps } from 'antd';

import {
  AIMessage,
  AIConversationStepStatus
} from '@/lib/apiclient/organization/v1/organization_pb';
import { timestampToDate, renderMarkdown } from '@/utils';

import { formatTime } from '../utils';

import { DiffViewer } from './diff-viewer';
import { SuggestedContexts } from './suggested-contexts';

import './message-bubble.less';

interface MessageBubbleProps {
  message?: AIMessage;
  representativeTime?: Timestamp;
}

export const MessageBubble = ({ message, representativeTime }: MessageBubbleProps) => {
  const isUserMessage = message.role === 'user';
  const messageTypeClass = isUserMessage ? 'user-message' : 'bot-message';

  const finalTimestamp = isUserMessage ? message?.createTime : representativeTime;

  return (
    <div
      className={`message-container mb-4 ${
        messageTypeClass === 'user-message' ? 'pl-16' : ''
      } animate-fade-in`}
    >
      <div className='message-wrapper'>
        <div
          className={`p-3 rounded-lg message-bubble ${
            messageTypeClass === 'bot-message'
              ? 'bot-message gradient-bg-blue'
              : 'user-message gradient-bg-gray'
          }`}
        >
          {!message.ownedByMe && (
            <div className='text-xs text-gray-400 mb-1'>
              <strong>{message.username || message.role}</strong>
            </div>
          )}

          {message.thinkingProcess && (
            <Collapse
              className='thinking-process-collapse'
              ghost
              expandIcon={(props) => (
                <Button
                  icon={<FontAwesomeIcon icon={props.isActive ? faMinusSquare : faPlusSquare} />}
                  type={'default'}
                  size='small'
                >
                  {props.isActive ? 'Collapse' : 'Expand'}
                </Button>
              )}
              expandIconPosition='end'
              items={[
                {
                  key: '1',
                  label: (
                    <strong className='text-sm thinking-process-title'>Thinking Process:</strong>
                  ),
                  children: (
                    <div className='thinking-process-container'>
                      <div
                        className='thinking-process-content text-sm rounded'
                        dangerouslySetInnerHTML={{
                          __html: renderMarkdown(message.thinkingProcess)
                        }}
                      />
                    </div>
                  )
                }
              ]}
            />
          )}

          {message.content && (
            <div
              className='text-sm markdown-content mt-2'
              dangerouslySetInnerHTML={{ __html: renderMarkdown(message.content) }}
            />
          )}
          {message.suggestedChanges?.length > 0 && (
            <>
              <strong className='block mt-4 mb-2 text-sm'>Suggested Changes:</strong>
              {message.suggestedChanges
                .filter(
                  (suggestedChange) =>
                    suggestedChange.patch &&
                    suggestedChange.patch.length > 0 &&
                    suggestedChange.patch !== '{}'
                )
                .map((suggestedChange, index) => (
                  <DiffViewer
                    key={index}
                    suggestedChange={suggestedChange}
                    messageTypeClass={messageTypeClass}
                    messageId={message.id}
                  />
                ))}
            </>
          )}
          {message.suggestedContexts?.length > 0 && (
            <div className='mt-2'>
              <SuggestedContexts suggestedContexts={message.suggestedContexts} />
            </div>
          )}

          <div className='text-xs text-gray-400 mt-2 pt-2 border-t border-gray-500 border-opacity-30'>
            {finalTimestamp
              ? (() => {
                  const date = timestampToDate(finalTimestamp);
                  const dateStr = date.toLocaleString();
                  return dateStr === 'Invalid Date' ? '' : dateStr;
                })()
              : ''}
          </div>

          {message?.steps?.length > 0 && (
            <div className='pt-4 pl-6 pb-2'>
              <Steps
                direction='vertical'
                size='default'
                items={message.steps.map((step) => {
                  return {
                    title: <strong>{step.name}</strong>,
                    description: (
                      <>
                        <div
                          className='text-gray-500 mb-1'
                          dangerouslySetInnerHTML={{ __html: renderMarkdown(step.summary) }}
                        />
                        <span className='text-gray-500'>
                          {step.endTime
                            ? formatTime(step.endTime?.toDate())
                            : formatTime(step.startTime?.toDate())}
                        </span>
                      </>
                    ),
                    icon: step.status ==
                      AIConversationStepStatus.AI_CONVERSATION_STEP_STATUS_RUNNING && (
                      <FontAwesomeIcon color='#9ca3af' icon={faSpinner} spin={true} />
                    ),
                    status:
                      (step.status ==
                        AIConversationStepStatus.AI_CONVERSATION_STEP_STATUS_RUNNING &&
                        'process') ||
                      (step.status == AIConversationStepStatus.AI_CONVERSATION_STEP_STATUS_FAILED &&
                        'error') ||
                      'finish'
                  };
                })}
                className='ai-message-steps'
              />
            </div>
          )}

          {!isUserMessage && message.isUseful !== undefined && (
            <div className='feedback-buttons'>
              <button className='feedback-button active'>
                <FontAwesomeIcon icon={message.isUseful ? faThumbsUp : faThumbsDown} />
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
