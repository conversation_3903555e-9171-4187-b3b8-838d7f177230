import { Flex, Tag, Tooltip } from 'antd';

export interface ResourceTagProps {
  resource: {
    clusterId?: string;
    name: string;
    kind: string;
  };
  closable?: boolean;
  onClose?: () => void;
}

export const ResourceTag = ({ resource, closable = false, onClose }: ResourceTagProps) => {
  const tooltipContent = (
    <div className='p-2 text-xs whitespace-nowrap'>
      <Flex vertical gap={2}>
        <Flex align='center' gap={1}>
          <strong className='text-[#64748b]'>Kind: </strong>
          <span className='ml-1 text-[#64748b]'>{resource.kind}</span>
        </Flex>
      </Flex>
    </div>
  );

  return (
    <div className='resource-tag'>
      <Tooltip
        title={tooltipContent}
        placement='bottom'
        color='#e2e8f0'
        overlayInnerStyle={{ width: 'fit-content' }}
      >
        <Tag
          className='text-[#64748b] mr-1 border-[#64748b81] bg-[#e2e8f0] hover:bg-slate-50'
          closable={closable}
          onClose={onClose}
        >
          {resource.name}
        </Tag>
      </Tooltip>
    </div>
  );
};
