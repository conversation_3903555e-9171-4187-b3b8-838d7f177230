import { faForward } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { zodResolver } from '@hookform/resolvers/zod';
import { Alert, AutoComplete, Button } from 'antd';
import moment from 'moment';
import { SubmitHandler, useForm } from 'react-hook-form';
import { z } from 'zod';

import { useUpdateTrialExpiration, useListUnbilledOrganizations } from '@/hook/api';
import { AuditForm } from '@/lib/components/shared/audit-form';
import { FieldContainer } from '@/lib/components/shared/forms';

import { Audit, BasicOrganization } from './lib/apiclient/aims/v1/aims_pb';
import DatePicker from './lib/components/datepicker';
import { FormSizer } from './lib/components/form-sizer';
import { Header } from './lib/components/header';
import { errorToString, zodAuditSchema } from './utils';

const schema = z.object({
  organizationName: z.string().min(1, 'Organization name is required.'),
  trialExpiration: z.number({ required_error: 'Trial expiration date is required.' }),
  audit: zodAuditSchema
});

type UpdateTrialForm = z.infer<typeof schema>;

export const UpdateTrial = () => {
  const { data: orgs = [] } = useListUnbilledOrganizations();

  const organizations: Record<string, BasicOrganization> = orgs.reduce(
    (acc, org) => {
      if (!org.billed) {
        acc[org.name] = org;
      }
      return acc;
    },
    {} as Record<string, BasicOrganization>
  );

  const { control, handleSubmit, setValue } = useForm({
    defaultValues: {
      organizationName: '',
      trialExpiration: 0,
      audit: {
        actor: '',
        reason: ''
      }
    },
    resolver: zodResolver(schema)
  });

  const { mutate, isPending, error, isError, isSuccess } = useUpdateTrialExpiration();

  const onSubmit: SubmitHandler<UpdateTrialForm> = (data) =>
    mutate({
      organizationId: organizations[data.organizationName].id,
      audit: new Audit(data.audit),
      trialExpiration: data.trialExpiration
    });

  return (
    <div className='w-full'>
      <Header>Update Free Trial Expiration</Header>
      <div className='flex gap-5 w-full'>
        <FormSizer>
          <FieldContainer label='Organization Name' name='organizationName' control={control}>
            {({ field }) => (
              <AutoComplete
                {...field}
                placeholder='my-org'
                className='w-full'
                options={(Object.keys(organizations || {}) || []).map((o) => {
                  return { value: o };
                })}
              />
            )}
          </FieldContainer>
          <FieldContainer label='Expiration Date' name='trialExpiration' control={control}>
            {({ field }) => (
              <DatePicker
                {...field}
                placeholder='01/01/2000'
                value={field.value ? moment.unix(field.value) : null}
                onChange={(date) => {
                  field.onChange(date ? date.unix() : null);
                }}
                className='w-full'
              />
            )}
          </FieldContainer>
          <Button
            className='block mb-6'
            onClick={() => {
              setValue('trialExpiration', moment().add(2, 'weeks').unix());
            }}
          >
            Two Weeks from Now
            <FontAwesomeIcon icon={faForward} className='ml-2' />
          </Button>

          <AuditForm control={control} />
          {isError && (
            <div className='mt-4'>
              <Alert type='error' message={errorToString(error)} className='mt-4' />
            </div>
          )}
          {isSuccess && (
            <Alert type='success' className='mt-4' message='Updated expiration date successfully' />
          )}
        </FormSizer>
      </div>
      <Button onClick={handleSubmit(onSubmit)} type='primary' loading={isPending}>
        Submit
      </Button>
    </div>
  );
};
