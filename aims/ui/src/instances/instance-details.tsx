import Editor from '@monaco-editor/react';
import JsonView from '@uiw/react-json-view';
import { Input, Tabs } from 'antd';
import classNames from 'classnames';
import { omit } from 'lodash';
import { useMemo } from 'react';
import { Link, useParams } from 'react-router-dom';
import yaml from 'yaml';

import { useGetInstanceById } from '@/hook/api';
import { Header } from '@/lib/components/header';
import { InfoItem } from '@/lib/components/info-item';
import { HealthState } from '@/lib/components/shared/health-state';
import { UnsupportedVersionBanner } from '@/lib/components/unsupported-version-banner';

import { ClustersTable } from '../lib/components/clusters-table';

export const InstanceDetails = () => {
  const { instanceId } = useParams();
  const { data } = useGetInstanceById(instanceId);

  const notificationConfigYAML = useMemo(() => {
    if (data?.notificationConfig?.config) {
      return yaml.stringify(data?.notificationConfig?.config || {});
    }

    return '';
  }, [data?.notificationConfig]);

  return (
    <div className='w-full'>
      <UnsupportedVersionBanner
        isUnsupported={data?.instance?.unsupportedVersion}
        version={data?.instance?.version}
      />
      <div className='flex'>
        <img src='/images/argo.svg' alt='argo' className='w-10 h-10' />
        <Header>{data?.instance?.name}</Header>
        <div className='ml-auto'>
          <span>{data?.instance?.id}</span>
          {data?.instance?.spec?.fqdn && (
            <a
              href={`https://${data?.instance?.spec?.fqdn}`}
              className='text-blue-500 block mt-2'
              target='_blank'
            >
              {data?.instance?.spec?.fqdn}
            </a>
          )}
        </div>
      </div>
      <div className='mb-6'>
        <div className='flex items-start mb-6'>
          <InfoItem label='VERSION'>{data?.instance?.version}</InfoItem>
          <InfoItem label='HEALTH'>
            {data?.instance?.healthStatus && (
              <HealthState
                health={data?.instance?.healthStatus}
                deletionTimestamp={''}
                className='font-semibold'
              />
            )}
          </InfoItem>
          <InfoItem label='RECONCILIATION'>
            {data?.instance?.reconciliationStatus && (
              <div className='text-sm font-semibold'>
                {data?.instance?.reconciliationStatus.code} <br />
                {data?.instance?.reconciliationStatus.message}
              </div>
            )}
          </InfoItem>

          <InfoItem label='WORKSPACE'>
            {data?.workspace ? (
              <Link
                to={`/organizations/${data.orgId}/workspaces/${data?.workspace?.id}`}
                className='text-blue-500 hover:underline font-semibold'
              >
                {data?.workspace?.name}
              </Link>
            ) : (
              'N/A'
            )}
          </InfoItem>
        </div>
        <Tabs
          items={[
            {
              key: 'instance',
              label: 'Configuration',
              children: (
                <div className='flex flex-wrap'>
                  {data?.instance?.info?.applicationsStatus && (
                    <JSONField label='APPS STATUS'>
                      <JsonView value={data?.instance?.info?.applicationsStatus} />
                    </JSONField>
                  )}
                  {data?.instance?.config && (
                    <JSONField label='INSTANCE CONFIG'>
                      <JsonView value={data?.instance?.config} />
                    </JSONField>
                  )}
                  {data?.instance?.rbacConfig && (
                    <JSONField label='RBAC CONFIG'>
                      <JsonView value={omit(data?.instance?.rbacConfig, ['policyCsv'])} />
                    </JSONField>
                  )}
                  {data?.instance?.rbacConfig?.policyCsv && (
                    <JSONField label='RBAC POLICY'>
                      <Input.TextArea
                        rows={10}
                        className='w-full'
                        value={data?.instance?.rbacConfig?.policyCsv}
                      />
                    </JSONField>
                  )}
                  {data?.notificationConfig && (
                    <div className='w-full pb-10'>
                      <div className='font-semibold mb-2 text-gray-500 text-sm'>
                        Notifications Config
                      </div>
                      <Editor
                        value={notificationConfigYAML}
                        height='50vh'
                        language='yaml'
                        onChange={() => {}}
                        className='h-full mt-2'
                        width='80%'
                        onMount={(editor) => {
                          editor.updateOptions({ readOnly: true });
                        }}
                      />
                    </div>
                  )}
                </div>
              )
            },
            {
              key: 'clusters',
              label: 'Clusters',
              children: <ClustersTable instanceId={instanceId} />
            }
          ]}
        />
      </div>
    </div>
  );
};

const JSONField = (props: { children: React.ReactNode; label: string; className?: string }) => (
  <div className={classNames('mb-6 w-5/12 mr-10', props?.className)}>
    <div className='font-semibold mb-2 text-gray-500 text-sm'>{props.label}</div>
    <pre className='text-lg max-h-72 overflow-y-auto rounded-md border-2 border-blue-100 p-4 tracking-wider'>
      {props.children}
    </pre>
  </div>
);
