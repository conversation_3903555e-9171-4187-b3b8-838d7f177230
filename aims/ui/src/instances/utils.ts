import { useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useDebounce } from 'use-debounce';

import { InternalInstance, ListOrganizationMembersResponse } from '@/lib/apiclient/aims/v1/aims_pb';
import { StatusCode } from '@/lib/apiclient/types/status/health/v1/health_pb';

export type InstanceFilterState = {
  fuzz: string;
  health?: StatusCode;
  apps?: number;
  clusters?: number;
};

export const instanceFilters = {
  toSearch: (state: InstanceFilterState) => {
    const search = new URLSearchParams();

    if (state.fuzz) {
      search.set('fuzz', state.fuzz);
    }

    if (state.health) {
      search.set('health', `${state.health}`);
    }

    if (state.apps) {
      search.set('apps', `${state.apps || 0}`);
    }

    if (state.clusters) {
      search.set('clusters', `${state.clusters || 0}`);
    }

    return search.toString();
  },
  toState: (search: string): InstanceFilterState => {
    const searchParams = new URLSearchParams(search);

    const fuzz = searchParams.get('fuzz');

    const health = searchParams.get('health');

    const apps = searchParams.get('apps');

    const clusters = searchParams.get('clusters');

    return {
      fuzz: fuzz || '',
      health: +health,
      apps: +apps || 0,
      clusters: +clusters || 0
    };
  }
};

export const useInstanceFilters = (
  instances: InternalInstance[],
  members: ListOrganizationMembersResponse
) => {
  const [search, setSearch] = useSearchParams();

  const filters = useMemo(() => instanceFilters.toState(search.toString()), [search]);

  const [debouncedFilters] = useDebounce(filters, 500);

  const filteredInstances = useMemo(
    () =>
      instances?.filter((instance) => {
        let fuzzMatched = false;
        if (!debouncedFilters?.fuzz) {
          fuzzMatched = true;
        }

        const fuzz = debouncedFilters.fuzz.toLowerCase().trim();

        const instanceMatch =
          instance?.instance?.id?.toLowerCase().includes(fuzz) ||
          instance?.instance?.name?.toLowerCase().includes(fuzz);

        if (instanceMatch) {
          fuzzMatched = true;
        }

        const orgMatch = instance?.instance?.ownerOrganizationName?.toLowerCase().includes(fuzz);

        if (orgMatch) {
          fuzzMatched = true;
        }

        const instMembers = members?.members?.[instance?.instance?.ownerOrganizationName];

        if (
          instMembers?.email?.length > 0 &&
          instMembers?.email?.find((m) => m.toLowerCase().includes(fuzz))
        ) {
          fuzzMatched = true;
        }

        let healthMatched = false;

        switch (debouncedFilters.health) {
          case StatusCode.UNKNOWN:
          case StatusCode.UNSPECIFIED:
            healthMatched = true;
            break;
          default:
            healthMatched = instance?.instance?.healthStatus.code === debouncedFilters.health;
        }

        const appsNumberMatched =
          instance?.instance?.info?.applicationsStatus?.applicationCount >= debouncedFilters.apps;

        const clustersNumberMatched = instance?.instance?.clusterCount >= debouncedFilters.clusters;

        return fuzzMatched && healthMatched && appsNumberMatched && clustersNumberMatched;
      }) || [],
    [debouncedFilters, instances, members]
  );

  return {
    filteredInstances,
    setSearch,
    filters
  };
};
