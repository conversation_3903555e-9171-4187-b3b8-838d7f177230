import { faBell, faWarning, faShip } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Menu } from 'antd';
import { useState } from 'react';

import { NotificationCategory } from '@/lib/apiclient/aims/v1/aims_pb';

import { NotificationCustomInput } from './notification-custom-input';
import { NotificationFeatureInput } from './notification-feature-input';
import { SendNotificationModal } from './send-notification-modal';
import { EmailEventPayload } from './types';

export const NotificationPage = () => {
  const [selectedNotification, setSelectedNotification] = useState('custom');

  const [modal, setModal] = useState({
    open: false,
    type: NotificationCategory.CUSTOM,
    state: {} as EmailEventPayload | null
  });

  const onModalClose = () =>
    setModal({ open: false, type: NotificationCategory.UNSPECIFIED, state: null });

  return (
    <div className='w-full'>
      <div className='flex gap-5 items-center h-fit'>
        <FontAwesomeIcon icon={faBell} className='text-2xl' />
        <h1>Notification Administration</h1>
      </div>
      <p className='mt-2'>Send CVEs patches, New feature notifications to organizations.</p>

      <div className='border-t-[1px] flex mt-5 gap-5 w-full'>
        <Menu
          className='p-5 w-4/12'
          mode='vertical'
          selectedKeys={[selectedNotification]}
          onClick={(e) => setSelectedNotification(e.key)}
          items={[
            {
              key: 'custom',
              label: 'Custom',
              icon: <FontAwesomeIcon icon={faWarning} />
            },
            {
              key: 'feature',
              label: 'New Feature',
              icon: <FontAwesomeIcon icon={faShip} />
            }
          ]}
        />
        <div className='w-8/12 p-5'>
          {(() => {
            if (selectedNotification === 'custom') {
              return (
                <NotificationCustomInput
                  onSubmit={(state) =>
                    setModal({
                      open: true,
                      type: NotificationCategory.CUSTOM,
                      state: { body: state.body, title: state.title, subject: state.subject }
                    })
                  }
                />
              );
            }

            if (selectedNotification === 'feature') {
              return (
                <NotificationFeatureInput
                  onSubmit={(state) =>
                    setModal({
                      open: true,
                      type: NotificationCategory.NEW_FEATURE,
                      state: { feature: state.feature }
                    })
                  }
                />
              );
            }

            return null;
          })()}
        </div>
      </div>

      <SendNotificationModal
        key={`${modal.open}`}
        open={modal.open}
        onClose={onModalClose}
        categoryType={modal.type}
        state={modal.state}
      />
    </div>
  );
};
