import { faForward } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Input } from 'antd';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { FieldContainer } from '@/lib/components/shared/forms';

const newFeatureForm = z.object({
  feature: z.string().min(1, 'Feature name is required.')
});

type NewFeatureFormType = z.infer<typeof newFeatureForm>;

export const NotificationFeatureInput = ({
  onSubmit
}: {
  onSubmit(state: NewFeatureFormType): void;
}) => {
  const form = useForm<NewFeatureFormType>({
    defaultValues: {
      feature: ''
    },
    resolver: zodResolver(newFeatureForm)
  });

  const handleSubmit = form.handleSubmit(onSubmit);

  return (
    <div className='rounded-2xl bg-gray-50 p-5 ring-1 ring-inset ring-gray-900/5'>
      <h2 className='font-semibold'>Send to AKP portal: </h2>
      <FieldContainer
        control={form.control}
        name='feature'
        label='New Feature Name'
        wrapperClassName='my-3'
      >
        {({ field }) => (
          <Input
            placeholder='Teams and Workspaces'
            value={field.value}
            onChange={(e) => field.onChange(e.target.value)}
          />
        )}
      </FieldContainer>
      <Button
        type='primary'
        className='mt-5'
        icon={<FontAwesomeIcon icon={faForward} />}
        onClick={handleSubmit}
      >
        Test and Send
      </Button>
    </div>
  );
};
