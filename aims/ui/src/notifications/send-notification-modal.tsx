import { Struct, JsonValue } from '@bufbuild/protobuf';
import { zodResolver } from '@hookform/resolvers/zod';
import JsonView from '@uiw/react-json-view';
import { List, Modal, Steps, Select, notification } from 'antd';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Link } from 'react-router-dom';
import { z } from 'zod';

import { useListAllOrganizations, useSendNotification } from '@/hook/api';
import {
  NotificationCategory,
  OrganizationFilter,
  SendNotificationRequest
} from '@/lib/apiclient/aims/v1/aims_pb';
import { AuditForm } from '@/lib/components/shared/audit-form';
import { zodAuditSchema } from '@/utils';

import { humanizeNotificationCategory } from './utils';

const schema = z.object({
  audit: zodAuditSchema,
  organizationId: z.string().optional()
});

type SendNotificationForm = z.infer<typeof schema>;

type SendNotificationModalProps = {
  open: boolean;
  onClose(): void;
  categoryType: NotificationCategory;
  state: object;
};

export const SendNotificationModal = (props: SendNotificationModalProps) => {
  const { mutate, isPending, isSuccess } = useSendNotification();

  const [orgSearch, setOrgSearch] = useState('');

  const [steps, setSteps] = useState(0);

  const { data: orgList, isPending: isPendingOrgList } = useListAllOrganizations(
    new OrganizationFilter({ fuzz: orgSearch, limit: 10 }),
    {
      enabled: steps === 1 && !!orgSearch,
      placeholderData: (d) => d
    }
  );

  const form = useForm<SendNotificationForm>({
    defaultValues: {
      audit: {
        actor: '',
        reason: ''
      },
      organizationId: undefined
    },
    resolver: zodResolver(schema)
  });

  const handleAuditSubmit = form.handleSubmit(() => {
    setSteps(1);
  });

  const audit = form.watch('audit');
  const orgId = form.watch('organizationId');

  return (
    <Modal
      title={`Send ${humanizeNotificationCategory(props.categoryType)} Notification`}
      open={props.open}
      onCancel={props.onClose}
      okButtonProps={{
        loading: isPending
      }}
      okText={steps === 0 ? 'Next' : steps === 1 ? 'Test' : 'Send'}
      onOk={() => {
        if (steps === 0) {
          handleAuditSubmit();
        } else {
          const request = new SendNotificationRequest({
            audit,
            category: props.categoryType,
            metadata: Struct.fromJson(props.state as JsonValue)
          });

          if (steps === 1) {
            request.testMode = true;
            request.organizationId = orgId;
          }

          mutate(request, {
            onSuccess: () => {
              if (steps === 1) {
                setSteps(2);
                return;
              }
              notification.success({
                message: 'Notification sent successfully.'
              });
              props.onClose();
            }
          });
        }
      }}
    >
      <Steps
        current={steps}
        items={[
          {
            title: 'Audit',
            onClick: () => steps > 0 && setSteps(0)
          },
          {
            title: 'Test',
            onClick: () => steps > 1 && setSteps(1)
          },
          {
            title: 'Send',
            onClick: () => isSuccess && setSteps(2)
          }
        ]}
        className='my-5'
      />

      {steps === 0 && (
        <div className='bg-slate-50 rounded-md p-5'>
          <List
            header='Insert'
            bordered
            dataSource={[
              {
                key: 'value',
                value: {
                  categoryType: props.categoryType,
                  state: props.state
                }
              }
            ]}
            renderItem={(item) => (
              <List.Item>
                <JsonView collapsed value={item.value} />
              </List.Item>
            )}
            className='mb-5'
          />
          <AuditForm control={form.control} />
        </div>
      )}

      {steps === 1 && (
        <div>
          <label>Select Your Organization To Test: </label>
          <Select
            placeholder='Search organization by your email or organization name/id'
            className='w-full mt-2'
            showSearch
            filterOption={false}
            searchValue={orgSearch}
            onSearch={setOrgSearch}
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            options={(orgList?.organizations || []).map((org: { name: any; id: any }) => ({
              label: org.name,
              value: org.id
            }))}
            value={orgId || ''}
            onChange={(value) => form.setValue('organizationId', value)}
            loading={isPendingOrgList}
          />
          <span className='text-xs font-light text-gray-400 italic'>
            make sure you are part of the organization you want to test
          </span>
        </div>
      )}

      {steps === 2 && (
        <div>
          <p>
            Notification sent successfully to organization id{' '}
            <Link to={`/organizations/${orgId}`} target='__blank'>
              <b>{orgId}</b>
            </Link>
            . You can check in AKP notification center and/or your email if this notification is
            suppossed to be send via emails.
          </p>

          <h2 className='mt-5 font-semibold'>
            Once you review and everything looks good, click on "Send".
          </h2>
        </div>
      )}
    </Modal>
  );
};
