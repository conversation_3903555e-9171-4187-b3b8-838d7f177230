import { PlainMessage } from '@bufbuild/protobuf';
import { useMutation, useQuery, UseQueryOptions } from '@tanstack/react-query';

import { queryKeys } from '../../constants/query-keys';
import {
  Audit,
  BasicOrganization,
  Customer,
  ListUnbilledOrganizationsResponse
} from '../../lib/apiclient/aims/v1/aims_pb';
import { apiFetch } from '../../utils/api-fetch';

export function useOnboardManualCustomer() {
  return useMutation({
    mutationFn: ({ customer, audit }: { customer: Customer; audit: PlainMessage<Audit> }) =>
      apiFetch(`v1/aims/onboard`, {
        method: 'POST',
        body: JSON.stringify({ customer, audit })
      })
  });
}

export function useListUnbilledOrganizations(options?: UseQueryOptions<BasicOrganization[]>) {
  return useQuery<BasicOrganization[]>({
    queryKey: queryKeys.organizations.unbilled().queryKey,
    queryFn: () =>
      apiFetch<ListUnbilledOrganizationsResponse>(`v1/aims/organizations/unbilled`).then(
        (response) => response.organizations
      ),
    ...options
  });
}
