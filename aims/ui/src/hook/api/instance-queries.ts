import { PlainMessage } from '@bufbuild/protobuf';
import { useMutation, useQuery, UseQueryOptions } from '@tanstack/react-query';

import { queryKeys } from '../../constants/query-keys';
import {
  Audit,
  ClusterFilter,
  GetInstanceByIdResponse,
  GetKargoInstanceByIdResponse,
  InstanceClusterMaintenanceRequest,
  InternalInstance,
  ListAgentsForKargoInstanceResponse,
  ListArgoInstancesRequest,
  ListArgoInstancesResponse,
  ListClustersForInstanceResponse,
  ListKargoInstancesRequest,
  ListKargoInstancesResponse
} from '../../lib/apiclient/aims/v1/aims_pb';
import { Cluster } from '../../lib/apiclient/argocd/v1/argocd_pb';
import { flattenObject } from '../../lib/components/audit-log/filters/utils';
import { apiFetch } from '../../utils/api-fetch';

function toURLSearchParams(obj: Record<string, unknown>): URLSearchParams {
  const params = new URLSearchParams();
  const flatObj = flattenObject(obj);
  for (const [key, value] of Object.entries(flatObj)) {
    if (value !== null && value !== undefined && value !== '') {
      params.append(key, String(value));
    }
  }
  return params;
}

function generateFilter(request: { filter?: { [key: string]: string | boolean | undefined } }) {
  const filters = new URLSearchParams();

  if (request.filter) {
    for (const [key, value] of Object.entries(request.filter)) {
      if (value !== undefined) {
        filters.set(`filter.${key}`, value.toString());
      }
    }
  }

  return filters;
}

function generateArgoFilter(request: PlainMessage<ListArgoInstancesRequest>) {
  return generateFilter(request);
}

export function generateKargoFilter(request: PlainMessage<ListKargoInstancesRequest>) {
  return generateFilter(request);
}

export function useListArgoInstances(
  request: PlainMessage<ListArgoInstancesRequest>,
  options?: Omit<UseQueryOptions<InternalInstance[]>, 'queryKey' | 'queryFn'>
) {
  const filters = generateArgoFilter(request);

  return useQuery<InternalInstance[]>({
    queryKey: queryKeys.instances.argoInstances(request).queryKey,
    queryFn: () =>
      apiFetch(`v1/aims/argo/instances?${filters.toString()}`)
        .then(ListArgoInstancesResponse.fromJson)
        .then((response) => response.instances),
    ...options
  });
}

export function useGetInstanceById(
  instanceId: string,
  options?: Omit<UseQueryOptions<InternalInstance>, 'queryKey' | 'queryFn'>
) {
  return useQuery<InternalInstance>({
    queryKey: queryKeys.instances.argoInstance(instanceId).queryKey,
    queryFn: () =>
      apiFetch(`v1/aims/instances/${instanceId}`)
        .then(GetInstanceByIdResponse.fromJson)
        .then((response) => response.instance),
    enabled: !!instanceId,
    ...options
  });
}

export function useGetInstanceClusters(
  instanceId: string,
  filters: ClusterFilter,
  options?: Omit<UseQueryOptions<Cluster[]>, 'queryKey' | 'queryFn'>
) {
  const query = new URLSearchParams();

  if (filters.fuzz) {
    query.set('filter.fuzz', filters.fuzz);
  }

  if (filters.timeFrom) {
    query.set('filter.timeFrom', filters.timeFrom);
  }

  return useQuery<Cluster[]>({
    queryKey: queryKeys.instances.instanceClusters(instanceId, filters).queryKey,
    queryFn: () =>
      apiFetch(`v1/aims/instances/${instanceId}/clusters?${query.toString()}`)
        .then(ListClustersForInstanceResponse.fromJson)
        .then((response) => response.clusters),
    enabled: !!instanceId,
    ...options
  });
}

export function useListKargoInstances(
  request: PlainMessage<ListKargoInstancesRequest>,
  options?: Omit<UseQueryOptions<PlainMessage<ListKargoInstancesResponse>>, 'queryKey' | 'queryFn'>
) {
  const filters = generateKargoFilter(request);

  return useQuery<PlainMessage<ListKargoInstancesResponse>>({
    queryKey: queryKeys.instances.kargoInstances(request).queryKey,
    queryFn: () => apiFetch(`v1/aims/kargo/instances?${filters.toString()}`),
    ...options
  });
}

export function useGetKargoInstanceById(
  id: string,
  options?: Omit<
    UseQueryOptions<PlainMessage<GetKargoInstanceByIdResponse>>,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<PlainMessage<GetKargoInstanceByIdResponse>>({
    queryKey: queryKeys.instances.kargoInstance(id).queryKey,
    queryFn: () => apiFetch(`v1/aims/kargo/instances/${id}`),
    enabled: !!id,
    ...options
  });
}

export function useDeleteArgoInstance() {
  return useMutation({
    mutationFn: ({ instanceId, audit }: { instanceId: string; audit: PlainMessage<Audit> }) => {
      const params = toURLSearchParams({ audit });
      return apiFetch(`v1/aims/instances/${instanceId}?${params.toString()}`, {
        method: 'DELETE'
      });
    }
  });
}

export function useDecrementInstance() {
  return useMutation({
    mutationFn: ({ instanceId, audit }: { instanceId: string; audit: PlainMessage<Audit> }) =>
      apiFetch(`v1/aims/instances/${instanceId}/generation/decrement`, {
        method: 'POST',
        body: JSON.stringify({ audit: audit })
      })
  });
}

export function useDeleteKargoInstance() {
  return useMutation({
    mutationFn: ({ id, audit }: { id: string; audit: PlainMessage<Audit> }) => {
      const params = toURLSearchParams({ audit });
      return apiFetch(`v1/aims/kargo/instances/${id}?${params.toString()}`, {
        method: 'DELETE'
      });
    }
  });
}

export function useListKargoInstanceClusters(
  id: string,
  options?: Omit<
    UseQueryOptions<PlainMessage<ListAgentsForKargoInstanceResponse>>,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<PlainMessage<ListAgentsForKargoInstanceResponse>>({
    queryKey: queryKeys.instances.kargoInstanceClusters(id).queryKey,
    queryFn: () => apiFetch(`v1/aims/kargo/instances/${id}/agents`),
    enabled: !!id,
    ...options
  });
}

export function useSetClusterInMaintenanceMode() {
  return useMutation({
    mutationFn: ({
      instanceId,
      clusterId,
      maintenanceMode,
      audit
    }: {
      instanceId: string;
      clusterId: string;
      maintenanceMode: boolean;
      audit: PlainMessage<Audit>;
    }) =>
      apiFetch(`v1/aims/instances/${instanceId}/clusters/${clusterId}`, {
        method: 'POST',
        body: new InstanceClusterMaintenanceRequest({
          audit,
          maintanenceMode: maintenanceMode
        }).toJsonString()
      })
  });
}
