import { PlainMessage } from '@bufbuild/protobuf';
import { capitalize, omit } from 'lodash';
import moment from 'moment';

import { BillingDetails, Organization } from '@/lib/apiclient/organization/v1/organization_pb';
import {
  OrganizationFeatureGates,
  OrganizationQuota,
  SystemFeatureGates
} from '@/lib/apiclient/types/features/v1/features_pb';
import { PlanTier } from '@/types';

export type Changes = Array<{
  key: string;
  oldValue: string;
  newValue: string;
}>;

const changes = <T>(old: T, _new: T) => {
  const changes: Changes = [];
  for (const [key, value] of Object.entries(old)) {
    const newValue = _new[key as keyof T];
    if (String(newValue) !== String(value)) {
      changes.push({
        key,
        oldValue: String(value),
        newValue: String(newValue)
      });
    }
  }
  return changes;
};

export const featureGatesChanges = (
  old: Partial<OrganizationFeatureGates>,
  _new: Partial<OrganizationFeatureGates>
) => {
  return changes(old, _new);
};

export const quotaChanges = (old: Partial<OrganizationQuota>, _new: Partial<OrganizationQuota>) => {
  return changes(old, _new);
};

export const isUnlimitedQuota = (
  resource: keyof PlainMessage<OrganizationQuota>,
  value: number | bigint
) => {
  switch (resource) {
    case 'maxClusters':
    case 'maxOrgMembers':
    case 'maxKargoInstances':
    case 'maxKargoAgents':
      return Number(value) === 0;
  }

  return false;
};

export const onlyRequireFeatureGates = (
  gates: PlainMessage<OrganizationFeatureGates>,
  systemGates: PlainMessage<SystemFeatureGates>
) => {
  const removeUnavailableSystemGates =
    Object.entries(systemGates || {})
      .filter(([, v]) => !v)
      .map(([k]) => k) || [];

  return omit(gates || {}, ['shards', ...removeUnavailableSystemGates]);
};

export const getStripeCustomerDashboard = (customerId: string) =>
  `https://dashboard.stripe.com/customers/${customerId}`;

export const getStripePriceDashboard = (priceId: string) =>
  `https://dashboard.stripe.com/prices/${priceId}`;

export const datesUtil = (date: number | string | Date) => {
  let momentFormat: moment.Moment;

  if (typeof date === 'number') {
    momentFormat = moment.unix(date);
  } else {
    momentFormat = moment(date);
  }

  return {
    formattedDate: momentFormat.format('D MMMM YYYY'),
    humanFriendly: momentFormat.fromNow()
  };
};

export const customerPlanStatus = (
  org: Organization,
  billingDetails: BillingDetails,
  expired: boolean
) => {
  if (org?.status?.trial && !billingDetails?.manual) {
    return `Free Trial expire${expired ? 'd' : 's'}`;
  }

  if (expired) {
    return 'Expired';
  }

  return 'Current plan renews';
};

// Create an API that get plans and its display name from organization_plan table
export const getPlanDisplayName = (plan: string) => {
  switch (plan as PlanTier) {
    case 'starter_v2':
      return 'Starter';
    case 'professional_plus_trial':
      return 'Pro Plus - Trial';
    case 'professional':
      return 'Pro';
    case 'professional_plus':
      return 'Pro Plus';
  }

  return (
    plan
      ?.split('_')
      ?.map((w) => capitalize(w))
      ?.join(' ') || plan
  );
};

export const getPlanTier = (org: Organization, billingDetails: BillingDetails): PlanTier => {
  if (billingDetails?.manual) {
    return 'custom';
  }

  // legacy
  // default plan is professional on free trial
  if (!org?.plan && org?.status?.trial) {
    return 'professional';
  }

  return (org?.plan as PlanTier) || 'unknown';
};
