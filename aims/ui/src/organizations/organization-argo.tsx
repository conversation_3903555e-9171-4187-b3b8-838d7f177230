import { useListOrganizationMembers, useListArgoInstances } from '@/hook/api';
import { ListArgo } from '@/instances/components/table-list';
import { useInstanceFilters } from '@/instances/utils';
import { BasicOrganization, ListArgoInstancesRequest } from '@/lib/apiclient/aims/v1/aims_pb';

type OrganizationUsageProps = {
  organization: BasicOrganization;
};
export const OrganizationArgoList = ({ organization }: OrganizationUsageProps) => {
  const filters = new ListArgoInstancesRequest({
    filter: {
      organizationId: organization.id
    }
  });

  const { data: instances, refetch, isFetching } = useListArgoInstances(filters);

  const { data: members, isFetching: membersFetching } = useListOrganizationMembers();

  const { filteredInstances } = useInstanceFilters(instances, members);

  return (
    <div className='w-full'>
      <ListArgo
        instances={filteredInstances}
        isOrganizationListing={true}
        isFetching={isFetching}
        members={members?.members}
        membersFetching={membersFetching}
        paid={organization?.status?.trial === false}
        refetch={refetch}
      />
    </div>
  );
};
