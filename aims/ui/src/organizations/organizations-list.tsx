import { PlainMessage } from '@bufbuild/protobuf';
import {
  faCheck,
  faCircleCheck,
  faExclamationTriangle,
  faFileInvoiceDollar,
  faUser,
  faUserGroup
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { zodResolver } from '@hookform/resolvers/zod';
import { Alert, Button, Modal, Table, Tooltip } from 'antd';
import moment from 'moment';
import { useEffect, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { Link } from 'react-router-dom';
import { z } from 'zod';

import { useModal } from '@/hook';
import {
  useGetInternalConfig,
  useListAllOrganizations,
  useToggleInstanceCreation
} from '@/hook/api';
import { Audit, BasicOrganization, Sort } from '@/lib/apiclient/aims/v1/aims_pb';
import { AuditForm } from '@/lib/components/shared/audit-form';
import { zodAuditSchema } from '@/utils';

import FilterSection, { getPlanColor } from './filters/filter-section';
import { useOrganizationFilters } from './filters/use-organization-filter';

const ERR_NO_CONFIGMAP = 'configmap not found';

const toggleSchema = z.object({
  audit: zodAuditSchema
});

type ToggleForm = z.infer<typeof toggleSchema>;

const ToggleInstanceCreationModal = ({
  visible,
  hide,
  disabled,
  onSuccess
}: {
  visible: boolean;
  hide: () => void;
  disabled: boolean;
  onSuccess: () => void;
}) => {
  const { mutate, isPending } = useToggleInstanceCreation();
  const { control, handleSubmit } = useForm<ToggleForm>({
    resolver: zodResolver(toggleSchema),
    defaultValues: {
      audit: {
        actor: '',
        reason: ''
      }
    }
  });

  const onSubmit = handleSubmit((data) => {
    mutate(
      {
        disabled: !disabled,
        audit: data.audit as PlainMessage<Audit>
      },
      {
        onSuccess: () => {
          onSuccess();
          hide();
        }
      }
    );
  });

  return (
    <Modal
      title={disabled ? 'Enable Instance Creation' : 'Disable Instance Creation'}
      visible={visible}
      onCancel={hide}
      footer={[
        <Button key='back' onClick={hide}>
          Cancel
        </Button>,
        <Button key='submit' type='primary' loading={isPending} onClick={onSubmit}>
          Confirm
        </Button>
      ]}
    >
      <p>
        Are you sure you want to {disabled ? 'enable' : 'disable'} instance creation for all
        organizations?
      </p>
      <AuditForm control={control} />
    </Modal>
  );
};

const formatPlanName = (plan: string, isManual: boolean) => {
  if (!plan) return 'No Plan';
  const planName = plan.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase());
  return isManual ? `${planName} (Manual)` : planName;
};

export const OrganizationsList = () => {
  const { filters, setFilters } = useOrganizationFilters();

  const {
    data,
    isFetching: isLoading,
    error: organizationsError
  } = useListAllOrganizations(filters);

  const { data: config, error, refetch } = useGetInternalConfig();

  const { show } = useModal((p) => (
    <ToggleInstanceCreationModal
      {...p}
      disabled={!!config?.disableFreeInstanceCreation}
      onSuccess={refetch}
    />
  ));

  const tableContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    tableContainerRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [filters]);

  return (
    <div className='w-full' ref={tableContainerRef}>
      <div className='flex items-center mb-4'>
        <div className='text-2xl font-bold'>Organizations List</div>
        <div className='ml-auto flex items-center'>
          {error && (
            <Alert
              message={
                error?.message === ERR_NO_CONFIGMAP
                  ? 'portal-internal configmap not found. It must exist to disable free instance creation.'
                  : 'Error fetching internal config'
              }
              type='error'
              className='mr-4'
            />
          )}
          <Button
            danger={!config?.disableFreeInstanceCreation}
            onClick={() => show()}
            disabled={error?.message === ERR_NO_CONFIGMAP}
          >
            <FontAwesomeIcon
              icon={config?.disableFreeInstanceCreation ? faCheck : faExclamationTriangle}
              className='mr-2'
            />
            {config?.disableFreeInstanceCreation
              ? 'Enable Instance Creation'
              : 'Disable Instance Creation'}
          </Button>
        </div>
      </div>

      {organizationsError && (
        <Alert
          message='Error fetching organizations'
          description={organizationsError.message}
          type='error'
          className='mb-4'
        />
      )}
      <FilterSection />

      <Table
        dataSource={data?.organizations}
        rowKey={(o: BasicOrganization) => `${o?.id}-${o?.name}`}
        className='w-full'
        pagination={{
          showSizeChanger: true,
          pageSize: filters.limit,
          current: (filters.offset / filters.limit || 0) + 1,
          pageSizeOptions: [5, 10, 50, 100],
          total: data?.count,
          showTotal: (total) => `Total ${total} Organizations`,
          onShowSizeChange(_, current) {
            setFilters({ ...filters, limit: current });
          }
        }}
        loading={isLoading}
        onChange={(pagination, _, sorter) => {
          let sortByCreation;
          // @ts-expect-error ANTD API is flexible
          switch (sorter?.order) {
            case 'ascend':
              sortByCreation = Sort.ASCENDING;
              break;
            case 'descend':
              sortByCreation = Sort.DESCENDING;
              break;
            default:
              sortByCreation = Sort.UNSPECIFIED;
              break;
          }

          if (filters.sortByCreation !== sortByCreation) {
            setFilters({ sortByCreation });
          } else {
            setFilters({
              limit: pagination.pageSize,
              offset: (pagination.current - 1) * pagination.pageSize
            });
          }
        }}
      >
        <Table.Column
          title='Organization'
          dataIndex='name'
          key='name'
          width={280}
          render={(name: string, record: BasicOrganization) => (
            <div>
              <div className='flex items-center'>
                <Link to={`/organizations/${record.id}`} className='font-bold text-blue-500'>
                  {record.manuallyVerified && (
                    <Tooltip title='Manually Verified'>
                      <FontAwesomeIcon icon={faCircleCheck} className='mr-2 text-green-500' />
                    </Tooltip>
                  )}
                  {record.billed && (
                    <Tooltip title='Paid Customer'>
                      <FontAwesomeIcon
                        icon={faFileInvoiceDollar}
                        className='mr-2 text-yellow-500'
                      />
                    </Tooltip>
                  )}
                  {name}
                </Link>
              </div>
              <div className='text-xs text-gray-500 mt-1'>ID: {record.id}</div>
            </div>
          )}
        />
        <Table.Column
          title='Plan'
          dataIndex='plan'
          key='plan'
          width={180}
          render={(plan: string, data: BasicOrganization) => {
            const colors = getPlanColor(plan);
            return (
              <span className={`text-sm ${colors.bg} ${colors.text} px-2 py-1 rounded`}>
                {formatPlanName(plan, data?.billingDetails?.manual || false)}
              </span>
            );
          }}
        />
        <Table.Column
          title='Members'
          dataIndex='emails'
          key='memberCount'
          width={100}
          align='center'
          render={(emails: string[]) => {
            const memberCount = emails?.length || 0;
            return (
              <div className='text-center'>
                {memberCount > 0 && (
                  <Tooltip title={emails.join(', ')}>
                    <div>
                      <FontAwesomeIcon
                        icon={memberCount === 1 ? faUser : faUserGroup}
                        className='mr-1 text-[12px]'
                      />
                      <span>{memberCount}</span>
                    </div>
                  </Tooltip>
                )}
              </div>
            );
          }}
        />
        <Table.Column
          title='Instances'
          dataIndex='numInstances'
          key='numInstances'
          width={100}
          align='center'
          render={(numInstances: string) => <div className='text-center'>{numInstances}</div>}
        />
        <Table.Column
          title='Created'
          dataIndex='creationTimestamp'
          sorter
          width={160}
          sortOrder={
            filters?.sortByCreation === Sort.ASCENDING
              ? 'ascend'
              : filters?.sortByCreation === Sort.DESCENDING
                ? 'descend'
                : null
          }
          render={(creationTimestamp: string) => (
            <Tooltip title={moment(creationTimestamp).format('YYYY-MM-DD HH:mm:ss')}>
              <div className='text-sm'>{moment(creationTimestamp).fromNow()}</div>
            </Tooltip>
          )}
        />
      </Table>
    </div>
  );
};
