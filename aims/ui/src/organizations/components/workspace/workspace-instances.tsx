import { Flex } from 'antd';

import { useWorkspaceContext } from '../../context/workspace-context';

import { WorkspaceInstancesList } from './workspace-instances-list';

export const WorkspaceInstances = () => {
  const { workspace } = useWorkspaceContext();

  return (
    <Flex gap={48}>
      <Flex flex={1} className='max-w-xl'>
        <WorkspaceInstancesList
          title='Argo CD'
          instances={workspace.argocdInstances}
          instancePath={'/instances/settings/:id'}
        />
      </Flex>
      <Flex flex={1} className='max-w-xl'>
        <WorkspaceInstancesList
          title='Kargo'
          instances={workspace.kargoInstances}
          instancePath={'/kargo/instances/:id'}
        />
      </Flex>
    </Flex>
  );
};
