import { faBullseye, faUsers } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Flex, Space, Typography } from 'antd';
import { Link, generatePath } from 'react-router-dom';

import { Workspace } from '@/lib/apiclient/organization/v1/organization_pb';
import { pluralize } from '@/utils';

type Props = {
  workspace: Workspace;
  orgId: string;
};

const INSTACES_LIMIT = 3;

export const WorkspaceTile = ({ workspace, orgId }: Props) => {
  const teamsCountInfo =
    workspace.teamMemberCount > 0 &&
    `${workspace.teamMemberCount} ${pluralize(workspace.teamMemberCount, 'Team')}`;
  const usersCountInfo =
    workspace.userMemberCount > 0 &&
    `${workspace.userMemberCount} ${pluralize(workspace.userMemberCount, 'Member')}`;

  const membersInfo = [teamsCountInfo, usersCountInfo].filter(Boolean).join(' + ');

  return (
    <Link
      to={{
        pathname: generatePath('/organizations/:orgId/workspaces/:workspaceId', {
          orgId,
          workspaceId: workspace.id
        })
      }}
      className='block hover:bg-gray-50 transition-colors'
      style={{ color: '#ccc !important' }}
    >
      <div
        className='rounded-md border-gray-100 border h-full'
        style={{
          flex: '1',
          flexDirection: 'column',
          display: 'flex',
          boxShadow: '0 2px 6px 0 rgba(0,0,0,0.16),0 0 0 1px rgba(0,0,0,0.1)'
        }}
      >
        <Typography.Title level={4}>
          <Flex justify='space-between' align='center' className='px-6 pt-5 pb-0'>
            {workspace.name}
            <FontAwesomeIcon icon={faBullseye} />
          </Flex>
        </Typography.Title>
        <Flex className='px-6 text-black' flex={1}>
          <Flex flex={1} vertical className='pb-4 pt-1'>
            <WorkspaceTileList instances={workspace.argocdInstances} title='Argo CD' />
          </Flex>

          <Flex flex={1} vertical className='border-l ml-2 pl-4 pb-4 pt-1'>
            <WorkspaceTileList instances={workspace.kargoInstances} title='Kargo' />
          </Flex>
        </Flex>
        <Space className='px-6 py-4 text-gray-500 border-t text-sm'>
          <FontAwesomeIcon icon={faUsers} />
          {membersInfo || 'No Members'}
        </Space>
      </div>
    </Link>
  );
};

type ListType = {
  instances: { name: string; id: string }[];
  title: string;
};

const WorkspaceTileList = ({ instances, title }: ListType) => {
  return (
    <div className='overflow-hidden'>
      <div className='text-gray-500'>{title}</div>
      <Space direction='vertical' size={0} className='text-sm mt-2'>
        {instances
          .slice(0, instances.length === INSTACES_LIMIT + 1 ? INSTACES_LIMIT + 1 : INSTACES_LIMIT)
          .map((instance) => (
            <div key={instance.id} className='text-ellipsis text-nowrap overflow-hidden'>
              {instance.name}
            </div>
          ))}
        {instances.length > INSTACES_LIMIT + 1 && (
          <div className='text-gray-400'>+ {instances.length - INSTACES_LIMIT} more</div>
        )}
        {instances.length === 0 && <div className='text-gray-400 text-sm'>No Instances</div>}
      </Space>
    </div>
  );
};
