import { faServer, faUsers, faUserShield } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Tabs, Typography, Breadcrumb } from 'antd';
import { useState, useEffect } from 'react';
import { Link, useParams, useSearchParams } from 'react-router-dom';

import { Loading } from '@/lib/components/loading';
import { useOrganization } from '@/organizations/context/organization-context';

import { useWorkspaceContext, WorkspaceContextProvider } from '../../context/workspace-context';

import { WorkspaceInstances } from './workspace-instances';
import { WorkspaceRole } from './workspace-role';
import { WorkspaceUsersTeams } from './workspace-users-teams';

enum WorkspaceTabs {
  team = 'team',
  instances = 'instances',
  roles = 'roles'
}

const workspaceTabs = {
  [WorkspaceTabs.team]: {
    path: 'team',
    label: 'Team & Members',
    icon: <FontAwesomeIcon icon={faUsers} />,
    view: <WorkspaceUsersTeams />
  },
  [WorkspaceTabs.instances]: {
    path: 'instances',
    label: 'Instances',
    icon: <FontAwesomeIcon icon={faServer} />,
    view: <WorkspaceInstances />
  },
  [WorkspaceTabs.roles]: {
    path: 'roles',
    label: 'Roles',
    icon: <FontAwesomeIcon icon={faUserShield} />,
    view: <WorkspaceRole />
  }
};

export const WorkspaceDetails = () => {
  return (
    <WorkspaceContextProvider>
      <WorkspaceDetailsContent />
    </WorkspaceContextProvider>
  );
};

const WorkspaceDetailsContent = () => {
  const { workspaceId, orgId } = useParams();
  const { workspace, isLoading } = useWorkspaceContext();
  const [searchParams, setSearchParams] = useSearchParams();
  const { organization } = useOrganization();
  const currentTab = (searchParams.get('tab') as WorkspaceTabs) || WorkspaceTabs.team;
  const [activeTab, setActiveTab] = useState<WorkspaceTabs>(currentTab);

  useEffect(() => {
    setActiveTab(currentTab);
  }, [currentTab]);

  const handleTabChange = (key: string) => {
    if (key in workspaceTabs && workspaceId) {
      setActiveTab(key as WorkspaceTabs);
      setSearchParams({ tab: key });
    }
  };

  return (
    <div className='w-full'>
      {isLoading && <Loading />}

      {workspace && (
        <>
          <Breadcrumb className='mb-4 text-gray-500'>
            <Breadcrumb.Item>
              <Link to='/organizations'>Organizations</Link>
            </Breadcrumb.Item>
            <Breadcrumb.Item>
              <Link to={`/organizations/${orgId}`}>{organization?.name}</Link>
            </Breadcrumb.Item>
            <Breadcrumb.Item>
              <Link to={`/organizations/${orgId}?tab=management&subtab=workspaces`}>
                Workspaces
              </Link>
            </Breadcrumb.Item>
            <Breadcrumb.Item>{workspace.name}</Breadcrumb.Item>
          </Breadcrumb>

          <div className='mb-4'>
            <Typography.Title level={3} className='m-0'>
              {workspace.name}
            </Typography.Title>
            <Typography.Text type='secondary'>
              {workspace.description || 'No description available'}
            </Typography.Text>
            <div className='text-gray-500 text-sm mt-4'>
              <FontAwesomeIcon icon={faUsers} className='mr-1' />
              {workspace.teamMemberCount} Teams | {workspace.userMemberCount} Members
            </div>
          </div>

          <Tabs
            className='mt-4'
            activeKey={activeTab}
            onChange={handleTabChange}
            items={Object.entries(workspaceTabs).map(([key, { label, icon }]) => ({
              key,
              label: (
                <span>
                  {icon} {label}
                </span>
              )
            }))}
          />

          {workspaceTabs[activeTab].view}
        </>
      )}
    </div>
  );
};
