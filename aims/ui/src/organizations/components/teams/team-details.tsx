import { Tabs, Breadcrumb, Typography } from 'antd';
import { Link } from 'react-router-dom';

import { useRequiredParams } from '@/hook';
import { useOrganization } from '@/organizations/context/organization-context';

import { TeamMembersList } from './team-members-list';

export const TeamDetails = () => {
  const { teamName, orgId } = useRequiredParams(['orgId', 'teamName']);
  const { organization } = useOrganization();
  return (
    <div className='w-full'>
      <Breadcrumb className='mb-4 text-gray-500'>
        <Breadcrumb.Item>
          <Link to='/organizations'>Organizations</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          <Link to={`/organizations/${orgId}`}>{organization?.name}</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          <Link to={`/organizations/${orgId}?tab=management&subtab=teams`}>Teams</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>{teamName}</Breadcrumb.Item>
      </Breadcrumb>
      <div className='mb-4'>
        <Typography.Title level={3} className='m-0'>
          {teamName}
        </Typography.Title>
      </div>

      <Tabs
        defaultActiveKey='members'
        items={[
          {
            label: 'Members',
            key: 'members',
            children: <TeamMembersList teamName={teamName} id={orgId} isActiveTab={true} />
          }
        ]}
      />
    </div>
  );
};
