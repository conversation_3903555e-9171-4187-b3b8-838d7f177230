import { Table, Typography } from 'antd';
import { useNavigate } from 'react-router-dom';

import { usePagination } from '@/hook';
import { useListTeams } from '@/hook/api';
import { BasicOrganization } from '@/lib/apiclient/aims/v1/aims_pb';
import { Team } from '@/lib/apiclient/organization/v1/organization_pb';

type TeamsProps = {
  organization: BasicOrganization;
};

export const Teams = ({ organization: { id } }: TeamsProps) => {
  const pagination = usePagination({ defaultLimit: 10 });

  const { data, isLoading } = useListTeams(id, pagination);
  const navigate = useNavigate();

  return (
    <Table<Team>
      dataSource={data?.teams}
      rowKey={(r) => r.name}
      loading={isLoading}
      pagination={{
        total: Number(data?.count),
        onChange: pagination?.setPage,
        pageSize: pagination?.pageSize,
        current: pagination?.currentPage,
        onShowSizeChange: (c, size) => pagination?.onPageSizeChange(size),
        hideOnSinglePage: true
      }}
      onRow={(record) => ({
        onClick: () => navigate(`/organizations/${id}/teams/${record.name}`),
        style: { cursor: 'pointer' }
      })}
    >
      <Table.Column<Team>
        title='Name'
        dataIndex='name'
        width={300}
        render={(name, record) => (
          <Typography.Link onClick={() => navigate(`/organizations/${id}/teams/${record.name}`)}>
            {name}
          </Typography.Link>
        )}
      />
      <Table.Column<Team>
        title='Description'
        dataIndex='description'
        render={(data) => data || 'NA'}
      />
      <Table.Column<Team> title='Members' dataIndex='memberCount' render={Number} width={100} />
    </Table>
  );
};
