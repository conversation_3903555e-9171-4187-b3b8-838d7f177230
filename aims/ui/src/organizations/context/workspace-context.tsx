import { notification } from 'antd';
import React, { createContext, useContext, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';

import { useRequiredParams } from '@/hook';
import { useGetWorkspace } from '@/hook/api';
import { Workspace } from '@/lib/apiclient/organization/v1/organization_pb';

interface WorkspaceContextType {
  workspace: Workspace | null;
  isLoading: boolean;
}

const WorkspaceContext = createContext<WorkspaceContextType | null>(null);

export const useWorkspaceContext = () => {
  const ctx = useContext(WorkspaceContext);
  if (!ctx) throw new Error('useWorkspaceContext must be used within a WorkspaceContextProvider');
  return ctx;
};

export const WorkspaceContextProvider = ({ children }: React.PropsWithChildren) => {
  const navigate = useNavigate();
  const { orgId, workspaceId } = useRequiredParams(['orgId', 'workspaceId']);

  const { data, error, isLoading } = useGetWorkspace(orgId, workspaceId);

  useEffect(() => {
    if (error) {
      notification.error(error);
      navigate('/organizations');
    }
  }, [error, navigate]);

  const contextValue = useMemo(
    () => ({
      workspace: data?.workspace || null,
      isLoading
    }),
    [data?.workspace, isLoading]
  );

  return <WorkspaceContext.Provider value={contextValue}>{children}</WorkspaceContext.Provider>;
};
