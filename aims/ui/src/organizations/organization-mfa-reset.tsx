import { PlainMessage } from '@bufbuild/protobuf';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Input, message, Modal, Table } from 'antd';
import { useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { useModal } from '@/hook';
import { useResetMFA } from '@/hook/api';
import { Audit } from '@/lib/apiclient/aims/v1/aims_pb';
import { AuditForm } from '@/lib/components/shared/audit-form';
import { zodAuditSchema } from '@/utils';

import { useOrganization } from './context/organization-context';

export const OrganizationMfaReset = () => {
  const [search, setSearch] = useState('');

  const { organization } = useOrganization();

  const mfaReset = useModal();

  const onOpenResetMFAModal = (email: string) =>
    mfaReset.show((p) => (
      <ResetModal open={p.visible} onClose={p.hide} email={email} orgId={organization?.id} />
    ));

  const filteredData = useMemo(
    () =>
      search
        ? organization.emails?.filter((value) => value?.includes(search))
        : organization.emails,
    [search, organization.emails]
  );

  return (
    <>
      <Input
        placeholder='Search'
        className='w-1/6 mb-2'
        value={search}
        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearch(e.target.value)}
      />
      <Table
        dataSource={filteredData || []}
        className='w-1/2'
        columns={[
          {
            title: 'User Email',
            render: (_, email) => email
          },
          {
            render: (_, email) => (
              <Button onClick={() => onOpenResetMFAModal(email)}>Reset MFA</Button>
            ),
            width: '30%'
          }
        ]}
        pagination={{
          pageSize: 10
        }}
      />
    </>
  );
};

const schema = z.object({ audit: zodAuditSchema });

const ResetModal = (props: { email: string; open: boolean; onClose(): void; orgId: string }) => {
  const resetMFAMutation = useResetMFA();

  const auditForm = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      audit: {
        reason: 'From slack channel request',
        actor: ''
      }
    }
  });

  const onSubmit = auditForm.handleSubmit(({ audit }) =>
    resetMFAMutation.mutate(
      {
        organizationId: props.orgId,
        audit: audit as PlainMessage<Audit>,
        email: props.email
      },
      {
        onSuccess: () => {
          message.success(`MFA reset successfully for user ${props.email}`);
          props.onClose();
        }
      }
    )
  );

  return (
    <Modal
      open={props.open}
      onCancel={props.onClose}
      title='Confirmation'
      okText='Reset MFA'
      onOk={onSubmit}
    >
      Reset MFA for user {props.email}
      <AuditForm control={auditForm.control} />
    </Modal>
  );
};
