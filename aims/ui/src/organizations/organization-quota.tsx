import { PlainMessage } from '@bufbuild/protobuf';
import { faCheck, faForward } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { zodResolver } from '@hookform/resolvers/zod';
import { notification, Modal, Button, List, Tag, InputNumber } from 'antd';
import { omit } from 'lodash';
import { FormProvider, useForm, useFormContext } from 'react-hook-form';
import { z } from 'zod';

import { queryClient } from '@/app';
import { queryKeys } from '@/constants/query-keys';
import { useDisclosure } from '@/hook';
import { useUpdateQuotas } from '@/hook/api';
import { Audit, BasicOrganization, UpdateQuotasResponse } from '@/lib/apiclient/aims/v1/aims_pb';
import { OrganizationQuota as TOrganizationQuota } from '@/lib/apiclient/types/features/v1/features_pb';
import { AuditForm } from '@/lib/components/shared/audit-form';
import { FieldContainer } from '@/lib/components/shared/forms';
import { zodAuditSchema } from '@/utils';

import { useOrganization } from './context/organization-context';
import { Changes, isUnlimitedQuota, quotaChanges } from './utils';

const schema = z.object({
  quota: z.record(z.string(), z.union([z.number().min(0).optional(), z.string().optional()])),
  audit: zodAuditSchema
});

export const OrganizationQuota = () => {
  const { organization } = useOrganization();

  const currentQuota = omit(organization?.quota || ({} as TOrganizationQuota), [
    'auditRecordArchiveMonths',
    'auditRecordMonths',
    'maxKargoProjects'
  ]);

  const {
    maxApplications = BigInt(0),
    maxClusters = BigInt(0),
    maxInstances = BigInt(0),
    maxKargoAgents = BigInt(0),
    maxKargoInstances = BigInt(0),
    maxKargoStages = BigInt(0),
    maxOrgMembers = BigInt(0),
    maxWorkspaces = BigInt(0),
    maxAiCostPerMonth = BigInt(0)
  } = currentQuota;

  const methods = useForm({
    defaultValues: {
      quota: {
        maxApplications: Number(maxApplications),
        maxClusters: Number(maxClusters),
        maxInstances: Number(maxInstances),
        maxKargoAgents: Number(maxKargoAgents),
        maxKargoInstances: Number(maxKargoInstances),
        maxKargoStages: Number(maxKargoStages),
        maxOrgMembers: Number(maxOrgMembers),
        maxWorkspaces: Number(maxWorkspaces),
        maxAiCostPerMonth: Number(maxAiCostPerMonth)
      },
      audit: {
        actor: '',
        reason: ''
      }
    },
    resolver: zodResolver(schema)
  });

  const quotas = methods.watch('quota');

  const changes = quotaChanges(currentQuota, quotas);

  return (
    <FormProvider {...methods}>
      <h2 className='text-lg font-semibold mb-2'>Quotas</h2>

      {/* SUBMIT */}
      <Submit changes={changes} />

      <div className='mt-3 flex'>
        <FieldContainer control={methods.control} name='quota.maxApplications' label='Applications'>
          {({ field }) => <InputNumber min={0} {...field} />}
        </FieldContainer>

        <FieldContainer control={methods.control} name='quota.maxClusters' label='Clusters'>
          {({ field }) => (
            <div>
              <InputNumber min={0} {...field} />
              <Tag
                className={'mt-1 cursor-pointer'}
                onClick={() => field.onChange(0)}
                icon={
                  isUnlimitedQuota('maxClusters', field.value as number) && (
                    <FontAwesomeIcon icon={faCheck} className='mr-2' />
                  )
                }
              >
                Unlimited
              </Tag>
            </div>
          )}
        </FieldContainer>

        <FieldContainer control={methods.control} name='quota.maxInstances' label='Instances'>
          {({ field }) => <InputNumber min={0} {...field} />}
        </FieldContainer>

        <FieldContainer control={methods.control} name='quota.maxKargoAgents' label='Kargo Agents'>
          {({ field }) => (
            <div>
              <InputNumber min={0} {...field} />
              <Tag
                className={'mt-1 cursor-pointer'}
                onClick={() => field.onChange(0)}
                icon={
                  isUnlimitedQuota('maxKargoAgents', field.value as number) && (
                    <FontAwesomeIcon icon={faCheck} className='mr-2' />
                  )
                }
              >
                Unlimited
              </Tag>
            </div>
          )}
        </FieldContainer>

        <FieldContainer
          control={methods.control}
          name='quota.maxKargoInstances'
          label='Kargo Instances'
        >
          {({ field }) => (
            <div>
              <InputNumber min={0} {...field} />
              <Tag
                className={'mt-1 cursor-pointer'}
                onClick={() => field.onChange(0)}
                icon={
                  isUnlimitedQuota('maxKargoInstances', field.value as number) && (
                    <FontAwesomeIcon icon={faCheck} className='mr-2' />
                  )
                }
              >
                Unlimited
              </Tag>
            </div>
          )}
        </FieldContainer>

        <FieldContainer control={methods.control} name='quota.maxKargoStages' label='Kargo Stages'>
          {({ field }) => <InputNumber min={0} {...field} />}
        </FieldContainer>

        <FieldContainer control={methods.control} name='quota.maxOrgMembers' label='Org Members'>
          {({ field }) => (
            <div className='mt-1'>
              <InputNumber min={0} {...field} />
              <Tag
                className={'mt-1 cursor-pointer'}
                onClick={() => field.onChange(0)}
                icon={
                  isUnlimitedQuota('maxOrgMembers', field.value as number) && (
                    <FontAwesomeIcon icon={faCheck} className='mr-2' />
                  )
                }
              >
                Unlimited
              </Tag>
            </div>
          )}
        </FieldContainer>

        <FieldContainer control={methods.control} name='quota.maxWorkspaces' label='Max Workspaces'>
          {({ field }) => <InputNumber min={0} {...field} />}
        </FieldContainer>
        <FieldContainer
          control={methods.control}
          name='quota.maxAiCostPerMonth'
          label='Max AI Cost Per Month (USD)'
        >
          {({ field }) => <InputNumber min={0} {...field} />}
        </FieldContainer>
      </div>
    </FormProvider>
  );
};

const Submit = (props: { changes: Changes; className?: string }) => {
  const { organization } = useOrganization();

  const { isOpen, onClose, onOpen } = useDisclosure();

  const { handleSubmit, control, reset } = useFormContext();

  const { mutate: update, isPending } = useUpdateQuotas();

  const fetchingData = !!queryClient.isFetching({
    queryKey: queryKeys.organizations.item(organization.id).queryKey
  });

  const onSubmit = handleSubmit((data) => {
    update(
      {
        id: organization.id,
        quota: data.quota as Partial<PlainMessage<TOrganizationQuota>>,
        audit: data.audit as PlainMessage<Audit>
      },
      {
        onSuccess: ({ quota }: PlainMessage<UpdateQuotasResponse>) => {
          queryClient.setQueryData(
            queryKeys.organizations.item(organization.id).queryKey,
            (data: BasicOrganization) => {
              return {
                ...data,
                quota
              };
            }
          );
          onClose();
          notification.success({
            message: 'Successfully updated feature gates.',
            placement: 'bottomRight'
          });
        }
      }
    );
  });

  return (
    <>
      <Button
        icon={<FontAwesomeIcon icon={faForward} />}
        className={'mb-5 ' + props.className}
        type='primary'
        disabled={props.changes.length == 0 || fetchingData}
        onClick={onOpen}
      >
        Review Update
      </Button>

      <Button disabled={props?.changes?.length === 0} onClick={() => reset()} className='ml-5'>
        Reset Changes
      </Button>

      <Modal
        title='Review Updates'
        okText='Update'
        confirmLoading={isPending}
        onOk={onSubmit}
        open={isOpen}
        onCancel={onClose}
        classNames={{
          body: 'my-5'
        }}
      >
        <List
          header='Changes'
          bordered
          dataSource={props.changes}
          renderItem={(item) => (
            <List.Item>
              {item.key} from {item.oldValue} to {item.newValue}
            </List.Item>
          )}
          className='mb-5'
        />
        <AuditForm control={control} />
      </Modal>
    </>
  );
};
