import { PlainMessage } from '@bufbuild/protobuf';
import { faDownload } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import JsonView from '@uiw/react-json-view';
import { Table } from 'antd';
import { Button } from 'antd';
import { omit } from 'lodash';
import { useContext } from 'react';
import { Link, useParams } from 'react-router-dom';

import { useModal } from '@/hook';
import { useGetKargoInstanceById, useListKargoInstanceClusters } from '@/hook/api';
import { KargoAgent } from '@/lib/apiclient/kargo/v1/kargo_pb';
import { DownloadManifestModal } from '@/lib/components/download-manifest-modal';
import { Header } from '@/lib/components/header';
import { InfoItem } from '@/lib/components/info-item';
import { Loading } from '@/lib/components/loading';
import { HealthState } from '@/lib/components/shared/health-state';
import { UnsupportedVersionBanner } from '@/lib/components/unsupported-version-banner';

import { KargoInstanceContext } from './context/instance';

export const KargoInstanceDetails = () => {
  const { instanceId } = useParams();

  const { data, isFetching, error } = useGetKargoInstanceById(instanceId);

  if (isFetching) {
    return <Loading />;
  }

  if (error) {
    return JSON.stringify(error);
  }

  return (
    <KargoInstanceContext.Provider value={{ instance: data }}>
      <Details />
    </KargoInstanceContext.Provider>
  );
};

const Details = () => {
  const data = useContext(KargoInstanceContext);

  const kargoInstance = data?.instance?.instance;

  return (
    <div className='w-full'>
      <UnsupportedVersionBanner
        isUnsupported={kargoInstance.instance.unsupportedVersion}
        version={kargoInstance.instance.version}
      />
      <div className='flex items-center gap-5 mb-4'>
        <img src='/images/kargo.png' alt='Kargo' style={{ width: '48px' }} />
        <div>
          <Header>{kargoInstance?.instance?.name}</Header>
        </div>
        <div className='ml-auto'>
          <span className='ml-auto'>{kargoInstance?.instance?.id}</span>
          {kargoInstance?.instance?.fqdn && (
            <a
              href={`https://${kargoInstance?.instance?.fqdn}`}
              className='text-blue-500 block mt-2'
              target='_blank'
            >
              {kargoInstance?.instance?.fqdn}
            </a>
          )}
        </div>
      </div>
      <div className='mb-6'>
        <div className='flex items-start mb-6'>
          <InfoItem label='HEALTH'>
            {kargoInstance?.instance?.healthStatus && (
              <HealthState
                // @ts-expect-error had split types (for AIMS and UI) but from same proto
                health={kargoInstance?.instance?.healthStatus}
                deletionTimestamp={kargoInstance?.instance?.deleteTime}
                className='font-semibold'
              />
            )}
          </InfoItem>
          <InfoItem label='RECONCILIATION'>
            {kargoInstance?.instance?.reconciliationStatus && (
              <>
                <HealthState
                  // @ts-expect-error had split types (for AIMS and UI) but from same proto
                  health={kargoInstance?.instance?.reconciliationStatus}
                  className='font-semibold'
                />
                <span className='mt-2 block text-sm'>
                  {kargoInstance?.instance?.reconciliationStatus?.message}
                </span>
              </>
            )}
          </InfoItem>

          <InfoItem label='WORKSPACE'>
            {kargoInstance?.workspace ? (
              <Link
                to={`/organizations/${kargoInstance?.organization?.id}/workspaces/${kargoInstance?.workspace?.id}`}
                className='text-blue-500 hover:underline font-semibold'
              >
                {kargoInstance?.workspace?.name}
              </Link>
            ) : (
              'N/A'
            )}
          </InfoItem>
          <InfoItem label='PROJECT | STAGE'>
            {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
            {(kargoInstance?.statusInfo as any)?.kargoStats?.stageCount ?? 0} | {''}
            {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
            {(kargoInstance?.statusInfo as any)?.kargoStats?.projectCount ?? 0}
          </InfoItem>
        </div>

        <Clusters />
      </div>
    </div>
  );
};

const Clusters = () => {
  const data = useContext(KargoInstanceContext);
  const { show } = useModal();

  const kargoInstance = data?.instance?.instance;

  const { data: clustersData, isFetching } = useListKargoInstanceClusters(
    kargoInstance?.instance?.id
  );

  if (isFetching) {
    return <Loading />;
  }

  return (
    <Table
      dataSource={clustersData?.agents || []}
      columns={[
        {
          key: 'id',
          title: 'Id/Name',
          render: (value: TKargoAgent) => (
            <>
              <b>{value?.name}</b>
              <div className='text-gray-400'>{value?.id}</div>
            </>
          )
        },
        {
          title: 'Status',
          width: 120,
          render: (_, agent) => (
            <>
              <HealthState
                deletionTimestamp={agent.deleteTime}
                // @ts-expect-error split types but same proto
                health={agent.healthStatus}
                className='text-xs'
              />
            </>
          )
        },
        {
          title: 'Agent Version',
          dataIndex: ['agentState', 'version'],
          width: 130,
          render: (val) => val || '-'
        },
        {
          title: 'Kargo Version',
          dataIndex: ['agentState', 'kargoVersion'],
          width: 150,
          render: (val) => val || '-'
        },
        { title: 'Namespace', dataIndex: ['data', 'namespace'], width: 120 },
        {
          title: 'Remote Argo CD',
          width: 150,
          render: (_, agent) => {
            if (!agent?.data?.remoteArgocd) {
              return '-';
            }

            return (
              <a
                href={`/instances/settings/${agent?.data?.remoteArgocd}`}
                className='text-blue-400'
                target='_blank'
              >
                {agent?.data?.remoteArgocd}
              </a>
            );
          }
        },
        {
          title: 'Data',
          render: (_, agent) => (
            <JsonView collapsed value={omit(agent?.data, ['namespace', 'remoteArgocd'])} />
          )
        },
        {
          title: 'Manifest',
          width: 120,
          render: (_, agent: TKargoAgent) => (
            <Button
              type='link'
              size='small'
              icon={<FontAwesomeIcon icon={faDownload} />}
              onClick={() =>
                show((modalProps) => (
                  <DownloadManifestModal
                    {...modalProps}
                    instanceId={kargoInstance?.instance?.id}
                    clusterId={agent.id}
                    clusterName={agent.name}
                    type='kargo'
                  />
                ))
              }
            >
              Download
            </Button>
          )
        }
      ]}
    />
  );
};

type TKargoAgent = PlainMessage<KargoAgent>;
