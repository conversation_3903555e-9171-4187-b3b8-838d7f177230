import { PlainMessage } from '@bufbuild/protobuf';
import { createQueryKeyStore } from '@lukemorales/query-key-factory';

import { InternalAuditLogsFilters } from '@/internal-audit-logs/use-audit-logs-filters';
import {
  ClusterFilter,
  ListArgoInstancesRequest,
  ListKargoInstancesRequest
} from '@/lib/apiclient/aims/v1/aims_pb';
import { OrganizationFilters } from '@/organizations/filters/use-organization-filter';
import { PaginationPaylod, RawAuditFilters } from '@/types';

export const queryKeys = createQueryKeyStore({
  organizations: {
    list: (filters: OrganizationFilters) => ({
      queryKey: ['organizations', 'list', filters]
    }),
    item: (id: string) => ({
      queryKey: ['organizations', 'item', id]
    }),
    members: (orgIds: string[]) => ({
      queryKey: ['organizations', 'members', orgIds]
    }),
    featureGates: (id: string) => ({
      queryKey: ['organizations', 'featureGates', id]
    }),
    domains: (id: string) => ({
      queryKey: ['organizations', 'domains', id]
    }),
    internalConfig: () => ({
      queryKey: ['organizations', 'internalConfig']
    }),
    auditLogs: (orgId: string, filters: RawAuditFilters) => ({
      queryKey: ['organizations', 'auditLogs', orgId, filters]
    }),
    kubeVisionUsage: (orgId: string, startTime: string, endTime: string) => ({
      queryKey: ['organizations', 'kubeVisionUsage', orgId, startTime, endTime]
    }),
    aiConversations: (orgId: string) => ({
      queryKey: ['organizations', 'aiConversations', orgId]
    }),
    aiConversationDetail: (orgId: string, id: string) => ({
      queryKey: ['organizations', 'aiConversationDetail', orgId, id]
    }),
    availablePlans: () => ({
      queryKey: ['organizations', 'availablePlans']
    }),
    teams: (organizationId: string, payload: PaginationPaylod) => ({
      queryKey: [organizationId, 'teams', payload.limit, payload.offset]
    }),
    teamMembers: (organizationId: string, teamName: string, payload: PaginationPaylod) => ({
      queryKey: [organizationId, teamName, payload.limit, payload.offset]
    }),
    customRoles: (organizationId: string, payload: PaginationPaylod) => ({
      queryKey: ['customRoles', organizationId, payload.limit, payload.offset]
    }),
    users: (organizationId: string) => ({
      queryKey: ['users', organizationId]
    }),
    unbilled: () => ({
      queryKey: ['organizations', 'unbilled']
    })
  },
  workspace: {
    list: (organizationId: string, payload: PaginationPaylod) => ({
      queryKey: [organizationId, 'listWorkspace', payload.limit, payload.offset]
    }),
    item: (organizationId: string, id: string) => ({
      queryKey: [organizationId, id]
    }),
    members: (organizationId: string, id: string) => ({
      queryKey: ['member', organizationId, id]
    }),
    customRoles: (organizationId: string, id: string, payload: PaginationPaylod) => ({
      queryKey: ['customRoles', organizationId, id, payload.limit, payload.offset]
    })
  },
  instances: {
    argoInstances: (request?: PlainMessage<ListArgoInstancesRequest>) => ({
      queryKey: ['argoInstances', request]
    }),
    argoInstance: (id: string) => ({
      queryKey: ['argoInstance', id]
    }),
    instanceClusters: (instanceId: string, filters?: ClusterFilter) => ({
      queryKey: ['instanceClusters', instanceId, filters]
    }),
    kargoInstances: (request?: PlainMessage<ListKargoInstancesRequest>) => ({
      queryKey: ['kargoInstances', request]
    }),
    kargoInstance: (id: string) => ({
      queryKey: ['kargoInstance', id]
    }),
    kargoInstanceClusters: (id: string) => ({
      queryKey: ['kargoInstanceClusters', id]
    }),
    paidInstances: () => ({
      queryKey: ['instances', 'paid']
    }),
    unpaidInstances: () => ({
      queryKey: ['instances', 'unpaid']
    })
  },
  internalAuditLogs: {
    list: (filters: InternalAuditLogsFilters) => ({
      queryKey: ['internal-audit-logs', 'list', filters]
    })
  }
});
