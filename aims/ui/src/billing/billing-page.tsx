import { faMoneyCheckDollar } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Skeleton } from 'antd';
import { useSearchParams } from 'react-router-dom';

import { useListAvailablePlans } from '@/hook/api';
import { Header } from '@/lib/components/header';

import { ListPlans } from './list-plans';
import { PlanUpdater } from './plan-updater';

export const BillingPage = () => {
  const [search] = useSearchParams();

  const listAvailablePlansQuery = useListAvailablePlans();

  if (listAvailablePlansQuery.isFetching) {
    return <Skeleton />;
  }

  return (
    <div className='w-full'>
      <Header description='describes available plans, their features and quotas. you would also be able to update the plan to enterprise'>
        <FontAwesomeIcon className='mr-4' icon={faMoneyCheckDollar} />
        Billing
      </Header>

      <div className='mt-5'>
        <ListPlans plans={listAvailablePlansQuery.data} />
      </div>

      <div className='mt-5'>
        <PlanUpdater
          plans={listAvailablePlansQuery.data}
          defaultSelectedOrgId={search.get('orgId')}
        />
      </div>
    </div>
  );
};
