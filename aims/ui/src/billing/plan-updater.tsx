import { PlainMessage } from '@bufbuild/protobuf';
import { QueryClientProvider } from '@tanstack/react-query';
import { Alert, Button, Modal, Select, Skeleton } from 'antd';
import { useState } from 'react';
import { Link } from 'react-router-dom';
import { useDebounce } from 'use-debounce';

import { queryClient } from '@/app';
import { useGetFeatureGates, useGetOrganization, useListAllOrganizations } from '@/hook/api';
import { BasicOrganization, Plan, GetFeatureGatesResponse } from '@/lib/apiclient/aims/v1/aims_pb';
import { firstCharacterUppercase } from '@/utils';

import { OrganizationBillingState } from './organization-billing-state';
import { PlanUpdaterForm } from './plan-updater-form';

export const PlanUpdater = (props: { plans: Plan[]; defaultSelectedOrgId?: string }) => {
  const [selectedOrganization, setSelectedOrganization] = useState(
    props.defaultSelectedOrgId || ''
  );

  const [organizationSearch, setOrganizationSearch] = useState('');

  const [debounceOrganizationSearch] = useDebounce(organizationSearch, 500);
  const orgSearchQuery = useListAllOrganizations({ limit: 10, fuzz: debounceOrganizationSearch });

  const orgDetailsQuery = useGetOrganization(selectedOrganization);

  const orgFeatureGatesQuery = useGetFeatureGates(selectedOrganization);

  return (
    <>
      <h1>Ensure Feature and Quotas for Enterprise Organization</h1>

      <div className='mt-5'>
        <label className='mr-5'>Select Organization: </label>
        <Select
          className='w-4/12'
          value={selectedOrganization}
          onChange={setSelectedOrganization}
          showSearch
          searchValue={organizationSearch}
          onSearch={setOrganizationSearch}
          loading={orgSearchQuery.isFetching}
          options={orgSearchQuery.data?.organizations?.map((org) => ({
            label: org.name,
            value: org.id
          }))}
          filterOption={false}
        />
      </div>

      <div className='mt-5'>
        {(orgDetailsQuery.isFetching || orgFeatureGatesQuery.isFetching) && <Skeleton />}
        {!orgDetailsQuery.isFetching &&
          !orgFeatureGatesQuery.isFetching &&
          orgDetailsQuery.isSuccess &&
          orgFeatureGatesQuery.isSuccess && (
            <>
              <OrganizationBillingState
                organization={orgDetailsQuery.data}
                featureGates={orgFeatureGatesQuery.data}
              />

              <div className='mt-5'>
                <PlanUpdaterInput
                  plans={props.plans}
                  organization={orgDetailsQuery.data}
                  featureGates={orgFeatureGatesQuery.data}
                />
              </div>
            </>
          )}
      </div>
    </>
  );
};

const PlanUpdaterInput = (props: {
  plans: Plan[];
  organization: BasicOrganization;
  featureGates: PlainMessage<GetFeatureGatesResponse>;
}) => {
  const currentPlan = props.organization.plan;
  const hasActiveSubscription = props.organization.billingDetails?.hasActiveSubscription;

  const [selectedPlan, setSelectedPlan] = useState(currentPlan);

  const currentPlanDetails = props.plans?.find((p) => p.name === currentPlan);

  const currentPlanProductId = currentPlanDetails?.productId;

  const isSelfServiceStripeUser =
    currentPlanProductId !== '_' && currentPlanProductId !== '' && hasActiveSubscription;

  const selectedPlanDetails = props.plans?.find((p) => p.name === selectedPlan);

  // new features
  // that were not available in previous plan but is in new plan

  // these features are in underscore format (or whatever that is called)
  // we want to convert to snake case
  const newPlanFeatures = Object.entries(selectedPlanDetails?.features)
    ?.filter(([, value]) => value === true)
    ?.map(([key, value]) => [
      key
        .split('_')
        .map((k, i) => (i === 0 ? k : firstCharacterUppercase(k)))
        .join(''),
      value
    ])
    ?.reduce(
      (prev, curr) => {
        prev[curr[0] as string] = curr[1] as boolean;
        return prev;
      },
      {} as Record<string, boolean>
    );
  const currentAvailableFeatures = props.featureGates.featureGates;

  const featuresThatWillSurelyBeAvailableWhenThisPlanIsUsed = [];

  for (const newAvailableFeature of Object.keys(newPlanFeatures)) {
    if (!currentAvailableFeatures[newAvailableFeature as keyof typeof currentAvailableFeatures]) {
      featuresThatWillSurelyBeAvailableWhenThisPlanIsUsed.push(newAvailableFeature);
    }
  }

  const featuresThatMaybeUnavailableWhenThisPlanIsUsed = [];

  for (const [currentAvailableFeature] of Object.entries(currentAvailableFeatures).filter(
    ([, value]) => value === true
  )) {
    if (!newPlanFeatures[currentAvailableFeature]) {
      featuresThatMaybeUnavailableWhenThisPlanIsUsed.push(currentAvailableFeature);
    }
  }

  return (
    <>
      <label className='mr-5'>Apply Feature & Quota of Plan: </label>

      <Select
        className='w-4/12 mr-5'
        value={selectedPlan}
        onChange={setSelectedPlan}
        options={props.plans
          .filter((p) => p.productId === '_')
          .map((p) => ({ label: p.name, value: p.name }))}
      />

      <Button
        type='primary'
        onClick={() =>
          Modal.confirm({
            title: 'Update Plan',
            content: (
              <QueryClientProvider client={queryClient}>
                <PlanUpdaterForm newPlan={selectedPlan} orgId={props.organization.id} />
              </QueryClientProvider>
            ),
            icon: null,
            okButtonProps: { hidden: true }
          })
        }
        disabled={isSelfServiceStripeUser}
      >
        Review & Update Plan
      </Button>

      <span className='ml-5'>Organization id: {props.organization.id}</span>

      {selectedPlan !== currentPlan &&
        (featuresThatWillSurelyBeAvailableWhenThisPlanIsUsed.length > 0 ||
          featuresThatMaybeUnavailableWhenThisPlanIsUsed.length > 0) && (
          <>
            <br />

            <Alert
              className='mt-5'
              type='warning'
              message={
                <>
                  <b>After updating the plan</b>

                  {featuresThatWillSurelyBeAvailableWhenThisPlanIsUsed.length > 0 && (
                    <>
                      <p>
                        Features that will surely be available which are not available as of now:{' '}
                        {featuresThatWillSurelyBeAvailableWhenThisPlanIsUsed.map((feature) => (
                          <i key={feature}>{feature}, </i>
                        ))}
                      </p>
                    </>
                  )}

                  {featuresThatMaybeUnavailableWhenThisPlanIsUsed.length > 0 && (
                    <>
                      <p>
                        Features that <b>may not be</b> available which are available as of now, you
                        can enable them from organization details page later on:{' '}
                        {featuresThatMaybeUnavailableWhenThisPlanIsUsed.map((feature) => (
                          <i key={feature}>{feature}, </i>
                        ))}
                      </p>
                    </>
                  )}
                </>
              }
            />
          </>
        )}

      {isSelfServiceStripeUser && (
        <>
          <br />

          <Alert
            className='mt-5'
            type='error'
            message='This customer is observed to subscribe to existing plan. To convert to enterprise, you need to visit stripe dashboard of this customer, figure out what to do with existing subscription (ie. cancel with refund), add new subscription with placeholder of any plan, set metadata "manual" to true'
          />
        </>
      )}

      {!props.organization.billingDetails?.customerId && (
        <>
          <br />

          <Alert
            className='mt-5'
            type='warning'
            message={
              <>
                This organization does not seem to exist in stripe. You might want to{' '}
                <Link className='text-blue-500' to='/onboarding'>
                  Onboard
                </Link>{' '}
                first if its supposed to be enterprise customer.
              </>
            }
          />
        </>
      )}

      <Alert
        className='mt-5'
        type='info'
        message={
          <>
            Recommendation: After updating plan you should visit{' '}
            <Link
              className='text-blue-500'
              to={`/organizations/${props.organization.id}?tab=usage`}
              target='_blank'
            >
              organization details
            </Link>{' '}
            and verify that quotas and features are what is expected. If there was previously
            modified quotas or features, you might want to double check since updating plan does not
            override those quotas or feature gates.
          </>
        }
      />

      <br />
      <br />
    </>
  );
};
