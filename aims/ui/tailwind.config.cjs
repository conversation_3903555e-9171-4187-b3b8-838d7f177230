const colors = require('tailwindcss/colors');

// https://github.com/tailwindlabs/tailwindcss/issues/4690#issuecomment-1046087220
delete colors['lightBlue'];
delete colors['warmGray'];
delete colors['trueGray'];
delete colors['coolGray'];
delete colors['blueGray'];

const config = {
  mode: 'jit',
  content: ['./src/**/*.{html,js,ts,tsx,jsx}'],

  theme: {
    extend: {
      colors: {
        ...colors,
        akuity: {
          50: '#F3F8FF',
          100: '#DEE9F9',
          200: '#BBCEEA',
          300: '#8FA0B9',
          400: '#74849C',
          500: '#516178',
          600: '#3F4C60',
          700: '#344154',
          800: '#253142',
          900: '#0F1724'
        }
      }
    }
  },

  corePlugins: {
    // https://github.com/ant-design/ant-design/issues/38794#issuecomment-1321806539
    preflight: false
  },

  plugins: []
};

module.exports = config;
