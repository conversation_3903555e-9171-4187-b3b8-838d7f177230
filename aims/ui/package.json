{"name": "aims", "version": "0.0.1", "scripts": {"dev": "vite", "build": "tsc --noEmit && vite build", "preview": "vite preview", "lint": "eslint . --max-warnings=0", "lint:fix": "eslint . --fix", "typecheck": "tsc --noEmit"}, "type": "module", "dependencies": {"@ant-design/v5-patch-for-react-19": "1.0.3", "@buf/googleapis_googleapis.bufbuild_es": "1.4.0-20241115201650-c0913f24652a.3", "@bufbuild/protobuf": "1.10.1", "@fortawesome/fontawesome-svg-core": "6.7.2", "@fortawesome/free-brands-svg-icons": "6.7.2", "@fortawesome/free-regular-svg-icons": "~6.7.2", "@fortawesome/free-solid-svg-icons": "6.7.2", "@fortawesome/react-fontawesome": "0.2.2", "@hookform/resolvers": "5.0.1", "@lukemorales/query-key-factory": "1.3.4", "@monaco-editor/react": "4.7.0", "@tanstack/react-query": "5.79.0", "@types/node": "22.15.27", "@types/react": "19.1.6", "@types/react-dom": "19.1.5", "@typescript-eslint/eslint-plugin": "8.33.0", "@typescript-eslint/parser": "8.33.0", "@uiw/react-json-view": "2.0.0-alpha.32", "@vitejs/plugin-react": "4.5.0", "antd": "5.25.3", "autoprefixer": "10.4.21", "chart.js": "4.4.9", "classnames": "2.5.1", "cssnano": "7.0.7", "less": "4.3.0", "lodash": "4.17.21", "marked": "15.0.12", "moment": "2.30.1", "postcss": "8.5.4", "prettier": "3.5.3", "rc-picker": "4.11.3", "react": "19.1.0", "react-chartjs-2": "5.3.0", "react-diff-viewer-continued": "3.4.0", "react-dom": "19.1.0", "react-hook-form": "7.56.4", "react-quill-new": "3.4.6", "react-router-dom": "7.6.1", "tailwindcss": "3.4.14", "typescript": "5.8.3", "use-debounce": "10.0.4", "vite": "6.3.5", "vite-tsconfig-paths": "5.1.4", "yaml": "2.8.0", "zod": "3.25.41"}, "packageManager": "pnpm@10.11.0", "devDependencies": {"@eslint/compat": "1.2.9", "@eslint/js": "9.27.0", "@types/lodash": "4.17.17", "eslint": "9.27.0", "eslint-config-prettier": "10.1.5", "eslint-plugin-import": "2.31.0", "eslint-plugin-prettier": "5.4.1", "eslint-plugin-react": "7.37.5", "globals": "16.2.0", "typescript-eslint": "8.33.0"}}