// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        (unknown)
// source: aims/v1/aims.proto

package aimsv1

import (
	v1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	v13 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
	v11 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
	v12 "github.com/akuityio/akuity-platform/pkg/api/gen/types/features/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	httpbody "google.golang.org/genproto/googleapis/api/httpbody"
	_ "google.golang.org/genproto/googleapis/api/visibility"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NotificationCategory int32

const (
	NotificationCategory_NOTIFICATION_CATEGORY_UNSPECIFIED NotificationCategory = 0
	NotificationCategory_NOTIFICATION_CATEGORY_CUSTOM      NotificationCategory = 1
	NotificationCategory_NOTIFICATION_CATEGORY_NEW_FEATURE NotificationCategory = 2
)

// Enum value maps for NotificationCategory.
var (
	NotificationCategory_name = map[int32]string{
		0: "NOTIFICATION_CATEGORY_UNSPECIFIED",
		1: "NOTIFICATION_CATEGORY_CUSTOM",
		2: "NOTIFICATION_CATEGORY_NEW_FEATURE",
	}
	NotificationCategory_value = map[string]int32{
		"NOTIFICATION_CATEGORY_UNSPECIFIED": 0,
		"NOTIFICATION_CATEGORY_CUSTOM":      1,
		"NOTIFICATION_CATEGORY_NEW_FEATURE": 2,
	}
)

func (x NotificationCategory) Enum() *NotificationCategory {
	p := new(NotificationCategory)
	*p = x
	return p
}

func (x NotificationCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NotificationCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_aims_v1_aims_proto_enumTypes[0].Descriptor()
}

func (NotificationCategory) Type() protoreflect.EnumType {
	return &file_aims_v1_aims_proto_enumTypes[0]
}

func (x NotificationCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NotificationCategory.Descriptor instead.
func (NotificationCategory) EnumDescriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{0}
}

type Sort int32

const (
	Sort_SORT_UNSPECIFIED Sort = 0
	Sort_SORT_ASCENDING   Sort = 1
	Sort_SORT_DESCENDING  Sort = 2
)

// Enum value maps for Sort.
var (
	Sort_name = map[int32]string{
		0: "SORT_UNSPECIFIED",
		1: "SORT_ASCENDING",
		2: "SORT_DESCENDING",
	}
	Sort_value = map[string]int32{
		"SORT_UNSPECIFIED": 0,
		"SORT_ASCENDING":   1,
		"SORT_DESCENDING":  2,
	}
)

func (x Sort) Enum() *Sort {
	p := new(Sort)
	*p = x
	return p
}

func (x Sort) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Sort) Descriptor() protoreflect.EnumDescriptor {
	return file_aims_v1_aims_proto_enumTypes[1].Descriptor()
}

func (Sort) Type() protoreflect.EnumType {
	return &file_aims_v1_aims_proto_enumTypes[1]
}

func (x Sort) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Sort.Descriptor instead.
func (Sort) EnumDescriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{1}
}

type WorkspaceMemberRole int32

const (
	WorkspaceMemberRole_WORKSPACE_MEMBER_ROLE_UNSPECIFIED WorkspaceMemberRole = 0
	WorkspaceMemberRole_WORKSPACE_MEMBER_ROLE_MEMBER      WorkspaceMemberRole = 1
	WorkspaceMemberRole_WORKSPACE_MEMBER_ROLE_ADMIN       WorkspaceMemberRole = 2
)

// Enum value maps for WorkspaceMemberRole.
var (
	WorkspaceMemberRole_name = map[int32]string{
		0: "WORKSPACE_MEMBER_ROLE_UNSPECIFIED",
		1: "WORKSPACE_MEMBER_ROLE_MEMBER",
		2: "WORKSPACE_MEMBER_ROLE_ADMIN",
	}
	WorkspaceMemberRole_value = map[string]int32{
		"WORKSPACE_MEMBER_ROLE_UNSPECIFIED": 0,
		"WORKSPACE_MEMBER_ROLE_MEMBER":      1,
		"WORKSPACE_MEMBER_ROLE_ADMIN":       2,
	}
)

func (x WorkspaceMemberRole) Enum() *WorkspaceMemberRole {
	p := new(WorkspaceMemberRole)
	*p = x
	return p
}

func (x WorkspaceMemberRole) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WorkspaceMemberRole) Descriptor() protoreflect.EnumDescriptor {
	return file_aims_v1_aims_proto_enumTypes[2].Descriptor()
}

func (WorkspaceMemberRole) Type() protoreflect.EnumType {
	return &file_aims_v1_aims_proto_enumTypes[2]
}

func (x WorkspaceMemberRole) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WorkspaceMemberRole.Descriptor instead.
func (WorkspaceMemberRole) EnumDescriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{2}
}

type SendNotificationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// test mode ensures that notification
	// 1. is only sent to the organization containing akuity member
	// 2. member in the audit email should be same in that organization
	TestMode *bool `protobuf:"varint,1,opt,name=test_mode,json=testMode,proto3,oneof" json:"test_mode,omitempty"`
	// currently, only used when test mode is on
	OrganizationId *string              `protobuf:"bytes,2,opt,name=organization_id,json=organizationId,proto3,oneof" json:"organization_id,omitempty"`
	Audit          *Audit               `protobuf:"bytes,3,opt,name=audit,proto3" json:"audit,omitempty"`
	Category       NotificationCategory `protobuf:"varint,4,opt,name=category,proto3,enum=akuity.aims.v1.NotificationCategory" json:"category,omitempty"`
	// this should match
	Metadata *structpb.Struct `protobuf:"bytes,5,opt,name=metadata,proto3" json:"metadata,omitempty"`
}

func (x *SendNotificationRequest) Reset() {
	*x = SendNotificationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendNotificationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendNotificationRequest) ProtoMessage() {}

func (x *SendNotificationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendNotificationRequest.ProtoReflect.Descriptor instead.
func (*SendNotificationRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{0}
}

func (x *SendNotificationRequest) GetTestMode() bool {
	if x != nil && x.TestMode != nil {
		return *x.TestMode
	}
	return false
}

func (x *SendNotificationRequest) GetOrganizationId() string {
	if x != nil && x.OrganizationId != nil {
		return *x.OrganizationId
	}
	return ""
}

func (x *SendNotificationRequest) GetAudit() *Audit {
	if x != nil {
		return x.Audit
	}
	return nil
}

func (x *SendNotificationRequest) GetCategory() NotificationCategory {
	if x != nil {
		return x.Category
	}
	return NotificationCategory_NOTIFICATION_CATEGORY_UNSPECIFIED
}

func (x *SendNotificationRequest) GetMetadata() *structpb.Struct {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type SendNotificationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SendNotificationResponse) Reset() {
	*x = SendNotificationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendNotificationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendNotificationResponse) ProtoMessage() {}

func (x *SendNotificationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendNotificationResponse.ProtoReflect.Descriptor instead.
func (*SendNotificationResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{1}
}

type ArgoInstanceFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Paid   *bool `protobuf:"varint,1,opt,name=paid,proto3,oneof" json:"paid,omitempty"`
	Unpaid *bool `protobuf:"varint,2,opt,name=unpaid,proto3,oneof" json:"unpaid,omitempty"`
	// search by instance name/id
	Fuzz     string  `protobuf:"bytes,3,opt,name=fuzz,proto3" json:"fuzz,omitempty"`
	TimeFrom *string `protobuf:"bytes,4,opt,name=time_from,json=timeFrom,proto3,oneof" json:"time_from,omitempty"`
	// filter by organization ID
	OrganizationId *string `protobuf:"bytes,5,opt,name=organization_id,json=organizationId,proto3,oneof" json:"organization_id,omitempty"`
}

func (x *ArgoInstanceFilter) Reset() {
	*x = ArgoInstanceFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArgoInstanceFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArgoInstanceFilter) ProtoMessage() {}

func (x *ArgoInstanceFilter) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArgoInstanceFilter.ProtoReflect.Descriptor instead.
func (*ArgoInstanceFilter) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{2}
}

func (x *ArgoInstanceFilter) GetPaid() bool {
	if x != nil && x.Paid != nil {
		return *x.Paid
	}
	return false
}

func (x *ArgoInstanceFilter) GetUnpaid() bool {
	if x != nil && x.Unpaid != nil {
		return *x.Unpaid
	}
	return false
}

func (x *ArgoInstanceFilter) GetFuzz() string {
	if x != nil {
		return x.Fuzz
	}
	return ""
}

func (x *ArgoInstanceFilter) GetTimeFrom() string {
	if x != nil && x.TimeFrom != nil {
		return *x.TimeFrom
	}
	return ""
}

func (x *ArgoInstanceFilter) GetOrganizationId() string {
	if x != nil && x.OrganizationId != nil {
		return *x.OrganizationId
	}
	return ""
}

type ListArgoInstancesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter *ArgoInstanceFilter `protobuf:"bytes,1,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
}

func (x *ListArgoInstancesRequest) Reset() {
	*x = ListArgoInstancesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListArgoInstancesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListArgoInstancesRequest) ProtoMessage() {}

func (x *ListArgoInstancesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListArgoInstancesRequest.ProtoReflect.Descriptor instead.
func (*ListArgoInstancesRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{3}
}

func (x *ListArgoInstancesRequest) GetFilter() *ArgoInstanceFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type ListArgoInstancesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Instances []*InternalInstance `protobuf:"bytes,1,rep,name=instances,proto3" json:"instances,omitempty"`
}

func (x *ListArgoInstancesResponse) Reset() {
	*x = ListArgoInstancesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListArgoInstancesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListArgoInstancesResponse) ProtoMessage() {}

func (x *ListArgoInstancesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListArgoInstancesResponse.ProtoReflect.Descriptor instead.
func (*ListArgoInstancesResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{4}
}

func (x *ListArgoInstancesResponse) GetInstances() []*InternalInstance {
	if x != nil {
		return x.Instances
	}
	return nil
}

type DeleteUnpaidInstanceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstanceId string `protobuf:"bytes,1,opt,name=instance_id,json=instanceId,proto3" json:"instance_id,omitempty"`
	Audit      *Audit `protobuf:"bytes,2,opt,name=audit,proto3" json:"audit,omitempty"`
}

func (x *DeleteUnpaidInstanceRequest) Reset() {
	*x = DeleteUnpaidInstanceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteUnpaidInstanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUnpaidInstanceRequest) ProtoMessage() {}

func (x *DeleteUnpaidInstanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUnpaidInstanceRequest.ProtoReflect.Descriptor instead.
func (*DeleteUnpaidInstanceRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteUnpaidInstanceRequest) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

func (x *DeleteUnpaidInstanceRequest) GetAudit() *Audit {
	if x != nil {
		return x.Audit
	}
	return nil
}

type DeleteUnpaidInstanceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteUnpaidInstanceResponse) Reset() {
	*x = DeleteUnpaidInstanceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteUnpaidInstanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUnpaidInstanceResponse) ProtoMessage() {}

func (x *DeleteUnpaidInstanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUnpaidInstanceResponse.ProtoReflect.Descriptor instead.
func (*DeleteUnpaidInstanceResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{6}
}

type OnboardManualCustomerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Customer *Customer `protobuf:"bytes,1,opt,name=customer,proto3" json:"customer,omitempty"`
	Audit    *Audit    `protobuf:"bytes,2,opt,name=audit,proto3" json:"audit,omitempty"`
}

func (x *OnboardManualCustomerRequest) Reset() {
	*x = OnboardManualCustomerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OnboardManualCustomerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnboardManualCustomerRequest) ProtoMessage() {}

func (x *OnboardManualCustomerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnboardManualCustomerRequest.ProtoReflect.Descriptor instead.
func (*OnboardManualCustomerRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{7}
}

func (x *OnboardManualCustomerRequest) GetCustomer() *Customer {
	if x != nil {
		return x.Customer
	}
	return nil
}

func (x *OnboardManualCustomerRequest) GetAudit() *Audit {
	if x != nil {
		return x.Audit
	}
	return nil
}

type OnboardManualCustomerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *OnboardManualCustomerResponse) Reset() {
	*x = OnboardManualCustomerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OnboardManualCustomerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnboardManualCustomerResponse) ProtoMessage() {}

func (x *OnboardManualCustomerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnboardManualCustomerResponse.ProtoReflect.Descriptor instead.
func (*OnboardManualCustomerResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{8}
}

type NotificationConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Config map[string]string `protobuf:"bytes,1,rep,name=config,proto3" json:"config,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *NotificationConfig) Reset() {
	*x = NotificationConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotificationConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationConfig) ProtoMessage() {}

func (x *NotificationConfig) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationConfig.ProtoReflect.Descriptor instead.
func (*NotificationConfig) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{9}
}

func (x *NotificationConfig) GetConfig() map[string]string {
	if x != nil {
		return x.Config
	}
	return nil
}

type InternalInstance struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Instance            *v1.Instance           `protobuf:"bytes,1,opt,name=instance,proto3" json:"instance,omitempty"`
	CreateTime          *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=create_time,json=createTime,proto3,oneof" json:"create_time,omitempty"`
	Expired             bool                   `protobuf:"varint,3,opt,name=expired,proto3" json:"expired,omitempty"`
	ConnectedClusters   uint32                 `protobuf:"varint,4,opt,name=connected_clusters,json=connectedClusters,proto3" json:"connected_clusters,omitempty"`
	StatusProcessedInfo *string                `protobuf:"bytes,5,opt,name=status_processed_info,json=statusProcessedInfo,proto3,oneof" json:"status_processed_info,omitempty"`
	OrgId               string                 `protobuf:"bytes,6,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	NotificationConfig  *NotificationConfig    `protobuf:"bytes,7,opt,name=notification_config,json=notificationConfig,proto3" json:"notification_config,omitempty"`
	Workspace           *Workspace             `protobuf:"bytes,8,opt,name=workspace,proto3,oneof" json:"workspace,omitempty"`
}

func (x *InternalInstance) Reset() {
	*x = InternalInstance{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InternalInstance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalInstance) ProtoMessage() {}

func (x *InternalInstance) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalInstance.ProtoReflect.Descriptor instead.
func (*InternalInstance) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{10}
}

func (x *InternalInstance) GetInstance() *v1.Instance {
	if x != nil {
		return x.Instance
	}
	return nil
}

func (x *InternalInstance) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *InternalInstance) GetExpired() bool {
	if x != nil {
		return x.Expired
	}
	return false
}

func (x *InternalInstance) GetConnectedClusters() uint32 {
	if x != nil {
		return x.ConnectedClusters
	}
	return 0
}

func (x *InternalInstance) GetStatusProcessedInfo() string {
	if x != nil && x.StatusProcessedInfo != nil {
		return *x.StatusProcessedInfo
	}
	return ""
}

func (x *InternalInstance) GetOrgId() string {
	if x != nil {
		return x.OrgId
	}
	return ""
}

func (x *InternalInstance) GetNotificationConfig() *NotificationConfig {
	if x != nil {
		return x.NotificationConfig
	}
	return nil
}

func (x *InternalInstance) GetWorkspace() *Workspace {
	if x != nil {
		return x.Workspace
	}
	return nil
}

type Customer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StripeId       string  `protobuf:"bytes,1,opt,name=stripe_id,json=stripeId,proto3" json:"stripe_id,omitempty"`
	OrganizationId string  `protobuf:"bytes,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	BillingName    *string `protobuf:"bytes,3,opt,name=billing_name,json=billingName,proto3,oneof" json:"billing_name,omitempty"`
	BillingEmail   *string `protobuf:"bytes,4,opt,name=billing_email,json=billingEmail,proto3,oneof" json:"billing_email,omitempty"`
}

func (x *Customer) Reset() {
	*x = Customer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Customer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Customer) ProtoMessage() {}

func (x *Customer) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Customer.ProtoReflect.Descriptor instead.
func (*Customer) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{11}
}

func (x *Customer) GetStripeId() string {
	if x != nil {
		return x.StripeId
	}
	return ""
}

func (x *Customer) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *Customer) GetBillingName() string {
	if x != nil && x.BillingName != nil {
		return *x.BillingName
	}
	return ""
}

func (x *Customer) GetBillingEmail() string {
	if x != nil && x.BillingEmail != nil {
		return *x.BillingEmail
	}
	return ""
}

type ListOrganizationDomainsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrganizationId string `protobuf:"bytes,1,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
}

func (x *ListOrganizationDomainsRequest) Reset() {
	*x = ListOrganizationDomainsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOrganizationDomainsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrganizationDomainsRequest) ProtoMessage() {}

func (x *ListOrganizationDomainsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrganizationDomainsRequest.ProtoReflect.Descriptor instead.
func (*ListOrganizationDomainsRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{12}
}

func (x *ListOrganizationDomainsRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

type ListOrganizationDomainsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domains []*v11.DomainVerification `protobuf:"bytes,1,rep,name=domains,proto3" json:"domains,omitempty"`
}

func (x *ListOrganizationDomainsResponse) Reset() {
	*x = ListOrganizationDomainsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOrganizationDomainsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrganizationDomainsResponse) ProtoMessage() {}

func (x *ListOrganizationDomainsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrganizationDomainsResponse.ProtoReflect.Descriptor instead.
func (*ListOrganizationDomainsResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{13}
}

func (x *ListOrganizationDomainsResponse) GetDomains() []*v11.DomainVerification {
	if x != nil {
		return x.Domains
	}
	return nil
}

type UpdateOrganizationDomainsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrganizationId string                    `protobuf:"bytes,1,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	Domains        []*v11.DomainVerification `protobuf:"bytes,2,rep,name=domains,proto3" json:"domains,omitempty"`
	Audit          *Audit                    `protobuf:"bytes,3,opt,name=audit,proto3" json:"audit,omitempty"`
}

func (x *UpdateOrganizationDomainsRequest) Reset() {
	*x = UpdateOrganizationDomainsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOrganizationDomainsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOrganizationDomainsRequest) ProtoMessage() {}

func (x *UpdateOrganizationDomainsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOrganizationDomainsRequest.ProtoReflect.Descriptor instead.
func (*UpdateOrganizationDomainsRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateOrganizationDomainsRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *UpdateOrganizationDomainsRequest) GetDomains() []*v11.DomainVerification {
	if x != nil {
		return x.Domains
	}
	return nil
}

func (x *UpdateOrganizationDomainsRequest) GetAudit() *Audit {
	if x != nil {
		return x.Audit
	}
	return nil
}

type UpdateOrganizationDomainsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domains []*v11.DomainVerification `protobuf:"bytes,1,rep,name=domains,proto3" json:"domains,omitempty"`
}

func (x *UpdateOrganizationDomainsResponse) Reset() {
	*x = UpdateOrganizationDomainsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOrganizationDomainsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOrganizationDomainsResponse) ProtoMessage() {}

func (x *UpdateOrganizationDomainsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOrganizationDomainsResponse.ProtoReflect.Descriptor instead.
func (*UpdateOrganizationDomainsResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{15}
}

func (x *UpdateOrganizationDomainsResponse) GetDomains() []*v11.DomainVerification {
	if x != nil {
		return x.Domains
	}
	return nil
}

type ListOrganizationMembersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrganizationId []string `protobuf:"bytes,1,rep,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
}

func (x *ListOrganizationMembersRequest) Reset() {
	*x = ListOrganizationMembersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOrganizationMembersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrganizationMembersRequest) ProtoMessage() {}

func (x *ListOrganizationMembersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrganizationMembersRequest.ProtoReflect.Descriptor instead.
func (*ListOrganizationMembersRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{16}
}

func (x *ListOrganizationMembersRequest) GetOrganizationId() []string {
	if x != nil {
		return x.OrganizationId
	}
	return nil
}

type OrganizationMembers struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Email []string `protobuf:"bytes,1,rep,name=email,proto3" json:"email,omitempty"`
}

func (x *OrganizationMembers) Reset() {
	*x = OrganizationMembers{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrganizationMembers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrganizationMembers) ProtoMessage() {}

func (x *OrganizationMembers) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrganizationMembers.ProtoReflect.Descriptor instead.
func (*OrganizationMembers) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{17}
}

func (x *OrganizationMembers) GetEmail() []string {
	if x != nil {
		return x.Email
	}
	return nil
}

type ListOrganizationMembersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Members map[string]*OrganizationMembers `protobuf:"bytes,1,rep,name=members,proto3" json:"members,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ListOrganizationMembersResponse) Reset() {
	*x = ListOrganizationMembersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOrganizationMembersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrganizationMembersResponse) ProtoMessage() {}

func (x *ListOrganizationMembersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrganizationMembersResponse.ProtoReflect.Descriptor instead.
func (*ListOrganizationMembersResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{18}
}

func (x *ListOrganizationMembersResponse) GetMembers() map[string]*OrganizationMembers {
	if x != nil {
		return x.Members
	}
	return nil
}

type ListUnbilledOrganizationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListUnbilledOrganizationsRequest) Reset() {
	*x = ListUnbilledOrganizationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListUnbilledOrganizationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUnbilledOrganizationsRequest) ProtoMessage() {}

func (x *ListUnbilledOrganizationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUnbilledOrganizationsRequest.ProtoReflect.Descriptor instead.
func (*ListUnbilledOrganizationsRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{19}
}

type ListUnbilledOrganizationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Organizations []*BasicOrganization `protobuf:"bytes,1,rep,name=organizations,proto3" json:"organizations,omitempty"`
}

func (x *ListUnbilledOrganizationsResponse) Reset() {
	*x = ListUnbilledOrganizationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListUnbilledOrganizationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUnbilledOrganizationsResponse) ProtoMessage() {}

func (x *ListUnbilledOrganizationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUnbilledOrganizationsResponse.ProtoReflect.Descriptor instead.
func (*ListUnbilledOrganizationsResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{20}
}

func (x *ListUnbilledOrganizationsResponse) GetOrganizations() []*BasicOrganization {
	if x != nil {
		return x.Organizations
	}
	return nil
}

type OrganizationFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// default limit 50
	Limit  *uint32 `protobuf:"varint,1,opt,name=limit,proto3,oneof" json:"limit,omitempty"`
	Offset *uint32 `protobuf:"varint,2,opt,name=offset,proto3,oneof" json:"offset,omitempty"`
	// fuzzy search
	// by name
	// by id
	// by member
	Fuzz             *string  `protobuf:"bytes,3,opt,name=fuzz,proto3,oneof" json:"fuzz,omitempty"`
	SortByCreation   *Sort    `protobuf:"varint,4,opt,name=sort_by_creation,json=sortByCreation,proto3,enum=akuity.aims.v1.Sort,oneof" json:"sort_by_creation,omitempty"`
	Billed           *bool    `protobuf:"varint,5,opt,name=billed,proto3,oneof" json:"billed,omitempty"`
	ManuallyVerified *bool    `protobuf:"varint,6,opt,name=manually_verified,json=manuallyVerified,proto3,oneof" json:"manually_verified,omitempty"`
	Plans            []string `protobuf:"bytes,7,rep,name=plans,proto3" json:"plans,omitempty"`
	StartTime        *string  `protobuf:"bytes,8,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
	EndTime          *string  `protobuf:"bytes,9,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
}

func (x *OrganizationFilter) Reset() {
	*x = OrganizationFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrganizationFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrganizationFilter) ProtoMessage() {}

func (x *OrganizationFilter) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrganizationFilter.ProtoReflect.Descriptor instead.
func (*OrganizationFilter) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{21}
}

func (x *OrganizationFilter) GetLimit() uint32 {
	if x != nil && x.Limit != nil {
		return *x.Limit
	}
	return 0
}

func (x *OrganizationFilter) GetOffset() uint32 {
	if x != nil && x.Offset != nil {
		return *x.Offset
	}
	return 0
}

func (x *OrganizationFilter) GetFuzz() string {
	if x != nil && x.Fuzz != nil {
		return *x.Fuzz
	}
	return ""
}

func (x *OrganizationFilter) GetSortByCreation() Sort {
	if x != nil && x.SortByCreation != nil {
		return *x.SortByCreation
	}
	return Sort_SORT_UNSPECIFIED
}

func (x *OrganizationFilter) GetBilled() bool {
	if x != nil && x.Billed != nil {
		return *x.Billed
	}
	return false
}

func (x *OrganizationFilter) GetManuallyVerified() bool {
	if x != nil && x.ManuallyVerified != nil {
		return *x.ManuallyVerified
	}
	return false
}

func (x *OrganizationFilter) GetPlans() []string {
	if x != nil {
		return x.Plans
	}
	return nil
}

func (x *OrganizationFilter) GetStartTime() string {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return ""
}

func (x *OrganizationFilter) GetEndTime() string {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return ""
}

type ListAllOrganizationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filters *OrganizationFilter `protobuf:"bytes,1,opt,name=filters,proto3,oneof" json:"filters,omitempty"`
}

func (x *ListAllOrganizationsRequest) Reset() {
	*x = ListAllOrganizationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAllOrganizationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAllOrganizationsRequest) ProtoMessage() {}

func (x *ListAllOrganizationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAllOrganizationsRequest.ProtoReflect.Descriptor instead.
func (*ListAllOrganizationsRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{22}
}

func (x *ListAllOrganizationsRequest) GetFilters() *OrganizationFilter {
	if x != nil {
		return x.Filters
	}
	return nil
}

type ListAllOrganizationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Organizations []*BasicOrganization `protobuf:"bytes,1,rep,name=organizations,proto3" json:"organizations,omitempty"`
	Count         uint32               `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *ListAllOrganizationsResponse) Reset() {
	*x = ListAllOrganizationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAllOrganizationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAllOrganizationsResponse) ProtoMessage() {}

func (x *ListAllOrganizationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAllOrganizationsResponse.ProtoReflect.Descriptor instead.
func (*ListAllOrganizationsResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{23}
}

func (x *ListAllOrganizationsResponse) GetOrganizations() []*BasicOrganization {
	if x != nil {
		return x.Organizations
	}
	return nil
}

func (x *ListAllOrganizationsResponse) GetCount() uint32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type GetOrganizationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrganizationId string `protobuf:"bytes,1,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
}

func (x *GetOrganizationRequest) Reset() {
	*x = GetOrganizationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrganizationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrganizationRequest) ProtoMessage() {}

func (x *GetOrganizationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrganizationRequest.ProtoReflect.Descriptor instead.
func (*GetOrganizationRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{24}
}

func (x *GetOrganizationRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

type GetOrganizationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Organization *BasicOrganization `protobuf:"bytes,1,opt,name=organization,proto3" json:"organization,omitempty"`
}

func (x *GetOrganizationResponse) Reset() {
	*x = GetOrganizationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrganizationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrganizationResponse) ProtoMessage() {}

func (x *GetOrganizationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrganizationResponse.ProtoReflect.Descriptor instead.
func (*GetOrganizationResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{25}
}

func (x *GetOrganizationResponse) GetOrganization() *BasicOrganization {
	if x != nil {
		return x.Organization
	}
	return nil
}

type StripeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SubscriptionEndTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=subscription_end_time,json=subscriptionEndTime,proto3" json:"subscription_end_time,omitempty"`
	Stale               bool                   `protobuf:"varint,3,opt,name=stale,proto3" json:"stale,omitempty"`
}

func (x *StripeData) Reset() {
	*x = StripeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeData) ProtoMessage() {}

func (x *StripeData) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeData.ProtoReflect.Descriptor instead.
func (*StripeData) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{26}
}

func (x *StripeData) GetSubscriptionEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.SubscriptionEndTime
	}
	return nil
}

func (x *StripeData) GetStale() bool {
	if x != nil {
		return x.Stale
	}
	return false
}

type BasicOrganization struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                string                  `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name              string                  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Billed            bool                    `protobuf:"varint,3,opt,name=billed,proto3" json:"billed,omitempty"`
	Emails            []string                `protobuf:"bytes,4,rep,name=emails,proto3" json:"emails,omitempty"`
	ManuallyVerified  bool                    `protobuf:"varint,6,opt,name=manually_verified,json=manuallyVerified,proto3" json:"manually_verified,omitempty"`
	NumInstances      uint64                  `protobuf:"varint,8,opt,name=num_instances,json=numInstances,proto3" json:"num_instances,omitempty"`
	Plan              string                  `protobuf:"bytes,9,opt,name=plan,proto3" json:"plan,omitempty"`
	Status            *v11.OrganizationStatus `protobuf:"bytes,10,opt,name=status,proto3,oneof" json:"status,omitempty"`
	Quota             *v12.OrganizationQuota  `protobuf:"bytes,11,opt,name=quota,proto3" json:"quota,omitempty"`
	Usage             *v12.OrganizationUsage  `protobuf:"bytes,12,opt,name=usage,proto3" json:"usage,omitempty"`
	BillingDetails    *v11.BillingDetails     `protobuf:"bytes,13,opt,name=billing_details,json=billingDetails,proto3" json:"billing_details,omitempty"`
	StripeData        *StripeData             `protobuf:"bytes,14,opt,name=stripe_data,json=stripeData,proto3" json:"stripe_data,omitempty"`
	CreationTimestamp *timestamppb.Timestamp  `protobuf:"bytes,15,opt,name=creation_timestamp,json=creationTimestamp,proto3,oneof" json:"creation_timestamp,omitempty"`
	Inactive          bool                    `protobuf:"varint,16,opt,name=inactive,proto3" json:"inactive,omitempty"`
	CanDelete         bool                    `protobuf:"varint,17,opt,name=can_delete,json=canDelete,proto3" json:"can_delete,omitempty"`
	Misc              map[string]int32        `protobuf:"bytes,18,rep,name=misc,proto3" json:"misc,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *BasicOrganization) Reset() {
	*x = BasicOrganization{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BasicOrganization) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BasicOrganization) ProtoMessage() {}

func (x *BasicOrganization) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BasicOrganization.ProtoReflect.Descriptor instead.
func (*BasicOrganization) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{27}
}

func (x *BasicOrganization) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BasicOrganization) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BasicOrganization) GetBilled() bool {
	if x != nil {
		return x.Billed
	}
	return false
}

func (x *BasicOrganization) GetEmails() []string {
	if x != nil {
		return x.Emails
	}
	return nil
}

func (x *BasicOrganization) GetManuallyVerified() bool {
	if x != nil {
		return x.ManuallyVerified
	}
	return false
}

func (x *BasicOrganization) GetNumInstances() uint64 {
	if x != nil {
		return x.NumInstances
	}
	return 0
}

func (x *BasicOrganization) GetPlan() string {
	if x != nil {
		return x.Plan
	}
	return ""
}

func (x *BasicOrganization) GetStatus() *v11.OrganizationStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *BasicOrganization) GetQuota() *v12.OrganizationQuota {
	if x != nil {
		return x.Quota
	}
	return nil
}

func (x *BasicOrganization) GetUsage() *v12.OrganizationUsage {
	if x != nil {
		return x.Usage
	}
	return nil
}

func (x *BasicOrganization) GetBillingDetails() *v11.BillingDetails {
	if x != nil {
		return x.BillingDetails
	}
	return nil
}

func (x *BasicOrganization) GetStripeData() *StripeData {
	if x != nil {
		return x.StripeData
	}
	return nil
}

func (x *BasicOrganization) GetCreationTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.CreationTimestamp
	}
	return nil
}

func (x *BasicOrganization) GetInactive() bool {
	if x != nil {
		return x.Inactive
	}
	return false
}

func (x *BasicOrganization) GetCanDelete() bool {
	if x != nil {
		return x.CanDelete
	}
	return false
}

func (x *BasicOrganization) GetMisc() map[string]int32 {
	if x != nil {
		return x.Misc
	}
	return nil
}

type PlanUsage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ControlPlanes int32 `protobuf:"varint,1,opt,name=control_planes,json=controlPlanes,proto3" json:"control_planes,omitempty"`
	Applications  int32 `protobuf:"varint,2,opt,name=applications,proto3" json:"applications,omitempty"`
	Clusters      int32 `protobuf:"varint,3,opt,name=clusters,proto3" json:"clusters,omitempty"`
	Members       int32 `protobuf:"varint,4,opt,name=members,proto3" json:"members,omitempty"`
}

func (x *PlanUsage) Reset() {
	*x = PlanUsage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlanUsage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlanUsage) ProtoMessage() {}

func (x *PlanUsage) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlanUsage.ProtoReflect.Descriptor instead.
func (*PlanUsage) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{28}
}

func (x *PlanUsage) GetControlPlanes() int32 {
	if x != nil {
		return x.ControlPlanes
	}
	return 0
}

func (x *PlanUsage) GetApplications() int32 {
	if x != nil {
		return x.Applications
	}
	return 0
}

func (x *PlanUsage) GetClusters() int32 {
	if x != nil {
		return x.Clusters
	}
	return 0
}

func (x *PlanUsage) GetMembers() int32 {
	if x != nil {
		return x.Members
	}
	return 0
}

type Audit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Actor  string `protobuf:"bytes,1,opt,name=actor,proto3" json:"actor,omitempty"`
	Reason string `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (x *Audit) Reset() {
	*x = Audit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Audit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Audit) ProtoMessage() {}

func (x *Audit) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Audit.ProtoReflect.Descriptor instead.
func (*Audit) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{29}
}

func (x *Audit) GetActor() string {
	if x != nil {
		return x.Actor
	}
	return ""
}

func (x *Audit) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type PatchFeatureGatesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string                        `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	FeatureGates *v12.OrganizationFeatureGates `protobuf:"bytes,2,opt,name=feature_gates,json=featureGates,proto3" json:"feature_gates,omitempty"`
	Audit        *Audit                        `protobuf:"bytes,3,opt,name=audit,proto3" json:"audit,omitempty"`
}

func (x *PatchFeatureGatesRequest) Reset() {
	*x = PatchFeatureGatesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PatchFeatureGatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PatchFeatureGatesRequest) ProtoMessage() {}

func (x *PatchFeatureGatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PatchFeatureGatesRequest.ProtoReflect.Descriptor instead.
func (*PatchFeatureGatesRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{30}
}

func (x *PatchFeatureGatesRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PatchFeatureGatesRequest) GetFeatureGates() *v12.OrganizationFeatureGates {
	if x != nil {
		return x.FeatureGates
	}
	return nil
}

func (x *PatchFeatureGatesRequest) GetAudit() *Audit {
	if x != nil {
		return x.Audit
	}
	return nil
}

type PatchFeatureGatesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FeatureGates *v12.OrganizationFeatureGates `protobuf:"bytes,1,opt,name=feature_gates,json=featureGates,proto3" json:"feature_gates,omitempty"`
}

func (x *PatchFeatureGatesResponse) Reset() {
	*x = PatchFeatureGatesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PatchFeatureGatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PatchFeatureGatesResponse) ProtoMessage() {}

func (x *PatchFeatureGatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PatchFeatureGatesResponse.ProtoReflect.Descriptor instead.
func (*PatchFeatureGatesResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{31}
}

func (x *PatchFeatureGatesResponse) GetFeatureGates() *v12.OrganizationFeatureGates {
	if x != nil {
		return x.FeatureGates
	}
	return nil
}

type GetFeatureGatesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetFeatureGatesRequest) Reset() {
	*x = GetFeatureGatesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFeatureGatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFeatureGatesRequest) ProtoMessage() {}

func (x *GetFeatureGatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFeatureGatesRequest.ProtoReflect.Descriptor instead.
func (*GetFeatureGatesRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{32}
}

func (x *GetFeatureGatesRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetFeatureGatesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FeatureGates       *v12.OrganizationFeatureGates `protobuf:"bytes,1,opt,name=feature_gates,json=featureGates,proto3" json:"feature_gates,omitempty"`
	SystemFeatureGates *v12.SystemFeatureGates       `protobuf:"bytes,2,opt,name=system_feature_gates,json=systemFeatureGates,proto3" json:"system_feature_gates,omitempty"`
}

func (x *GetFeatureGatesResponse) Reset() {
	*x = GetFeatureGatesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFeatureGatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFeatureGatesResponse) ProtoMessage() {}

func (x *GetFeatureGatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFeatureGatesResponse.ProtoReflect.Descriptor instead.
func (*GetFeatureGatesResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{33}
}

func (x *GetFeatureGatesResponse) GetFeatureGates() *v12.OrganizationFeatureGates {
	if x != nil {
		return x.FeatureGates
	}
	return nil
}

func (x *GetFeatureGatesResponse) GetSystemFeatureGates() *v12.SystemFeatureGates {
	if x != nil {
		return x.SystemFeatureGates
	}
	return nil
}

type UpdateQuotasRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Audit *Audit                 `protobuf:"bytes,2,opt,name=audit,proto3" json:"audit,omitempty"`
	Quota *v12.OrganizationQuota `protobuf:"bytes,3,opt,name=quota,proto3" json:"quota,omitempty"`
}

func (x *UpdateQuotasRequest) Reset() {
	*x = UpdateQuotasRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateQuotasRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateQuotasRequest) ProtoMessage() {}

func (x *UpdateQuotasRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateQuotasRequest.ProtoReflect.Descriptor instead.
func (*UpdateQuotasRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{34}
}

func (x *UpdateQuotasRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateQuotasRequest) GetAudit() *Audit {
	if x != nil {
		return x.Audit
	}
	return nil
}

func (x *UpdateQuotasRequest) GetQuota() *v12.OrganizationQuota {
	if x != nil {
		return x.Quota
	}
	return nil
}

type UpdateQuotasResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Quota             *v12.OrganizationQuota `protobuf:"bytes,1,opt,name=quota,proto3" json:"quota,omitempty"`
	CreationTimestamp *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=creation_timestamp,json=creationTimestamp,proto3,oneof" json:"creation_timestamp,omitempty"`
}

func (x *UpdateQuotasResponse) Reset() {
	*x = UpdateQuotasResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateQuotasResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateQuotasResponse) ProtoMessage() {}

func (x *UpdateQuotasResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateQuotasResponse.ProtoReflect.Descriptor instead.
func (*UpdateQuotasResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{35}
}

func (x *UpdateQuotasResponse) GetQuota() *v12.OrganizationQuota {
	if x != nil {
		return x.Quota
	}
	return nil
}

func (x *UpdateQuotasResponse) GetCreationTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.CreationTimestamp
	}
	return nil
}

type UpdateOrganizationTrialExpirationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrganizationId  string `protobuf:"bytes,1,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	TrialExpiration uint64 `protobuf:"varint,2,opt,name=trial_expiration,json=trialExpiration,proto3" json:"trial_expiration,omitempty"`
	Audit           *Audit `protobuf:"bytes,3,opt,name=audit,proto3" json:"audit,omitempty"`
}

func (x *UpdateOrganizationTrialExpirationRequest) Reset() {
	*x = UpdateOrganizationTrialExpirationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOrganizationTrialExpirationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOrganizationTrialExpirationRequest) ProtoMessage() {}

func (x *UpdateOrganizationTrialExpirationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOrganizationTrialExpirationRequest.ProtoReflect.Descriptor instead.
func (*UpdateOrganizationTrialExpirationRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{36}
}

func (x *UpdateOrganizationTrialExpirationRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *UpdateOrganizationTrialExpirationRequest) GetTrialExpiration() uint64 {
	if x != nil {
		return x.TrialExpiration
	}
	return 0
}

func (x *UpdateOrganizationTrialExpirationRequest) GetAudit() *Audit {
	if x != nil {
		return x.Audit
	}
	return nil
}

type UpdateOrganizationTrialExpirationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateOrganizationTrialExpirationResponse) Reset() {
	*x = UpdateOrganizationTrialExpirationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOrganizationTrialExpirationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOrganizationTrialExpirationResponse) ProtoMessage() {}

func (x *UpdateOrganizationTrialExpirationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOrganizationTrialExpirationResponse.ProtoReflect.Descriptor instead.
func (*UpdateOrganizationTrialExpirationResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{37}
}

type DecrementInstanceGenerationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstanceId string `protobuf:"bytes,1,opt,name=instance_id,json=instanceId,proto3" json:"instance_id,omitempty"`
	Audit      *Audit `protobuf:"bytes,2,opt,name=audit,proto3" json:"audit,omitempty"`
}

func (x *DecrementInstanceGenerationRequest) Reset() {
	*x = DecrementInstanceGenerationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DecrementInstanceGenerationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DecrementInstanceGenerationRequest) ProtoMessage() {}

func (x *DecrementInstanceGenerationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DecrementInstanceGenerationRequest.ProtoReflect.Descriptor instead.
func (*DecrementInstanceGenerationRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{38}
}

func (x *DecrementInstanceGenerationRequest) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

func (x *DecrementInstanceGenerationRequest) GetAudit() *Audit {
	if x != nil {
		return x.Audit
	}
	return nil
}

type DecrementInstanceGenerationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DecrementInstanceGenerationResponse) Reset() {
	*x = DecrementInstanceGenerationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DecrementInstanceGenerationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DecrementInstanceGenerationResponse) ProtoMessage() {}

func (x *DecrementInstanceGenerationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DecrementInstanceGenerationResponse.ProtoReflect.Descriptor instead.
func (*DecrementInstanceGenerationResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{39}
}

type GetInstanceByIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstanceId string `protobuf:"bytes,1,opt,name=instance_id,json=instanceId,proto3" json:"instance_id,omitempty"`
}

func (x *GetInstanceByIdRequest) Reset() {
	*x = GetInstanceByIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstanceByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstanceByIdRequest) ProtoMessage() {}

func (x *GetInstanceByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstanceByIdRequest.ProtoReflect.Descriptor instead.
func (*GetInstanceByIdRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{40}
}

func (x *GetInstanceByIdRequest) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

type GetInstanceByIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Instance *InternalInstance `protobuf:"bytes,1,opt,name=instance,proto3" json:"instance,omitempty"`
}

func (x *GetInstanceByIdResponse) Reset() {
	*x = GetInstanceByIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstanceByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstanceByIdResponse) ProtoMessage() {}

func (x *GetInstanceByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstanceByIdResponse.ProtoReflect.Descriptor instead.
func (*GetInstanceByIdResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{41}
}

func (x *GetInstanceByIdResponse) GetInstance() *InternalInstance {
	if x != nil {
		return x.Instance
	}
	return nil
}

type GetKargoInstanceByIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstanceId string `protobuf:"bytes,1,opt,name=instance_id,json=instanceId,proto3" json:"instance_id,omitempty"`
}

func (x *GetKargoInstanceByIdRequest) Reset() {
	*x = GetKargoInstanceByIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetKargoInstanceByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKargoInstanceByIdRequest) ProtoMessage() {}

func (x *GetKargoInstanceByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKargoInstanceByIdRequest.ProtoReflect.Descriptor instead.
func (*GetKargoInstanceByIdRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{42}
}

func (x *GetKargoInstanceByIdRequest) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

type GetKargoInstanceByIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Instance *InternalKargoInstance `protobuf:"bytes,1,opt,name=instance,proto3" json:"instance,omitempty"`
}

func (x *GetKargoInstanceByIdResponse) Reset() {
	*x = GetKargoInstanceByIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetKargoInstanceByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKargoInstanceByIdResponse) ProtoMessage() {}

func (x *GetKargoInstanceByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKargoInstanceByIdResponse.ProtoReflect.Descriptor instead.
func (*GetKargoInstanceByIdResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{43}
}

func (x *GetKargoInstanceByIdResponse) GetInstance() *InternalKargoInstance {
	if x != nil {
		return x.Instance
	}
	return nil
}

type ClusterFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// search by name/id/namespace
	Fuzz     string  `protobuf:"bytes,1,opt,name=fuzz,proto3" json:"fuzz,omitempty"`
	TimeFrom *string `protobuf:"bytes,3,opt,name=time_from,json=timeFrom,proto3,oneof" json:"time_from,omitempty"`
}

func (x *ClusterFilter) Reset() {
	*x = ClusterFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClusterFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClusterFilter) ProtoMessage() {}

func (x *ClusterFilter) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClusterFilter.ProtoReflect.Descriptor instead.
func (*ClusterFilter) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{44}
}

func (x *ClusterFilter) GetFuzz() string {
	if x != nil {
		return x.Fuzz
	}
	return ""
}

func (x *ClusterFilter) GetTimeFrom() string {
	if x != nil && x.TimeFrom != nil {
		return *x.TimeFrom
	}
	return ""
}

type ListClustersForInstanceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstanceId string         `protobuf:"bytes,1,opt,name=instance_id,json=instanceId,proto3" json:"instance_id,omitempty"`
	Filter     *ClusterFilter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListClustersForInstanceRequest) Reset() {
	*x = ListClustersForInstanceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListClustersForInstanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClustersForInstanceRequest) ProtoMessage() {}

func (x *ListClustersForInstanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClustersForInstanceRequest.ProtoReflect.Descriptor instead.
func (*ListClustersForInstanceRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{45}
}

func (x *ListClustersForInstanceRequest) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

func (x *ListClustersForInstanceRequest) GetFilter() *ClusterFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type ListClustersForInstanceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clusters []*v1.Cluster `protobuf:"bytes,1,rep,name=clusters,proto3" json:"clusters,omitempty"`
}

func (x *ListClustersForInstanceResponse) Reset() {
	*x = ListClustersForInstanceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListClustersForInstanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClustersForInstanceResponse) ProtoMessage() {}

func (x *ListClustersForInstanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClustersForInstanceResponse.ProtoReflect.Descriptor instead.
func (*ListClustersForInstanceResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{46}
}

func (x *ListClustersForInstanceResponse) GetClusters() []*v1.Cluster {
	if x != nil {
		return x.Clusters
	}
	return nil
}

type ListAgentsForKargoInstanceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstanceId string `protobuf:"bytes,1,opt,name=instance_id,json=instanceId,proto3" json:"instance_id,omitempty"`
}

func (x *ListAgentsForKargoInstanceRequest) Reset() {
	*x = ListAgentsForKargoInstanceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAgentsForKargoInstanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAgentsForKargoInstanceRequest) ProtoMessage() {}

func (x *ListAgentsForKargoInstanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAgentsForKargoInstanceRequest.ProtoReflect.Descriptor instead.
func (*ListAgentsForKargoInstanceRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{47}
}

func (x *ListAgentsForKargoInstanceRequest) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

type ListAgentsForKargoInstanceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Agents []*v13.KargoAgent `protobuf:"bytes,1,rep,name=agents,proto3" json:"agents,omitempty"`
}

func (x *ListAgentsForKargoInstanceResponse) Reset() {
	*x = ListAgentsForKargoInstanceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAgentsForKargoInstanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAgentsForKargoInstanceResponse) ProtoMessage() {}

func (x *ListAgentsForKargoInstanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAgentsForKargoInstanceResponse.ProtoReflect.Descriptor instead.
func (*ListAgentsForKargoInstanceResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{48}
}

func (x *ListAgentsForKargoInstanceResponse) GetAgents() []*v13.KargoAgent {
	if x != nil {
		return x.Agents
	}
	return nil
}

type SetManuallyVerifiedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrganizationId string `protobuf:"bytes,1,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	Verified       bool   `protobuf:"varint,2,opt,name=verified,proto3" json:"verified,omitempty"`
	Audit          *Audit `protobuf:"bytes,3,opt,name=audit,proto3" json:"audit,omitempty"`
}

func (x *SetManuallyVerifiedRequest) Reset() {
	*x = SetManuallyVerifiedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetManuallyVerifiedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetManuallyVerifiedRequest) ProtoMessage() {}

func (x *SetManuallyVerifiedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetManuallyVerifiedRequest.ProtoReflect.Descriptor instead.
func (*SetManuallyVerifiedRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{49}
}

func (x *SetManuallyVerifiedRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *SetManuallyVerifiedRequest) GetVerified() bool {
	if x != nil {
		return x.Verified
	}
	return false
}

func (x *SetManuallyVerifiedRequest) GetAudit() *Audit {
	if x != nil {
		return x.Audit
	}
	return nil
}

type SetManuallyVerifiedResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetManuallyVerifiedResponse) Reset() {
	*x = SetManuallyVerifiedResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetManuallyVerifiedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetManuallyVerifiedResponse) ProtoMessage() {}

func (x *SetManuallyVerifiedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetManuallyVerifiedResponse.ProtoReflect.Descriptor instead.
func (*SetManuallyVerifiedResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{50}
}

type SetDisabledInstanceCreationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Disabled bool   `protobuf:"varint,1,opt,name=disabled,proto3" json:"disabled,omitempty"`
	Audit    *Audit `protobuf:"bytes,2,opt,name=audit,proto3" json:"audit,omitempty"`
}

func (x *SetDisabledInstanceCreationRequest) Reset() {
	*x = SetDisabledInstanceCreationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetDisabledInstanceCreationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetDisabledInstanceCreationRequest) ProtoMessage() {}

func (x *SetDisabledInstanceCreationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetDisabledInstanceCreationRequest.ProtoReflect.Descriptor instead.
func (*SetDisabledInstanceCreationRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{51}
}

func (x *SetDisabledInstanceCreationRequest) GetDisabled() bool {
	if x != nil {
		return x.Disabled
	}
	return false
}

func (x *SetDisabledInstanceCreationRequest) GetAudit() *Audit {
	if x != nil {
		return x.Audit
	}
	return nil
}

type SetDisabledInstanceCreationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetDisabledInstanceCreationResponse) Reset() {
	*x = SetDisabledInstanceCreationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetDisabledInstanceCreationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetDisabledInstanceCreationResponse) ProtoMessage() {}

func (x *SetDisabledInstanceCreationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetDisabledInstanceCreationResponse.ProtoReflect.Descriptor instead.
func (*SetDisabledInstanceCreationResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{52}
}

type DeleteOrganizationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrganizationId string `protobuf:"bytes,1,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	Audit          *Audit `protobuf:"bytes,2,opt,name=audit,proto3" json:"audit,omitempty"`
}

func (x *DeleteOrganizationRequest) Reset() {
	*x = DeleteOrganizationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteOrganizationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteOrganizationRequest) ProtoMessage() {}

func (x *DeleteOrganizationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteOrganizationRequest.ProtoReflect.Descriptor instead.
func (*DeleteOrganizationRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{53}
}

func (x *DeleteOrganizationRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *DeleteOrganizationRequest) GetAudit() *Audit {
	if x != nil {
		return x.Audit
	}
	return nil
}

type DeleteOrganizationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteOrganizationResponse) Reset() {
	*x = DeleteOrganizationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteOrganizationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteOrganizationResponse) ProtoMessage() {}

func (x *DeleteOrganizationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteOrganizationResponse.ProtoReflect.Descriptor instead.
func (*DeleteOrganizationResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{54}
}

type GetInternalConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetInternalConfigRequest) Reset() {
	*x = GetInternalConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInternalConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInternalConfigRequest) ProtoMessage() {}

func (x *GetInternalConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInternalConfigRequest.ProtoReflect.Descriptor instead.
func (*GetInternalConfigRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{55}
}

type InternalConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DisableFreeInstanceCreation bool `protobuf:"varint,1,opt,name=disable_free_instance_creation,json=disableFreeInstanceCreation,proto3" json:"disable_free_instance_creation,omitempty"`
}

func (x *InternalConfig) Reset() {
	*x = InternalConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InternalConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalConfig) ProtoMessage() {}

func (x *InternalConfig) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalConfig.ProtoReflect.Descriptor instead.
func (*InternalConfig) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{56}
}

func (x *InternalConfig) GetDisableFreeInstanceCreation() bool {
	if x != nil {
		return x.DisableFreeInstanceCreation
	}
	return false
}

type GetInternalConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Config *InternalConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *GetInternalConfigResponse) Reset() {
	*x = GetInternalConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInternalConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInternalConfigResponse) ProtoMessage() {}

func (x *GetInternalConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInternalConfigResponse.ProtoReflect.Descriptor instead.
func (*GetInternalConfigResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{57}
}

func (x *GetInternalConfigResponse) GetConfig() *InternalConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

type InternalKargoInstance struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Instance *v13.KargoInstance `protobuf:"bytes,1,opt,name=instance,proto3" json:"instance,omitempty"`
	// only basic fields of BasicOrganization
	Organization      *BasicOrganization     `protobuf:"bytes,2,opt,name=organization,proto3" json:"organization,omitempty"`
	CreationTimestamp *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=creation_timestamp,json=creationTimestamp,proto3,oneof" json:"creation_timestamp,omitempty"`
	Workspace         *Workspace             `protobuf:"bytes,4,opt,name=workspace,proto3,oneof" json:"workspace,omitempty"`
	StatusInfo        *structpb.Struct       `protobuf:"bytes,5,opt,name=status_info,json=statusInfo,proto3,oneof" json:"status_info,omitempty"`
}

func (x *InternalKargoInstance) Reset() {
	*x = InternalKargoInstance{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InternalKargoInstance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalKargoInstance) ProtoMessage() {}

func (x *InternalKargoInstance) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalKargoInstance.ProtoReflect.Descriptor instead.
func (*InternalKargoInstance) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{58}
}

func (x *InternalKargoInstance) GetInstance() *v13.KargoInstance {
	if x != nil {
		return x.Instance
	}
	return nil
}

func (x *InternalKargoInstance) GetOrganization() *BasicOrganization {
	if x != nil {
		return x.Organization
	}
	return nil
}

func (x *InternalKargoInstance) GetCreationTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.CreationTimestamp
	}
	return nil
}

func (x *InternalKargoInstance) GetWorkspace() *Workspace {
	if x != nil {
		return x.Workspace
	}
	return nil
}

func (x *InternalKargoInstance) GetStatusInfo() *structpb.Struct {
	if x != nil {
		return x.StatusInfo
	}
	return nil
}

type KargoInstanceFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Paid   *bool `protobuf:"varint,1,opt,name=paid,proto3,oneof" json:"paid,omitempty"`
	Unpaid *bool `protobuf:"varint,2,opt,name=unpaid,proto3,oneof" json:"unpaid,omitempty"`
	// search by instance name/id
	Fuzz     string  `protobuf:"bytes,3,opt,name=fuzz,proto3" json:"fuzz,omitempty"`
	TimeFrom *string `protobuf:"bytes,4,opt,name=time_from,json=timeFrom,proto3,oneof" json:"time_from,omitempty"`
	// filter by organization ID
	OrganizationId *string `protobuf:"bytes,5,opt,name=organization_id,json=organizationId,proto3,oneof" json:"organization_id,omitempty"`
}

func (x *KargoInstanceFilter) Reset() {
	*x = KargoInstanceFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KargoInstanceFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KargoInstanceFilter) ProtoMessage() {}

func (x *KargoInstanceFilter) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KargoInstanceFilter.ProtoReflect.Descriptor instead.
func (*KargoInstanceFilter) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{59}
}

func (x *KargoInstanceFilter) GetPaid() bool {
	if x != nil && x.Paid != nil {
		return *x.Paid
	}
	return false
}

func (x *KargoInstanceFilter) GetUnpaid() bool {
	if x != nil && x.Unpaid != nil {
		return *x.Unpaid
	}
	return false
}

func (x *KargoInstanceFilter) GetFuzz() string {
	if x != nil {
		return x.Fuzz
	}
	return ""
}

func (x *KargoInstanceFilter) GetTimeFrom() string {
	if x != nil && x.TimeFrom != nil {
		return *x.TimeFrom
	}
	return ""
}

func (x *KargoInstanceFilter) GetOrganizationId() string {
	if x != nil && x.OrganizationId != nil {
		return *x.OrganizationId
	}
	return ""
}

type ListKargoInstancesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter *KargoInstanceFilter `protobuf:"bytes,1,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
}

func (x *ListKargoInstancesRequest) Reset() {
	*x = ListKargoInstancesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListKargoInstancesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListKargoInstancesRequest) ProtoMessage() {}

func (x *ListKargoInstancesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListKargoInstancesRequest.ProtoReflect.Descriptor instead.
func (*ListKargoInstancesRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{60}
}

func (x *ListKargoInstancesRequest) GetFilter() *KargoInstanceFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type ListKargoInstancesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Instances []*InternalKargoInstance `protobuf:"bytes,1,rep,name=instances,proto3" json:"instances,omitempty"`
}

func (x *ListKargoInstancesResponse) Reset() {
	*x = ListKargoInstancesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListKargoInstancesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListKargoInstancesResponse) ProtoMessage() {}

func (x *ListKargoInstancesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListKargoInstancesResponse.ProtoReflect.Descriptor instead.
func (*ListKargoInstancesResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{61}
}

func (x *ListKargoInstancesResponse) GetInstances() []*InternalKargoInstance {
	if x != nil {
		return x.Instances
	}
	return nil
}

type InstanceClusterMaintenanceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstanceId      string `protobuf:"bytes,1,opt,name=instance_id,json=instanceId,proto3" json:"instance_id,omitempty"`
	ClusterId       string `protobuf:"bytes,2,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	MaintanenceMode bool   `protobuf:"varint,3,opt,name=maintanence_mode,json=maintanenceMode,proto3" json:"maintanence_mode,omitempty"`
	Audit           *Audit `protobuf:"bytes,4,opt,name=audit,proto3" json:"audit,omitempty"`
}

func (x *InstanceClusterMaintenanceRequest) Reset() {
	*x = InstanceClusterMaintenanceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InstanceClusterMaintenanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstanceClusterMaintenanceRequest) ProtoMessage() {}

func (x *InstanceClusterMaintenanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstanceClusterMaintenanceRequest.ProtoReflect.Descriptor instead.
func (*InstanceClusterMaintenanceRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{62}
}

func (x *InstanceClusterMaintenanceRequest) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

func (x *InstanceClusterMaintenanceRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *InstanceClusterMaintenanceRequest) GetMaintanenceMode() bool {
	if x != nil {
		return x.MaintanenceMode
	}
	return false
}

func (x *InstanceClusterMaintenanceRequest) GetAudit() *Audit {
	if x != nil {
		return x.Audit
	}
	return nil
}

type InstanceClusterMaintenanceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *InstanceClusterMaintenanceResponse) Reset() {
	*x = InstanceClusterMaintenanceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InstanceClusterMaintenanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstanceClusterMaintenanceResponse) ProtoMessage() {}

func (x *InstanceClusterMaintenanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstanceClusterMaintenanceResponse.ProtoReflect.Descriptor instead.
func (*InstanceClusterMaintenanceResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{63}
}

type ListAuditLogsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrganizationId string            `protobuf:"bytes,1,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	Filters        *v11.AuditFilters `protobuf:"bytes,2,opt,name=filters,proto3,oneof" json:"filters,omitempty"`
}

func (x *ListAuditLogsRequest) Reset() {
	*x = ListAuditLogsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAuditLogsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAuditLogsRequest) ProtoMessage() {}

func (x *ListAuditLogsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAuditLogsRequest.ProtoReflect.Descriptor instead.
func (*ListAuditLogsRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{64}
}

func (x *ListAuditLogsRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *ListAuditLogsRequest) GetFilters() *v11.AuditFilters {
	if x != nil {
		return x.Filters
	}
	return nil
}

type ListAuditLogsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuditLogs []*v11.AuditLog `protobuf:"bytes,1,rep,name=audit_logs,json=auditLogs,proto3" json:"audit_logs,omitempty"`
	Count     uint32          `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *ListAuditLogsResponse) Reset() {
	*x = ListAuditLogsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAuditLogsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAuditLogsResponse) ProtoMessage() {}

func (x *ListAuditLogsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAuditLogsResponse.ProtoReflect.Descriptor instead.
func (*ListAuditLogsResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{65}
}

func (x *ListAuditLogsResponse) GetAuditLogs() []*v11.AuditLog {
	if x != nil {
		return x.AuditLogs
	}
	return nil
}

func (x *ListAuditLogsResponse) GetCount() uint32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type ListAvailablePlansRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListAvailablePlansRequest) Reset() {
	*x = ListAvailablePlansRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAvailablePlansRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAvailablePlansRequest) ProtoMessage() {}

func (x *ListAvailablePlansRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAvailablePlansRequest.ProtoReflect.Descriptor instead.
func (*ListAvailablePlansRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{66}
}

type Plan struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name      string           `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	ProductId string           `protobuf:"bytes,2,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	Features  *structpb.Struct `protobuf:"bytes,3,opt,name=features,proto3" json:"features,omitempty"`
	Quotas    *structpb.Struct `protobuf:"bytes,4,opt,name=quotas,proto3" json:"quotas,omitempty"`
	Default   bool             `protobuf:"varint,5,opt,name=default,proto3" json:"default,omitempty"`
}

func (x *Plan) Reset() {
	*x = Plan{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Plan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Plan) ProtoMessage() {}

func (x *Plan) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Plan.ProtoReflect.Descriptor instead.
func (*Plan) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{67}
}

func (x *Plan) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Plan) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *Plan) GetFeatures() *structpb.Struct {
	if x != nil {
		return x.Features
	}
	return nil
}

func (x *Plan) GetQuotas() *structpb.Struct {
	if x != nil {
		return x.Quotas
	}
	return nil
}

func (x *Plan) GetDefault() bool {
	if x != nil {
		return x.Default
	}
	return false
}

type ListAvailablePlansResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Plans []*Plan `protobuf:"bytes,1,rep,name=plans,proto3" json:"plans,omitempty"`
}

func (x *ListAvailablePlansResponse) Reset() {
	*x = ListAvailablePlansResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAvailablePlansResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAvailablePlansResponse) ProtoMessage() {}

func (x *ListAvailablePlansResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAvailablePlansResponse.ProtoReflect.Descriptor instead.
func (*ListAvailablePlansResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{68}
}

func (x *ListAvailablePlansResponse) GetPlans() []*Plan {
	if x != nil {
		return x.Plans
	}
	return nil
}

type UpdateOrganizationBillingPlanRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrganizationId string `protobuf:"bytes,1,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	Plan           string `protobuf:"bytes,2,opt,name=plan,proto3" json:"plan,omitempty"`
	Audit          *Audit `protobuf:"bytes,3,opt,name=audit,proto3" json:"audit,omitempty"`
}

func (x *UpdateOrganizationBillingPlanRequest) Reset() {
	*x = UpdateOrganizationBillingPlanRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOrganizationBillingPlanRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOrganizationBillingPlanRequest) ProtoMessage() {}

func (x *UpdateOrganizationBillingPlanRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOrganizationBillingPlanRequest.ProtoReflect.Descriptor instead.
func (*UpdateOrganizationBillingPlanRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{69}
}

func (x *UpdateOrganizationBillingPlanRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *UpdateOrganizationBillingPlanRequest) GetPlan() string {
	if x != nil {
		return x.Plan
	}
	return ""
}

func (x *UpdateOrganizationBillingPlanRequest) GetAudit() *Audit {
	if x != nil {
		return x.Audit
	}
	return nil
}

type UpdateOrganizationBillingPlanResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateOrganizationBillingPlanResponse) Reset() {
	*x = UpdateOrganizationBillingPlanResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOrganizationBillingPlanResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOrganizationBillingPlanResponse) ProtoMessage() {}

func (x *UpdateOrganizationBillingPlanResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOrganizationBillingPlanResponse.ProtoReflect.Descriptor instead.
func (*UpdateOrganizationBillingPlanResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{70}
}

type GetKubeVisionUsageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrganizationId string                 `protobuf:"bytes,1,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	StartTime      *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
	EndTime        *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
}

func (x *GetKubeVisionUsageRequest) Reset() {
	*x = GetKubeVisionUsageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetKubeVisionUsageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKubeVisionUsageRequest) ProtoMessage() {}

func (x *GetKubeVisionUsageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKubeVisionUsageRequest.ProtoReflect.Descriptor instead.
func (*GetKubeVisionUsageRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{71}
}

func (x *GetKubeVisionUsageRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *GetKubeVisionUsageRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *GetKubeVisionUsageRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

type GetKubeVisionUsageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Usage []*v11.KubeVisionUsage `protobuf:"bytes,1,rep,name=usage,proto3" json:"usage,omitempty"`
}

func (x *GetKubeVisionUsageResponse) Reset() {
	*x = GetKubeVisionUsageResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetKubeVisionUsageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKubeVisionUsageResponse) ProtoMessage() {}

func (x *GetKubeVisionUsageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKubeVisionUsageResponse.ProtoReflect.Descriptor instead.
func (*GetKubeVisionUsageResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{72}
}

func (x *GetKubeVisionUsageResponse) GetUsage() []*v11.KubeVisionUsage {
	if x != nil {
		return x.Usage
	}
	return nil
}

type ResetMFARequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrganizationId string `protobuf:"bytes,1,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	Email          string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	Audit          *Audit `protobuf:"bytes,3,opt,name=audit,proto3" json:"audit,omitempty"`
}

func (x *ResetMFARequest) Reset() {
	*x = ResetMFARequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResetMFARequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetMFARequest) ProtoMessage() {}

func (x *ResetMFARequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetMFARequest.ProtoReflect.Descriptor instead.
func (*ResetMFARequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{73}
}

func (x *ResetMFARequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *ResetMFARequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *ResetMFARequest) GetAudit() *Audit {
	if x != nil {
		return x.Audit
	}
	return nil
}

type ResetMFAResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ResetMFAResponse) Reset() {
	*x = ResetMFAResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResetMFAResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetMFAResponse) ProtoMessage() {}

func (x *ResetMFAResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetMFAResponse.ProtoReflect.Descriptor instead.
func (*ResetMFAResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{74}
}

type Team struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Description string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	CreateTime  *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	MemberCount int64                  `protobuf:"varint,4,opt,name=member_count,json=memberCount,proto3" json:"member_count,omitempty"`
}

func (x *Team) Reset() {
	*x = Team{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Team) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Team) ProtoMessage() {}

func (x *Team) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Team.ProtoReflect.Descriptor instead.
func (*Team) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{75}
}

func (x *Team) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Team) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Team) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Team) GetMemberCount() int64 {
	if x != nil {
		return x.MemberCount
	}
	return 0
}

type UserTeam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Team     *Team `protobuf:"bytes,1,opt,name=team,proto3" json:"team,omitempty"`
	IsMember bool  `protobuf:"varint,2,opt,name=is_member,json=isMember,proto3" json:"is_member,omitempty"`
}

func (x *UserTeam) Reset() {
	*x = UserTeam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserTeam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserTeam) ProtoMessage() {}

func (x *UserTeam) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserTeam.ProtoReflect.Descriptor instead.
func (*UserTeam) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{76}
}

func (x *UserTeam) GetTeam() *Team {
	if x != nil {
		return x.Team
	}
	return nil
}

func (x *UserTeam) GetIsMember() bool {
	if x != nil {
		return x.IsMember
	}
	return false
}

type ListTeamsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrganizationId string  `protobuf:"bytes,1,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	Limit          *uint32 `protobuf:"varint,2,opt,name=limit,proto3,oneof" json:"limit,omitempty"`
	Offset         *uint32 `protobuf:"varint,3,opt,name=offset,proto3,oneof" json:"offset,omitempty"`
}

func (x *ListTeamsRequest) Reset() {
	*x = ListTeamsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTeamsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTeamsRequest) ProtoMessage() {}

func (x *ListTeamsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTeamsRequest.ProtoReflect.Descriptor instead.
func (*ListTeamsRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{77}
}

func (x *ListTeamsRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *ListTeamsRequest) GetLimit() uint32 {
	if x != nil && x.Limit != nil {
		return *x.Limit
	}
	return 0
}

func (x *ListTeamsRequest) GetOffset() uint32 {
	if x != nil && x.Offset != nil {
		return *x.Offset
	}
	return 0
}

type ListTeamsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Teams []*Team `protobuf:"bytes,1,rep,name=teams,proto3" json:"teams,omitempty"`
	Count uint32  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *ListTeamsResponse) Reset() {
	*x = ListTeamsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTeamsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTeamsResponse) ProtoMessage() {}

func (x *ListTeamsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTeamsResponse.ProtoReflect.Descriptor instead.
func (*ListTeamsResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{78}
}

func (x *ListTeamsResponse) GetTeams() []*Team {
	if x != nil {
		return x.Teams
	}
	return nil
}

func (x *ListTeamsResponse) GetCount() uint32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type TeamMember struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Email string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
}

func (x *TeamMember) Reset() {
	*x = TeamMember{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamMember) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamMember) ProtoMessage() {}

func (x *TeamMember) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamMember.ProtoReflect.Descriptor instead.
func (*TeamMember) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{79}
}

func (x *TeamMember) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TeamMember) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

type ListTeamMembersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrganizationId string `protobuf:"bytes,1,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	TeamName       string `protobuf:"bytes,2,opt,name=team_name,json=teamName,proto3" json:"team_name,omitempty"`
	Limit          *int64 `protobuf:"varint,3,opt,name=limit,proto3,oneof" json:"limit,omitempty"`
	Offset         *int64 `protobuf:"varint,4,opt,name=offset,proto3,oneof" json:"offset,omitempty"`
}

func (x *ListTeamMembersRequest) Reset() {
	*x = ListTeamMembersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTeamMembersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTeamMembersRequest) ProtoMessage() {}

func (x *ListTeamMembersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTeamMembersRequest.ProtoReflect.Descriptor instead.
func (*ListTeamMembersRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{80}
}

func (x *ListTeamMembersRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *ListTeamMembersRequest) GetTeamName() string {
	if x != nil {
		return x.TeamName
	}
	return ""
}

func (x *ListTeamMembersRequest) GetLimit() int64 {
	if x != nil && x.Limit != nil {
		return *x.Limit
	}
	return 0
}

func (x *ListTeamMembersRequest) GetOffset() int64 {
	if x != nil && x.Offset != nil {
		return *x.Offset
	}
	return 0
}

type ListTeamMembersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TeamMembers []*TeamMember `protobuf:"bytes,1,rep,name=team_members,json=teamMembers,proto3" json:"team_members,omitempty"`
	Count       int64         `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *ListTeamMembersResponse) Reset() {
	*x = ListTeamMembersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTeamMembersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTeamMembersResponse) ProtoMessage() {}

func (x *ListTeamMembersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTeamMembersResponse.ProtoReflect.Descriptor instead.
func (*ListTeamMembersResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{81}
}

func (x *ListTeamMembersResponse) GetTeamMembers() []*TeamMember {
	if x != nil {
		return x.TeamMembers
	}
	return nil
}

func (x *ListTeamMembersResponse) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type WorkspaceArgoCDInstance struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *WorkspaceArgoCDInstance) Reset() {
	*x = WorkspaceArgoCDInstance{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkspaceArgoCDInstance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspaceArgoCDInstance) ProtoMessage() {}

func (x *WorkspaceArgoCDInstance) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspaceArgoCDInstance.ProtoReflect.Descriptor instead.
func (*WorkspaceArgoCDInstance) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{82}
}

func (x *WorkspaceArgoCDInstance) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *WorkspaceArgoCDInstance) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type WorkspaceKargoInstance struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *WorkspaceKargoInstance) Reset() {
	*x = WorkspaceKargoInstance{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkspaceKargoInstance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspaceKargoInstance) ProtoMessage() {}

func (x *WorkspaceKargoInstance) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspaceKargoInstance.ProtoReflect.Descriptor instead.
func (*WorkspaceKargoInstance) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{83}
}

func (x *WorkspaceKargoInstance) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *WorkspaceKargoInstance) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type WorkspaceUserMember struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Email string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
}

func (x *WorkspaceUserMember) Reset() {
	*x = WorkspaceUserMember{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[84]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkspaceUserMember) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspaceUserMember) ProtoMessage() {}

func (x *WorkspaceUserMember) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[84]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspaceUserMember.ProtoReflect.Descriptor instead.
func (*WorkspaceUserMember) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{84}
}

func (x *WorkspaceUserMember) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *WorkspaceUserMember) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

type WorkspaceTeamMember struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name        string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	CreateTime  *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	MemberCount int64                  `protobuf:"varint,5,opt,name=member_count,json=memberCount,proto3" json:"member_count,omitempty"`
}

func (x *WorkspaceTeamMember) Reset() {
	*x = WorkspaceTeamMember{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[85]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkspaceTeamMember) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspaceTeamMember) ProtoMessage() {}

func (x *WorkspaceTeamMember) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[85]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspaceTeamMember.ProtoReflect.Descriptor instead.
func (*WorkspaceTeamMember) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{85}
}

func (x *WorkspaceTeamMember) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *WorkspaceTeamMember) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *WorkspaceTeamMember) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *WorkspaceTeamMember) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *WorkspaceTeamMember) GetMemberCount() int64 {
	if x != nil {
		return x.MemberCount
	}
	return 0
}

type WorkspaceMember struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   string              `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Role WorkspaceMemberRole `protobuf:"varint,2,opt,name=role,proto3,enum=akuity.aims.v1.WorkspaceMemberRole" json:"role,omitempty"`
	// Types that are assignable to Member:
	//
	//	*WorkspaceMember_User
	//	*WorkspaceMember_Team
	Member isWorkspaceMember_Member `protobuf_oneof:"member"`
}

func (x *WorkspaceMember) Reset() {
	*x = WorkspaceMember{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[86]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkspaceMember) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspaceMember) ProtoMessage() {}

func (x *WorkspaceMember) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[86]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspaceMember.ProtoReflect.Descriptor instead.
func (*WorkspaceMember) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{86}
}

func (x *WorkspaceMember) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *WorkspaceMember) GetRole() WorkspaceMemberRole {
	if x != nil {
		return x.Role
	}
	return WorkspaceMemberRole_WORKSPACE_MEMBER_ROLE_UNSPECIFIED
}

func (m *WorkspaceMember) GetMember() isWorkspaceMember_Member {
	if m != nil {
		return m.Member
	}
	return nil
}

func (x *WorkspaceMember) GetUser() *WorkspaceUserMember {
	if x, ok := x.GetMember().(*WorkspaceMember_User); ok {
		return x.User
	}
	return nil
}

func (x *WorkspaceMember) GetTeam() *WorkspaceTeamMember {
	if x, ok := x.GetMember().(*WorkspaceMember_Team); ok {
		return x.Team
	}
	return nil
}

type isWorkspaceMember_Member interface {
	isWorkspaceMember_Member()
}

type WorkspaceMember_User struct {
	User *WorkspaceUserMember `protobuf:"bytes,3,opt,name=user,proto3,oneof"`
}

type WorkspaceMember_Team struct {
	Team *WorkspaceTeamMember `protobuf:"bytes,4,opt,name=team,proto3,oneof"`
}

func (*WorkspaceMember_User) isWorkspaceMember_Member() {}

func (*WorkspaceMember_Team) isWorkspaceMember_Member() {}

type WorkspaceMemberRef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Role WorkspaceMemberRole `protobuf:"varint,1,opt,name=role,proto3,enum=akuity.aims.v1.WorkspaceMemberRole" json:"role,omitempty"`
	// Types that are assignable to Member:
	//
	//	*WorkspaceMemberRef_UserId
	//	*WorkspaceMemberRef_UserEmail
	//	*WorkspaceMemberRef_TeamName
	Member isWorkspaceMemberRef_Member `protobuf_oneof:"member"`
}

func (x *WorkspaceMemberRef) Reset() {
	*x = WorkspaceMemberRef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[87]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkspaceMemberRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspaceMemberRef) ProtoMessage() {}

func (x *WorkspaceMemberRef) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[87]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspaceMemberRef.ProtoReflect.Descriptor instead.
func (*WorkspaceMemberRef) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{87}
}

func (x *WorkspaceMemberRef) GetRole() WorkspaceMemberRole {
	if x != nil {
		return x.Role
	}
	return WorkspaceMemberRole_WORKSPACE_MEMBER_ROLE_UNSPECIFIED
}

func (m *WorkspaceMemberRef) GetMember() isWorkspaceMemberRef_Member {
	if m != nil {
		return m.Member
	}
	return nil
}

func (x *WorkspaceMemberRef) GetUserId() string {
	if x, ok := x.GetMember().(*WorkspaceMemberRef_UserId); ok {
		return x.UserId
	}
	return ""
}

func (x *WorkspaceMemberRef) GetUserEmail() string {
	if x, ok := x.GetMember().(*WorkspaceMemberRef_UserEmail); ok {
		return x.UserEmail
	}
	return ""
}

func (x *WorkspaceMemberRef) GetTeamName() string {
	if x, ok := x.GetMember().(*WorkspaceMemberRef_TeamName); ok {
		return x.TeamName
	}
	return ""
}

type isWorkspaceMemberRef_Member interface {
	isWorkspaceMemberRef_Member()
}

type WorkspaceMemberRef_UserId struct {
	UserId string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3,oneof"`
}

type WorkspaceMemberRef_UserEmail struct {
	UserEmail string `protobuf:"bytes,3,opt,name=user_email,json=userEmail,proto3,oneof"`
}

type WorkspaceMemberRef_TeamName struct {
	TeamName string `protobuf:"bytes,4,opt,name=team_name,json=teamName,proto3,oneof"`
}

func (*WorkspaceMemberRef_UserId) isWorkspaceMemberRef_Member() {}

func (*WorkspaceMemberRef_UserEmail) isWorkspaceMemberRef_Member() {}

func (*WorkspaceMemberRef_TeamName) isWorkspaceMemberRef_Member() {}

type CreateWorkspaceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrganizationId string  `protobuf:"bytes,1,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	Name           string  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description    *string `protobuf:"bytes,3,opt,name=description,proto3,oneof" json:"description,omitempty"`
}

func (x *CreateWorkspaceRequest) Reset() {
	*x = CreateWorkspaceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[88]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateWorkspaceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWorkspaceRequest) ProtoMessage() {}

func (x *CreateWorkspaceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[88]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWorkspaceRequest.ProtoReflect.Descriptor instead.
func (*CreateWorkspaceRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{88}
}

func (x *CreateWorkspaceRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *CreateWorkspaceRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateWorkspaceRequest) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

type CreateWorkspaceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Workspace *Workspace `protobuf:"bytes,1,opt,name=workspace,proto3" json:"workspace,omitempty"`
}

func (x *CreateWorkspaceResponse) Reset() {
	*x = CreateWorkspaceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[89]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateWorkspaceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWorkspaceResponse) ProtoMessage() {}

func (x *CreateWorkspaceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[89]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWorkspaceResponse.ProtoReflect.Descriptor instead.
func (*CreateWorkspaceResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{89}
}

func (x *CreateWorkspaceResponse) GetWorkspace() *Workspace {
	if x != nil {
		return x.Workspace
	}
	return nil
}

type Workspace struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              string                     `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name            string                     `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description     string                     `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	CreateTime      *timestamppb.Timestamp     `protobuf:"bytes,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	ArgocdInstances []*WorkspaceArgoCDInstance `protobuf:"bytes,5,rep,name=argocd_instances,json=argocdInstances,proto3" json:"argocd_instances,omitempty"`
	KargoInstances  []*WorkspaceKargoInstance  `protobuf:"bytes,6,rep,name=kargo_instances,json=kargoInstances,proto3" json:"kargo_instances,omitempty"`
	TeamMemberCount uint32                     `protobuf:"varint,7,opt,name=team_member_count,json=teamMemberCount,proto3" json:"team_member_count,omitempty"`
	UserMemberCount uint32                     `protobuf:"varint,8,opt,name=user_member_count,json=userMemberCount,proto3" json:"user_member_count,omitempty"`
	IsDefault       bool                       `protobuf:"varint,9,opt,name=is_default,json=isDefault,proto3" json:"is_default,omitempty"`
}

func (x *Workspace) Reset() {
	*x = Workspace{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[90]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Workspace) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Workspace) ProtoMessage() {}

func (x *Workspace) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[90]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Workspace.ProtoReflect.Descriptor instead.
func (*Workspace) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{90}
}

func (x *Workspace) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Workspace) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Workspace) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Workspace) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Workspace) GetArgocdInstances() []*WorkspaceArgoCDInstance {
	if x != nil {
		return x.ArgocdInstances
	}
	return nil
}

func (x *Workspace) GetKargoInstances() []*WorkspaceKargoInstance {
	if x != nil {
		return x.KargoInstances
	}
	return nil
}

func (x *Workspace) GetTeamMemberCount() uint32 {
	if x != nil {
		return x.TeamMemberCount
	}
	return 0
}

func (x *Workspace) GetUserMemberCount() uint32 {
	if x != nil {
		return x.UserMemberCount
	}
	return 0
}

func (x *Workspace) GetIsDefault() bool {
	if x != nil {
		return x.IsDefault
	}
	return false
}

type ListWorkspacesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrganizationId string  `protobuf:"bytes,1,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	Limit          *uint32 `protobuf:"varint,2,opt,name=limit,proto3,oneof" json:"limit,omitempty"`
	Offset         *uint32 `protobuf:"varint,3,opt,name=offset,proto3,oneof" json:"offset,omitempty"`
}

func (x *ListWorkspacesRequest) Reset() {
	*x = ListWorkspacesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[91]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWorkspacesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkspacesRequest) ProtoMessage() {}

func (x *ListWorkspacesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[91]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkspacesRequest.ProtoReflect.Descriptor instead.
func (*ListWorkspacesRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{91}
}

func (x *ListWorkspacesRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *ListWorkspacesRequest) GetLimit() uint32 {
	if x != nil && x.Limit != nil {
		return *x.Limit
	}
	return 0
}

func (x *ListWorkspacesRequest) GetOffset() uint32 {
	if x != nil && x.Offset != nil {
		return *x.Offset
	}
	return 0
}

type ListWorkspacesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Workspaces []*Workspace `protobuf:"bytes,1,rep,name=workspaces,proto3" json:"workspaces,omitempty"`
	Count      uint32       `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *ListWorkspacesResponse) Reset() {
	*x = ListWorkspacesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[92]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWorkspacesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkspacesResponse) ProtoMessage() {}

func (x *ListWorkspacesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[92]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkspacesResponse.ProtoReflect.Descriptor instead.
func (*ListWorkspacesResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{92}
}

func (x *ListWorkspacesResponse) GetWorkspaces() []*Workspace {
	if x != nil {
		return x.Workspaces
	}
	return nil
}

func (x *ListWorkspacesResponse) GetCount() uint32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type GetWorkspaceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrganizationId string `protobuf:"bytes,1,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	WorkspaceId    string `protobuf:"bytes,2,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"`
}

func (x *GetWorkspaceRequest) Reset() {
	*x = GetWorkspaceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[93]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkspaceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkspaceRequest) ProtoMessage() {}

func (x *GetWorkspaceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[93]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkspaceRequest.ProtoReflect.Descriptor instead.
func (*GetWorkspaceRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{93}
}

func (x *GetWorkspaceRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *GetWorkspaceRequest) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

type GetWorkspaceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Workspace *Workspace `protobuf:"bytes,1,opt,name=workspace,proto3" json:"workspace,omitempty"`
}

func (x *GetWorkspaceResponse) Reset() {
	*x = GetWorkspaceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[94]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkspaceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkspaceResponse) ProtoMessage() {}

func (x *GetWorkspaceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[94]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkspaceResponse.ProtoReflect.Descriptor instead.
func (*GetWorkspaceResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{94}
}

func (x *GetWorkspaceResponse) GetWorkspace() *Workspace {
	if x != nil {
		return x.Workspace
	}
	return nil
}

type ListWorkspaceMembersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrganizationId string  `protobuf:"bytes,1,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	WorkspaceId    string  `protobuf:"bytes,2,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"`
	Limit          *uint32 `protobuf:"varint,3,opt,name=limit,proto3,oneof" json:"limit,omitempty"`
	Offset         *uint32 `protobuf:"varint,4,opt,name=offset,proto3,oneof" json:"offset,omitempty"`
}

func (x *ListWorkspaceMembersRequest) Reset() {
	*x = ListWorkspaceMembersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[95]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWorkspaceMembersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkspaceMembersRequest) ProtoMessage() {}

func (x *ListWorkspaceMembersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[95]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkspaceMembersRequest.ProtoReflect.Descriptor instead.
func (*ListWorkspaceMembersRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{95}
}

func (x *ListWorkspaceMembersRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *ListWorkspaceMembersRequest) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

func (x *ListWorkspaceMembersRequest) GetLimit() uint32 {
	if x != nil && x.Limit != nil {
		return *x.Limit
	}
	return 0
}

func (x *ListWorkspaceMembersRequest) GetOffset() uint32 {
	if x != nil && x.Offset != nil {
		return *x.Offset
	}
	return 0
}

type ListWorkspaceMembersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceMembers []*WorkspaceMember `protobuf:"bytes,1,rep,name=workspace_members,json=workspaceMembers,proto3" json:"workspace_members,omitempty"`
	TeamMemberCount  uint32             `protobuf:"varint,2,opt,name=team_member_count,json=teamMemberCount,proto3" json:"team_member_count,omitempty"`
	UserMemberCount  uint32             `protobuf:"varint,3,opt,name=user_member_count,json=userMemberCount,proto3" json:"user_member_count,omitempty"`
}

func (x *ListWorkspaceMembersResponse) Reset() {
	*x = ListWorkspaceMembersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[96]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWorkspaceMembersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkspaceMembersResponse) ProtoMessage() {}

func (x *ListWorkspaceMembersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[96]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkspaceMembersResponse.ProtoReflect.Descriptor instead.
func (*ListWorkspaceMembersResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{96}
}

func (x *ListWorkspaceMembersResponse) GetWorkspaceMembers() []*WorkspaceMember {
	if x != nil {
		return x.WorkspaceMembers
	}
	return nil
}

func (x *ListWorkspaceMembersResponse) GetTeamMemberCount() uint32 {
	if x != nil {
		return x.TeamMemberCount
	}
	return 0
}

func (x *ListWorkspaceMembersResponse) GetUserMemberCount() uint32 {
	if x != nil {
		return x.UserMemberCount
	}
	return 0
}

type CustomRole struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name        string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Policy      string `protobuf:"bytes,4,opt,name=policy,proto3" json:"policy,omitempty"`
}

func (x *CustomRole) Reset() {
	*x = CustomRole{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[97]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomRole) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomRole) ProtoMessage() {}

func (x *CustomRole) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[97]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomRole.ProtoReflect.Descriptor instead.
func (*CustomRole) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{97}
}

func (x *CustomRole) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CustomRole) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CustomRole) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CustomRole) GetPolicy() string {
	if x != nil {
		return x.Policy
	}
	return ""
}

type ListWorkspaceCustomRolesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrganizationId string  `protobuf:"bytes,1,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	WorkspaceId    string  `protobuf:"bytes,2,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"`
	Limit          *uint32 `protobuf:"varint,3,opt,name=limit,proto3,oneof" json:"limit,omitempty"`
	Offset         *uint32 `protobuf:"varint,4,opt,name=offset,proto3,oneof" json:"offset,omitempty"`
}

func (x *ListWorkspaceCustomRolesRequest) Reset() {
	*x = ListWorkspaceCustomRolesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[98]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWorkspaceCustomRolesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkspaceCustomRolesRequest) ProtoMessage() {}

func (x *ListWorkspaceCustomRolesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[98]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkspaceCustomRolesRequest.ProtoReflect.Descriptor instead.
func (*ListWorkspaceCustomRolesRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{98}
}

func (x *ListWorkspaceCustomRolesRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *ListWorkspaceCustomRolesRequest) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

func (x *ListWorkspaceCustomRolesRequest) GetLimit() uint32 {
	if x != nil && x.Limit != nil {
		return *x.Limit
	}
	return 0
}

func (x *ListWorkspaceCustomRolesRequest) GetOffset() uint32 {
	if x != nil && x.Offset != nil {
		return *x.Offset
	}
	return 0
}

type ListWorkspaceCustomRolesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomRoles []*CustomRole `protobuf:"bytes,1,rep,name=custom_roles,json=customRoles,proto3" json:"custom_roles,omitempty"`
	WorkspaceId string        `protobuf:"bytes,2,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"`
	TotalCount  int64         `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *ListWorkspaceCustomRolesResponse) Reset() {
	*x = ListWorkspaceCustomRolesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[99]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWorkspaceCustomRolesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkspaceCustomRolesResponse) ProtoMessage() {}

func (x *ListWorkspaceCustomRolesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[99]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkspaceCustomRolesResponse.ProtoReflect.Descriptor instead.
func (*ListWorkspaceCustomRolesResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{99}
}

func (x *ListWorkspaceCustomRolesResponse) GetCustomRoles() []*CustomRole {
	if x != nil {
		return x.CustomRoles
	}
	return nil
}

func (x *ListWorkspaceCustomRolesResponse) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

func (x *ListWorkspaceCustomRolesResponse) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type ListOrganizationCustomRolesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrganizationId string  `protobuf:"bytes,1,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	Limit          *uint32 `protobuf:"varint,2,opt,name=limit,proto3,oneof" json:"limit,omitempty"`
	Offset         *uint32 `protobuf:"varint,3,opt,name=offset,proto3,oneof" json:"offset,omitempty"`
}

func (x *ListOrganizationCustomRolesRequest) Reset() {
	*x = ListOrganizationCustomRolesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[100]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOrganizationCustomRolesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrganizationCustomRolesRequest) ProtoMessage() {}

func (x *ListOrganizationCustomRolesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[100]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrganizationCustomRolesRequest.ProtoReflect.Descriptor instead.
func (*ListOrganizationCustomRolesRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{100}
}

func (x *ListOrganizationCustomRolesRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *ListOrganizationCustomRolesRequest) GetLimit() uint32 {
	if x != nil && x.Limit != nil {
		return *x.Limit
	}
	return 0
}

func (x *ListOrganizationCustomRolesRequest) GetOffset() uint32 {
	if x != nil && x.Offset != nil {
		return *x.Offset
	}
	return 0
}

type ListOrganizationCustomRolesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomRoles []*CustomRole `protobuf:"bytes,1,rep,name=custom_roles,json=customRoles,proto3" json:"custom_roles,omitempty"`
	TotalCount  int64         `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *ListOrganizationCustomRolesResponse) Reset() {
	*x = ListOrganizationCustomRolesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[101]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOrganizationCustomRolesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrganizationCustomRolesResponse) ProtoMessage() {}

func (x *ListOrganizationCustomRolesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[101]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrganizationCustomRolesResponse.ProtoReflect.Descriptor instead.
func (*ListOrganizationCustomRolesResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{101}
}

func (x *ListOrganizationCustomRolesResponse) GetCustomRoles() []*CustomRole {
	if x != nil {
		return x.CustomRoles
	}
	return nil
}

func (x *ListOrganizationCustomRolesResponse) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type ListOrganizationUsersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrganizationId string `protobuf:"bytes,1,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
}

func (x *ListOrganizationUsersRequest) Reset() {
	*x = ListOrganizationUsersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[102]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOrganizationUsersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrganizationUsersRequest) ProtoMessage() {}

func (x *ListOrganizationUsersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[102]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrganizationUsersRequest.ProtoReflect.Descriptor instead.
func (*ListOrganizationUsersRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{102}
}

func (x *ListOrganizationUsersRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

type ListOrganizationUsersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Users []*OrganizationUser `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
}

func (x *ListOrganizationUsersResponse) Reset() {
	*x = ListOrganizationUsersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[103]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOrganizationUsersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrganizationUsersResponse) ProtoMessage() {}

func (x *ListOrganizationUsersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[103]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrganizationUsersResponse.ProtoReflect.Descriptor instead.
func (*ListOrganizationUsersResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{103}
}

func (x *ListOrganizationUsersResponse) GetUsers() []*OrganizationUser {
	if x != nil {
		return x.Users
	}
	return nil
}

type WorkspaceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *WorkspaceInfo) Reset() {
	*x = WorkspaceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[104]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkspaceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspaceInfo) ProtoMessage() {}

func (x *WorkspaceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[104]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspaceInfo.ProtoReflect.Descriptor instead.
func (*WorkspaceInfo) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{104}
}

func (x *WorkspaceInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *WorkspaceInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type TeamInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *TeamInfo) Reset() {
	*x = TeamInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[105]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamInfo) ProtoMessage() {}

func (x *TeamInfo) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[105]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamInfo.ProtoReflect.Descriptor instead.
func (*TeamInfo) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{105}
}

func (x *TeamInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TeamInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type OrganizationUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         string           `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Email      string           `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	Role       string           `protobuf:"bytes,3,opt,name=role,proto3" json:"role,omitempty"`
	Workspaces []*WorkspaceInfo `protobuf:"bytes,4,rep,name=workspaces,proto3" json:"workspaces,omitempty"`
	Teams      []*TeamInfo      `protobuf:"bytes,5,rep,name=teams,proto3" json:"teams,omitempty"`
}

func (x *OrganizationUser) Reset() {
	*x = OrganizationUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[106]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrganizationUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrganizationUser) ProtoMessage() {}

func (x *OrganizationUser) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[106]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrganizationUser.ProtoReflect.Descriptor instead.
func (*OrganizationUser) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{106}
}

func (x *OrganizationUser) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *OrganizationUser) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *OrganizationUser) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *OrganizationUser) GetWorkspaces() []*WorkspaceInfo {
	if x != nil {
		return x.Workspaces
	}
	return nil
}

func (x *OrganizationUser) GetTeams() []*TeamInfo {
	if x != nil {
		return x.Teams
	}
	return nil
}

type AuditLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp string                 `protobuf:"bytes,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Action    string                 `protobuf:"bytes,2,opt,name=action,proto3" json:"action,omitempty"`
	Actor     *AuditLog_AuditActor   `protobuf:"bytes,3,opt,name=actor,proto3" json:"actor,omitempty"`
	Object    *AuditLog_AuditObject  `protobuf:"bytes,4,opt,name=object,proto3" json:"object,omitempty"`
	Details   *AuditLog_AuditDetails `protobuf:"bytes,5,opt,name=details,proto3" json:"details,omitempty"`
}

func (x *AuditLog) Reset() {
	*x = AuditLog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[107]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuditLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuditLog) ProtoMessage() {}

func (x *AuditLog) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[107]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuditLog.ProtoReflect.Descriptor instead.
func (*AuditLog) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{107}
}

func (x *AuditLog) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

func (x *AuditLog) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *AuditLog) GetActor() *AuditLog_AuditActor {
	if x != nil {
		return x.Actor
	}
	return nil
}

func (x *AuditLog) GetObject() *AuditLog_AuditObject {
	if x != nil {
		return x.Object
	}
	return nil
}

func (x *AuditLog) GetDetails() *AuditLog_AuditDetails {
	if x != nil {
		return x.Details
	}
	return nil
}

type InternalAuditFilters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId    []string `protobuf:"bytes,1,rep,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	ObjectType *string  `protobuf:"bytes,2,opt,name=object_type,json=objectType,proto3,oneof" json:"object_type,omitempty"`
	Action     []string `protobuf:"bytes,3,rep,name=action,proto3" json:"action,omitempty"`
	StartTime  *string  `protobuf:"bytes,4,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
	EndTime    *string  `protobuf:"bytes,5,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
	Limit      *uint32  `protobuf:"varint,6,opt,name=limit,proto3,oneof" json:"limit,omitempty"`
	Offset     *uint32  `protobuf:"varint,7,opt,name=offset,proto3,oneof" json:"offset,omitempty"`
}

func (x *InternalAuditFilters) Reset() {
	*x = InternalAuditFilters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[108]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InternalAuditFilters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuditFilters) ProtoMessage() {}

func (x *InternalAuditFilters) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[108]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuditFilters.ProtoReflect.Descriptor instead.
func (*InternalAuditFilters) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{108}
}

func (x *InternalAuditFilters) GetActorId() []string {
	if x != nil {
		return x.ActorId
	}
	return nil
}

func (x *InternalAuditFilters) GetObjectType() string {
	if x != nil && x.ObjectType != nil {
		return *x.ObjectType
	}
	return ""
}

func (x *InternalAuditFilters) GetAction() []string {
	if x != nil {
		return x.Action
	}
	return nil
}

func (x *InternalAuditFilters) GetStartTime() string {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return ""
}

func (x *InternalAuditFilters) GetEndTime() string {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return ""
}

func (x *InternalAuditFilters) GetLimit() uint32 {
	if x != nil && x.Limit != nil {
		return *x.Limit
	}
	return 0
}

func (x *InternalAuditFilters) GetOffset() uint32 {
	if x != nil && x.Offset != nil {
		return *x.Offset
	}
	return 0
}

type GetInternalAuditLogsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filters *InternalAuditFilters `protobuf:"bytes,1,opt,name=filters,proto3" json:"filters,omitempty"`
}

func (x *GetInternalAuditLogsRequest) Reset() {
	*x = GetInternalAuditLogsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[109]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInternalAuditLogsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInternalAuditLogsRequest) ProtoMessage() {}

func (x *GetInternalAuditLogsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[109]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInternalAuditLogsRequest.ProtoReflect.Descriptor instead.
func (*GetInternalAuditLogsRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{109}
}

func (x *GetInternalAuditLogsRequest) GetFilters() *InternalAuditFilters {
	if x != nil {
		return x.Filters
	}
	return nil
}

type GetInternalAuditLogsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items      []*AuditLog `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	TotalCount uint32      `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *GetInternalAuditLogsResponse) Reset() {
	*x = GetInternalAuditLogsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[110]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInternalAuditLogsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInternalAuditLogsResponse) ProtoMessage() {}

func (x *GetInternalAuditLogsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[110]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInternalAuditLogsResponse.ProtoReflect.Descriptor instead.
func (*GetInternalAuditLogsResponse) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{110}
}

func (x *GetInternalAuditLogsResponse) GetItems() []*AuditLog {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *GetInternalAuditLogsResponse) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type GetClusterManifestsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstanceId string `protobuf:"bytes,1,opt,name=instance_id,json=instanceId,proto3" json:"instance_id,omitempty"`
	ClusterId  string `protobuf:"bytes,2,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	Audit      *Audit `protobuf:"bytes,3,opt,name=audit,proto3" json:"audit,omitempty"`
}

func (x *GetClusterManifestsRequest) Reset() {
	*x = GetClusterManifestsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[111]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetClusterManifestsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClusterManifestsRequest) ProtoMessage() {}

func (x *GetClusterManifestsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[111]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClusterManifestsRequest.ProtoReflect.Descriptor instead.
func (*GetClusterManifestsRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{111}
}

func (x *GetClusterManifestsRequest) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

func (x *GetClusterManifestsRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *GetClusterManifestsRequest) GetAudit() *Audit {
	if x != nil {
		return x.Audit
	}
	return nil
}

type GetKargoAgentManifestsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstanceId string `protobuf:"bytes,1,opt,name=instance_id,json=instanceId,proto3" json:"instance_id,omitempty"`
	AgentId    string `protobuf:"bytes,2,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	Audit      *Audit `protobuf:"bytes,3,opt,name=audit,proto3" json:"audit,omitempty"`
}

func (x *GetKargoAgentManifestsRequest) Reset() {
	*x = GetKargoAgentManifestsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[112]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetKargoAgentManifestsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKargoAgentManifestsRequest) ProtoMessage() {}

func (x *GetKargoAgentManifestsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[112]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKargoAgentManifestsRequest.ProtoReflect.Descriptor instead.
func (*GetKargoAgentManifestsRequest) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{112}
}

func (x *GetKargoAgentManifestsRequest) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

func (x *GetKargoAgentManifestsRequest) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *GetKargoAgentManifestsRequest) GetAudit() *Audit {
	if x != nil {
		return x.Audit
	}
	return nil
}

type AuditLog_AuditActor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type string  `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Id   string  `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Ip   *string `protobuf:"bytes,3,opt,name=ip,proto3,oneof" json:"ip,omitempty"`
}

func (x *AuditLog_AuditActor) Reset() {
	*x = AuditLog_AuditActor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[116]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuditLog_AuditActor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuditLog_AuditActor) ProtoMessage() {}

func (x *AuditLog_AuditActor) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[116]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuditLog_AuditActor.ProtoReflect.Descriptor instead.
func (*AuditLog_AuditActor) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{107, 0}
}

func (x *AuditLog_AuditActor) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *AuditLog_AuditActor) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AuditLog_AuditActor) GetIp() string {
	if x != nil && x.Ip != nil {
		return *x.Ip
	}
	return ""
}

type AuditLog_AuditObject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Id   string `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *AuditLog_AuditObject) Reset() {
	*x = AuditLog_AuditObject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[117]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuditLog_AuditObject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuditLog_AuditObject) ProtoMessage() {}

func (x *AuditLog_AuditObject) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[117]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuditLog_AuditObject.ProtoReflect.Descriptor instead.
func (*AuditLog_AuditObject) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{107, 1}
}

func (x *AuditLog_AuditObject) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *AuditLog_AuditObject) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type AuditLog_AuditDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Patch   string `protobuf:"bytes,2,opt,name=patch,proto3" json:"patch,omitempty"`
}

func (x *AuditLog_AuditDetails) Reset() {
	*x = AuditLog_AuditDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_aims_v1_aims_proto_msgTypes[118]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuditLog_AuditDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuditLog_AuditDetails) ProtoMessage() {}

func (x *AuditLog_AuditDetails) ProtoReflect() protoreflect.Message {
	mi := &file_aims_v1_aims_proto_msgTypes[118]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuditLog_AuditDetails.ProtoReflect.Descriptor instead.
func (*AuditLog_AuditDetails) Descriptor() ([]byte, []int) {
	return file_aims_v1_aims_proto_rawDescGZIP(), []int{107, 2}
}

func (x *AuditLog_AuditDetails) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *AuditLog_AuditDetails) GetPatch() string {
	if x != nil {
		return x.Patch
	}
	return ""
}

var File_aims_v1_aims_proto protoreflect.FileDescriptor

var file_aims_v1_aims_proto_rawDesc = []byte{
	0x0a, 0x12, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d,
	0x73, 0x2e, 0x76, 0x31, 0x1a, 0x16, 0x61, 0x72, 0x67, 0x6f, 0x63, 0x64, 0x2f, 0x76, 0x31, 0x2f,
	0x61, 0x72, 0x67, 0x6f, 0x63, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x68, 0x74, 0x74, 0x70, 0x62, 0x6f, 0x64, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x14, 0x6b, 0x61, 0x72, 0x67, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x6b, 0x61, 0x72, 0x67,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x2f, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x66,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xaf, 0x02,
	0x0a, 0x17, 0x53, 0x65, 0x6e, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x09, 0x74, 0x65, 0x73,
	0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x08,
	0x74, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a, 0x0f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x05, 0x61, 0x75, 0x64,
	0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74,
	0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52,
	0x05, 0x61, 0x75, 0x64, 0x69, 0x74, 0x12, 0x40, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74,
	0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x08,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x33, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x42, 0x0c, 0x0a,
	0x0a, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x22,
	0x1a, 0x0a, 0x18, 0x53, 0x65, 0x6e, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xe4, 0x01, 0x0a, 0x12,
	0x41, 0x72, 0x67, 0x6f, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x12, 0x17, 0x0a, 0x04, 0x70, 0x61, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x48, 0x00, 0x52, 0x04, 0x70, 0x61, 0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x75,
	0x6e, 0x70, 0x61, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52, 0x06, 0x75,
	0x6e, 0x70, 0x61, 0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x75, 0x7a, 0x7a,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x75, 0x7a, 0x7a, 0x12, 0x20, 0x0a, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x02, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x88, 0x01, 0x01, 0x12, 0x2c,
	0x0a, 0x0f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05,
	0x5f, 0x70, 0x61, 0x69, 0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x75, 0x6e, 0x70, 0x61, 0x69, 0x64,
	0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x42, 0x12,
	0x0a, 0x10, 0x5f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x22, 0x66, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x72, 0x67, 0x6f, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3f,
	0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x72, 0x67, 0x6f, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x48, 0x00, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01, 0x42,
	0x09, 0x0a, 0x07, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x5b, 0x0a, 0x19, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x72, 0x67, 0x6f, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3e, 0x0a, 0x09, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x6b, 0x75,
	0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x09, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x22, 0x6b, 0x0a, 0x1b, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x55, 0x6e, 0x70, 0x61, 0x69, 0x64, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x05, 0x61, 0x75, 0x64, 0x69, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e,
	0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x05, 0x61,
	0x75, 0x64, 0x69, 0x74, 0x22, 0x1e, 0x0a, 0x1c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x6e,
	0x70, 0x61, 0x69, 0x64, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x81, 0x01, 0x0a, 0x1c, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79,
	0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x52, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12, 0x2b, 0x0a, 0x05, 0x61,
	0x75, 0x64, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x6b, 0x75,
	0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x64, 0x69,
	0x74, 0x52, 0x05, 0x61, 0x75, 0x64, 0x69, 0x74, 0x22, 0x1f, 0x0a, 0x1d, 0x4f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x97, 0x01, 0x0a, 0x12, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x46, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0x39, 0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0xf0, 0x03, 0x0a, 0x10, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x36, 0x0a, 0x08, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x6b, 0x75,
	0x69, 0x74, 0x79, 0x2e, 0x61, 0x72, 0x67, 0x6f, 0x63, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x08, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x12, 0x40, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x48, 0x00, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x12, 0x2d, 0x0a, 0x12,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x12, 0x37, 0x0a, 0x15, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x13, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x49, 0x6e, 0x66,
	0x6f, 0x88, 0x01, 0x01, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x72, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49, 0x64, 0x12, 0x53, 0x0a, 0x13, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74,
	0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x12, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x3c, 0x0a, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x48, 0x02,
	0x52, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0e,
	0x0a, 0x0c, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x18,
	0x0a, 0x16, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x22, 0xc5, 0x01, 0x0a, 0x08, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x49, 0x64,
	0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0c, 0x62, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x0b, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x28, 0x0a, 0x0d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x0c, 0x62, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f,
	0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x10, 0x0a, 0x0e,
	0x5f, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0x49,
	0x0a, 0x1e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x67, 0x0a, 0x1f, 0x4c, 0x69, 0x73,
	0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x44, 0x0a, 0x07,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x56, 0x65, 0x72,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x64, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x73, 0x22, 0xbe, 0x01, 0x0a, 0x20, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x44, 0x0a, 0x07, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x73, 0x12, 0x2b, 0x0a, 0x05, 0x61, 0x75, 0x64, 0x69, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61,
	0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x05, 0x61, 0x75,
	0x64, 0x69, 0x74, 0x22, 0x69, 0x0a, 0x21, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x44, 0x0a, 0x07, 0x64, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x6b, 0x75, 0x69,
	0x74, 0x79, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x73, 0x22, 0x49,
	0x0a, 0x1e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x2b, 0x0a, 0x13, 0x4f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0xda, 0x01, 0x0a, 0x1f, 0x4c, 0x69, 0x73, 0x74, 0x4f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x56, 0x0a, 0x07, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x61, 0x6b,
	0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x1a, 0x5f, 0x0a, 0x0c, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x39, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x22, 0x0a, 0x20, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x6e, 0x62, 0x69, 0x6c,
	0x6c, 0x65, 0x64, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x6c, 0x0a, 0x21, 0x4c, 0x69, 0x73, 0x74, 0x55,
	0x6e, 0x62, 0x69, 0x6c, 0x6c, 0x65, 0x64, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x0d,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x73, 0x69, 0x63, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xc3, 0x03, 0x0a, 0x12, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x05,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x00, 0x52, 0x05, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x01, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65,
	0x74, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x66, 0x75, 0x7a, 0x7a, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x02, 0x52, 0x04, 0x66, 0x75, 0x7a, 0x7a, 0x88, 0x01, 0x01, 0x12, 0x43, 0x0a,
	0x10, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x62, 0x79, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79,
	0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x48, 0x03, 0x52,
	0x0e, 0x73, 0x6f, 0x72, 0x74, 0x42, 0x79, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88,
	0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x62, 0x69, 0x6c, 0x6c, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x08, 0x48, 0x04, 0x52, 0x06, 0x62, 0x69, 0x6c, 0x6c, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x30, 0x0a, 0x11, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x6c, 0x79, 0x5f, 0x76, 0x65, 0x72, 0x69,
	0x66, 0x69, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x48, 0x05, 0x52, 0x10, 0x6d, 0x61,
	0x6e, 0x75, 0x61, 0x6c, 0x6c, 0x79, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x88, 0x01,
	0x01, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x6c, 0x61, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x05, 0x70, 0x6c, 0x61, 0x6e, 0x73, 0x12, 0x22, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x48, 0x06, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x65,
	0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x48, 0x07, 0x52,
	0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74,
	0x42, 0x07, 0x0a, 0x05, 0x5f, 0x66, 0x75, 0x7a, 0x7a, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x73, 0x6f,
	0x72, 0x74, 0x5f, 0x62, 0x79, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x09,
	0x0a, 0x07, 0x5f, 0x62, 0x69, 0x6c, 0x6c, 0x65, 0x64, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x6d, 0x61,
	0x6e, 0x75, 0x61, 0x6c, 0x6c, 0x79, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x0b,
	0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x6c, 0x0a, 0x1b, 0x4c,
	0x69, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x41, 0x0a, 0x07, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x6b,
	0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x48,
	0x00, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a,
	0x08, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x22, 0x7d, 0x0a, 0x1c, 0x4c, 0x69, 0x73,
	0x74, 0x41, 0x6c, 0x6c, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x0d, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x61, 0x73, 0x69, 0x63, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x41, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x60, 0x0a, 0x17, 0x47,
	0x65, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x45, 0x0a, 0x0c, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61,
	0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61,
	0x73, 0x69, 0x63, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x0c, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x72, 0x0a,
	0x0a, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x4e, 0x0a, 0x15, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x13, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x73, 0x74, 0x61, 0x6c,
	0x65, 0x22, 0xd1, 0x06, 0x0a, 0x11, 0x42, 0x61, 0x73, 0x69, 0x63, 0x4f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x62,
	0x69, 0x6c, 0x6c, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x62, 0x69, 0x6c,
	0x6c, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x06, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x6d,
	0x61, 0x6e, 0x75, 0x61, 0x6c, 0x6c, 0x79, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x6c, 0x79,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6e, 0x75, 0x6d, 0x5f,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0c, 0x6e, 0x75, 0x6d, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x6c, 0x61, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x6c, 0x61,
	0x6e, 0x12, 0x47, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x00, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x41, 0x0a, 0x05, 0x71, 0x75,
	0x6f, 0x74, 0x61, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x6b, 0x75, 0x69,
	0x74, 0x79, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x52, 0x05, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x12, 0x41, 0x0a,
	0x05, 0x75, 0x73, 0x61, 0x67, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61,
	0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x66, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x05, 0x75, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x4f, 0x0a, 0x0f, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x6b, 0x75, 0x69,
	0x74, 0x79, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x0e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e,
	0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x0a, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x4e,
	0x0a, 0x12, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x01, 0x52, 0x11, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x88, 0x01, 0x01, 0x12, 0x1a,
	0x0a, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x61,
	0x6e, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09,
	0x63, 0x61, 0x6e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x3f, 0x0a, 0x04, 0x6d, 0x69, 0x73,
	0x63, 0x18, 0x12, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79,
	0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x73, 0x69, 0x63, 0x4f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4d, 0x69, 0x73, 0x63, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x6d, 0x69, 0x73, 0x63, 0x1a, 0x37, 0x0a, 0x09, 0x4d, 0x69,
	0x73, 0x63, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x15,
	0x0a, 0x13, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x8c, 0x01, 0x0a, 0x09, 0x50, 0x6c, 0x61, 0x6e, 0x55, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x70,
	0x6c, 0x61, 0x6e, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x50, 0x6c, 0x61, 0x6e, 0x65, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1a,
	0x0a, 0x08, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x22, 0x35, 0x0a, 0x05, 0x41, 0x75, 0x64, 0x69, 0x74, 0x12, 0x14, 0x0a,
	0x05, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0xb0, 0x01, 0x0a, 0x18,
	0x50, 0x61, 0x74, 0x63, 0x68, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x47, 0x61, 0x74, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x57, 0x0a, 0x0d, 0x66, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x32, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x66,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x47, 0x61,
	0x74, 0x65, 0x73, 0x52, 0x0c, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x47, 0x61, 0x74, 0x65,
	0x73, 0x12, 0x2b, 0x0a, 0x05, 0x61, 0x75, 0x64, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x05, 0x61, 0x75, 0x64, 0x69, 0x74, 0x22, 0x74,
	0x0a, 0x19, 0x50, 0x61, 0x74, 0x63, 0x68, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x47, 0x61,
	0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x57, 0x0a, 0x0d, 0x66,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x32, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x47, 0x61, 0x74, 0x65, 0x73, 0x52, 0x0c, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x47,
	0x61, 0x74, 0x65, 0x73, 0x22, 0x28, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x47, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0xd2,
	0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x47, 0x61, 0x74,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x57, 0x0a, 0x0d, 0x66, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x32, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x47, 0x61, 0x74, 0x65, 0x73, 0x52, 0x0c, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x47, 0x61,
	0x74, 0x65, 0x73, 0x12, 0x5e, 0x0a, 0x14, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x66, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x47, 0x61, 0x74, 0x65, 0x73, 0x52,
	0x12, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x47, 0x61,
	0x74, 0x65, 0x73, 0x22, 0x95, 0x01, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x51, 0x75,
	0x6f, 0x74, 0x61, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2b, 0x0a, 0x05, 0x61,
	0x75, 0x64, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x6b, 0x75,
	0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x64, 0x69,
	0x74, 0x52, 0x05, 0x61, 0x75, 0x64, 0x69, 0x74, 0x12, 0x41, 0x0a, 0x05, 0x71, 0x75, 0x6f, 0x74,
	0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x51,
	0x75, 0x6f, 0x74, 0x61, 0x52, 0x05, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x22, 0xc0, 0x01, 0x0a, 0x14,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x41, 0x0a, 0x05, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x6f, 0x74, 0x61,
	0x52, 0x05, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x12, 0x4e, 0x0a, 0x12, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48,
	0x00, 0x52, 0x11, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x88, 0x01, 0x01, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0xab,
	0x01, 0x0a, 0x28, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x69, 0x61, 0x6c, 0x45, 0x78, 0x70, 0x69, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x74, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0f,
	0x74, 0x72, 0x69, 0x61, 0x6c, 0x45, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x2b, 0x0a, 0x05, 0x61, 0x75, 0x64, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x05, 0x61, 0x75, 0x64, 0x69, 0x74, 0x22, 0x2b, 0x0a, 0x29,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x72, 0x69, 0x61, 0x6c, 0x45, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x72, 0x0a, 0x22, 0x44, 0x65, 0x63,
	0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x47, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x2b, 0x0a, 0x05, 0x61, 0x75, 0x64, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x05, 0x61, 0x75, 0x64, 0x69, 0x74, 0x22, 0x25, 0x0a,
	0x23, 0x44, 0x65, 0x63, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x39, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f,
	0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22,
	0x57, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x79,
	0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3c, 0x0a, 0x08, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61,
	0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x08,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x22, 0x3e, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x4b,
	0x61, 0x72, 0x67, 0x6f, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x79, 0x49, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0x61, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x4b,
	0x61, 0x72, 0x67, 0x6f, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x79, 0x49, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x41, 0x0a, 0x08, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x6b, 0x75,
	0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x4b, 0x61, 0x72, 0x67, 0x6f, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x52, 0x08, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x22, 0x53, 0x0a, 0x0d, 0x43,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04,
	0x66, 0x75, 0x7a, 0x7a, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x75, 0x7a, 0x7a,
	0x12, 0x20, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x88,
	0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d,
	0x22, 0x78, 0x0a, 0x1e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73,
	0x46, 0x6f, 0x72, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x58, 0x0a, 0x1f, 0x4c, 0x69,
	0x73, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x35, 0x0a,
	0x08, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x72, 0x67, 0x6f, 0x63, 0x64, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x52, 0x08, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x73, 0x22, 0x44, 0x0a, 0x21, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x73, 0x46, 0x6f, 0x72, 0x4b, 0x61, 0x72, 0x67, 0x6f, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0x59, 0x0a, 0x22, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x4b, 0x61, 0x72, 0x67, 0x6f,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x33, 0x0a, 0x06, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x6b, 0x61, 0x72, 0x67, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x4b, 0x61, 0x72, 0x67, 0x6f, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x06, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x8e, 0x01, 0x0a, 0x1a, 0x53, 0x65, 0x74, 0x4d, 0x61, 0x6e,
	0x75, 0x61, 0x6c, 0x6c, 0x79, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x2b, 0x0a, 0x05, 0x61, 0x75, 0x64,
	0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74,
	0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52,
	0x05, 0x61, 0x75, 0x64, 0x69, 0x74, 0x22, 0x1d, 0x0a, 0x1b, 0x53, 0x65, 0x74, 0x4d, 0x61, 0x6e,
	0x75, 0x61, 0x6c, 0x6c, 0x79, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x6d, 0x0a, 0x22, 0x53, 0x65, 0x74, 0x44, 0x69, 0x73, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x64,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x64,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x2b, 0x0a, 0x05, 0x61, 0x75, 0x64, 0x69, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e,
	0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x05, 0x61,
	0x75, 0x64, 0x69, 0x74, 0x22, 0x25, 0x0a, 0x23, 0x53, 0x65, 0x74, 0x44, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x71, 0x0a, 0x19, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x2b, 0x0a, 0x05, 0x61, 0x75, 0x64, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x05, 0x61, 0x75, 0x64, 0x69, 0x74, 0x22, 0x1c,
	0x0a, 0x1a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1a, 0x0a, 0x18,
	0x47, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x55, 0x0a, 0x0e, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x43, 0x0a, 0x1e, 0x64, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x66, 0x72, 0x65, 0x65, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x1b, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x72, 0x65, 0x65, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0x53, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x06,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61,
	0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x22, 0x9c, 0x03, 0x0a, 0x15, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x4b, 0x61, 0x72, 0x67, 0x6f, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x3a,
	0x0a, 0x08, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x6b, 0x61, 0x72, 0x67, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x4b, 0x61, 0x72, 0x67, 0x6f, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x52, 0x08, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x45, 0x0a, 0x0c, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x61, 0x73, 0x69, 0x63, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x4e, 0x0a, 0x12, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x11, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x88, 0x01,
	0x01, 0x12, 0x3c, 0x0a, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69,
	0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x48,
	0x01, 0x52, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x3d, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x48, 0x02, 0x52,
	0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x88, 0x01, 0x01, 0x42, 0x15,
	0x0a, 0x13, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x22, 0xe5, 0x01, 0x0a, 0x13, 0x4b, 0x61, 0x72, 0x67, 0x6f, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x04, 0x70,
	0x61, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x04, 0x70, 0x61, 0x69,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x75, 0x6e, 0x70, 0x61, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52, 0x06, 0x75, 0x6e, 0x70, 0x61, 0x69, 0x64, 0x88, 0x01,
	0x01, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x75, 0x7a, 0x7a, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x66, 0x75, 0x7a, 0x7a, 0x12, 0x20, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x66, 0x72,
	0x6f, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65,
	0x46, 0x72, 0x6f, 0x6d, 0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a, 0x0f, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x03, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x70, 0x61, 0x69, 0x64, 0x42, 0x09,
	0x0a, 0x07, 0x5f, 0x75, 0x6e, 0x70, 0x61, 0x69, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x22, 0x68, 0x0a, 0x19, 0x4c,
	0x69, 0x73, 0x74, 0x4b, 0x61, 0x72, 0x67, 0x6f, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x40, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74,
	0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x61, 0x72, 0x67, 0x6f, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x48, 0x00, 0x52,
	0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x61, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x4b, 0x61, 0x72,
	0x67, 0x6f, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x43, 0x0a, 0x09, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e,
	0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x4b, 0x61, 0x72, 0x67, 0x6f, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x09, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x22, 0xbb, 0x01, 0x0a, 0x21, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4d, 0x61, 0x69, 0x6e,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f,
	0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x29,
	0x0a, 0x10, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x6e, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61,
	0x6e, 0x65, 0x6e, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x2b, 0x0a, 0x05, 0x61, 0x75, 0x64,
	0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74,
	0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52,
	0x05, 0x61, 0x75, 0x64, 0x69, 0x74, 0x22, 0x24, 0x0a, 0x22, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x90, 0x01, 0x0a,
	0x14, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x75, 0x64, 0x69, 0x74, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x43,
	0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x48, 0x00, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73,
	0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x22,
	0x6e, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x75, 0x64, 0x69, 0x74, 0x4c, 0x6f, 0x67, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a, 0x0a, 0x61, 0x75, 0x64, 0x69,
	0x74, 0x5f, 0x6c, 0x6f, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61,
	0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x4c, 0x6f, 0x67, 0x52, 0x09,
	0x61, 0x75, 0x64, 0x69, 0x74, 0x4c, 0x6f, 0x67, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22,
	0x1b, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x50, 0x6c, 0x61, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xb9, 0x01, 0x0a,
	0x04, 0x50, 0x6c, 0x61, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x08, 0x66, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x52, 0x08, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x12, 0x2f, 0x0a,
	0x06, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x06, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x22, 0x48, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x6e, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2a, 0x0a, 0x05, 0x70, 0x6c, 0x61, 0x6e, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61,
	0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x52, 0x05, 0x70, 0x6c, 0x61,
	0x6e, 0x73, 0x22, 0x90, 0x01, 0x0a, 0x24, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x50, 0x6c, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6c, 0x61, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x70, 0x6c, 0x61, 0x6e, 0x12, 0x2b, 0x0a, 0x05, 0x61, 0x75, 0x64, 0x69,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79,
	0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x05,
	0x61, 0x75, 0x64, 0x69, 0x74, 0x22, 0x27, 0x0a, 0x25, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x50, 0x6c, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xdc,
	0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4b, 0x75, 0x62, 0x65, 0x56, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0f,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x3a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x48, 0x01, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01,
	0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x5b, 0x0a,
	0x1a, 0x47, 0x65, 0x74, 0x4b, 0x75, 0x62, 0x65, 0x56, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x73,
	0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d, 0x0a, 0x05, 0x75,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x6b, 0x75,
	0x69, 0x74, 0x79, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x75, 0x62, 0x65, 0x56, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x73,
	0x61, 0x67, 0x65, 0x52, 0x05, 0x75, 0x73, 0x61, 0x67, 0x65, 0x22, 0x7d, 0x0a, 0x0f, 0x52, 0x65,
	0x73, 0x65, 0x74, 0x4d, 0x46, 0x41, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a,
	0x0f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x2b, 0x0a, 0x05,
	0x61, 0x75, 0x64, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x6b,
	0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x64,
	0x69, 0x74, 0x52, 0x05, 0x61, 0x75, 0x64, 0x69, 0x74, 0x22, 0x12, 0x0a, 0x10, 0x52, 0x65, 0x73,
	0x65, 0x74, 0x4d, 0x46, 0x41, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x9c, 0x01,
	0x0a, 0x04, 0x54, 0x65, 0x61, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x0b,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0b, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x51, 0x0a, 0x08,
	0x55, 0x73, 0x65, 0x72, 0x54, 0x65, 0x61, 0x6d, 0x12, 0x28, 0x0a, 0x04, 0x74, 0x65, 0x61, 0x6d,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e,
	0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x04, 0x74, 0x65,
	0x61, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x22,
	0x88, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x19, 0x0a,
	0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x00, 0x52, 0x05,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73,
	0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x01, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73,
	0x65, 0x74, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x42,
	0x09, 0x0a, 0x07, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x22, 0x55, 0x0a, 0x11, 0x4c, 0x69,
	0x73, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x2a, 0x0a, 0x05, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x65, 0x61, 0x6d, 0x52, 0x05, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0x32, 0x0a, 0x0a, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0xab, 0x01, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65,
	0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65, 0x61,
	0x6d, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65,
	0x61, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x88, 0x01,
	0x01, 0x12, 0x1b, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x48, 0x01, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x88, 0x01, 0x01, 0x42, 0x08,
	0x0a, 0x06, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x6f, 0x66, 0x66,
	0x73, 0x65, 0x74, 0x22, 0x6e, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x4d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d,
	0x0a, 0x0c, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69,
	0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x52, 0x0b, 0x74, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x14, 0x0a,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x22, 0x3d, 0x0a, 0x17, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x41, 0x72, 0x67, 0x6f, 0x43, 0x44, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x22, 0x3c, 0x0a, 0x16, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4b,
	0x61, 0x72, 0x67, 0x6f, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x22, 0x3b, 0x0a, 0x13, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0xbb, 0x01,
	0x0a, 0x13, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x4d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xda, 0x01, 0x0a, 0x0f,
	0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x37, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e,
	0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x57,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x6f,
	0x6c, 0x65, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x12, 0x39, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e,
	0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x48, 0x00, 0x52, 0x04, 0x75,
	0x73, 0x65, 0x72, 0x12, 0x39, 0x0a, 0x04, 0x74, 0x65, 0x61, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x54, 0x65, 0x61, 0x6d,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x48, 0x00, 0x52, 0x04, 0x74, 0x65, 0x61, 0x6d, 0x42, 0x08,
	0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x22, 0xb2, 0x01, 0x0a, 0x12, 0x57, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x66, 0x12,
	0x37, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e,
	0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x57,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x6f,
	0x6c, 0x65, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x12, 0x19, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1d, 0x0a, 0x09, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x08, 0x74, 0x65, 0x61, 0x6d, 0x4e,
	0x61, 0x6d, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x8c, 0x01,
	0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c,
	0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x52, 0x0a, 0x17,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x6b, 0x75,
	0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x22, 0xaa, 0x03, 0x0a, 0x09, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x52, 0x0a, 0x10, 0x61, 0x72, 0x67, 0x6f, 0x63, 0x64, 0x5f, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x6b,
	0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x41, 0x72, 0x67, 0x6f, 0x43, 0x44, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x0f, 0x61, 0x72, 0x67, 0x6f, 0x63, 0x64, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x4f, 0x0a, 0x0f, 0x6b, 0x61, 0x72, 0x67, 0x6f, 0x5f, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4b, 0x61, 0x72, 0x67, 0x6f, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x0e, 0x6b, 0x61, 0x72, 0x67, 0x6f, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0f, 0x74, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x75,
	0x73, 0x65, 0x72, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x22, 0x8d, 0x01,
	0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x19, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x48,
	0x00, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x6f,
	0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x01, 0x52, 0x06, 0x6f,
	0x66, 0x66, 0x73, 0x65, 0x74, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x22, 0x69, 0x0a,
	0x16, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x6b,
	0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x61, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x57,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x27, 0x0a, 0x0f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x22, 0x4f, 0x0a, 0x14, 0x47,
	0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e,
	0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x52, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x22, 0xb6, 0x01, 0x0a,
	0x1b, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0f,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x00, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x48, 0x01, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x88, 0x01, 0x01,
	0x42, 0x08, 0x0a, 0x06, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x6f,
	0x66, 0x66, 0x73, 0x65, 0x74, 0x22, 0xc4, 0x01, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4c, 0x0a, 0x11, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x52, 0x10, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0f, 0x74, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x2a, 0x0a, 0x11, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x75, 0x73, 0x65,
	0x72, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x6a, 0x0a, 0x0a,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x16, 0x0a, 0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x22, 0xba, 0x01, 0x0a, 0x1f, 0x4c, 0x69, 0x73,
	0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x52, 0x6f, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0f,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x00, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x48, 0x01, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x88, 0x01, 0x01,
	0x42, 0x08, 0x0a, 0x06, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x6f,
	0x66, 0x66, 0x73, 0x65, 0x74, 0x22, 0xa5, 0x01, 0x0a, 0x20, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x6c,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d, 0x0a, 0x0c, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x0b, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x9a, 0x01,
	0x0a, 0x22, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x19, 0x0a,
	0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x00, 0x52, 0x05,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73,
	0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x01, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73,
	0x65, 0x74, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x42,
	0x09, 0x0a, 0x07, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x22, 0x85, 0x01, 0x0a, 0x23, 0x4c,
	0x69, 0x73, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x3d, 0x0a, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x72, 0x6f, 0x6c,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74,
	0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x52, 0x6f, 0x6c, 0x65, 0x52, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x6c, 0x65,
	0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x22, 0x47, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x57, 0x0a, 0x1d, 0x4c,
	0x69, 0x73, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55,
	0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x05,
	0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x6b,
	0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x75,
	0x73, 0x65, 0x72, 0x73, 0x22, 0x33, 0x0a, 0x0d, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x2e, 0x0a, 0x08, 0x54, 0x65, 0x61,
	0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xbb, 0x01, 0x0a, 0x10, 0x4f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x12, 0x3d, 0x0a, 0x0a, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61,
	0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x65, 0x61, 0x6d, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e,
	0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x05, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x22, 0xbb, 0x03, 0x0a, 0x08, 0x41, 0x75, 0x64, 0x69,
	0x74, 0x4c, 0x6f, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x05, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x6b, 0x75, 0x69,
	0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74,
	0x4c, 0x6f, 0x67, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x05,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x3c, 0x0a, 0x06, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61,
	0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x4c, 0x6f, 0x67, 0x2e,
	0x41, 0x75, 0x64, 0x69, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x06, 0x6f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x12, 0x3f, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69,
	0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x4c, 0x6f, 0x67, 0x2e, 0x41,
	0x75, 0x64, 0x69, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x07, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x1a, 0x4c, 0x0a, 0x0a, 0x41, 0x75, 0x64, 0x69, 0x74, 0x41, 0x63, 0x74,
	0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x13, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x02, 0x69, 0x70, 0x88, 0x01, 0x01, 0x42, 0x05, 0x0a, 0x03, 0x5f,
	0x69, 0x70, 0x1a, 0x31, 0x0a, 0x0b, 0x41, 0x75, 0x64, 0x69, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x1a, 0x3e, 0x0a, 0x0c, 0x41, 0x75, 0x64, 0x69, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x70, 0x61, 0x74, 0x63, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x70, 0x61, 0x74, 0x63, 0x68, 0x22, 0xac, 0x02, 0x0a, 0x14, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x41, 0x75, 0x64, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x19,
	0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0b, 0x6f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x0a, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x65,
	0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52,
	0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x03, 0x52, 0x05, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x04, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74,
	0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42,
	0x08, 0x0a, 0x06, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x6f, 0x66,
	0x66, 0x73, 0x65, 0x74, 0x22, 0x5d, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x41, 0x75, 0x64, 0x69, 0x74, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x3e, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69,
	0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x41, 0x75,
	0x64, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x73, 0x22, 0x6f, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x41, 0x75, 0x64, 0x69, 0x74, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x4c, 0x6f, 0x67, 0x52, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0x89, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x4d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x05, 0x61, 0x75, 0x64, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x05, 0x61, 0x75, 0x64, 0x69, 0x74,
	0x22, 0x88, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x4b, 0x61, 0x72, 0x67, 0x6f, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x4d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2b,
	0x0a, 0x05, 0x61, 0x75, 0x64, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x75, 0x64, 0x69, 0x74, 0x52, 0x05, 0x61, 0x75, 0x64, 0x69, 0x74, 0x2a, 0x86, 0x01, 0x0a, 0x14,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x12, 0x25, 0x0a, 0x21, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x4e,
	0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x10, 0x01, 0x12, 0x25, 0x0a,
	0x21, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x41,
	0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x46, 0x45, 0x41, 0x54, 0x55,
	0x52, 0x45, 0x10, 0x02, 0x2a, 0x45, 0x0a, 0x04, 0x53, 0x6f, 0x72, 0x74, 0x12, 0x14, 0x0a, 0x10,
	0x53, 0x4f, 0x52, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x4f, 0x52, 0x54, 0x5f, 0x41, 0x53, 0x43, 0x45, 0x4e,
	0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x4f, 0x52, 0x54, 0x5f, 0x44,
	0x45, 0x53, 0x43, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x2a, 0x7f, 0x0a, 0x13, 0x57,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x6f,
	0x6c, 0x65, 0x12, 0x25, 0x0a, 0x21, 0x57, 0x4f, 0x52, 0x4b, 0x53, 0x50, 0x41, 0x43, 0x45, 0x5f,
	0x4d, 0x45, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x57, 0x4f, 0x52,
	0x4b, 0x53, 0x50, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x45, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x52, 0x4f,
	0x4c, 0x45, 0x5f, 0x4d, 0x45, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x57,
	0x4f, 0x52, 0x4b, 0x53, 0x50, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x45, 0x4d, 0x42, 0x45, 0x52, 0x5f,
	0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x41, 0x44, 0x4d, 0x49, 0x4e, 0x10, 0x02, 0x32, 0xbe, 0x3b, 0x0a,
	0x0b, 0x41, 0x69, 0x6d, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x90, 0x01, 0x0a,
	0x0f, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x79, 0x49, 0x64,
	0x12, 0x26, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x79, 0x49,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74,
	0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26, 0x12, 0x24, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x73, 0x2f, 0x7b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x7d, 0x12,
	0xa5, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4b, 0x61, 0x72, 0x67, 0x6f, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x42, 0x79, 0x49, 0x64, 0x12, 0x2b, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74,
	0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4b, 0x61, 0x72,
	0x67, 0x6f, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61,
	0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4b, 0x61, 0x72, 0x67, 0x6f, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x32, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2c, 0x12, 0x2a, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x6b, 0x61, 0x72, 0x67, 0x6f, 0x2f,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x7d, 0x12, 0x9b, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x41, 0x75, 0x64, 0x69, 0x74, 0x4c, 0x6f, 0x67, 0x73,
	0x12, 0x2b, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x41, 0x75, 0x64,
	0x69, 0x74, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e,
	0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x41, 0x75, 0x64, 0x69, 0x74, 0x4c,
	0x6f, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x28, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x22, 0x12, 0x20, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d,
	0x73, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2d, 0x61, 0x75, 0x64, 0x69, 0x74,
	0x2d, 0x6c, 0x6f, 0x67, 0x73, 0x12, 0x8d, 0x01, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x72,
	0x67, 0x6f, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x28, 0x2e, 0x61, 0x6b,
	0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x41, 0x72, 0x67, 0x6f, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61,
	0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x72, 0x67, 0x6f, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x12, 0x1b, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x61, 0x72, 0x67, 0x6f, 0x2f, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x9f, 0x01, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x55, 0x6e, 0x70, 0x61, 0x69, 0x64, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x2b,
	0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x6e, 0x70, 0x61, 0x69, 0x64, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x61, 0x6b,
	0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x55, 0x6e, 0x70, 0x61, 0x69, 0x64, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x26, 0x2a, 0x24, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x7d, 0x12, 0xaa, 0x01, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x55, 0x6e, 0x70, 0x61, 0x69, 0x64, 0x4b, 0x61, 0x72, 0x67, 0x6f, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x2b, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61,
	0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x6e, 0x70,
	0x61, 0x69, 0x64, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x6e, 0x70, 0x61, 0x69, 0x64,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x32, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2c, 0x2a, 0x2a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x6b, 0x61, 0x72, 0x67, 0x6f, 0x2f, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x7d, 0x12, 0x91, 0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x4b, 0x61, 0x72,
	0x67, 0x6f, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x29, 0x2e, 0x61, 0x6b,
	0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x4b, 0x61, 0x72, 0x67, 0x6f, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e,
	0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4b, 0x61, 0x72, 0x67,
	0x6f, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x12, 0x1c, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x6b, 0x61, 0x72, 0x67, 0x6f, 0x2f, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x95, 0x01, 0x0a, 0x15, 0x4f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x12, 0x2c, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x4d, 0x61, 0x6e, 0x75, 0x61,
	0x6c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2d, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x3a, 0x01, 0x2a, 0x22, 0x14, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x12, 0x98, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69,
	0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61,
	0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x34, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2e, 0x12, 0x2c, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x7d, 0x12, 0x99, 0x01, 0x0a, 0x0f,
	0x47, 0x65, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x47, 0x61, 0x74, 0x65, 0x73, 0x12,
	0x26, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x47, 0x61, 0x74, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79,
	0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x47, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x35, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2f, 0x12, 0x2d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x2d, 0x67, 0x61, 0x74, 0x65, 0x73, 0x12, 0xa2, 0x01, 0x0a, 0x11, 0x50, 0x61, 0x74, 0x63,
	0x68, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x47, 0x61, 0x74, 0x65, 0x73, 0x12, 0x28, 0x2e,
	0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x61, 0x74, 0x63, 0x68, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x47, 0x61, 0x74, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79,
	0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x74, 0x63, 0x68, 0x46, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x47, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x38, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x32, 0x3a, 0x01, 0x2a, 0x32, 0x2d, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x66,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x2d, 0x67, 0x61, 0x74, 0x65, 0x73, 0x12, 0xca, 0x01, 0x0a,
	0x1a, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x31, 0x2e, 0x61, 0x6b,
	0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4d, 0x61, 0x69, 0x6e,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32,
	0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4d,
	0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x45, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3f, 0x3a, 0x01, 0x2a, 0x22, 0x3a, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x7d, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x2f, 0x7b, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x7d, 0x12, 0x8c, 0x01, 0x0a, 0x0c, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x73, 0x12, 0x23, 0x2e, 0x61, 0x6b, 0x75,
	0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x24, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x31, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2b, 0x3a, 0x01, 0x2a,
	0x1a, 0x26, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x69, 0x64,
	0x7d, 0x2f, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x73, 0x12, 0xad, 0x01, 0x0a, 0x19, 0x4c, 0x69, 0x73,
	0x74, 0x55, 0x6e, 0x62, 0x69, 0x6c, 0x6c, 0x65, 0x64, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x30, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e,
	0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x6e, 0x62, 0x69,
	0x6c, 0x6c, 0x65, 0x64, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74,
	0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x6e,
	0x62, 0x69, 0x6c, 0x6c, 0x65, 0x64, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2b, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x25, 0x12, 0x23, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d,
	0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x75, 0x6e, 0x62, 0x69, 0x6c, 0x6c, 0x65, 0x64, 0x12, 0x95, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73,
	0x74, 0x41, 0x6c, 0x6c, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x2b, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c,
	0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x22, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x1c, 0x12, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69,
	0x6d, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0xa6, 0x01, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x2e, 0x2e, 0x61,
	0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x61,
	0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2a, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x24, 0x12, 0x22, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x69, 0x6d, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0xd7, 0x01, 0x0a, 0x21, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x72, 0x69, 0x61, 0x6c, 0x45, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x38, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x72, 0x69, 0x61, 0x6c, 0x45, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x61, 0x6b, 0x75, 0x69,
	0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x69,
	0x61, 0x6c, 0x45, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x3d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x37, 0x3a, 0x01, 0x2a, 0x22,
	0x32, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x7d, 0x2f, 0x74, 0x72,
	0x69, 0x61, 0x6c, 0x12, 0xcc, 0x01, 0x0a, 0x1b, 0x44, 0x65, 0x63, 0x72, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x32, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x63, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79,
	0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x63, 0x72, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x44, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x3e, 0x3a, 0x01, 0x2a, 0x22, 0x39, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31,
	0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x2f,
	0x7b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x7d, 0x2f, 0x67, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x64, 0x65, 0x63, 0x72, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0xb1, 0x01, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x2e,
	0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f,
	0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x35, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2f, 0x12, 0x2d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31,
	0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x2f,
	0x7b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x7d, 0x2f, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x12, 0xbe, 0x01, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x4b, 0x61, 0x72, 0x67, 0x6f, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x31, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61,
	0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x73, 0x46, 0x6f, 0x72, 0x4b, 0x61, 0x72, 0x67, 0x6f, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74,
	0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x4b, 0x61, 0x72, 0x67, 0x6f, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x39, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x33, 0x12, 0x31, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69,
	0x6d, 0x73, 0x2f, 0x6b, 0x61, 0x72, 0x67, 0x6f, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x73, 0x2f, 0x7b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x7d,
	0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x12, 0xa7, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x43,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x73, 0x12,
	0x2a, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4d, 0x61, 0x6e, 0x69, 0x66,
	0x65, 0x73, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x42, 0x6f, 0x64,
	0x79, 0x22, 0x4c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x46, 0x12, 0x44, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x73, 0x2f, 0x7b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x7d, 0x2f,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x2f, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x7d, 0x2f, 0x6d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x73, 0x30,
	0x01, 0x12, 0xaf, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4b, 0x61, 0x72, 0x67, 0x6f, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x4d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x73, 0x12, 0x2d, 0x2e, 0x61,
	0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x4b, 0x61, 0x72, 0x67, 0x6f, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4d, 0x61, 0x6e, 0x69, 0x66,
	0x65, 0x73, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x42, 0x6f, 0x64,
	0x79, 0x22, 0x4e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x48, 0x12, 0x46, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x6b, 0x61, 0x72, 0x67, 0x6f, 0x2f, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x7d, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x2f, 0x7b, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x7d, 0x2f, 0x6d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74,
	0x73, 0x30, 0x01, 0x12, 0xb0, 0x01, 0x0a, 0x13, 0x53, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x75, 0x61,
	0x6c, 0x6c, 0x79, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x2a, 0x2e, 0x61, 0x6b,
	0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74,
	0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x6c, 0x79, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79,
	0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x75,
	0x61, 0x6c, 0x6c, 0x79, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x40, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3a, 0x3a, 0x01, 0x2a, 0x22,
	0x35, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x7d, 0x2f, 0x76, 0x65,
	0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0xbf, 0x01, 0x0a, 0x1b, 0x53, 0x65, 0x74, 0x44, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e,
	0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x44, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x61, 0x6b, 0x75,
	0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x44,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x37, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x31, 0x3a, 0x01, 0x2a, 0x22, 0x2c, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0xa1, 0x01, 0x0a, 0x12, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x29, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x6b, 0x75,
	0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x34, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2e, 0x2a, 0x2c,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x7d, 0x12, 0x8e, 0x01, 0x0a,
	0x11, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x28, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x61,
	0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x12,
	0x1c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x8b, 0x01,
	0x0a, 0x10, 0x53, 0x65, 0x6e, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x27, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61, 0x6b,
	0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e,
	0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x3a, 0x01, 0x2a,
	0x22, 0x19, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x9d, 0x01, 0x0a, 0x0d,
	0x4c, 0x69, 0x73, 0x74, 0x41, 0x75, 0x64, 0x69, 0x74, 0x4c, 0x6f, 0x67, 0x73, 0x12, 0x24, 0x2e,
	0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x41, 0x75, 0x64, 0x69, 0x74, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x75, 0x64, 0x69, 0x74, 0x4c, 0x6f,
	0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x3f, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x39, 0x12, 0x37, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73,
	0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x7d,
	0x2f, 0x61, 0x75, 0x64, 0x69, 0x74, 0x2d, 0x6c, 0x6f, 0x67, 0x73, 0x12, 0x87, 0x01, 0x0a, 0x12,
	0x4c, 0x69, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x6c, 0x61,
	0x6e, 0x73, 0x12, 0x29, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x50, 0x6c, 0x61, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e,
	0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x6e,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1a, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x14, 0x12, 0x12, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f,
	0x70, 0x6c, 0x61, 0x6e, 0x73, 0x12, 0xca, 0x01, 0x0a, 0x1d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x50, 0x6c, 0x61, 0x6e, 0x12, 0x34, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79,
	0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x50, 0x6c, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e,
	0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x50, 0x6c, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x3c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x36, 0x3a, 0x01, 0x2a, 0x22,
	0x31, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x7d, 0x2f, 0x70, 0x6c,
	0x61, 0x6e, 0x12, 0xab, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4b, 0x75, 0x62, 0x65, 0x56, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x29, 0x2e, 0x61, 0x6b, 0x75, 0x69,
	0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4b, 0x75,
	0x62, 0x65, 0x56, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69,
	0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4b, 0x75, 0x62, 0x65, 0x56, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x3e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x38, 0x12, 0x36, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x7d, 0x2f, 0x6b, 0x38, 0x73, 0x2f, 0x75, 0x73, 0x61, 0x67, 0x65,
	0x12, 0xb8, 0x01, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x73, 0x12, 0x2e, 0x2e, 0x61,
	0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x61,
	0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x3c, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x36, 0x12, 0x34, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x69, 0x6d, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x7b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x7d, 0x2f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x73, 0x12, 0xc1, 0x01, 0x0a, 0x19,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x73, 0x12, 0x30, 0x2e, 0x61, 0x6b, 0x75, 0x69,
	0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x61, 0x6b,
	0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x3f,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x39, 0x3a, 0x01, 0x2a, 0x22, 0x34, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x7d, 0x2f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x73, 0x12,
	0x70, 0x0a, 0x08, 0x52, 0x65, 0x73, 0x65, 0x74, 0x4d, 0x46, 0x41, 0x12, 0x1f, 0x2e, 0x61, 0x6b,
	0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73,
	0x65, 0x74, 0x4d, 0x46, 0x41, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x61,
	0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x73, 0x65, 0x74, 0x4d, 0x46, 0x41, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x21,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x3a, 0x01, 0x2a, 0x22, 0x16, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x6d, 0x66, 0x61, 0x2f, 0x72, 0x65, 0x73, 0x65,
	0x74, 0x12, 0x8c, 0x01, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x12,
	0x20, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x21, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x3a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x34, 0x12, 0x32, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x7d, 0x2f, 0x74, 0x65, 0x61, 0x6d, 0x73,
	0x12, 0xb2, 0x01, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x12, 0x26, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69,
	0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61,
	0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x4e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x48, 0x12, 0x46, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x7d, 0x2f, 0x74, 0x65, 0x61, 0x6d,
	0x73, 0x2f, 0x7b, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0xa0, 0x01, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x12, 0x25, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74,
	0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x26, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x3f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x39, 0x12,
	0x37, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x7d, 0x2f, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x12, 0xa9, 0x01, 0x0a, 0x0c, 0x47, 0x65, 0x74,
	0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x23, 0x2e, 0x61, 0x6b, 0x75, 0x69,
	0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24,
	0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x4e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x48, 0x12, 0x46, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x7d, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x7d, 0x12, 0xc9, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x2b, 0x2e,
	0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x61, 0x6b, 0x75,
	0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x56, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x50,
	0x12, 0x4e, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x7d, 0x2f, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x7d, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x12, 0xda, 0x01, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x12, 0x2f, 0x2e,
	0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30,
	0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x5b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x55, 0x12, 0x53, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x7d, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x73, 0x2f, 0x7b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x7d,
	0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x2d, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x12, 0xc9, 0x01,
	0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x12, 0x32, 0x2e,
	0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x33, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x41, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3b, 0x12, 0x39,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x7d, 0x2f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x2d, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x12, 0xb0, 0x01, 0x0a, 0x15, 0x4c, 0x69,
	0x73, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73,
	0x65, 0x72, 0x73, 0x12, 0x2c, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2d, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x3a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x34, 0x12, 0x32, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x7d, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x12, 0xc5, 0x01, 0x0a,
	0x13, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x49, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x32, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x49, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74,
	0x79, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x49, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x45, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x3f, 0x12, 0x3d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x69, 0x6d, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x7b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x7d, 0x2f, 0x61, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0xc4, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x41, 0x49, 0x43, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x2e, 0x61, 0x6b, 0x75,
	0x69, 0x74, 0x79, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x49, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x61,
	0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x49, 0x43, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x4a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x44, 0x12, 0x42, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31,
	0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x7d, 0x2f, 0x61, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x1a, 0x10, 0xfa, 0xd2, 0xe4,
	0x93, 0x02, 0x0a, 0x12, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x42, 0xbe, 0x01,
	0x0a, 0x12, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x61, 0x69, 0x6d,
	0x73, 0x2e, 0x76, 0x31, 0x42, 0x09, 0x41, 0x69, 0x6d, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50,
	0x01, 0x5a, 0x43, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x6b,
	0x75, 0x69, 0x74, 0x79, 0x69, 0x6f, 0x2f, 0x61, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x2d, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x70, 0x6b, 0x67, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x61, 0x69, 0x6d, 0x73, 0x2f, 0x76, 0x31, 0x3b,
	0x61, 0x69, 0x6d, 0x73, 0x76, 0x31, 0xa2, 0x02, 0x03, 0x41, 0x41, 0x58, 0xaa, 0x02, 0x0e, 0x41,
	0x6b, 0x75, 0x69, 0x74, 0x79, 0x2e, 0x41, 0x69, 0x6d, 0x73, 0x2e, 0x56, 0x31, 0xca, 0x02, 0x0e,
	0x41, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x5c, 0x41, 0x69, 0x6d, 0x73, 0x5c, 0x56, 0x31, 0xe2, 0x02,
	0x1a, 0x41, 0x6b, 0x75, 0x69, 0x74, 0x79, 0x5c, 0x41, 0x69, 0x6d, 0x73, 0x5c, 0x56, 0x31, 0x5c,
	0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0xea, 0x02, 0x10, 0x41, 0x6b,
	0x75, 0x69, 0x74, 0x79, 0x3a, 0x3a, 0x41, 0x69, 0x6d, 0x73, 0x3a, 0x3a, 0x56, 0x31, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_aims_v1_aims_proto_rawDescOnce sync.Once
	file_aims_v1_aims_proto_rawDescData = file_aims_v1_aims_proto_rawDesc
)

func file_aims_v1_aims_proto_rawDescGZIP() []byte {
	file_aims_v1_aims_proto_rawDescOnce.Do(func() {
		file_aims_v1_aims_proto_rawDescData = protoimpl.X.CompressGZIP(file_aims_v1_aims_proto_rawDescData)
	})
	return file_aims_v1_aims_proto_rawDescData
}

var file_aims_v1_aims_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_aims_v1_aims_proto_msgTypes = make([]protoimpl.MessageInfo, 119)
var file_aims_v1_aims_proto_goTypes = []interface{}{
	(NotificationCategory)(0),                         // 0: akuity.aims.v1.NotificationCategory
	(Sort)(0),                                         // 1: akuity.aims.v1.Sort
	(WorkspaceMemberRole)(0),                          // 2: akuity.aims.v1.WorkspaceMemberRole
	(*SendNotificationRequest)(nil),                   // 3: akuity.aims.v1.SendNotificationRequest
	(*SendNotificationResponse)(nil),                  // 4: akuity.aims.v1.SendNotificationResponse
	(*ArgoInstanceFilter)(nil),                        // 5: akuity.aims.v1.ArgoInstanceFilter
	(*ListArgoInstancesRequest)(nil),                  // 6: akuity.aims.v1.ListArgoInstancesRequest
	(*ListArgoInstancesResponse)(nil),                 // 7: akuity.aims.v1.ListArgoInstancesResponse
	(*DeleteUnpaidInstanceRequest)(nil),               // 8: akuity.aims.v1.DeleteUnpaidInstanceRequest
	(*DeleteUnpaidInstanceResponse)(nil),              // 9: akuity.aims.v1.DeleteUnpaidInstanceResponse
	(*OnboardManualCustomerRequest)(nil),              // 10: akuity.aims.v1.OnboardManualCustomerRequest
	(*OnboardManualCustomerResponse)(nil),             // 11: akuity.aims.v1.OnboardManualCustomerResponse
	(*NotificationConfig)(nil),                        // 12: akuity.aims.v1.NotificationConfig
	(*InternalInstance)(nil),                          // 13: akuity.aims.v1.InternalInstance
	(*Customer)(nil),                                  // 14: akuity.aims.v1.Customer
	(*ListOrganizationDomainsRequest)(nil),            // 15: akuity.aims.v1.ListOrganizationDomainsRequest
	(*ListOrganizationDomainsResponse)(nil),           // 16: akuity.aims.v1.ListOrganizationDomainsResponse
	(*UpdateOrganizationDomainsRequest)(nil),          // 17: akuity.aims.v1.UpdateOrganizationDomainsRequest
	(*UpdateOrganizationDomainsResponse)(nil),         // 18: akuity.aims.v1.UpdateOrganizationDomainsResponse
	(*ListOrganizationMembersRequest)(nil),            // 19: akuity.aims.v1.ListOrganizationMembersRequest
	(*OrganizationMembers)(nil),                       // 20: akuity.aims.v1.OrganizationMembers
	(*ListOrganizationMembersResponse)(nil),           // 21: akuity.aims.v1.ListOrganizationMembersResponse
	(*ListUnbilledOrganizationsRequest)(nil),          // 22: akuity.aims.v1.ListUnbilledOrganizationsRequest
	(*ListUnbilledOrganizationsResponse)(nil),         // 23: akuity.aims.v1.ListUnbilledOrganizationsResponse
	(*OrganizationFilter)(nil),                        // 24: akuity.aims.v1.OrganizationFilter
	(*ListAllOrganizationsRequest)(nil),               // 25: akuity.aims.v1.ListAllOrganizationsRequest
	(*ListAllOrganizationsResponse)(nil),              // 26: akuity.aims.v1.ListAllOrganizationsResponse
	(*GetOrganizationRequest)(nil),                    // 27: akuity.aims.v1.GetOrganizationRequest
	(*GetOrganizationResponse)(nil),                   // 28: akuity.aims.v1.GetOrganizationResponse
	(*StripeData)(nil),                                // 29: akuity.aims.v1.StripeData
	(*BasicOrganization)(nil),                         // 30: akuity.aims.v1.BasicOrganization
	(*PlanUsage)(nil),                                 // 31: akuity.aims.v1.PlanUsage
	(*Audit)(nil),                                     // 32: akuity.aims.v1.Audit
	(*PatchFeatureGatesRequest)(nil),                  // 33: akuity.aims.v1.PatchFeatureGatesRequest
	(*PatchFeatureGatesResponse)(nil),                 // 34: akuity.aims.v1.PatchFeatureGatesResponse
	(*GetFeatureGatesRequest)(nil),                    // 35: akuity.aims.v1.GetFeatureGatesRequest
	(*GetFeatureGatesResponse)(nil),                   // 36: akuity.aims.v1.GetFeatureGatesResponse
	(*UpdateQuotasRequest)(nil),                       // 37: akuity.aims.v1.UpdateQuotasRequest
	(*UpdateQuotasResponse)(nil),                      // 38: akuity.aims.v1.UpdateQuotasResponse
	(*UpdateOrganizationTrialExpirationRequest)(nil),  // 39: akuity.aims.v1.UpdateOrganizationTrialExpirationRequest
	(*UpdateOrganizationTrialExpirationResponse)(nil), // 40: akuity.aims.v1.UpdateOrganizationTrialExpirationResponse
	(*DecrementInstanceGenerationRequest)(nil),        // 41: akuity.aims.v1.DecrementInstanceGenerationRequest
	(*DecrementInstanceGenerationResponse)(nil),       // 42: akuity.aims.v1.DecrementInstanceGenerationResponse
	(*GetInstanceByIdRequest)(nil),                    // 43: akuity.aims.v1.GetInstanceByIdRequest
	(*GetInstanceByIdResponse)(nil),                   // 44: akuity.aims.v1.GetInstanceByIdResponse
	(*GetKargoInstanceByIdRequest)(nil),               // 45: akuity.aims.v1.GetKargoInstanceByIdRequest
	(*GetKargoInstanceByIdResponse)(nil),              // 46: akuity.aims.v1.GetKargoInstanceByIdResponse
	(*ClusterFilter)(nil),                             // 47: akuity.aims.v1.ClusterFilter
	(*ListClustersForInstanceRequest)(nil),            // 48: akuity.aims.v1.ListClustersForInstanceRequest
	(*ListClustersForInstanceResponse)(nil),           // 49: akuity.aims.v1.ListClustersForInstanceResponse
	(*ListAgentsForKargoInstanceRequest)(nil),         // 50: akuity.aims.v1.ListAgentsForKargoInstanceRequest
	(*ListAgentsForKargoInstanceResponse)(nil),        // 51: akuity.aims.v1.ListAgentsForKargoInstanceResponse
	(*SetManuallyVerifiedRequest)(nil),                // 52: akuity.aims.v1.SetManuallyVerifiedRequest
	(*SetManuallyVerifiedResponse)(nil),               // 53: akuity.aims.v1.SetManuallyVerifiedResponse
	(*SetDisabledInstanceCreationRequest)(nil),        // 54: akuity.aims.v1.SetDisabledInstanceCreationRequest
	(*SetDisabledInstanceCreationResponse)(nil),       // 55: akuity.aims.v1.SetDisabledInstanceCreationResponse
	(*DeleteOrganizationRequest)(nil),                 // 56: akuity.aims.v1.DeleteOrganizationRequest
	(*DeleteOrganizationResponse)(nil),                // 57: akuity.aims.v1.DeleteOrganizationResponse
	(*GetInternalConfigRequest)(nil),                  // 58: akuity.aims.v1.GetInternalConfigRequest
	(*InternalConfig)(nil),                            // 59: akuity.aims.v1.InternalConfig
	(*GetInternalConfigResponse)(nil),                 // 60: akuity.aims.v1.GetInternalConfigResponse
	(*InternalKargoInstance)(nil),                     // 61: akuity.aims.v1.InternalKargoInstance
	(*KargoInstanceFilter)(nil),                       // 62: akuity.aims.v1.KargoInstanceFilter
	(*ListKargoInstancesRequest)(nil),                 // 63: akuity.aims.v1.ListKargoInstancesRequest
	(*ListKargoInstancesResponse)(nil),                // 64: akuity.aims.v1.ListKargoInstancesResponse
	(*InstanceClusterMaintenanceRequest)(nil),         // 65: akuity.aims.v1.InstanceClusterMaintenanceRequest
	(*InstanceClusterMaintenanceResponse)(nil),        // 66: akuity.aims.v1.InstanceClusterMaintenanceResponse
	(*ListAuditLogsRequest)(nil),                      // 67: akuity.aims.v1.ListAuditLogsRequest
	(*ListAuditLogsResponse)(nil),                     // 68: akuity.aims.v1.ListAuditLogsResponse
	(*ListAvailablePlansRequest)(nil),                 // 69: akuity.aims.v1.ListAvailablePlansRequest
	(*Plan)(nil),                                      // 70: akuity.aims.v1.Plan
	(*ListAvailablePlansResponse)(nil),                // 71: akuity.aims.v1.ListAvailablePlansResponse
	(*UpdateOrganizationBillingPlanRequest)(nil),      // 72: akuity.aims.v1.UpdateOrganizationBillingPlanRequest
	(*UpdateOrganizationBillingPlanResponse)(nil),     // 73: akuity.aims.v1.UpdateOrganizationBillingPlanResponse
	(*GetKubeVisionUsageRequest)(nil),                 // 74: akuity.aims.v1.GetKubeVisionUsageRequest
	(*GetKubeVisionUsageResponse)(nil),                // 75: akuity.aims.v1.GetKubeVisionUsageResponse
	(*ResetMFARequest)(nil),                           // 76: akuity.aims.v1.ResetMFARequest
	(*ResetMFAResponse)(nil),                          // 77: akuity.aims.v1.ResetMFAResponse
	(*Team)(nil),                                      // 78: akuity.aims.v1.Team
	(*UserTeam)(nil),                                  // 79: akuity.aims.v1.UserTeam
	(*ListTeamsRequest)(nil),                          // 80: akuity.aims.v1.ListTeamsRequest
	(*ListTeamsResponse)(nil),                         // 81: akuity.aims.v1.ListTeamsResponse
	(*TeamMember)(nil),                                // 82: akuity.aims.v1.TeamMember
	(*ListTeamMembersRequest)(nil),                    // 83: akuity.aims.v1.ListTeamMembersRequest
	(*ListTeamMembersResponse)(nil),                   // 84: akuity.aims.v1.ListTeamMembersResponse
	(*WorkspaceArgoCDInstance)(nil),                   // 85: akuity.aims.v1.WorkspaceArgoCDInstance
	(*WorkspaceKargoInstance)(nil),                    // 86: akuity.aims.v1.WorkspaceKargoInstance
	(*WorkspaceUserMember)(nil),                       // 87: akuity.aims.v1.WorkspaceUserMember
	(*WorkspaceTeamMember)(nil),                       // 88: akuity.aims.v1.WorkspaceTeamMember
	(*WorkspaceMember)(nil),                           // 89: akuity.aims.v1.WorkspaceMember
	(*WorkspaceMemberRef)(nil),                        // 90: akuity.aims.v1.WorkspaceMemberRef
	(*CreateWorkspaceRequest)(nil),                    // 91: akuity.aims.v1.CreateWorkspaceRequest
	(*CreateWorkspaceResponse)(nil),                   // 92: akuity.aims.v1.CreateWorkspaceResponse
	(*Workspace)(nil),                                 // 93: akuity.aims.v1.Workspace
	(*ListWorkspacesRequest)(nil),                     // 94: akuity.aims.v1.ListWorkspacesRequest
	(*ListWorkspacesResponse)(nil),                    // 95: akuity.aims.v1.ListWorkspacesResponse
	(*GetWorkspaceRequest)(nil),                       // 96: akuity.aims.v1.GetWorkspaceRequest
	(*GetWorkspaceResponse)(nil),                      // 97: akuity.aims.v1.GetWorkspaceResponse
	(*ListWorkspaceMembersRequest)(nil),               // 98: akuity.aims.v1.ListWorkspaceMembersRequest
	(*ListWorkspaceMembersResponse)(nil),              // 99: akuity.aims.v1.ListWorkspaceMembersResponse
	(*CustomRole)(nil),                                // 100: akuity.aims.v1.CustomRole
	(*ListWorkspaceCustomRolesRequest)(nil),           // 101: akuity.aims.v1.ListWorkspaceCustomRolesRequest
	(*ListWorkspaceCustomRolesResponse)(nil),          // 102: akuity.aims.v1.ListWorkspaceCustomRolesResponse
	(*ListOrganizationCustomRolesRequest)(nil),        // 103: akuity.aims.v1.ListOrganizationCustomRolesRequest
	(*ListOrganizationCustomRolesResponse)(nil),       // 104: akuity.aims.v1.ListOrganizationCustomRolesResponse
	(*ListOrganizationUsersRequest)(nil),              // 105: akuity.aims.v1.ListOrganizationUsersRequest
	(*ListOrganizationUsersResponse)(nil),             // 106: akuity.aims.v1.ListOrganizationUsersResponse
	(*WorkspaceInfo)(nil),                             // 107: akuity.aims.v1.WorkspaceInfo
	(*TeamInfo)(nil),                                  // 108: akuity.aims.v1.TeamInfo
	(*OrganizationUser)(nil),                          // 109: akuity.aims.v1.OrganizationUser
	(*AuditLog)(nil),                                  // 110: akuity.aims.v1.AuditLog
	(*InternalAuditFilters)(nil),                      // 111: akuity.aims.v1.InternalAuditFilters
	(*GetInternalAuditLogsRequest)(nil),               // 112: akuity.aims.v1.GetInternalAuditLogsRequest
	(*GetInternalAuditLogsResponse)(nil),              // 113: akuity.aims.v1.GetInternalAuditLogsResponse
	(*GetClusterManifestsRequest)(nil),                // 114: akuity.aims.v1.GetClusterManifestsRequest
	(*GetKargoAgentManifestsRequest)(nil),             // 115: akuity.aims.v1.GetKargoAgentManifestsRequest
	nil,                                               // 116: akuity.aims.v1.NotificationConfig.ConfigEntry
	nil,                                               // 117: akuity.aims.v1.ListOrganizationMembersResponse.MembersEntry
	nil,                                               // 118: akuity.aims.v1.BasicOrganization.MiscEntry
	(*AuditLog_AuditActor)(nil),                       // 119: akuity.aims.v1.AuditLog.AuditActor
	(*AuditLog_AuditObject)(nil),                      // 120: akuity.aims.v1.AuditLog.AuditObject
	(*AuditLog_AuditDetails)(nil),                     // 121: akuity.aims.v1.AuditLog.AuditDetails
	(*structpb.Struct)(nil),                           // 122: google.protobuf.Struct
	(*v1.Instance)(nil),                               // 123: akuity.argocd.v1.Instance
	(*timestamppb.Timestamp)(nil),                     // 124: google.protobuf.Timestamp
	(*v11.DomainVerification)(nil),                    // 125: akuity.organization.v1.DomainVerification
	(*v11.OrganizationStatus)(nil),                    // 126: akuity.organization.v1.OrganizationStatus
	(*v12.OrganizationQuota)(nil),                     // 127: akuity.types.features.v1.OrganizationQuota
	(*v12.OrganizationUsage)(nil),                     // 128: akuity.types.features.v1.OrganizationUsage
	(*v11.BillingDetails)(nil),                        // 129: akuity.organization.v1.BillingDetails
	(*v12.OrganizationFeatureGates)(nil),              // 130: akuity.types.features.v1.OrganizationFeatureGates
	(*v12.SystemFeatureGates)(nil),                    // 131: akuity.types.features.v1.SystemFeatureGates
	(*v1.Cluster)(nil),                                // 132: akuity.argocd.v1.Cluster
	(*v13.KargoAgent)(nil),                            // 133: akuity.kargo.v1.KargoAgent
	(*v13.KargoInstance)(nil),                         // 134: akuity.kargo.v1.KargoInstance
	(*v11.AuditFilters)(nil),                          // 135: akuity.organization.v1.AuditFilters
	(*v11.AuditLog)(nil),                              // 136: akuity.organization.v1.AuditLog
	(*v11.KubeVisionUsage)(nil),                       // 137: akuity.organization.v1.KubeVisionUsage
	(*v11.ListAIConversationsRequest)(nil),            // 138: akuity.organization.v1.ListAIConversationsRequest
	(*v11.GetAIConversationRequest)(nil),              // 139: akuity.organization.v1.GetAIConversationRequest
	(*httpbody.HttpBody)(nil),                         // 140: google.api.HttpBody
	(*v11.ListAIConversationsResponse)(nil),           // 141: akuity.organization.v1.ListAIConversationsResponse
	(*v11.GetAIConversationResponse)(nil),             // 142: akuity.organization.v1.GetAIConversationResponse
}
var file_aims_v1_aims_proto_depIdxs = []int32{
	32,  // 0: akuity.aims.v1.SendNotificationRequest.audit:type_name -> akuity.aims.v1.Audit
	0,   // 1: akuity.aims.v1.SendNotificationRequest.category:type_name -> akuity.aims.v1.NotificationCategory
	122, // 2: akuity.aims.v1.SendNotificationRequest.metadata:type_name -> google.protobuf.Struct
	5,   // 3: akuity.aims.v1.ListArgoInstancesRequest.filter:type_name -> akuity.aims.v1.ArgoInstanceFilter
	13,  // 4: akuity.aims.v1.ListArgoInstancesResponse.instances:type_name -> akuity.aims.v1.InternalInstance
	32,  // 5: akuity.aims.v1.DeleteUnpaidInstanceRequest.audit:type_name -> akuity.aims.v1.Audit
	14,  // 6: akuity.aims.v1.OnboardManualCustomerRequest.customer:type_name -> akuity.aims.v1.Customer
	32,  // 7: akuity.aims.v1.OnboardManualCustomerRequest.audit:type_name -> akuity.aims.v1.Audit
	116, // 8: akuity.aims.v1.NotificationConfig.config:type_name -> akuity.aims.v1.NotificationConfig.ConfigEntry
	123, // 9: akuity.aims.v1.InternalInstance.instance:type_name -> akuity.argocd.v1.Instance
	124, // 10: akuity.aims.v1.InternalInstance.create_time:type_name -> google.protobuf.Timestamp
	12,  // 11: akuity.aims.v1.InternalInstance.notification_config:type_name -> akuity.aims.v1.NotificationConfig
	93,  // 12: akuity.aims.v1.InternalInstance.workspace:type_name -> akuity.aims.v1.Workspace
	125, // 13: akuity.aims.v1.ListOrganizationDomainsResponse.domains:type_name -> akuity.organization.v1.DomainVerification
	125, // 14: akuity.aims.v1.UpdateOrganizationDomainsRequest.domains:type_name -> akuity.organization.v1.DomainVerification
	32,  // 15: akuity.aims.v1.UpdateOrganizationDomainsRequest.audit:type_name -> akuity.aims.v1.Audit
	125, // 16: akuity.aims.v1.UpdateOrganizationDomainsResponse.domains:type_name -> akuity.organization.v1.DomainVerification
	117, // 17: akuity.aims.v1.ListOrganizationMembersResponse.members:type_name -> akuity.aims.v1.ListOrganizationMembersResponse.MembersEntry
	30,  // 18: akuity.aims.v1.ListUnbilledOrganizationsResponse.organizations:type_name -> akuity.aims.v1.BasicOrganization
	1,   // 19: akuity.aims.v1.OrganizationFilter.sort_by_creation:type_name -> akuity.aims.v1.Sort
	24,  // 20: akuity.aims.v1.ListAllOrganizationsRequest.filters:type_name -> akuity.aims.v1.OrganizationFilter
	30,  // 21: akuity.aims.v1.ListAllOrganizationsResponse.organizations:type_name -> akuity.aims.v1.BasicOrganization
	30,  // 22: akuity.aims.v1.GetOrganizationResponse.organization:type_name -> akuity.aims.v1.BasicOrganization
	124, // 23: akuity.aims.v1.StripeData.subscription_end_time:type_name -> google.protobuf.Timestamp
	126, // 24: akuity.aims.v1.BasicOrganization.status:type_name -> akuity.organization.v1.OrganizationStatus
	127, // 25: akuity.aims.v1.BasicOrganization.quota:type_name -> akuity.types.features.v1.OrganizationQuota
	128, // 26: akuity.aims.v1.BasicOrganization.usage:type_name -> akuity.types.features.v1.OrganizationUsage
	129, // 27: akuity.aims.v1.BasicOrganization.billing_details:type_name -> akuity.organization.v1.BillingDetails
	29,  // 28: akuity.aims.v1.BasicOrganization.stripe_data:type_name -> akuity.aims.v1.StripeData
	124, // 29: akuity.aims.v1.BasicOrganization.creation_timestamp:type_name -> google.protobuf.Timestamp
	118, // 30: akuity.aims.v1.BasicOrganization.misc:type_name -> akuity.aims.v1.BasicOrganization.MiscEntry
	130, // 31: akuity.aims.v1.PatchFeatureGatesRequest.feature_gates:type_name -> akuity.types.features.v1.OrganizationFeatureGates
	32,  // 32: akuity.aims.v1.PatchFeatureGatesRequest.audit:type_name -> akuity.aims.v1.Audit
	130, // 33: akuity.aims.v1.PatchFeatureGatesResponse.feature_gates:type_name -> akuity.types.features.v1.OrganizationFeatureGates
	130, // 34: akuity.aims.v1.GetFeatureGatesResponse.feature_gates:type_name -> akuity.types.features.v1.OrganizationFeatureGates
	131, // 35: akuity.aims.v1.GetFeatureGatesResponse.system_feature_gates:type_name -> akuity.types.features.v1.SystemFeatureGates
	32,  // 36: akuity.aims.v1.UpdateQuotasRequest.audit:type_name -> akuity.aims.v1.Audit
	127, // 37: akuity.aims.v1.UpdateQuotasRequest.quota:type_name -> akuity.types.features.v1.OrganizationQuota
	127, // 38: akuity.aims.v1.UpdateQuotasResponse.quota:type_name -> akuity.types.features.v1.OrganizationQuota
	124, // 39: akuity.aims.v1.UpdateQuotasResponse.creation_timestamp:type_name -> google.protobuf.Timestamp
	32,  // 40: akuity.aims.v1.UpdateOrganizationTrialExpirationRequest.audit:type_name -> akuity.aims.v1.Audit
	32,  // 41: akuity.aims.v1.DecrementInstanceGenerationRequest.audit:type_name -> akuity.aims.v1.Audit
	13,  // 42: akuity.aims.v1.GetInstanceByIdResponse.instance:type_name -> akuity.aims.v1.InternalInstance
	61,  // 43: akuity.aims.v1.GetKargoInstanceByIdResponse.instance:type_name -> akuity.aims.v1.InternalKargoInstance
	47,  // 44: akuity.aims.v1.ListClustersForInstanceRequest.filter:type_name -> akuity.aims.v1.ClusterFilter
	132, // 45: akuity.aims.v1.ListClustersForInstanceResponse.clusters:type_name -> akuity.argocd.v1.Cluster
	133, // 46: akuity.aims.v1.ListAgentsForKargoInstanceResponse.agents:type_name -> akuity.kargo.v1.KargoAgent
	32,  // 47: akuity.aims.v1.SetManuallyVerifiedRequest.audit:type_name -> akuity.aims.v1.Audit
	32,  // 48: akuity.aims.v1.SetDisabledInstanceCreationRequest.audit:type_name -> akuity.aims.v1.Audit
	32,  // 49: akuity.aims.v1.DeleteOrganizationRequest.audit:type_name -> akuity.aims.v1.Audit
	59,  // 50: akuity.aims.v1.GetInternalConfigResponse.config:type_name -> akuity.aims.v1.InternalConfig
	134, // 51: akuity.aims.v1.InternalKargoInstance.instance:type_name -> akuity.kargo.v1.KargoInstance
	30,  // 52: akuity.aims.v1.InternalKargoInstance.organization:type_name -> akuity.aims.v1.BasicOrganization
	124, // 53: akuity.aims.v1.InternalKargoInstance.creation_timestamp:type_name -> google.protobuf.Timestamp
	93,  // 54: akuity.aims.v1.InternalKargoInstance.workspace:type_name -> akuity.aims.v1.Workspace
	122, // 55: akuity.aims.v1.InternalKargoInstance.status_info:type_name -> google.protobuf.Struct
	62,  // 56: akuity.aims.v1.ListKargoInstancesRequest.filter:type_name -> akuity.aims.v1.KargoInstanceFilter
	61,  // 57: akuity.aims.v1.ListKargoInstancesResponse.instances:type_name -> akuity.aims.v1.InternalKargoInstance
	32,  // 58: akuity.aims.v1.InstanceClusterMaintenanceRequest.audit:type_name -> akuity.aims.v1.Audit
	135, // 59: akuity.aims.v1.ListAuditLogsRequest.filters:type_name -> akuity.organization.v1.AuditFilters
	136, // 60: akuity.aims.v1.ListAuditLogsResponse.audit_logs:type_name -> akuity.organization.v1.AuditLog
	122, // 61: akuity.aims.v1.Plan.features:type_name -> google.protobuf.Struct
	122, // 62: akuity.aims.v1.Plan.quotas:type_name -> google.protobuf.Struct
	70,  // 63: akuity.aims.v1.ListAvailablePlansResponse.plans:type_name -> akuity.aims.v1.Plan
	32,  // 64: akuity.aims.v1.UpdateOrganizationBillingPlanRequest.audit:type_name -> akuity.aims.v1.Audit
	124, // 65: akuity.aims.v1.GetKubeVisionUsageRequest.start_time:type_name -> google.protobuf.Timestamp
	124, // 66: akuity.aims.v1.GetKubeVisionUsageRequest.end_time:type_name -> google.protobuf.Timestamp
	137, // 67: akuity.aims.v1.GetKubeVisionUsageResponse.usage:type_name -> akuity.organization.v1.KubeVisionUsage
	32,  // 68: akuity.aims.v1.ResetMFARequest.audit:type_name -> akuity.aims.v1.Audit
	124, // 69: akuity.aims.v1.Team.create_time:type_name -> google.protobuf.Timestamp
	78,  // 70: akuity.aims.v1.UserTeam.team:type_name -> akuity.aims.v1.Team
	78,  // 71: akuity.aims.v1.ListTeamsResponse.teams:type_name -> akuity.aims.v1.Team
	82,  // 72: akuity.aims.v1.ListTeamMembersResponse.team_members:type_name -> akuity.aims.v1.TeamMember
	124, // 73: akuity.aims.v1.WorkspaceTeamMember.create_time:type_name -> google.protobuf.Timestamp
	2,   // 74: akuity.aims.v1.WorkspaceMember.role:type_name -> akuity.aims.v1.WorkspaceMemberRole
	87,  // 75: akuity.aims.v1.WorkspaceMember.user:type_name -> akuity.aims.v1.WorkspaceUserMember
	88,  // 76: akuity.aims.v1.WorkspaceMember.team:type_name -> akuity.aims.v1.WorkspaceTeamMember
	2,   // 77: akuity.aims.v1.WorkspaceMemberRef.role:type_name -> akuity.aims.v1.WorkspaceMemberRole
	93,  // 78: akuity.aims.v1.CreateWorkspaceResponse.workspace:type_name -> akuity.aims.v1.Workspace
	124, // 79: akuity.aims.v1.Workspace.create_time:type_name -> google.protobuf.Timestamp
	85,  // 80: akuity.aims.v1.Workspace.argocd_instances:type_name -> akuity.aims.v1.WorkspaceArgoCDInstance
	86,  // 81: akuity.aims.v1.Workspace.kargo_instances:type_name -> akuity.aims.v1.WorkspaceKargoInstance
	93,  // 82: akuity.aims.v1.ListWorkspacesResponse.workspaces:type_name -> akuity.aims.v1.Workspace
	93,  // 83: akuity.aims.v1.GetWorkspaceResponse.workspace:type_name -> akuity.aims.v1.Workspace
	89,  // 84: akuity.aims.v1.ListWorkspaceMembersResponse.workspace_members:type_name -> akuity.aims.v1.WorkspaceMember
	100, // 85: akuity.aims.v1.ListWorkspaceCustomRolesResponse.custom_roles:type_name -> akuity.aims.v1.CustomRole
	100, // 86: akuity.aims.v1.ListOrganizationCustomRolesResponse.custom_roles:type_name -> akuity.aims.v1.CustomRole
	109, // 87: akuity.aims.v1.ListOrganizationUsersResponse.users:type_name -> akuity.aims.v1.OrganizationUser
	107, // 88: akuity.aims.v1.OrganizationUser.workspaces:type_name -> akuity.aims.v1.WorkspaceInfo
	108, // 89: akuity.aims.v1.OrganizationUser.teams:type_name -> akuity.aims.v1.TeamInfo
	119, // 90: akuity.aims.v1.AuditLog.actor:type_name -> akuity.aims.v1.AuditLog.AuditActor
	120, // 91: akuity.aims.v1.AuditLog.object:type_name -> akuity.aims.v1.AuditLog.AuditObject
	121, // 92: akuity.aims.v1.AuditLog.details:type_name -> akuity.aims.v1.AuditLog.AuditDetails
	111, // 93: akuity.aims.v1.GetInternalAuditLogsRequest.filters:type_name -> akuity.aims.v1.InternalAuditFilters
	110, // 94: akuity.aims.v1.GetInternalAuditLogsResponse.items:type_name -> akuity.aims.v1.AuditLog
	32,  // 95: akuity.aims.v1.GetClusterManifestsRequest.audit:type_name -> akuity.aims.v1.Audit
	32,  // 96: akuity.aims.v1.GetKargoAgentManifestsRequest.audit:type_name -> akuity.aims.v1.Audit
	20,  // 97: akuity.aims.v1.ListOrganizationMembersResponse.MembersEntry.value:type_name -> akuity.aims.v1.OrganizationMembers
	43,  // 98: akuity.aims.v1.AimsService.GetInstanceById:input_type -> akuity.aims.v1.GetInstanceByIdRequest
	45,  // 99: akuity.aims.v1.AimsService.GetKargoInstanceById:input_type -> akuity.aims.v1.GetKargoInstanceByIdRequest
	112, // 100: akuity.aims.v1.AimsService.GetInternalAuditLogs:input_type -> akuity.aims.v1.GetInternalAuditLogsRequest
	6,   // 101: akuity.aims.v1.AimsService.ListArgoInstances:input_type -> akuity.aims.v1.ListArgoInstancesRequest
	8,   // 102: akuity.aims.v1.AimsService.DeleteUnpaidInstance:input_type -> akuity.aims.v1.DeleteUnpaidInstanceRequest
	8,   // 103: akuity.aims.v1.AimsService.DeleteUnpaidKargoInstance:input_type -> akuity.aims.v1.DeleteUnpaidInstanceRequest
	63,  // 104: akuity.aims.v1.AimsService.ListKargoInstances:input_type -> akuity.aims.v1.ListKargoInstancesRequest
	10,  // 105: akuity.aims.v1.AimsService.OnboardManualCustomer:input_type -> akuity.aims.v1.OnboardManualCustomerRequest
	27,  // 106: akuity.aims.v1.AimsService.GetOrganization:input_type -> akuity.aims.v1.GetOrganizationRequest
	35,  // 107: akuity.aims.v1.AimsService.GetFeatureGates:input_type -> akuity.aims.v1.GetFeatureGatesRequest
	33,  // 108: akuity.aims.v1.AimsService.PatchFeatureGates:input_type -> akuity.aims.v1.PatchFeatureGatesRequest
	65,  // 109: akuity.aims.v1.AimsService.InstanceClusterMaintenance:input_type -> akuity.aims.v1.InstanceClusterMaintenanceRequest
	37,  // 110: akuity.aims.v1.AimsService.UpdateQuotas:input_type -> akuity.aims.v1.UpdateQuotasRequest
	22,  // 111: akuity.aims.v1.AimsService.ListUnbilledOrganizations:input_type -> akuity.aims.v1.ListUnbilledOrganizationsRequest
	25,  // 112: akuity.aims.v1.AimsService.ListAllOrganizations:input_type -> akuity.aims.v1.ListAllOrganizationsRequest
	19,  // 113: akuity.aims.v1.AimsService.ListOrganizationMembers:input_type -> akuity.aims.v1.ListOrganizationMembersRequest
	39,  // 114: akuity.aims.v1.AimsService.UpdateOrganizationTrialExpiration:input_type -> akuity.aims.v1.UpdateOrganizationTrialExpirationRequest
	41,  // 115: akuity.aims.v1.AimsService.DecrementInstanceGeneration:input_type -> akuity.aims.v1.DecrementInstanceGenerationRequest
	48,  // 116: akuity.aims.v1.AimsService.ListClustersForInstance:input_type -> akuity.aims.v1.ListClustersForInstanceRequest
	50,  // 117: akuity.aims.v1.AimsService.ListAgentsForKargoInstance:input_type -> akuity.aims.v1.ListAgentsForKargoInstanceRequest
	114, // 118: akuity.aims.v1.AimsService.GetClusterManifests:input_type -> akuity.aims.v1.GetClusterManifestsRequest
	115, // 119: akuity.aims.v1.AimsService.GetKargoAgentManifests:input_type -> akuity.aims.v1.GetKargoAgentManifestsRequest
	52,  // 120: akuity.aims.v1.AimsService.SetManuallyVerified:input_type -> akuity.aims.v1.SetManuallyVerifiedRequest
	54,  // 121: akuity.aims.v1.AimsService.SetDisabledInstanceCreation:input_type -> akuity.aims.v1.SetDisabledInstanceCreationRequest
	56,  // 122: akuity.aims.v1.AimsService.DeleteOrganization:input_type -> akuity.aims.v1.DeleteOrganizationRequest
	58,  // 123: akuity.aims.v1.AimsService.GetInternalConfig:input_type -> akuity.aims.v1.GetInternalConfigRequest
	3,   // 124: akuity.aims.v1.AimsService.SendNotification:input_type -> akuity.aims.v1.SendNotificationRequest
	67,  // 125: akuity.aims.v1.AimsService.ListAuditLogs:input_type -> akuity.aims.v1.ListAuditLogsRequest
	69,  // 126: akuity.aims.v1.AimsService.ListAvailablePlans:input_type -> akuity.aims.v1.ListAvailablePlansRequest
	72,  // 127: akuity.aims.v1.AimsService.UpdateOrganizationBillingPlan:input_type -> akuity.aims.v1.UpdateOrganizationBillingPlanRequest
	74,  // 128: akuity.aims.v1.AimsService.GetKubeVisionUsage:input_type -> akuity.aims.v1.GetKubeVisionUsageRequest
	15,  // 129: akuity.aims.v1.AimsService.ListOrganizationDomains:input_type -> akuity.aims.v1.ListOrganizationDomainsRequest
	17,  // 130: akuity.aims.v1.AimsService.UpdateOrganizationDomains:input_type -> akuity.aims.v1.UpdateOrganizationDomainsRequest
	76,  // 131: akuity.aims.v1.AimsService.ResetMFA:input_type -> akuity.aims.v1.ResetMFARequest
	80,  // 132: akuity.aims.v1.AimsService.ListTeams:input_type -> akuity.aims.v1.ListTeamsRequest
	83,  // 133: akuity.aims.v1.AimsService.ListTeamMembers:input_type -> akuity.aims.v1.ListTeamMembersRequest
	94,  // 134: akuity.aims.v1.AimsService.ListWorkspaces:input_type -> akuity.aims.v1.ListWorkspacesRequest
	96,  // 135: akuity.aims.v1.AimsService.GetWorkspace:input_type -> akuity.aims.v1.GetWorkspaceRequest
	98,  // 136: akuity.aims.v1.AimsService.ListWorkspaceMembers:input_type -> akuity.aims.v1.ListWorkspaceMembersRequest
	101, // 137: akuity.aims.v1.AimsService.ListWorkspaceCustomRoles:input_type -> akuity.aims.v1.ListWorkspaceCustomRolesRequest
	103, // 138: akuity.aims.v1.AimsService.ListOrganizationCustomRoles:input_type -> akuity.aims.v1.ListOrganizationCustomRolesRequest
	105, // 139: akuity.aims.v1.AimsService.ListOrganizationUsers:input_type -> akuity.aims.v1.ListOrganizationUsersRequest
	138, // 140: akuity.aims.v1.AimsService.ListAIConversations:input_type -> akuity.organization.v1.ListAIConversationsRequest
	139, // 141: akuity.aims.v1.AimsService.GetAIConversation:input_type -> akuity.organization.v1.GetAIConversationRequest
	44,  // 142: akuity.aims.v1.AimsService.GetInstanceById:output_type -> akuity.aims.v1.GetInstanceByIdResponse
	46,  // 143: akuity.aims.v1.AimsService.GetKargoInstanceById:output_type -> akuity.aims.v1.GetKargoInstanceByIdResponse
	113, // 144: akuity.aims.v1.AimsService.GetInternalAuditLogs:output_type -> akuity.aims.v1.GetInternalAuditLogsResponse
	7,   // 145: akuity.aims.v1.AimsService.ListArgoInstances:output_type -> akuity.aims.v1.ListArgoInstancesResponse
	9,   // 146: akuity.aims.v1.AimsService.DeleteUnpaidInstance:output_type -> akuity.aims.v1.DeleteUnpaidInstanceResponse
	9,   // 147: akuity.aims.v1.AimsService.DeleteUnpaidKargoInstance:output_type -> akuity.aims.v1.DeleteUnpaidInstanceResponse
	64,  // 148: akuity.aims.v1.AimsService.ListKargoInstances:output_type -> akuity.aims.v1.ListKargoInstancesResponse
	11,  // 149: akuity.aims.v1.AimsService.OnboardManualCustomer:output_type -> akuity.aims.v1.OnboardManualCustomerResponse
	28,  // 150: akuity.aims.v1.AimsService.GetOrganization:output_type -> akuity.aims.v1.GetOrganizationResponse
	36,  // 151: akuity.aims.v1.AimsService.GetFeatureGates:output_type -> akuity.aims.v1.GetFeatureGatesResponse
	34,  // 152: akuity.aims.v1.AimsService.PatchFeatureGates:output_type -> akuity.aims.v1.PatchFeatureGatesResponse
	66,  // 153: akuity.aims.v1.AimsService.InstanceClusterMaintenance:output_type -> akuity.aims.v1.InstanceClusterMaintenanceResponse
	38,  // 154: akuity.aims.v1.AimsService.UpdateQuotas:output_type -> akuity.aims.v1.UpdateQuotasResponse
	23,  // 155: akuity.aims.v1.AimsService.ListUnbilledOrganizations:output_type -> akuity.aims.v1.ListUnbilledOrganizationsResponse
	26,  // 156: akuity.aims.v1.AimsService.ListAllOrganizations:output_type -> akuity.aims.v1.ListAllOrganizationsResponse
	21,  // 157: akuity.aims.v1.AimsService.ListOrganizationMembers:output_type -> akuity.aims.v1.ListOrganizationMembersResponse
	40,  // 158: akuity.aims.v1.AimsService.UpdateOrganizationTrialExpiration:output_type -> akuity.aims.v1.UpdateOrganizationTrialExpirationResponse
	42,  // 159: akuity.aims.v1.AimsService.DecrementInstanceGeneration:output_type -> akuity.aims.v1.DecrementInstanceGenerationResponse
	49,  // 160: akuity.aims.v1.AimsService.ListClustersForInstance:output_type -> akuity.aims.v1.ListClustersForInstanceResponse
	51,  // 161: akuity.aims.v1.AimsService.ListAgentsForKargoInstance:output_type -> akuity.aims.v1.ListAgentsForKargoInstanceResponse
	140, // 162: akuity.aims.v1.AimsService.GetClusterManifests:output_type -> google.api.HttpBody
	140, // 163: akuity.aims.v1.AimsService.GetKargoAgentManifests:output_type -> google.api.HttpBody
	53,  // 164: akuity.aims.v1.AimsService.SetManuallyVerified:output_type -> akuity.aims.v1.SetManuallyVerifiedResponse
	55,  // 165: akuity.aims.v1.AimsService.SetDisabledInstanceCreation:output_type -> akuity.aims.v1.SetDisabledInstanceCreationResponse
	57,  // 166: akuity.aims.v1.AimsService.DeleteOrganization:output_type -> akuity.aims.v1.DeleteOrganizationResponse
	60,  // 167: akuity.aims.v1.AimsService.GetInternalConfig:output_type -> akuity.aims.v1.GetInternalConfigResponse
	4,   // 168: akuity.aims.v1.AimsService.SendNotification:output_type -> akuity.aims.v1.SendNotificationResponse
	68,  // 169: akuity.aims.v1.AimsService.ListAuditLogs:output_type -> akuity.aims.v1.ListAuditLogsResponse
	71,  // 170: akuity.aims.v1.AimsService.ListAvailablePlans:output_type -> akuity.aims.v1.ListAvailablePlansResponse
	73,  // 171: akuity.aims.v1.AimsService.UpdateOrganizationBillingPlan:output_type -> akuity.aims.v1.UpdateOrganizationBillingPlanResponse
	75,  // 172: akuity.aims.v1.AimsService.GetKubeVisionUsage:output_type -> akuity.aims.v1.GetKubeVisionUsageResponse
	16,  // 173: akuity.aims.v1.AimsService.ListOrganizationDomains:output_type -> akuity.aims.v1.ListOrganizationDomainsResponse
	18,  // 174: akuity.aims.v1.AimsService.UpdateOrganizationDomains:output_type -> akuity.aims.v1.UpdateOrganizationDomainsResponse
	77,  // 175: akuity.aims.v1.AimsService.ResetMFA:output_type -> akuity.aims.v1.ResetMFAResponse
	81,  // 176: akuity.aims.v1.AimsService.ListTeams:output_type -> akuity.aims.v1.ListTeamsResponse
	84,  // 177: akuity.aims.v1.AimsService.ListTeamMembers:output_type -> akuity.aims.v1.ListTeamMembersResponse
	95,  // 178: akuity.aims.v1.AimsService.ListWorkspaces:output_type -> akuity.aims.v1.ListWorkspacesResponse
	97,  // 179: akuity.aims.v1.AimsService.GetWorkspace:output_type -> akuity.aims.v1.GetWorkspaceResponse
	99,  // 180: akuity.aims.v1.AimsService.ListWorkspaceMembers:output_type -> akuity.aims.v1.ListWorkspaceMembersResponse
	102, // 181: akuity.aims.v1.AimsService.ListWorkspaceCustomRoles:output_type -> akuity.aims.v1.ListWorkspaceCustomRolesResponse
	104, // 182: akuity.aims.v1.AimsService.ListOrganizationCustomRoles:output_type -> akuity.aims.v1.ListOrganizationCustomRolesResponse
	106, // 183: akuity.aims.v1.AimsService.ListOrganizationUsers:output_type -> akuity.aims.v1.ListOrganizationUsersResponse
	141, // 184: akuity.aims.v1.AimsService.ListAIConversations:output_type -> akuity.organization.v1.ListAIConversationsResponse
	142, // 185: akuity.aims.v1.AimsService.GetAIConversation:output_type -> akuity.organization.v1.GetAIConversationResponse
	142, // [142:186] is the sub-list for method output_type
	98,  // [98:142] is the sub-list for method input_type
	98,  // [98:98] is the sub-list for extension type_name
	98,  // [98:98] is the sub-list for extension extendee
	0,   // [0:98] is the sub-list for field type_name
}

func init() { file_aims_v1_aims_proto_init() }
func file_aims_v1_aims_proto_init() {
	if File_aims_v1_aims_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_aims_v1_aims_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendNotificationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendNotificationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArgoInstanceFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListArgoInstancesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListArgoInstancesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteUnpaidInstanceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteUnpaidInstanceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OnboardManualCustomerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OnboardManualCustomerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NotificationConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InternalInstance); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Customer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOrganizationDomainsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOrganizationDomainsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOrganizationDomainsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOrganizationDomainsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOrganizationMembersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrganizationMembers); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOrganizationMembersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListUnbilledOrganizationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListUnbilledOrganizationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrganizationFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAllOrganizationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAllOrganizationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrganizationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrganizationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BasicOrganization); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlanUsage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Audit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PatchFeatureGatesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PatchFeatureGatesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFeatureGatesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFeatureGatesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateQuotasRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateQuotasResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOrganizationTrialExpirationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOrganizationTrialExpirationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DecrementInstanceGenerationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DecrementInstanceGenerationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstanceByIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstanceByIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetKargoInstanceByIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetKargoInstanceByIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClusterFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListClustersForInstanceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListClustersForInstanceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAgentsForKargoInstanceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAgentsForKargoInstanceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetManuallyVerifiedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetManuallyVerifiedResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetDisabledInstanceCreationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetDisabledInstanceCreationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteOrganizationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteOrganizationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInternalConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InternalConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInternalConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InternalKargoInstance); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KargoInstanceFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListKargoInstancesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListKargoInstancesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InstanceClusterMaintenanceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InstanceClusterMaintenanceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAuditLogsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAuditLogsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAvailablePlansRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Plan); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAvailablePlansResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOrganizationBillingPlanRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOrganizationBillingPlanResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetKubeVisionUsageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetKubeVisionUsageResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResetMFARequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResetMFAResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Team); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserTeam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTeamsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTeamsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamMember); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[80].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTeamMembersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[81].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTeamMembersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[82].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkspaceArgoCDInstance); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[83].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkspaceKargoInstance); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[84].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkspaceUserMember); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[85].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkspaceTeamMember); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[86].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkspaceMember); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[87].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkspaceMemberRef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[88].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateWorkspaceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[89].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateWorkspaceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[90].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Workspace); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[91].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWorkspacesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[92].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWorkspacesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[93].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorkspaceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[94].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorkspaceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[95].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWorkspaceMembersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[96].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWorkspaceMembersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[97].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomRole); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[98].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWorkspaceCustomRolesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[99].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWorkspaceCustomRolesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[100].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOrganizationCustomRolesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[101].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOrganizationCustomRolesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[102].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOrganizationUsersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[103].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOrganizationUsersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[104].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkspaceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[105].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[106].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrganizationUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[107].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuditLog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[108].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InternalAuditFilters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[109].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInternalAuditLogsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[110].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInternalAuditLogsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[111].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetClusterManifestsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[112].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetKargoAgentManifestsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[116].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuditLog_AuditActor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[117].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuditLog_AuditObject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_aims_v1_aims_proto_msgTypes[118].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuditLog_AuditDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_aims_v1_aims_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_aims_v1_aims_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_aims_v1_aims_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_aims_v1_aims_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_aims_v1_aims_proto_msgTypes[11].OneofWrappers = []interface{}{}
	file_aims_v1_aims_proto_msgTypes[21].OneofWrappers = []interface{}{}
	file_aims_v1_aims_proto_msgTypes[22].OneofWrappers = []interface{}{}
	file_aims_v1_aims_proto_msgTypes[27].OneofWrappers = []interface{}{}
	file_aims_v1_aims_proto_msgTypes[35].OneofWrappers = []interface{}{}
	file_aims_v1_aims_proto_msgTypes[44].OneofWrappers = []interface{}{}
	file_aims_v1_aims_proto_msgTypes[58].OneofWrappers = []interface{}{}
	file_aims_v1_aims_proto_msgTypes[59].OneofWrappers = []interface{}{}
	file_aims_v1_aims_proto_msgTypes[60].OneofWrappers = []interface{}{}
	file_aims_v1_aims_proto_msgTypes[64].OneofWrappers = []interface{}{}
	file_aims_v1_aims_proto_msgTypes[71].OneofWrappers = []interface{}{}
	file_aims_v1_aims_proto_msgTypes[77].OneofWrappers = []interface{}{}
	file_aims_v1_aims_proto_msgTypes[80].OneofWrappers = []interface{}{}
	file_aims_v1_aims_proto_msgTypes[86].OneofWrappers = []interface{}{
		(*WorkspaceMember_User)(nil),
		(*WorkspaceMember_Team)(nil),
	}
	file_aims_v1_aims_proto_msgTypes[87].OneofWrappers = []interface{}{
		(*WorkspaceMemberRef_UserId)(nil),
		(*WorkspaceMemberRef_UserEmail)(nil),
		(*WorkspaceMemberRef_TeamName)(nil),
	}
	file_aims_v1_aims_proto_msgTypes[88].OneofWrappers = []interface{}{}
	file_aims_v1_aims_proto_msgTypes[91].OneofWrappers = []interface{}{}
	file_aims_v1_aims_proto_msgTypes[95].OneofWrappers = []interface{}{}
	file_aims_v1_aims_proto_msgTypes[98].OneofWrappers = []interface{}{}
	file_aims_v1_aims_proto_msgTypes[100].OneofWrappers = []interface{}{}
	file_aims_v1_aims_proto_msgTypes[108].OneofWrappers = []interface{}{}
	file_aims_v1_aims_proto_msgTypes[116].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_aims_v1_aims_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   119,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_aims_v1_aims_proto_goTypes,
		DependencyIndexes: file_aims_v1_aims_proto_depIdxs,
		EnumInfos:         file_aims_v1_aims_proto_enumTypes,
		MessageInfos:      file_aims_v1_aims_proto_msgTypes,
	}.Build()
	File_aims_v1_aims_proto = out.File
	file_aims_v1_aims_proto_rawDesc = nil
	file_aims_v1_aims_proto_goTypes = nil
	file_aims_v1_aims_proto_depIdxs = nil
}
