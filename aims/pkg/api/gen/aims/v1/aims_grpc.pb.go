// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             (unknown)
// source: aims/v1/aims.proto

package aimsv1

import (
	context "context"
	v1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
	httpbody "google.golang.org/genproto/googleapis/api/httpbody"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	AimsService_GetInstanceById_FullMethodName                   = "/akuity.aims.v1.AimsService/GetInstanceById"
	AimsService_GetKargoInstanceById_FullMethodName              = "/akuity.aims.v1.AimsService/GetKargoInstanceById"
	AimsService_GetInternalAuditLogs_FullMethodName              = "/akuity.aims.v1.AimsService/GetInternalAuditLogs"
	AimsService_ListArgoInstances_FullMethodName                 = "/akuity.aims.v1.AimsService/ListArgoInstances"
	AimsService_DeleteUnpaidInstance_FullMethodName              = "/akuity.aims.v1.AimsService/DeleteUnpaidInstance"
	AimsService_DeleteUnpaidKargoInstance_FullMethodName         = "/akuity.aims.v1.AimsService/DeleteUnpaidKargoInstance"
	AimsService_ListKargoInstances_FullMethodName                = "/akuity.aims.v1.AimsService/ListKargoInstances"
	AimsService_OnboardManualCustomer_FullMethodName             = "/akuity.aims.v1.AimsService/OnboardManualCustomer"
	AimsService_GetOrganization_FullMethodName                   = "/akuity.aims.v1.AimsService/GetOrganization"
	AimsService_GetFeatureGates_FullMethodName                   = "/akuity.aims.v1.AimsService/GetFeatureGates"
	AimsService_PatchFeatureGates_FullMethodName                 = "/akuity.aims.v1.AimsService/PatchFeatureGates"
	AimsService_InstanceClusterMaintenance_FullMethodName        = "/akuity.aims.v1.AimsService/InstanceClusterMaintenance"
	AimsService_UpdateQuotas_FullMethodName                      = "/akuity.aims.v1.AimsService/UpdateQuotas"
	AimsService_ListUnbilledOrganizations_FullMethodName         = "/akuity.aims.v1.AimsService/ListUnbilledOrganizations"
	AimsService_ListAllOrganizations_FullMethodName              = "/akuity.aims.v1.AimsService/ListAllOrganizations"
	AimsService_ListOrganizationMembers_FullMethodName           = "/akuity.aims.v1.AimsService/ListOrganizationMembers"
	AimsService_UpdateOrganizationTrialExpiration_FullMethodName = "/akuity.aims.v1.AimsService/UpdateOrganizationTrialExpiration"
	AimsService_DecrementInstanceGeneration_FullMethodName       = "/akuity.aims.v1.AimsService/DecrementInstanceGeneration"
	AimsService_ListClustersForInstance_FullMethodName           = "/akuity.aims.v1.AimsService/ListClustersForInstance"
	AimsService_ListAgentsForKargoInstance_FullMethodName        = "/akuity.aims.v1.AimsService/ListAgentsForKargoInstance"
	AimsService_GetClusterManifests_FullMethodName               = "/akuity.aims.v1.AimsService/GetClusterManifests"
	AimsService_GetKargoAgentManifests_FullMethodName            = "/akuity.aims.v1.AimsService/GetKargoAgentManifests"
	AimsService_SetManuallyVerified_FullMethodName               = "/akuity.aims.v1.AimsService/SetManuallyVerified"
	AimsService_SetDisabledInstanceCreation_FullMethodName       = "/akuity.aims.v1.AimsService/SetDisabledInstanceCreation"
	AimsService_DeleteOrganization_FullMethodName                = "/akuity.aims.v1.AimsService/DeleteOrganization"
	AimsService_GetInternalConfig_FullMethodName                 = "/akuity.aims.v1.AimsService/GetInternalConfig"
	AimsService_SendNotification_FullMethodName                  = "/akuity.aims.v1.AimsService/SendNotification"
	AimsService_ListAuditLogs_FullMethodName                     = "/akuity.aims.v1.AimsService/ListAuditLogs"
	AimsService_ListAvailablePlans_FullMethodName                = "/akuity.aims.v1.AimsService/ListAvailablePlans"
	AimsService_UpdateOrganizationBillingPlan_FullMethodName     = "/akuity.aims.v1.AimsService/UpdateOrganizationBillingPlan"
	AimsService_GetKubeVisionUsage_FullMethodName                = "/akuity.aims.v1.AimsService/GetKubeVisionUsage"
	AimsService_ListOrganizationDomains_FullMethodName           = "/akuity.aims.v1.AimsService/ListOrganizationDomains"
	AimsService_UpdateOrganizationDomains_FullMethodName         = "/akuity.aims.v1.AimsService/UpdateOrganizationDomains"
	AimsService_ResetMFA_FullMethodName                          = "/akuity.aims.v1.AimsService/ResetMFA"
	AimsService_ListTeams_FullMethodName                         = "/akuity.aims.v1.AimsService/ListTeams"
	AimsService_ListTeamMembers_FullMethodName                   = "/akuity.aims.v1.AimsService/ListTeamMembers"
	AimsService_ListWorkspaces_FullMethodName                    = "/akuity.aims.v1.AimsService/ListWorkspaces"
	AimsService_GetWorkspace_FullMethodName                      = "/akuity.aims.v1.AimsService/GetWorkspace"
	AimsService_ListWorkspaceMembers_FullMethodName              = "/akuity.aims.v1.AimsService/ListWorkspaceMembers"
	AimsService_ListWorkspaceCustomRoles_FullMethodName          = "/akuity.aims.v1.AimsService/ListWorkspaceCustomRoles"
	AimsService_ListOrganizationCustomRoles_FullMethodName       = "/akuity.aims.v1.AimsService/ListOrganizationCustomRoles"
	AimsService_ListOrganizationUsers_FullMethodName             = "/akuity.aims.v1.AimsService/ListOrganizationUsers"
	AimsService_ListAIConversations_FullMethodName               = "/akuity.aims.v1.AimsService/ListAIConversations"
	AimsService_GetAIConversation_FullMethodName                 = "/akuity.aims.v1.AimsService/GetAIConversation"
)

// AimsServiceClient is the client API for AimsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AimsServiceClient interface {
	GetInstanceById(ctx context.Context, in *GetInstanceByIdRequest, opts ...grpc.CallOption) (*GetInstanceByIdResponse, error)
	GetKargoInstanceById(ctx context.Context, in *GetKargoInstanceByIdRequest, opts ...grpc.CallOption) (*GetKargoInstanceByIdResponse, error)
	GetInternalAuditLogs(ctx context.Context, in *GetInternalAuditLogsRequest, opts ...grpc.CallOption) (*GetInternalAuditLogsResponse, error)
	ListArgoInstances(ctx context.Context, in *ListArgoInstancesRequest, opts ...grpc.CallOption) (*ListArgoInstancesResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	DeleteUnpaidInstance(ctx context.Context, in *DeleteUnpaidInstanceRequest, opts ...grpc.CallOption) (*DeleteUnpaidInstanceResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	// buf:lint:ignore RPC_REQUEST_STANDARD_NAME
	DeleteUnpaidKargoInstance(ctx context.Context, in *DeleteUnpaidInstanceRequest, opts ...grpc.CallOption) (*DeleteUnpaidInstanceResponse, error)
	ListKargoInstances(ctx context.Context, in *ListKargoInstancesRequest, opts ...grpc.CallOption) (*ListKargoInstancesResponse, error)
	OnboardManualCustomer(ctx context.Context, in *OnboardManualCustomerRequest, opts ...grpc.CallOption) (*OnboardManualCustomerResponse, error)
	GetOrganization(ctx context.Context, in *GetOrganizationRequest, opts ...grpc.CallOption) (*GetOrganizationResponse, error)
	GetFeatureGates(ctx context.Context, in *GetFeatureGatesRequest, opts ...grpc.CallOption) (*GetFeatureGatesResponse, error)
	PatchFeatureGates(ctx context.Context, in *PatchFeatureGatesRequest, opts ...grpc.CallOption) (*PatchFeatureGatesResponse, error)
	InstanceClusterMaintenance(ctx context.Context, in *InstanceClusterMaintenanceRequest, opts ...grpc.CallOption) (*InstanceClusterMaintenanceResponse, error)
	UpdateQuotas(ctx context.Context, in *UpdateQuotasRequest, opts ...grpc.CallOption) (*UpdateQuotasResponse, error)
	ListUnbilledOrganizations(ctx context.Context, in *ListUnbilledOrganizationsRequest, opts ...grpc.CallOption) (*ListUnbilledOrganizationsResponse, error)
	ListAllOrganizations(ctx context.Context, in *ListAllOrganizationsRequest, opts ...grpc.CallOption) (*ListAllOrganizationsResponse, error)
	ListOrganizationMembers(ctx context.Context, in *ListOrganizationMembersRequest, opts ...grpc.CallOption) (*ListOrganizationMembersResponse, error)
	UpdateOrganizationTrialExpiration(ctx context.Context, in *UpdateOrganizationTrialExpirationRequest, opts ...grpc.CallOption) (*UpdateOrganizationTrialExpirationResponse, error)
	DecrementInstanceGeneration(ctx context.Context, in *DecrementInstanceGenerationRequest, opts ...grpc.CallOption) (*DecrementInstanceGenerationResponse, error)
	ListClustersForInstance(ctx context.Context, in *ListClustersForInstanceRequest, opts ...grpc.CallOption) (*ListClustersForInstanceResponse, error)
	ListAgentsForKargoInstance(ctx context.Context, in *ListAgentsForKargoInstanceRequest, opts ...grpc.CallOption) (*ListAgentsForKargoInstanceResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	GetClusterManifests(ctx context.Context, in *GetClusterManifestsRequest, opts ...grpc.CallOption) (AimsService_GetClusterManifestsClient, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	GetKargoAgentManifests(ctx context.Context, in *GetKargoAgentManifestsRequest, opts ...grpc.CallOption) (AimsService_GetKargoAgentManifestsClient, error)
	SetManuallyVerified(ctx context.Context, in *SetManuallyVerifiedRequest, opts ...grpc.CallOption) (*SetManuallyVerifiedResponse, error)
	SetDisabledInstanceCreation(ctx context.Context, in *SetDisabledInstanceCreationRequest, opts ...grpc.CallOption) (*SetDisabledInstanceCreationResponse, error)
	DeleteOrganization(ctx context.Context, in *DeleteOrganizationRequest, opts ...grpc.CallOption) (*DeleteOrganizationResponse, error)
	GetInternalConfig(ctx context.Context, in *GetInternalConfigRequest, opts ...grpc.CallOption) (*GetInternalConfigResponse, error)
	SendNotification(ctx context.Context, in *SendNotificationRequest, opts ...grpc.CallOption) (*SendNotificationResponse, error)
	ListAuditLogs(ctx context.Context, in *ListAuditLogsRequest, opts ...grpc.CallOption) (*ListAuditLogsResponse, error)
	ListAvailablePlans(ctx context.Context, in *ListAvailablePlansRequest, opts ...grpc.CallOption) (*ListAvailablePlansResponse, error)
	UpdateOrganizationBillingPlan(ctx context.Context, in *UpdateOrganizationBillingPlanRequest, opts ...grpc.CallOption) (*UpdateOrganizationBillingPlanResponse, error)
	GetKubeVisionUsage(ctx context.Context, in *GetKubeVisionUsageRequest, opts ...grpc.CallOption) (*GetKubeVisionUsageResponse, error)
	ListOrganizationDomains(ctx context.Context, in *ListOrganizationDomainsRequest, opts ...grpc.CallOption) (*ListOrganizationDomainsResponse, error)
	UpdateOrganizationDomains(ctx context.Context, in *UpdateOrganizationDomainsRequest, opts ...grpc.CallOption) (*UpdateOrganizationDomainsResponse, error)
	ResetMFA(ctx context.Context, in *ResetMFARequest, opts ...grpc.CallOption) (*ResetMFAResponse, error)
	ListTeams(ctx context.Context, in *ListTeamsRequest, opts ...grpc.CallOption) (*ListTeamsResponse, error)
	ListTeamMembers(ctx context.Context, in *ListTeamMembersRequest, opts ...grpc.CallOption) (*ListTeamMembersResponse, error)
	ListWorkspaces(ctx context.Context, in *ListWorkspacesRequest, opts ...grpc.CallOption) (*ListWorkspacesResponse, error)
	GetWorkspace(ctx context.Context, in *GetWorkspaceRequest, opts ...grpc.CallOption) (*GetWorkspaceResponse, error)
	ListWorkspaceMembers(ctx context.Context, in *ListWorkspaceMembersRequest, opts ...grpc.CallOption) (*ListWorkspaceMembersResponse, error)
	ListWorkspaceCustomRoles(ctx context.Context, in *ListWorkspaceCustomRolesRequest, opts ...grpc.CallOption) (*ListWorkspaceCustomRolesResponse, error)
	ListOrganizationCustomRoles(ctx context.Context, in *ListOrganizationCustomRolesRequest, opts ...grpc.CallOption) (*ListOrganizationCustomRolesResponse, error)
	ListOrganizationUsers(ctx context.Context, in *ListOrganizationUsersRequest, opts ...grpc.CallOption) (*ListOrganizationUsersResponse, error)
	ListAIConversations(ctx context.Context, in *v1.ListAIConversationsRequest, opts ...grpc.CallOption) (*v1.ListAIConversationsResponse, error)
	GetAIConversation(ctx context.Context, in *v1.GetAIConversationRequest, opts ...grpc.CallOption) (*v1.GetAIConversationResponse, error)
}

type aimsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAimsServiceClient(cc grpc.ClientConnInterface) AimsServiceClient {
	return &aimsServiceClient{cc}
}

func (c *aimsServiceClient) GetInstanceById(ctx context.Context, in *GetInstanceByIdRequest, opts ...grpc.CallOption) (*GetInstanceByIdResponse, error) {
	out := new(GetInstanceByIdResponse)
	err := c.cc.Invoke(ctx, AimsService_GetInstanceById_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) GetKargoInstanceById(ctx context.Context, in *GetKargoInstanceByIdRequest, opts ...grpc.CallOption) (*GetKargoInstanceByIdResponse, error) {
	out := new(GetKargoInstanceByIdResponse)
	err := c.cc.Invoke(ctx, AimsService_GetKargoInstanceById_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) GetInternalAuditLogs(ctx context.Context, in *GetInternalAuditLogsRequest, opts ...grpc.CallOption) (*GetInternalAuditLogsResponse, error) {
	out := new(GetInternalAuditLogsResponse)
	err := c.cc.Invoke(ctx, AimsService_GetInternalAuditLogs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) ListArgoInstances(ctx context.Context, in *ListArgoInstancesRequest, opts ...grpc.CallOption) (*ListArgoInstancesResponse, error) {
	out := new(ListArgoInstancesResponse)
	err := c.cc.Invoke(ctx, AimsService_ListArgoInstances_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) DeleteUnpaidInstance(ctx context.Context, in *DeleteUnpaidInstanceRequest, opts ...grpc.CallOption) (*DeleteUnpaidInstanceResponse, error) {
	out := new(DeleteUnpaidInstanceResponse)
	err := c.cc.Invoke(ctx, AimsService_DeleteUnpaidInstance_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) DeleteUnpaidKargoInstance(ctx context.Context, in *DeleteUnpaidInstanceRequest, opts ...grpc.CallOption) (*DeleteUnpaidInstanceResponse, error) {
	out := new(DeleteUnpaidInstanceResponse)
	err := c.cc.Invoke(ctx, AimsService_DeleteUnpaidKargoInstance_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) ListKargoInstances(ctx context.Context, in *ListKargoInstancesRequest, opts ...grpc.CallOption) (*ListKargoInstancesResponse, error) {
	out := new(ListKargoInstancesResponse)
	err := c.cc.Invoke(ctx, AimsService_ListKargoInstances_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) OnboardManualCustomer(ctx context.Context, in *OnboardManualCustomerRequest, opts ...grpc.CallOption) (*OnboardManualCustomerResponse, error) {
	out := new(OnboardManualCustomerResponse)
	err := c.cc.Invoke(ctx, AimsService_OnboardManualCustomer_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) GetOrganization(ctx context.Context, in *GetOrganizationRequest, opts ...grpc.CallOption) (*GetOrganizationResponse, error) {
	out := new(GetOrganizationResponse)
	err := c.cc.Invoke(ctx, AimsService_GetOrganization_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) GetFeatureGates(ctx context.Context, in *GetFeatureGatesRequest, opts ...grpc.CallOption) (*GetFeatureGatesResponse, error) {
	out := new(GetFeatureGatesResponse)
	err := c.cc.Invoke(ctx, AimsService_GetFeatureGates_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) PatchFeatureGates(ctx context.Context, in *PatchFeatureGatesRequest, opts ...grpc.CallOption) (*PatchFeatureGatesResponse, error) {
	out := new(PatchFeatureGatesResponse)
	err := c.cc.Invoke(ctx, AimsService_PatchFeatureGates_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) InstanceClusterMaintenance(ctx context.Context, in *InstanceClusterMaintenanceRequest, opts ...grpc.CallOption) (*InstanceClusterMaintenanceResponse, error) {
	out := new(InstanceClusterMaintenanceResponse)
	err := c.cc.Invoke(ctx, AimsService_InstanceClusterMaintenance_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) UpdateQuotas(ctx context.Context, in *UpdateQuotasRequest, opts ...grpc.CallOption) (*UpdateQuotasResponse, error) {
	out := new(UpdateQuotasResponse)
	err := c.cc.Invoke(ctx, AimsService_UpdateQuotas_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) ListUnbilledOrganizations(ctx context.Context, in *ListUnbilledOrganizationsRequest, opts ...grpc.CallOption) (*ListUnbilledOrganizationsResponse, error) {
	out := new(ListUnbilledOrganizationsResponse)
	err := c.cc.Invoke(ctx, AimsService_ListUnbilledOrganizations_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) ListAllOrganizations(ctx context.Context, in *ListAllOrganizationsRequest, opts ...grpc.CallOption) (*ListAllOrganizationsResponse, error) {
	out := new(ListAllOrganizationsResponse)
	err := c.cc.Invoke(ctx, AimsService_ListAllOrganizations_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) ListOrganizationMembers(ctx context.Context, in *ListOrganizationMembersRequest, opts ...grpc.CallOption) (*ListOrganizationMembersResponse, error) {
	out := new(ListOrganizationMembersResponse)
	err := c.cc.Invoke(ctx, AimsService_ListOrganizationMembers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) UpdateOrganizationTrialExpiration(ctx context.Context, in *UpdateOrganizationTrialExpirationRequest, opts ...grpc.CallOption) (*UpdateOrganizationTrialExpirationResponse, error) {
	out := new(UpdateOrganizationTrialExpirationResponse)
	err := c.cc.Invoke(ctx, AimsService_UpdateOrganizationTrialExpiration_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) DecrementInstanceGeneration(ctx context.Context, in *DecrementInstanceGenerationRequest, opts ...grpc.CallOption) (*DecrementInstanceGenerationResponse, error) {
	out := new(DecrementInstanceGenerationResponse)
	err := c.cc.Invoke(ctx, AimsService_DecrementInstanceGeneration_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) ListClustersForInstance(ctx context.Context, in *ListClustersForInstanceRequest, opts ...grpc.CallOption) (*ListClustersForInstanceResponse, error) {
	out := new(ListClustersForInstanceResponse)
	err := c.cc.Invoke(ctx, AimsService_ListClustersForInstance_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) ListAgentsForKargoInstance(ctx context.Context, in *ListAgentsForKargoInstanceRequest, opts ...grpc.CallOption) (*ListAgentsForKargoInstanceResponse, error) {
	out := new(ListAgentsForKargoInstanceResponse)
	err := c.cc.Invoke(ctx, AimsService_ListAgentsForKargoInstance_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) GetClusterManifests(ctx context.Context, in *GetClusterManifestsRequest, opts ...grpc.CallOption) (AimsService_GetClusterManifestsClient, error) {
	stream, err := c.cc.NewStream(ctx, &AimsService_ServiceDesc.Streams[0], AimsService_GetClusterManifests_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &aimsServiceGetClusterManifestsClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type AimsService_GetClusterManifestsClient interface {
	Recv() (*httpbody.HttpBody, error)
	grpc.ClientStream
}

type aimsServiceGetClusterManifestsClient struct {
	grpc.ClientStream
}

func (x *aimsServiceGetClusterManifestsClient) Recv() (*httpbody.HttpBody, error) {
	m := new(httpbody.HttpBody)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *aimsServiceClient) GetKargoAgentManifests(ctx context.Context, in *GetKargoAgentManifestsRequest, opts ...grpc.CallOption) (AimsService_GetKargoAgentManifestsClient, error) {
	stream, err := c.cc.NewStream(ctx, &AimsService_ServiceDesc.Streams[1], AimsService_GetKargoAgentManifests_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &aimsServiceGetKargoAgentManifestsClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type AimsService_GetKargoAgentManifestsClient interface {
	Recv() (*httpbody.HttpBody, error)
	grpc.ClientStream
}

type aimsServiceGetKargoAgentManifestsClient struct {
	grpc.ClientStream
}

func (x *aimsServiceGetKargoAgentManifestsClient) Recv() (*httpbody.HttpBody, error) {
	m := new(httpbody.HttpBody)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *aimsServiceClient) SetManuallyVerified(ctx context.Context, in *SetManuallyVerifiedRequest, opts ...grpc.CallOption) (*SetManuallyVerifiedResponse, error) {
	out := new(SetManuallyVerifiedResponse)
	err := c.cc.Invoke(ctx, AimsService_SetManuallyVerified_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) SetDisabledInstanceCreation(ctx context.Context, in *SetDisabledInstanceCreationRequest, opts ...grpc.CallOption) (*SetDisabledInstanceCreationResponse, error) {
	out := new(SetDisabledInstanceCreationResponse)
	err := c.cc.Invoke(ctx, AimsService_SetDisabledInstanceCreation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) DeleteOrganization(ctx context.Context, in *DeleteOrganizationRequest, opts ...grpc.CallOption) (*DeleteOrganizationResponse, error) {
	out := new(DeleteOrganizationResponse)
	err := c.cc.Invoke(ctx, AimsService_DeleteOrganization_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) GetInternalConfig(ctx context.Context, in *GetInternalConfigRequest, opts ...grpc.CallOption) (*GetInternalConfigResponse, error) {
	out := new(GetInternalConfigResponse)
	err := c.cc.Invoke(ctx, AimsService_GetInternalConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) SendNotification(ctx context.Context, in *SendNotificationRequest, opts ...grpc.CallOption) (*SendNotificationResponse, error) {
	out := new(SendNotificationResponse)
	err := c.cc.Invoke(ctx, AimsService_SendNotification_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) ListAuditLogs(ctx context.Context, in *ListAuditLogsRequest, opts ...grpc.CallOption) (*ListAuditLogsResponse, error) {
	out := new(ListAuditLogsResponse)
	err := c.cc.Invoke(ctx, AimsService_ListAuditLogs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) ListAvailablePlans(ctx context.Context, in *ListAvailablePlansRequest, opts ...grpc.CallOption) (*ListAvailablePlansResponse, error) {
	out := new(ListAvailablePlansResponse)
	err := c.cc.Invoke(ctx, AimsService_ListAvailablePlans_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) UpdateOrganizationBillingPlan(ctx context.Context, in *UpdateOrganizationBillingPlanRequest, opts ...grpc.CallOption) (*UpdateOrganizationBillingPlanResponse, error) {
	out := new(UpdateOrganizationBillingPlanResponse)
	err := c.cc.Invoke(ctx, AimsService_UpdateOrganizationBillingPlan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) GetKubeVisionUsage(ctx context.Context, in *GetKubeVisionUsageRequest, opts ...grpc.CallOption) (*GetKubeVisionUsageResponse, error) {
	out := new(GetKubeVisionUsageResponse)
	err := c.cc.Invoke(ctx, AimsService_GetKubeVisionUsage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) ListOrganizationDomains(ctx context.Context, in *ListOrganizationDomainsRequest, opts ...grpc.CallOption) (*ListOrganizationDomainsResponse, error) {
	out := new(ListOrganizationDomainsResponse)
	err := c.cc.Invoke(ctx, AimsService_ListOrganizationDomains_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) UpdateOrganizationDomains(ctx context.Context, in *UpdateOrganizationDomainsRequest, opts ...grpc.CallOption) (*UpdateOrganizationDomainsResponse, error) {
	out := new(UpdateOrganizationDomainsResponse)
	err := c.cc.Invoke(ctx, AimsService_UpdateOrganizationDomains_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) ResetMFA(ctx context.Context, in *ResetMFARequest, opts ...grpc.CallOption) (*ResetMFAResponse, error) {
	out := new(ResetMFAResponse)
	err := c.cc.Invoke(ctx, AimsService_ResetMFA_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) ListTeams(ctx context.Context, in *ListTeamsRequest, opts ...grpc.CallOption) (*ListTeamsResponse, error) {
	out := new(ListTeamsResponse)
	err := c.cc.Invoke(ctx, AimsService_ListTeams_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) ListTeamMembers(ctx context.Context, in *ListTeamMembersRequest, opts ...grpc.CallOption) (*ListTeamMembersResponse, error) {
	out := new(ListTeamMembersResponse)
	err := c.cc.Invoke(ctx, AimsService_ListTeamMembers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) ListWorkspaces(ctx context.Context, in *ListWorkspacesRequest, opts ...grpc.CallOption) (*ListWorkspacesResponse, error) {
	out := new(ListWorkspacesResponse)
	err := c.cc.Invoke(ctx, AimsService_ListWorkspaces_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) GetWorkspace(ctx context.Context, in *GetWorkspaceRequest, opts ...grpc.CallOption) (*GetWorkspaceResponse, error) {
	out := new(GetWorkspaceResponse)
	err := c.cc.Invoke(ctx, AimsService_GetWorkspace_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) ListWorkspaceMembers(ctx context.Context, in *ListWorkspaceMembersRequest, opts ...grpc.CallOption) (*ListWorkspaceMembersResponse, error) {
	out := new(ListWorkspaceMembersResponse)
	err := c.cc.Invoke(ctx, AimsService_ListWorkspaceMembers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) ListWorkspaceCustomRoles(ctx context.Context, in *ListWorkspaceCustomRolesRequest, opts ...grpc.CallOption) (*ListWorkspaceCustomRolesResponse, error) {
	out := new(ListWorkspaceCustomRolesResponse)
	err := c.cc.Invoke(ctx, AimsService_ListWorkspaceCustomRoles_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) ListOrganizationCustomRoles(ctx context.Context, in *ListOrganizationCustomRolesRequest, opts ...grpc.CallOption) (*ListOrganizationCustomRolesResponse, error) {
	out := new(ListOrganizationCustomRolesResponse)
	err := c.cc.Invoke(ctx, AimsService_ListOrganizationCustomRoles_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) ListOrganizationUsers(ctx context.Context, in *ListOrganizationUsersRequest, opts ...grpc.CallOption) (*ListOrganizationUsersResponse, error) {
	out := new(ListOrganizationUsersResponse)
	err := c.cc.Invoke(ctx, AimsService_ListOrganizationUsers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) ListAIConversations(ctx context.Context, in *v1.ListAIConversationsRequest, opts ...grpc.CallOption) (*v1.ListAIConversationsResponse, error) {
	out := new(v1.ListAIConversationsResponse)
	err := c.cc.Invoke(ctx, AimsService_ListAIConversations_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimsServiceClient) GetAIConversation(ctx context.Context, in *v1.GetAIConversationRequest, opts ...grpc.CallOption) (*v1.GetAIConversationResponse, error) {
	out := new(v1.GetAIConversationResponse)
	err := c.cc.Invoke(ctx, AimsService_GetAIConversation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AimsServiceServer is the server API for AimsService service.
// All implementations must embed UnimplementedAimsServiceServer
// for forward compatibility
type AimsServiceServer interface {
	GetInstanceById(context.Context, *GetInstanceByIdRequest) (*GetInstanceByIdResponse, error)
	GetKargoInstanceById(context.Context, *GetKargoInstanceByIdRequest) (*GetKargoInstanceByIdResponse, error)
	GetInternalAuditLogs(context.Context, *GetInternalAuditLogsRequest) (*GetInternalAuditLogsResponse, error)
	ListArgoInstances(context.Context, *ListArgoInstancesRequest) (*ListArgoInstancesResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	DeleteUnpaidInstance(context.Context, *DeleteUnpaidInstanceRequest) (*DeleteUnpaidInstanceResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	// buf:lint:ignore RPC_REQUEST_STANDARD_NAME
	DeleteUnpaidKargoInstance(context.Context, *DeleteUnpaidInstanceRequest) (*DeleteUnpaidInstanceResponse, error)
	ListKargoInstances(context.Context, *ListKargoInstancesRequest) (*ListKargoInstancesResponse, error)
	OnboardManualCustomer(context.Context, *OnboardManualCustomerRequest) (*OnboardManualCustomerResponse, error)
	GetOrganization(context.Context, *GetOrganizationRequest) (*GetOrganizationResponse, error)
	GetFeatureGates(context.Context, *GetFeatureGatesRequest) (*GetFeatureGatesResponse, error)
	PatchFeatureGates(context.Context, *PatchFeatureGatesRequest) (*PatchFeatureGatesResponse, error)
	InstanceClusterMaintenance(context.Context, *InstanceClusterMaintenanceRequest) (*InstanceClusterMaintenanceResponse, error)
	UpdateQuotas(context.Context, *UpdateQuotasRequest) (*UpdateQuotasResponse, error)
	ListUnbilledOrganizations(context.Context, *ListUnbilledOrganizationsRequest) (*ListUnbilledOrganizationsResponse, error)
	ListAllOrganizations(context.Context, *ListAllOrganizationsRequest) (*ListAllOrganizationsResponse, error)
	ListOrganizationMembers(context.Context, *ListOrganizationMembersRequest) (*ListOrganizationMembersResponse, error)
	UpdateOrganizationTrialExpiration(context.Context, *UpdateOrganizationTrialExpirationRequest) (*UpdateOrganizationTrialExpirationResponse, error)
	DecrementInstanceGeneration(context.Context, *DecrementInstanceGenerationRequest) (*DecrementInstanceGenerationResponse, error)
	ListClustersForInstance(context.Context, *ListClustersForInstanceRequest) (*ListClustersForInstanceResponse, error)
	ListAgentsForKargoInstance(context.Context, *ListAgentsForKargoInstanceRequest) (*ListAgentsForKargoInstanceResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	GetClusterManifests(*GetClusterManifestsRequest, AimsService_GetClusterManifestsServer) error
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	GetKargoAgentManifests(*GetKargoAgentManifestsRequest, AimsService_GetKargoAgentManifestsServer) error
	SetManuallyVerified(context.Context, *SetManuallyVerifiedRequest) (*SetManuallyVerifiedResponse, error)
	SetDisabledInstanceCreation(context.Context, *SetDisabledInstanceCreationRequest) (*SetDisabledInstanceCreationResponse, error)
	DeleteOrganization(context.Context, *DeleteOrganizationRequest) (*DeleteOrganizationResponse, error)
	GetInternalConfig(context.Context, *GetInternalConfigRequest) (*GetInternalConfigResponse, error)
	SendNotification(context.Context, *SendNotificationRequest) (*SendNotificationResponse, error)
	ListAuditLogs(context.Context, *ListAuditLogsRequest) (*ListAuditLogsResponse, error)
	ListAvailablePlans(context.Context, *ListAvailablePlansRequest) (*ListAvailablePlansResponse, error)
	UpdateOrganizationBillingPlan(context.Context, *UpdateOrganizationBillingPlanRequest) (*UpdateOrganizationBillingPlanResponse, error)
	GetKubeVisionUsage(context.Context, *GetKubeVisionUsageRequest) (*GetKubeVisionUsageResponse, error)
	ListOrganizationDomains(context.Context, *ListOrganizationDomainsRequest) (*ListOrganizationDomainsResponse, error)
	UpdateOrganizationDomains(context.Context, *UpdateOrganizationDomainsRequest) (*UpdateOrganizationDomainsResponse, error)
	ResetMFA(context.Context, *ResetMFARequest) (*ResetMFAResponse, error)
	ListTeams(context.Context, *ListTeamsRequest) (*ListTeamsResponse, error)
	ListTeamMembers(context.Context, *ListTeamMembersRequest) (*ListTeamMembersResponse, error)
	ListWorkspaces(context.Context, *ListWorkspacesRequest) (*ListWorkspacesResponse, error)
	GetWorkspace(context.Context, *GetWorkspaceRequest) (*GetWorkspaceResponse, error)
	ListWorkspaceMembers(context.Context, *ListWorkspaceMembersRequest) (*ListWorkspaceMembersResponse, error)
	ListWorkspaceCustomRoles(context.Context, *ListWorkspaceCustomRolesRequest) (*ListWorkspaceCustomRolesResponse, error)
	ListOrganizationCustomRoles(context.Context, *ListOrganizationCustomRolesRequest) (*ListOrganizationCustomRolesResponse, error)
	ListOrganizationUsers(context.Context, *ListOrganizationUsersRequest) (*ListOrganizationUsersResponse, error)
	ListAIConversations(context.Context, *v1.ListAIConversationsRequest) (*v1.ListAIConversationsResponse, error)
	GetAIConversation(context.Context, *v1.GetAIConversationRequest) (*v1.GetAIConversationResponse, error)
	mustEmbedUnimplementedAimsServiceServer()
}

// UnimplementedAimsServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAimsServiceServer struct {
}

func (UnimplementedAimsServiceServer) GetInstanceById(context.Context, *GetInstanceByIdRequest) (*GetInstanceByIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInstanceById not implemented")
}
func (UnimplementedAimsServiceServer) GetKargoInstanceById(context.Context, *GetKargoInstanceByIdRequest) (*GetKargoInstanceByIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetKargoInstanceById not implemented")
}
func (UnimplementedAimsServiceServer) GetInternalAuditLogs(context.Context, *GetInternalAuditLogsRequest) (*GetInternalAuditLogsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInternalAuditLogs not implemented")
}
func (UnimplementedAimsServiceServer) ListArgoInstances(context.Context, *ListArgoInstancesRequest) (*ListArgoInstancesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListArgoInstances not implemented")
}
func (UnimplementedAimsServiceServer) DeleteUnpaidInstance(context.Context, *DeleteUnpaidInstanceRequest) (*DeleteUnpaidInstanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUnpaidInstance not implemented")
}
func (UnimplementedAimsServiceServer) DeleteUnpaidKargoInstance(context.Context, *DeleteUnpaidInstanceRequest) (*DeleteUnpaidInstanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUnpaidKargoInstance not implemented")
}
func (UnimplementedAimsServiceServer) ListKargoInstances(context.Context, *ListKargoInstancesRequest) (*ListKargoInstancesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListKargoInstances not implemented")
}
func (UnimplementedAimsServiceServer) OnboardManualCustomer(context.Context, *OnboardManualCustomerRequest) (*OnboardManualCustomerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OnboardManualCustomer not implemented")
}
func (UnimplementedAimsServiceServer) GetOrganization(context.Context, *GetOrganizationRequest) (*GetOrganizationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrganization not implemented")
}
func (UnimplementedAimsServiceServer) GetFeatureGates(context.Context, *GetFeatureGatesRequest) (*GetFeatureGatesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFeatureGates not implemented")
}
func (UnimplementedAimsServiceServer) PatchFeatureGates(context.Context, *PatchFeatureGatesRequest) (*PatchFeatureGatesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PatchFeatureGates not implemented")
}
func (UnimplementedAimsServiceServer) InstanceClusterMaintenance(context.Context, *InstanceClusterMaintenanceRequest) (*InstanceClusterMaintenanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InstanceClusterMaintenance not implemented")
}
func (UnimplementedAimsServiceServer) UpdateQuotas(context.Context, *UpdateQuotasRequest) (*UpdateQuotasResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateQuotas not implemented")
}
func (UnimplementedAimsServiceServer) ListUnbilledOrganizations(context.Context, *ListUnbilledOrganizationsRequest) (*ListUnbilledOrganizationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUnbilledOrganizations not implemented")
}
func (UnimplementedAimsServiceServer) ListAllOrganizations(context.Context, *ListAllOrganizationsRequest) (*ListAllOrganizationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAllOrganizations not implemented")
}
func (UnimplementedAimsServiceServer) ListOrganizationMembers(context.Context, *ListOrganizationMembersRequest) (*ListOrganizationMembersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListOrganizationMembers not implemented")
}
func (UnimplementedAimsServiceServer) UpdateOrganizationTrialExpiration(context.Context, *UpdateOrganizationTrialExpirationRequest) (*UpdateOrganizationTrialExpirationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateOrganizationTrialExpiration not implemented")
}
func (UnimplementedAimsServiceServer) DecrementInstanceGeneration(context.Context, *DecrementInstanceGenerationRequest) (*DecrementInstanceGenerationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DecrementInstanceGeneration not implemented")
}
func (UnimplementedAimsServiceServer) ListClustersForInstance(context.Context, *ListClustersForInstanceRequest) (*ListClustersForInstanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListClustersForInstance not implemented")
}
func (UnimplementedAimsServiceServer) ListAgentsForKargoInstance(context.Context, *ListAgentsForKargoInstanceRequest) (*ListAgentsForKargoInstanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAgentsForKargoInstance not implemented")
}
func (UnimplementedAimsServiceServer) GetClusterManifests(*GetClusterManifestsRequest, AimsService_GetClusterManifestsServer) error {
	return status.Errorf(codes.Unimplemented, "method GetClusterManifests not implemented")
}
func (UnimplementedAimsServiceServer) GetKargoAgentManifests(*GetKargoAgentManifestsRequest, AimsService_GetKargoAgentManifestsServer) error {
	return status.Errorf(codes.Unimplemented, "method GetKargoAgentManifests not implemented")
}
func (UnimplementedAimsServiceServer) SetManuallyVerified(context.Context, *SetManuallyVerifiedRequest) (*SetManuallyVerifiedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetManuallyVerified not implemented")
}
func (UnimplementedAimsServiceServer) SetDisabledInstanceCreation(context.Context, *SetDisabledInstanceCreationRequest) (*SetDisabledInstanceCreationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetDisabledInstanceCreation not implemented")
}
func (UnimplementedAimsServiceServer) DeleteOrganization(context.Context, *DeleteOrganizationRequest) (*DeleteOrganizationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteOrganization not implemented")
}
func (UnimplementedAimsServiceServer) GetInternalConfig(context.Context, *GetInternalConfigRequest) (*GetInternalConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInternalConfig not implemented")
}
func (UnimplementedAimsServiceServer) SendNotification(context.Context, *SendNotificationRequest) (*SendNotificationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendNotification not implemented")
}
func (UnimplementedAimsServiceServer) ListAuditLogs(context.Context, *ListAuditLogsRequest) (*ListAuditLogsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAuditLogs not implemented")
}
func (UnimplementedAimsServiceServer) ListAvailablePlans(context.Context, *ListAvailablePlansRequest) (*ListAvailablePlansResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAvailablePlans not implemented")
}
func (UnimplementedAimsServiceServer) UpdateOrganizationBillingPlan(context.Context, *UpdateOrganizationBillingPlanRequest) (*UpdateOrganizationBillingPlanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateOrganizationBillingPlan not implemented")
}
func (UnimplementedAimsServiceServer) GetKubeVisionUsage(context.Context, *GetKubeVisionUsageRequest) (*GetKubeVisionUsageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetKubeVisionUsage not implemented")
}
func (UnimplementedAimsServiceServer) ListOrganizationDomains(context.Context, *ListOrganizationDomainsRequest) (*ListOrganizationDomainsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListOrganizationDomains not implemented")
}
func (UnimplementedAimsServiceServer) UpdateOrganizationDomains(context.Context, *UpdateOrganizationDomainsRequest) (*UpdateOrganizationDomainsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateOrganizationDomains not implemented")
}
func (UnimplementedAimsServiceServer) ResetMFA(context.Context, *ResetMFARequest) (*ResetMFAResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetMFA not implemented")
}
func (UnimplementedAimsServiceServer) ListTeams(context.Context, *ListTeamsRequest) (*ListTeamsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTeams not implemented")
}
func (UnimplementedAimsServiceServer) ListTeamMembers(context.Context, *ListTeamMembersRequest) (*ListTeamMembersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTeamMembers not implemented")
}
func (UnimplementedAimsServiceServer) ListWorkspaces(context.Context, *ListWorkspacesRequest) (*ListWorkspacesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWorkspaces not implemented")
}
func (UnimplementedAimsServiceServer) GetWorkspace(context.Context, *GetWorkspaceRequest) (*GetWorkspaceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWorkspace not implemented")
}
func (UnimplementedAimsServiceServer) ListWorkspaceMembers(context.Context, *ListWorkspaceMembersRequest) (*ListWorkspaceMembersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWorkspaceMembers not implemented")
}
func (UnimplementedAimsServiceServer) ListWorkspaceCustomRoles(context.Context, *ListWorkspaceCustomRolesRequest) (*ListWorkspaceCustomRolesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWorkspaceCustomRoles not implemented")
}
func (UnimplementedAimsServiceServer) ListOrganizationCustomRoles(context.Context, *ListOrganizationCustomRolesRequest) (*ListOrganizationCustomRolesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListOrganizationCustomRoles not implemented")
}
func (UnimplementedAimsServiceServer) ListOrganizationUsers(context.Context, *ListOrganizationUsersRequest) (*ListOrganizationUsersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListOrganizationUsers not implemented")
}
func (UnimplementedAimsServiceServer) ListAIConversations(context.Context, *v1.ListAIConversationsRequest) (*v1.ListAIConversationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAIConversations not implemented")
}
func (UnimplementedAimsServiceServer) GetAIConversation(context.Context, *v1.GetAIConversationRequest) (*v1.GetAIConversationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAIConversation not implemented")
}
func (UnimplementedAimsServiceServer) mustEmbedUnimplementedAimsServiceServer() {}

// UnsafeAimsServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AimsServiceServer will
// result in compilation errors.
type UnsafeAimsServiceServer interface {
	mustEmbedUnimplementedAimsServiceServer()
}

func RegisterAimsServiceServer(s grpc.ServiceRegistrar, srv AimsServiceServer) {
	s.RegisterService(&AimsService_ServiceDesc, srv)
}

func _AimsService_GetInstanceById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInstanceByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).GetInstanceById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_GetInstanceById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).GetInstanceById(ctx, req.(*GetInstanceByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_GetKargoInstanceById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKargoInstanceByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).GetKargoInstanceById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_GetKargoInstanceById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).GetKargoInstanceById(ctx, req.(*GetKargoInstanceByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_GetInternalAuditLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInternalAuditLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).GetInternalAuditLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_GetInternalAuditLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).GetInternalAuditLogs(ctx, req.(*GetInternalAuditLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_ListArgoInstances_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListArgoInstancesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).ListArgoInstances(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_ListArgoInstances_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).ListArgoInstances(ctx, req.(*ListArgoInstancesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_DeleteUnpaidInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteUnpaidInstanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).DeleteUnpaidInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_DeleteUnpaidInstance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).DeleteUnpaidInstance(ctx, req.(*DeleteUnpaidInstanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_DeleteUnpaidKargoInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteUnpaidInstanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).DeleteUnpaidKargoInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_DeleteUnpaidKargoInstance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).DeleteUnpaidKargoInstance(ctx, req.(*DeleteUnpaidInstanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_ListKargoInstances_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListKargoInstancesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).ListKargoInstances(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_ListKargoInstances_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).ListKargoInstances(ctx, req.(*ListKargoInstancesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_OnboardManualCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OnboardManualCustomerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).OnboardManualCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_OnboardManualCustomer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).OnboardManualCustomer(ctx, req.(*OnboardManualCustomerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_GetOrganization_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrganizationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).GetOrganization(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_GetOrganization_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).GetOrganization(ctx, req.(*GetOrganizationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_GetFeatureGates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFeatureGatesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).GetFeatureGates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_GetFeatureGates_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).GetFeatureGates(ctx, req.(*GetFeatureGatesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_PatchFeatureGates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PatchFeatureGatesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).PatchFeatureGates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_PatchFeatureGates_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).PatchFeatureGates(ctx, req.(*PatchFeatureGatesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_InstanceClusterMaintenance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InstanceClusterMaintenanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).InstanceClusterMaintenance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_InstanceClusterMaintenance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).InstanceClusterMaintenance(ctx, req.(*InstanceClusterMaintenanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_UpdateQuotas_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateQuotasRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).UpdateQuotas(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_UpdateQuotas_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).UpdateQuotas(ctx, req.(*UpdateQuotasRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_ListUnbilledOrganizations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUnbilledOrganizationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).ListUnbilledOrganizations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_ListUnbilledOrganizations_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).ListUnbilledOrganizations(ctx, req.(*ListUnbilledOrganizationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_ListAllOrganizations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAllOrganizationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).ListAllOrganizations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_ListAllOrganizations_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).ListAllOrganizations(ctx, req.(*ListAllOrganizationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_ListOrganizationMembers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListOrganizationMembersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).ListOrganizationMembers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_ListOrganizationMembers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).ListOrganizationMembers(ctx, req.(*ListOrganizationMembersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_UpdateOrganizationTrialExpiration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateOrganizationTrialExpirationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).UpdateOrganizationTrialExpiration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_UpdateOrganizationTrialExpiration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).UpdateOrganizationTrialExpiration(ctx, req.(*UpdateOrganizationTrialExpirationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_DecrementInstanceGeneration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DecrementInstanceGenerationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).DecrementInstanceGeneration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_DecrementInstanceGeneration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).DecrementInstanceGeneration(ctx, req.(*DecrementInstanceGenerationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_ListClustersForInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListClustersForInstanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).ListClustersForInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_ListClustersForInstance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).ListClustersForInstance(ctx, req.(*ListClustersForInstanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_ListAgentsForKargoInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAgentsForKargoInstanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).ListAgentsForKargoInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_ListAgentsForKargoInstance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).ListAgentsForKargoInstance(ctx, req.(*ListAgentsForKargoInstanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_GetClusterManifests_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(GetClusterManifestsRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(AimsServiceServer).GetClusterManifests(m, &aimsServiceGetClusterManifestsServer{stream})
}

type AimsService_GetClusterManifestsServer interface {
	Send(*httpbody.HttpBody) error
	grpc.ServerStream
}

type aimsServiceGetClusterManifestsServer struct {
	grpc.ServerStream
}

func (x *aimsServiceGetClusterManifestsServer) Send(m *httpbody.HttpBody) error {
	return x.ServerStream.SendMsg(m)
}

func _AimsService_GetKargoAgentManifests_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(GetKargoAgentManifestsRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(AimsServiceServer).GetKargoAgentManifests(m, &aimsServiceGetKargoAgentManifestsServer{stream})
}

type AimsService_GetKargoAgentManifestsServer interface {
	Send(*httpbody.HttpBody) error
	grpc.ServerStream
}

type aimsServiceGetKargoAgentManifestsServer struct {
	grpc.ServerStream
}

func (x *aimsServiceGetKargoAgentManifestsServer) Send(m *httpbody.HttpBody) error {
	return x.ServerStream.SendMsg(m)
}

func _AimsService_SetManuallyVerified_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetManuallyVerifiedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).SetManuallyVerified(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_SetManuallyVerified_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).SetManuallyVerified(ctx, req.(*SetManuallyVerifiedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_SetDisabledInstanceCreation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetDisabledInstanceCreationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).SetDisabledInstanceCreation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_SetDisabledInstanceCreation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).SetDisabledInstanceCreation(ctx, req.(*SetDisabledInstanceCreationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_DeleteOrganization_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteOrganizationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).DeleteOrganization(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_DeleteOrganization_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).DeleteOrganization(ctx, req.(*DeleteOrganizationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_GetInternalConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInternalConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).GetInternalConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_GetInternalConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).GetInternalConfig(ctx, req.(*GetInternalConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_SendNotification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendNotificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).SendNotification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_SendNotification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).SendNotification(ctx, req.(*SendNotificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_ListAuditLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAuditLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).ListAuditLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_ListAuditLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).ListAuditLogs(ctx, req.(*ListAuditLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_ListAvailablePlans_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAvailablePlansRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).ListAvailablePlans(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_ListAvailablePlans_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).ListAvailablePlans(ctx, req.(*ListAvailablePlansRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_UpdateOrganizationBillingPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateOrganizationBillingPlanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).UpdateOrganizationBillingPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_UpdateOrganizationBillingPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).UpdateOrganizationBillingPlan(ctx, req.(*UpdateOrganizationBillingPlanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_GetKubeVisionUsage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKubeVisionUsageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).GetKubeVisionUsage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_GetKubeVisionUsage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).GetKubeVisionUsage(ctx, req.(*GetKubeVisionUsageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_ListOrganizationDomains_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListOrganizationDomainsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).ListOrganizationDomains(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_ListOrganizationDomains_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).ListOrganizationDomains(ctx, req.(*ListOrganizationDomainsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_UpdateOrganizationDomains_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateOrganizationDomainsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).UpdateOrganizationDomains(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_UpdateOrganizationDomains_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).UpdateOrganizationDomains(ctx, req.(*UpdateOrganizationDomainsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_ResetMFA_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetMFARequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).ResetMFA(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_ResetMFA_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).ResetMFA(ctx, req.(*ResetMFARequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_ListTeams_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTeamsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).ListTeams(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_ListTeams_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).ListTeams(ctx, req.(*ListTeamsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_ListTeamMembers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTeamMembersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).ListTeamMembers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_ListTeamMembers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).ListTeamMembers(ctx, req.(*ListTeamMembersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_ListWorkspaces_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListWorkspacesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).ListWorkspaces(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_ListWorkspaces_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).ListWorkspaces(ctx, req.(*ListWorkspacesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_GetWorkspace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWorkspaceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).GetWorkspace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_GetWorkspace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).GetWorkspace(ctx, req.(*GetWorkspaceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_ListWorkspaceMembers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListWorkspaceMembersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).ListWorkspaceMembers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_ListWorkspaceMembers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).ListWorkspaceMembers(ctx, req.(*ListWorkspaceMembersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_ListWorkspaceCustomRoles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListWorkspaceCustomRolesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).ListWorkspaceCustomRoles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_ListWorkspaceCustomRoles_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).ListWorkspaceCustomRoles(ctx, req.(*ListWorkspaceCustomRolesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_ListOrganizationCustomRoles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListOrganizationCustomRolesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).ListOrganizationCustomRoles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_ListOrganizationCustomRoles_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).ListOrganizationCustomRoles(ctx, req.(*ListOrganizationCustomRolesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_ListOrganizationUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListOrganizationUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).ListOrganizationUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_ListOrganizationUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).ListOrganizationUsers(ctx, req.(*ListOrganizationUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_ListAIConversations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.ListAIConversationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).ListAIConversations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_ListAIConversations_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).ListAIConversations(ctx, req.(*v1.ListAIConversationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimsService_GetAIConversation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.GetAIConversationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimsServiceServer).GetAIConversation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimsService_GetAIConversation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimsServiceServer).GetAIConversation(ctx, req.(*v1.GetAIConversationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AimsService_ServiceDesc is the grpc.ServiceDesc for AimsService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AimsService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "akuity.aims.v1.AimsService",
	HandlerType: (*AimsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetInstanceById",
			Handler:    _AimsService_GetInstanceById_Handler,
		},
		{
			MethodName: "GetKargoInstanceById",
			Handler:    _AimsService_GetKargoInstanceById_Handler,
		},
		{
			MethodName: "GetInternalAuditLogs",
			Handler:    _AimsService_GetInternalAuditLogs_Handler,
		},
		{
			MethodName: "ListArgoInstances",
			Handler:    _AimsService_ListArgoInstances_Handler,
		},
		{
			MethodName: "DeleteUnpaidInstance",
			Handler:    _AimsService_DeleteUnpaidInstance_Handler,
		},
		{
			MethodName: "DeleteUnpaidKargoInstance",
			Handler:    _AimsService_DeleteUnpaidKargoInstance_Handler,
		},
		{
			MethodName: "ListKargoInstances",
			Handler:    _AimsService_ListKargoInstances_Handler,
		},
		{
			MethodName: "OnboardManualCustomer",
			Handler:    _AimsService_OnboardManualCustomer_Handler,
		},
		{
			MethodName: "GetOrganization",
			Handler:    _AimsService_GetOrganization_Handler,
		},
		{
			MethodName: "GetFeatureGates",
			Handler:    _AimsService_GetFeatureGates_Handler,
		},
		{
			MethodName: "PatchFeatureGates",
			Handler:    _AimsService_PatchFeatureGates_Handler,
		},
		{
			MethodName: "InstanceClusterMaintenance",
			Handler:    _AimsService_InstanceClusterMaintenance_Handler,
		},
		{
			MethodName: "UpdateQuotas",
			Handler:    _AimsService_UpdateQuotas_Handler,
		},
		{
			MethodName: "ListUnbilledOrganizations",
			Handler:    _AimsService_ListUnbilledOrganizations_Handler,
		},
		{
			MethodName: "ListAllOrganizations",
			Handler:    _AimsService_ListAllOrganizations_Handler,
		},
		{
			MethodName: "ListOrganizationMembers",
			Handler:    _AimsService_ListOrganizationMembers_Handler,
		},
		{
			MethodName: "UpdateOrganizationTrialExpiration",
			Handler:    _AimsService_UpdateOrganizationTrialExpiration_Handler,
		},
		{
			MethodName: "DecrementInstanceGeneration",
			Handler:    _AimsService_DecrementInstanceGeneration_Handler,
		},
		{
			MethodName: "ListClustersForInstance",
			Handler:    _AimsService_ListClustersForInstance_Handler,
		},
		{
			MethodName: "ListAgentsForKargoInstance",
			Handler:    _AimsService_ListAgentsForKargoInstance_Handler,
		},
		{
			MethodName: "SetManuallyVerified",
			Handler:    _AimsService_SetManuallyVerified_Handler,
		},
		{
			MethodName: "SetDisabledInstanceCreation",
			Handler:    _AimsService_SetDisabledInstanceCreation_Handler,
		},
		{
			MethodName: "DeleteOrganization",
			Handler:    _AimsService_DeleteOrganization_Handler,
		},
		{
			MethodName: "GetInternalConfig",
			Handler:    _AimsService_GetInternalConfig_Handler,
		},
		{
			MethodName: "SendNotification",
			Handler:    _AimsService_SendNotification_Handler,
		},
		{
			MethodName: "ListAuditLogs",
			Handler:    _AimsService_ListAuditLogs_Handler,
		},
		{
			MethodName: "ListAvailablePlans",
			Handler:    _AimsService_ListAvailablePlans_Handler,
		},
		{
			MethodName: "UpdateOrganizationBillingPlan",
			Handler:    _AimsService_UpdateOrganizationBillingPlan_Handler,
		},
		{
			MethodName: "GetKubeVisionUsage",
			Handler:    _AimsService_GetKubeVisionUsage_Handler,
		},
		{
			MethodName: "ListOrganizationDomains",
			Handler:    _AimsService_ListOrganizationDomains_Handler,
		},
		{
			MethodName: "UpdateOrganizationDomains",
			Handler:    _AimsService_UpdateOrganizationDomains_Handler,
		},
		{
			MethodName: "ResetMFA",
			Handler:    _AimsService_ResetMFA_Handler,
		},
		{
			MethodName: "ListTeams",
			Handler:    _AimsService_ListTeams_Handler,
		},
		{
			MethodName: "ListTeamMembers",
			Handler:    _AimsService_ListTeamMembers_Handler,
		},
		{
			MethodName: "ListWorkspaces",
			Handler:    _AimsService_ListWorkspaces_Handler,
		},
		{
			MethodName: "GetWorkspace",
			Handler:    _AimsService_GetWorkspace_Handler,
		},
		{
			MethodName: "ListWorkspaceMembers",
			Handler:    _AimsService_ListWorkspaceMembers_Handler,
		},
		{
			MethodName: "ListWorkspaceCustomRoles",
			Handler:    _AimsService_ListWorkspaceCustomRoles_Handler,
		},
		{
			MethodName: "ListOrganizationCustomRoles",
			Handler:    _AimsService_ListOrganizationCustomRoles_Handler,
		},
		{
			MethodName: "ListOrganizationUsers",
			Handler:    _AimsService_ListOrganizationUsers_Handler,
		},
		{
			MethodName: "ListAIConversations",
			Handler:    _AimsService_ListAIConversations_Handler,
		},
		{
			MethodName: "GetAIConversation",
			Handler:    _AimsService_GetAIConversation_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "GetClusterManifests",
			Handler:       _AimsService_GetClusterManifests_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "GetKargoAgentManifests",
			Handler:       _AimsService_GetKargoAgentManifests_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "aims/v1/aims.proto",
}
