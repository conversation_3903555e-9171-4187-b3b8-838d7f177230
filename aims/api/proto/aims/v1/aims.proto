syntax = "proto3";

package akuity.aims.v1;

import "argocd/v1/argocd.proto";
import "google/api/annotations.proto";
import "google/api/httpbody.proto";
import "google/api/visibility.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "kargo/v1/kargo.proto";
import "organization/v1/organization.proto";
import "types/features/v1/features.proto";

service AimsService {
  option (google.api.api_visibility).restriction = "INTERNAL";

  rpc GetInstanceById(GetInstanceByIdRequest) returns (GetInstanceByIdResponse) {
    option (google.api.http) = {get: "/api/v1/aims/instances/{instance_id}"};
  }

  rpc GetKargoInstanceById(GetKargoInstanceByIdRequest) returns (GetKargoInstanceByIdResponse) {
    option (google.api.http) = {get: "/api/v1/aims/kargo/instances/{instance_id}"};
  }

  rpc GetInternalAuditLogs(GetInternalAuditLogsRequest) returns (GetInternalAuditLogsResponse) {
    option (google.api.http) = {get: "/api/v1/aims/internal-audit-logs"};
  }

  rpc ListArgoInstances(ListArgoInstancesRequest) returns (ListArgoInstancesResponse) {
    option (google.api.http) = {get: "/api/v1/aims/argo/instances"};
  }

  // buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
  rpc DeleteUnpaidInstance(DeleteUnpaidInstanceRequest) returns (DeleteUnpaidInstanceResponse) {
    option (google.api.http) = {delete: "/api/v1/aims/instances/{instance_id}"};
  }

  // buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
  // buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
  // buf:lint:ignore RPC_REQUEST_STANDARD_NAME
  rpc DeleteUnpaidKargoInstance(DeleteUnpaidInstanceRequest) returns (DeleteUnpaidInstanceResponse) {
    option (google.api.http) = {delete: "/api/v1/aims/kargo/instances/{instance_id}"};
  }

  rpc ListKargoInstances(ListKargoInstancesRequest) returns (ListKargoInstancesResponse) {
    option (google.api.http) = {get: "/api/v1/aims/kargo/instances"};
  }

  rpc OnboardManualCustomer(OnboardManualCustomerRequest) returns (OnboardManualCustomerResponse) {
    option (google.api.http) = {
      post: "/api/v1/aims/onboard"
      body: "*"
    };
  }

  rpc GetOrganization(GetOrganizationRequest) returns (GetOrganizationResponse) {
    option (google.api.http) = {get: "/api/v1/aims/organizations/{organization_id}"};
  }

  rpc GetFeatureGates(GetFeatureGatesRequest) returns (GetFeatureGatesResponse) {
    option (google.api.http) = {get: "/api/v1/aims/organizations/{id}/feature-gates"};
  }

  rpc PatchFeatureGates(PatchFeatureGatesRequest) returns (PatchFeatureGatesResponse) {
    option (google.api.http) = {
      patch: "/api/v1/aims/organizations/{id}/feature-gates"
      body: "*"
    };
  }

  rpc InstanceClusterMaintenance(InstanceClusterMaintenanceRequest) returns (InstanceClusterMaintenanceResponse) {
    option (google.api.http) = {
      post: "/api/v1/aims/instances/{instance_id}/clusters/{cluster_id}"
      body: "*"
    };
  }

  rpc UpdateQuotas(UpdateQuotasRequest) returns (UpdateQuotasResponse) {
    option (google.api.http) = {
      put: "/api/v1/aims/organizations/{id}/quotas"
      body: "*"
    };
  }

  rpc ListUnbilledOrganizations(ListUnbilledOrganizationsRequest) returns (ListUnbilledOrganizationsResponse) {
    option (google.api.http) = {get: "/api/v1/aims/organizations/unbilled"};
  }

  rpc ListAllOrganizations(ListAllOrganizationsRequest) returns (ListAllOrganizationsResponse) {
    option (google.api.http) = {get: "/api/v1/aims/organizations"};
  }

  rpc ListOrganizationMembers(ListOrganizationMembersRequest) returns (ListOrganizationMembersResponse) {
    option (google.api.http) = {get: "/api/v1/aims/organizations/members"};
  }

  rpc UpdateOrganizationTrialExpiration(UpdateOrganizationTrialExpirationRequest) returns (UpdateOrganizationTrialExpirationResponse) {
    option (google.api.http) = {
      post: "/api/v1/aims/organizations/{organization_id}/trial"
      body: "*"
    };
  }

  rpc DecrementInstanceGeneration(DecrementInstanceGenerationRequest) returns (DecrementInstanceGenerationResponse) {
    option (google.api.http) = {
      post: "/api/v1/aims/instances/{instance_id}/generation/decrement"
      body: "*"
    };
  }

  rpc ListClustersForInstance(ListClustersForInstanceRequest) returns (ListClustersForInstanceResponse) {
    option (google.api.http) = {get: "/api/v1/aims/instances/{instance_id}/clusters"};
  }

  rpc ListAgentsForKargoInstance(ListAgentsForKargoInstanceRequest) returns (ListAgentsForKargoInstanceResponse) {
    option (google.api.http) = {get: "/api/v1/aims/kargo/instances/{instance_id}/agents"};
  }

  // buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
  // buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
  rpc GetClusterManifests(GetClusterManifestsRequest) returns (stream google.api.HttpBody) {
    option (google.api.http) = {get: "/api/v1/aims/instances/{instance_id}/clusters/{cluster_id}/manifests"};
  }

  // buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
  // buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
  rpc GetKargoAgentManifests(GetKargoAgentManifestsRequest) returns (stream google.api.HttpBody) {
    option (google.api.http) = {get: "/api/v1/aims/kargo/instances/{instance_id}/agents/{agent_id}/manifests"};
  }

  rpc SetManuallyVerified(SetManuallyVerifiedRequest) returns (SetManuallyVerifiedResponse) {
    option (google.api.http) = {
      post: "/api/v1/aims/organizations/{organization_id}/verified"
      body: "*"
    };
  }

  rpc SetDisabledInstanceCreation(SetDisabledInstanceCreationRequest) returns (SetDisabledInstanceCreationResponse) {
    option (google.api.http) = {
      post: "/api/v1/aims/organizations/instance_creation"
      body: "*"
    };
  }

  rpc DeleteOrganization(DeleteOrganizationRequest) returns (DeleteOrganizationResponse) {
    option (google.api.http) = {delete: "/api/v1/aims/organizations/{organization_id}"};
  }

  rpc GetInternalConfig(GetInternalConfigRequest) returns (GetInternalConfigResponse) {
    option (google.api.http) = {get: "/api/v1/aims/internal/config"};
  }

  rpc SendNotification(SendNotificationRequest) returns (SendNotificationResponse) {
    option (google.api.http) = {
      post: "/api/v1/aims/notification"
      body: "*"
    };
  }

  rpc ListAuditLogs(ListAuditLogsRequest) returns (ListAuditLogsResponse) {
    option (google.api.http) = {get: "/api/v1/aims/organizations/{organization_id}/audit-logs"};
  }

  rpc ListAvailablePlans(ListAvailablePlansRequest) returns (ListAvailablePlansResponse) {
    option (google.api.http) = {get: "/api/v1/aims/plans"};
  }

  rpc UpdateOrganizationBillingPlan(UpdateOrganizationBillingPlanRequest) returns (UpdateOrganizationBillingPlanResponse) {
    option (google.api.http) = {
      post: "/api/v1/aims/organizations/{organization_id}/plan"
      body: "*"
    };
  }

  rpc GetKubeVisionUsage(GetKubeVisionUsageRequest) returns (GetKubeVisionUsageResponse) {
    option (google.api.http) = {get: "/api/v1/aims/organizations/{organization_id}/k8s/usage"};
  }

  rpc ListOrganizationDomains(ListOrganizationDomainsRequest) returns (ListOrganizationDomainsResponse) {
    option (google.api.http) = {get: "/api/v1/aims/organizations/{organization_id}/domains"};
  }

  rpc UpdateOrganizationDomains(UpdateOrganizationDomainsRequest) returns (UpdateOrganizationDomainsResponse) {
    option (google.api.http) = {
      post: "/api/v1/aims/organizations/{organization_id}/domains"
      body: "*"
    };
  }

  rpc ResetMFA(ResetMFARequest) returns (ResetMFAResponse) {
    option (google.api.http) = {
      post: "/api/v1/aims/mfa/reset"
      body: "*"
    };
  }

  rpc ListTeams(ListTeamsRequest) returns (ListTeamsResponse) {
    option (google.api.http) = {get: "/api/v1/aims/organizations/{organization_id}/teams"};
  }

  rpc ListTeamMembers(ListTeamMembersRequest) returns (ListTeamMembersResponse) {
    option (google.api.http) = {get: "/api/v1/aims/organizations/{organization_id}/teams/{team_name}/members"};
  }

  rpc ListWorkspaces(ListWorkspacesRequest) returns (ListWorkspacesResponse) {
    option (google.api.http) = {get: "/api/v1/aims/organizations/{organization_id}/workspaces"};
  }

  rpc GetWorkspace(GetWorkspaceRequest) returns (GetWorkspaceResponse) {
    option (google.api.http) = {get: "/api/v1/aims/organizations/{organization_id}/workspaces/{workspace_id}"};
  }

  rpc ListWorkspaceMembers(ListWorkspaceMembersRequest) returns (ListWorkspaceMembersResponse) {
    option (google.api.http) = {get: "/api/v1/aims/organizations/{organization_id}/workspaces/{workspace_id}/members"};
  }

  rpc ListWorkspaceCustomRoles(ListWorkspaceCustomRolesRequest) returns (ListWorkspaceCustomRolesResponse) {
    option (google.api.http) = {get: "/api/v1/aims/organizations/{organization_id}/workspaces/{workspace_id}/custom-roles"};
  }

  rpc ListOrganizationCustomRoles(ListOrganizationCustomRolesRequest) returns (ListOrganizationCustomRolesResponse) {
    option (google.api.http) = {get: "/api/v1/aims/organizations/{organization_id}/custom-roles"};
  }

  rpc ListOrganizationUsers(ListOrganizationUsersRequest) returns (ListOrganizationUsersResponse) {
    option (google.api.http) = {get: "/api/v1/aims/organizations/{organization_id}/users"};
  }

  rpc ListAIConversations(akuity.organization.v1.ListAIConversationsRequest) returns (akuity.organization.v1.ListAIConversationsResponse) {
    option (google.api.http) = {get: "/api/v1/aims/organizations/{organization_id}/ai/conversations"};
  }

  rpc GetAIConversation(akuity.organization.v1.GetAIConversationRequest) returns (akuity.organization.v1.GetAIConversationResponse) {
    option (google.api.http) = {get: "/api/v1/aims/organizations/{organization_id}/ai/conversations/{id}"};
  }
}

enum NotificationCategory {
  NOTIFICATION_CATEGORY_UNSPECIFIED = 0;
  NOTIFICATION_CATEGORY_CUSTOM = 1;
  NOTIFICATION_CATEGORY_NEW_FEATURE = 2;
}

message SendNotificationRequest {
  // test mode ensures that notification
  // 1. is only sent to the organization containing akuity member
  // 2. member in the audit email should be same in that organization
  optional bool test_mode = 1;
  // currently, only used when test mode is on
  optional string organization_id = 2;
  Audit audit = 3;
  NotificationCategory category = 4;
  // this should match
  google.protobuf.Struct metadata = 5;
}

message SendNotificationResponse {
  /* explicitly empty */
}

message ArgoInstanceFilter {
  optional bool paid = 1;
  optional bool unpaid = 2;
  // search by instance name/id
  string fuzz = 3;
  optional string time_from = 4;
  // filter by organization ID
  optional string organization_id = 5;
}

message ListArgoInstancesRequest {
  optional ArgoInstanceFilter filter = 1;
}

message ListArgoInstancesResponse {
  repeated InternalInstance instances = 1;
}

message DeleteUnpaidInstanceRequest {
  string instance_id = 1;
  Audit audit = 2;
}

message DeleteUnpaidInstanceResponse {
  /* explicitly empty */
}

message OnboardManualCustomerRequest {
  Customer customer = 1;
  Audit audit = 2;
}

message OnboardManualCustomerResponse {
  /* explicitly empty */
}

message NotificationConfig {
  map<string, string> config = 1;
}

message InternalInstance {
  akuity.argocd.v1.Instance instance = 1;
  optional google.protobuf.Timestamp create_time = 2;
  bool expired = 3;
  uint32 connected_clusters = 4;
  optional string status_processed_info = 5;
  string org_id = 6;
  NotificationConfig notification_config = 7;
  optional Workspace workspace = 8;
}

message Customer {
  string stripe_id = 1;
  string organization_id = 2;
  optional string billing_name = 3;
  optional string billing_email = 4;
}

message ListOrganizationDomainsRequest {
  string organization_id = 1;
}

message ListOrganizationDomainsResponse {
  repeated akuity.organization.v1.DomainVerification domains = 1;
}

message UpdateOrganizationDomainsRequest {
  string organization_id = 1;
  repeated akuity.organization.v1.DomainVerification domains = 2;
  Audit audit = 3;
}

message UpdateOrganizationDomainsResponse {
  repeated akuity.organization.v1.DomainVerification domains = 1;
}

message ListOrganizationMembersRequest {
  repeated string organization_id = 1;
}

message OrganizationMembers {
  repeated string email = 1;
}

message ListOrganizationMembersResponse {
  map<string, OrganizationMembers> members = 1;
}

message ListUnbilledOrganizationsRequest {
  /* explicitly empty */
}

message ListUnbilledOrganizationsResponse {
  repeated BasicOrganization organizations = 1;
}

enum Sort {
  SORT_UNSPECIFIED = 0;
  SORT_ASCENDING = 1;
  SORT_DESCENDING = 2;
}

message OrganizationFilter {
  // default limit 50
  optional uint32 limit = 1;
  optional uint32 offset = 2;
  // fuzzy search
  // by name
  // by id
  // by member
  optional string fuzz = 3;
  optional Sort sort_by_creation = 4;
  optional bool billed = 5;
  optional bool manually_verified = 6;
  repeated string plans = 7;
  optional string start_time = 8;
  optional string end_time = 9;
}

message ListAllOrganizationsRequest {
  optional OrganizationFilter filters = 1;
}

message ListAllOrganizationsResponse {
  repeated BasicOrganization organizations = 1;
  uint32 count = 2;
}

message GetOrganizationRequest {
  string organization_id = 1;
}

message GetOrganizationResponse {
  BasicOrganization organization = 1;
}

message StripeData {
  google.protobuf.Timestamp subscription_end_time = 2;
  bool stale = 3;
}

message BasicOrganization {
  string id = 1;
  string name = 2;
  bool billed = 3;
  repeated string emails = 4;
  bool manually_verified = 6;
  uint64 num_instances = 8;
  string plan = 9;
  optional akuity.organization.v1.OrganizationStatus status = 10;
  akuity.types.features.v1.OrganizationQuota quota = 11;
  akuity.types.features.v1.OrganizationUsage usage = 12;
  akuity.organization.v1.BillingDetails billing_details = 13;
  StripeData stripe_data = 14;
  optional google.protobuf.Timestamp creation_timestamp = 15;
  bool inactive = 16;
  bool can_delete = 17;
  map<string, int32> misc = 18;
}

message PlanUsage {
  int32 control_planes = 1;
  int32 applications = 2;
  int32 clusters = 3;
  int32 members = 4;
}

message Audit {
  string actor = 1;
  string reason = 2;
}

message PatchFeatureGatesRequest {
  string id = 1;
  akuity.types.features.v1.OrganizationFeatureGates feature_gates = 2;
  Audit audit = 3;
}

message PatchFeatureGatesResponse {
  akuity.types.features.v1.OrganizationFeatureGates feature_gates = 1;
}

message GetFeatureGatesRequest {
  string id = 1;
}

message GetFeatureGatesResponse {
  akuity.types.features.v1.OrganizationFeatureGates feature_gates = 1;
  akuity.types.features.v1.SystemFeatureGates system_feature_gates = 2;
}

message UpdateQuotasRequest {
  string id = 1;
  Audit audit = 2;
  akuity.types.features.v1.OrganizationQuota quota = 3;
}

message UpdateQuotasResponse {
  akuity.types.features.v1.OrganizationQuota quota = 1;
  optional google.protobuf.Timestamp creation_timestamp = 9;
}

message UpdateOrganizationTrialExpirationRequest {
  string organization_id = 1;
  uint64 trial_expiration = 2;
  Audit audit = 3;
}

message UpdateOrganizationTrialExpirationResponse {
  /* explicitly empty */
}

message DecrementInstanceGenerationRequest {
  string instance_id = 1;
  Audit audit = 2;
}

message DecrementInstanceGenerationResponse {
  /* explicitly empty */
}

message GetInstanceByIdRequest {
  string instance_id = 1;
}

message GetInstanceByIdResponse {
  InternalInstance instance = 1;
}

message GetKargoInstanceByIdRequest {
  string instance_id = 1;
}

message GetKargoInstanceByIdResponse {
  InternalKargoInstance instance = 1;
}

message ClusterFilter {
  // search by name/id/namespace
  string fuzz = 1;
  optional string time_from = 3;
}

message ListClustersForInstanceRequest {
  string instance_id = 1;
  ClusterFilter filter = 2;
}

message ListClustersForInstanceResponse {
  repeated akuity.argocd.v1.Cluster clusters = 1;
}

message ListAgentsForKargoInstanceRequest {
  string instance_id = 1;
}

message ListAgentsForKargoInstanceResponse {
  repeated akuity.kargo.v1.KargoAgent agents = 1;
}

message SetManuallyVerifiedRequest {
  string organization_id = 1;
  bool verified = 2;
  Audit audit = 3;
}

message SetManuallyVerifiedResponse {
  /* explicitly empty */
}

message SetDisabledInstanceCreationRequest {
  bool disabled = 1;
  Audit audit = 2;
}

message SetDisabledInstanceCreationResponse {
  /* explicitly empty */
}

message DeleteOrganizationRequest {
  string organization_id = 1;
  Audit audit = 2;
}

message DeleteOrganizationResponse {
  /* explicitly empty */
}

message GetInternalConfigRequest {
  /* explicitly empty */
}

message InternalConfig {
  bool disable_free_instance_creation = 1;
}

message GetInternalConfigResponse {
  InternalConfig config = 1;
}

message InternalKargoInstance {
  akuity.kargo.v1.KargoInstance instance = 1;
  // only basic fields of BasicOrganization
  BasicOrganization organization = 2;
  optional google.protobuf.Timestamp creation_timestamp = 3;
  optional Workspace workspace = 4;
  optional google.protobuf.Struct status_info = 5;
}

message KargoInstanceFilter {
  optional bool paid = 1;
  optional bool unpaid = 2;
  // search by instance name/id
  string fuzz = 3;
  optional string time_from = 4;
  // filter by organization ID
  optional string organization_id = 5;
}

message ListKargoInstancesRequest {
  optional KargoInstanceFilter filter = 1;
}

message ListKargoInstancesResponse {
  repeated InternalKargoInstance instances = 1;
}

message InstanceClusterMaintenanceRequest {
  string instance_id = 1;
  string cluster_id = 2;
  bool maintanence_mode = 3;
  Audit audit = 4;
}

message InstanceClusterMaintenanceResponse {
  /* explicitly empty */
}

message ListAuditLogsRequest {
  string organization_id = 1;
  optional akuity.organization.v1.AuditFilters filters = 2;
}

message ListAuditLogsResponse {
  repeated akuity.organization.v1.AuditLog audit_logs = 1;
  uint32 count = 2;
}

message ListAvailablePlansRequest {
  /* explicitly empty */
}

message Plan {
  string name = 1;
  string product_id = 2;
  google.protobuf.Struct features = 3;
  google.protobuf.Struct quotas = 4;
  bool default = 5;
}

message ListAvailablePlansResponse {
  repeated Plan plans = 1;
}

message UpdateOrganizationBillingPlanRequest {
  string organization_id = 1;
  string plan = 2;
  Audit audit = 3;
}

message UpdateOrganizationBillingPlanResponse {
  /* explicitly empty */
}

message GetKubeVisionUsageRequest {
  string organization_id = 1;
  optional google.protobuf.Timestamp start_time = 2;
  optional google.protobuf.Timestamp end_time = 3;
}

message GetKubeVisionUsageResponse {
  repeated akuity.organization.v1.KubeVisionUsage usage = 1;
}

message ResetMFARequest {
  string organization_id = 1;
  string email = 2;
  Audit audit = 3;
}

message ResetMFAResponse {
  /* explicitly empty */
}

message Team {
  string name = 1;
  string description = 2;
  google.protobuf.Timestamp create_time = 3;
  int64 member_count = 4;
}

message UserTeam {
  Team team = 1;
  bool is_member = 2;
}

message ListTeamsRequest {
  string organization_id = 1;
  optional uint32 limit = 2;
  optional uint32 offset = 3;
}

message ListTeamsResponse {
  repeated Team teams = 1;
  uint32 count = 2;
}

message TeamMember {
  string id = 1;
  string email = 2;
}

message ListTeamMembersRequest {
  string organization_id = 1;
  string team_name = 2;
  optional int64 limit = 3;
  optional int64 offset = 4;
}

message ListTeamMembersResponse {
  repeated TeamMember team_members = 1;
  int64 count = 2;
}

message WorkspaceArgoCDInstance {
  string id = 1;
  string name = 2;
}

message WorkspaceKargoInstance {
  string id = 1;
  string name = 2;
}

enum WorkspaceMemberRole {
  WORKSPACE_MEMBER_ROLE_UNSPECIFIED = 0;
  WORKSPACE_MEMBER_ROLE_MEMBER = 1;
  WORKSPACE_MEMBER_ROLE_ADMIN = 2;
}

message WorkspaceUserMember {
  string id = 1;
  string email = 2;
}

message WorkspaceTeamMember {
  string id = 1;
  string name = 2;
  string description = 3;
  google.protobuf.Timestamp create_time = 4;
  int64 member_count = 5;
}

message WorkspaceMember {
  string id = 1;
  WorkspaceMemberRole role = 2;
  oneof member {
    WorkspaceUserMember user = 3;
    WorkspaceTeamMember team = 4;
  }
}

message WorkspaceMemberRef {
  WorkspaceMemberRole role = 1;
  oneof member {
    string user_id = 2;
    string user_email = 3;
    string team_name = 4;
  }
}

message CreateWorkspaceRequest {
  string organization_id = 1;
  string name = 2;
  optional string description = 3;
}

message CreateWorkspaceResponse {
  Workspace workspace = 1;
}

message Workspace {
  string id = 1;
  string name = 2;
  string description = 3;
  google.protobuf.Timestamp create_time = 4;
  repeated WorkspaceArgoCDInstance argocd_instances = 5;
  repeated WorkspaceKargoInstance kargo_instances = 6;
  uint32 team_member_count = 7;
  uint32 user_member_count = 8;
  bool is_default = 9;
}

message ListWorkspacesRequest {
  string organization_id = 1;
  optional uint32 limit = 2;
  optional uint32 offset = 3;
}

message ListWorkspacesResponse {
  repeated Workspace workspaces = 1;
  uint32 count = 2;
}

message GetWorkspaceRequest {
  string organization_id = 1;
  string workspace_id = 2;
}

message GetWorkspaceResponse {
  Workspace workspace = 1;
}

message ListWorkspaceMembersRequest {
  string organization_id = 1;
  string workspace_id = 2;
  optional uint32 limit = 3;
  optional uint32 offset = 4;
}

message ListWorkspaceMembersResponse {
  repeated WorkspaceMember workspace_members = 1;
  uint32 team_member_count = 2;
  uint32 user_member_count = 3;
}

message CustomRole {
  string id = 1;
  string name = 2;
  string description = 3;
  string policy = 4;
}

message ListWorkspaceCustomRolesRequest {
  string organization_id = 1;
  string workspace_id = 2;
  optional uint32 limit = 3;
  optional uint32 offset = 4;
}

message ListWorkspaceCustomRolesResponse {
  repeated CustomRole custom_roles = 1;
  string workspace_id = 2;
  int64 total_count = 3;
}

message ListOrganizationCustomRolesRequest {
  string organization_id = 1;
  optional uint32 limit = 2;
  optional uint32 offset = 3;
}

message ListOrganizationCustomRolesResponse {
  repeated CustomRole custom_roles = 1;
  int64 total_count = 2;
}

message ListOrganizationUsersRequest {
  string organization_id = 1;
}

message ListOrganizationUsersResponse {
  repeated OrganizationUser users = 1;
}

message WorkspaceInfo {
  string id = 1;
  string name = 2;
}

message TeamInfo {
  string id = 1;
  string name = 2;
}

message OrganizationUser {
  string id = 1;
  string email = 2;
  string role = 3;
  repeated WorkspaceInfo workspaces = 4;
  repeated TeamInfo teams = 5;
}

message AuditLog {
  message AuditActor {
    string type = 1;
    string id = 2;
    optional string ip = 3;
  }

  message AuditObject {
    string type = 1;
    string id = 2;
  }

  message AuditDetails {
    string message = 1;
    string patch = 2;
  }

  string timestamp = 1;
  string action = 2;
  AuditActor actor = 3;
  AuditObject object = 4;
  AuditDetails details = 5;
}

message InternalAuditFilters {
  repeated string actor_id = 1;
  optional string object_type = 2;
  repeated string action = 3;
  optional string start_time = 4;
  optional string end_time = 5;
  optional uint32 limit = 6;
  optional uint32 offset = 7;
}

message GetInternalAuditLogsRequest {
  InternalAuditFilters filters = 1;
}

message GetInternalAuditLogsResponse {
  repeated AuditLog items = 1;
  uint32 total_count = 2;
}

message GetClusterManifestsRequest {
  string instance_id = 1;
  string cluster_id = 2;
  Audit audit = 3;
}

message GetKargoAgentManifestsRequest {
  string instance_id = 1;
  string agent_id = 2;
  Audit audit = 3;
}
