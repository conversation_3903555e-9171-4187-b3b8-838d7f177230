syntax = "proto3";

package akuity.notifications.webhook.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/akuityio/akuity-platform/pkg/api/gen/notifications/v1;webhookv1";

// `WebhookEventType` defines the types of webhook events.
enum WebhookEventType {
  WEBHOOK_EVENT_TYPE_UNSPECIFIED = 0; // Unspecified event type.
  WEBHOOK_EVENT_TYPE_PING = 1; // Ping event type.
  WEBHOOK_EVENT_TYPE_AUDIT = 2; // Audit log event type.
  WEBHOOK_EVENT_TYPE_USAGE = 3; // Usage alert event type.
}

// `WebhookEventPayload` is the payload sent to the webhook endpoint.
message WebhookEventPayload {
  google.protobuf.Timestamp event_time = 1; // The time the event was triggered.
  WebhookEventType event_type = 2; // The type of event.
  string organization_id = 3; // The organization ID.
  string event_id = 4; // The event ID.
  oneof metadata { // The event metadata.
    PingEvent ping_event = 5; // Metadata for a ping event.
    UsageUpdateEvent usage_event = 6; // Metadata for a usage update event.
    AuditEvent audit_event = 7; // Metadata for an audit event.
  }
}

// `PingEvent` contains metadata for a ping event.
message PingEvent {
  // NotificationConfigID is the organization notification config ID that triggered the ping
  string notification_config_id = 1;
}

message UsageUpdateEvent {
  string product = 1; // The product associated with the usage update. E.g. "ArgoCD", "Kargo", "Akuity Agent".
  string usage_type = 2; // The type of usage. E.g. "applications", "projects".
  double usage_threshold = 3; // The usage threshold.
  int64 max_limit = 4; // The maximum limit.
  int64 usage = 5; // The current usage.
}

/* @exclude taken from akuity.organization.v1.AuditLog */
// `AuditEvent` contains metadata for an audit event.
message AuditEvent {
  message EventAuditActor {
    string type = 1; // The type of actor.
    string id = 2; // The ID of the actor.
    optional string ip = 3; // The IP address of the actor.
  }

  message EventAuditObject {
    string type = 1; // The type of object. E.g. "team_member", "team", "custom_role", "kargo_instance", etc.

    message EventAuditObjId {
      string name = 1; // The name of the object.
      string kind = 2; // The kind of object.
      string group = 3; // The group of the object.
    }

    message EventAuditParentId {
      string name = 1; // The name of the parent.
      string parent_name = 2; // The name of the parent object's parent (if present).
      string application_name = 3; // The name of the application.
    }

    EventAuditObjId id = 2; // The ID of the object.
    EventAuditParentId parent_id = 3; // The parent ID of the object.
  }

  message EventAuditDetails {
    string message = 1; // The message associated with the event.
    string patch = 2; // The patch associated with the event.
    string action_type = 3; // The action type.
  }

  string timestamp = 1; // The time the event was triggered.
  string action = 2; // The action that was performed.
  EventAuditActor actor = 3; // The actor that triggered the event.
  EventAuditObject object = 4; // The object that was acted upon.
  EventAuditDetails details = 5; // The details of the event.
}
