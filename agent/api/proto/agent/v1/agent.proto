syntax = "proto3";

package akuity.agent.v1;

import "google/api/annotations.proto";
import "google/api/visibility.proto";
import "google/protobuf/struct.proto";
import "types/k8s/v1/k8s.proto";

service AgentService {
  option (google.api.api_visibility).restriction = "INTERNAL";

  rpc UpdateClusterKubernetesInfo(UpdateClusterKubernetesInfoRequest) returns (UpdateClusterKubernetesInfoResponse) {
    option (google.api.http) = {
      post: "/agent-api/v1/clusters/{cluster_id}/k8s-info"
      body: "*"
    };
  }
  rpc UpdateKubernetesResources(UpdateKubernetesResourcesRequest) returns (UpdateKubernetesResourcesResponse) {
    option (google.api.http) = {
      post: "/agent-api/v1/clusters/{cluster_id}/k8s-resources"
      body: "*"
    };
  }
  rpc UpdateKubernetesEvents(UpdateKubernetesEventsRequest) returns (UpdateKubernetesEventsResponse) {
    option (google.api.http) = {
      post: "/agent-api/v1/clusters/{cluster_id}/k8s-events"
      body: "*"
    };
  }

  rpc CollectLogs(stream CollectLogsRequest) returns (CollectLogsResponse) {
    option (google.api.http) = {
      post: "/agent-api/v1/stream/argocd/agent/logs"
      body: "*"
      additional_bindings: {
        post: "/agent-api/v1/stream/kargo/agent/logs"
        body: "*"
      }
    };
  }
}

message UpdateClusterKubernetesInfoRequest {
  string cluster_id = 1;
  ClusterKubernetesInfo cluster_k8s_info = 2;
}

message UpdateClusterKubernetesInfoResponse {
  /* explicitly empty */
}

message UpdateKubernetesResourcesRequest {
  string cluster_id = 1;
  repeated ResourceEvent events = 2;
  // If overrideAll is true, then all current resources in the portal db will be pruned and use the resources from the request as the current state of the resources.
  bool override_all = 3;
}

message UpdateKubernetesResourcesResponse {
  /* empty */
}

message UpdateKubernetesEventsRequest {
  string cluster_id = 1;
  repeated KubernetesEvent events = 2;
}

message UpdateKubernetesEventsResponse {
  /* empty */
}

message ClusterKubernetesInfo {
  string kubernetes_version = 1;
  repeated akuity.types.k8s.v1.ResourceType resource_types = 2;
  uint32 api_resource_count = 3;
  uint32 object_count = 4;
  bool metric_server_unavailable = 5;
  optional string last_refresh_time = 6;
}

enum ResourceEventType {
  RESOURCE_EVENT_TYPE_UNSPECIFIED = 0;
  RESOURCE_EVENT_TYPE_ADDED = 1;
  RESOURCE_EVENT_TYPE_MODIFIED = 2;
  RESOURCE_EVENT_TYPE_DELETED = 3;
}

message ResourceEvent {
  Resource resource = 1;
  ResourceEventType type = 2;
}

message Resource {
  string name = 1;
  optional string namespace = 2;
  akuity.types.k8s.v1.GroupVersionKind group_version_kind = 3;
  map<string, string> columns = 4;
  optional ArgoCDApplicationInfo argocd_application_info = 5;
  string uid = 6;
  string create_time = 7;
  optional string owner_id = 8;
  int32 children_count = 9;
  string delete_time = 10;
}

message ArgoCDApplicationInfo {
  string name = 1;
  string sync_status = 2;
  string health_status = 3;
}

message KubernetesEvent {
  string id = 1;
  string group = 2;
  string kind = 3;
  string name = 4;
  string namespace = 5;
  KubernetesEventInfo info = 6;
  string timestamp = 7;
  string severity = 8;
  string reason = 9;
  string last_timestamp = 10;
  int32 count = 11;
}

message KubernetesEventInfo {
  string message = 1;
  string old_value = 2;
  string new_value = 3;
}

message CollectLogsRequest {
  LogType log_type = 2;
  string log_chunk = 3;
  google.protobuf.Struct metadata = 4;
  int64 ttl_seconds = 5;
  bool finished = 6;
}

enum LogType {
  LOG_TYPE_UNSPECIFIED = 0;
  LOG_TYPE_KARGO_ANALYSIS_JOB = 1;
}

message CollectLogsResponse {
  /* empty */
}
