package pages

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/playwright-community/playwright-go"

	"github.com/akuityio/akuity-platform/test/utils/ui/akuity/components"
)

func NewKargoDetailsPage(page playwright.Page) *KargoDetailsPage {
	return &KargoDetailsPage{page: page}
}

type KargoDetailsPage struct {
	*AgentModal
	page playwright.Page
}

func (p *KargoDetailsPage) ClickInstallAgent(agentName string) *AgentModal {
	if err := p.page.Locator(fmt.Sprintf(`tr[data-row-key*="%s"] svg.fa-wrench`, agentName)).First().Click(); err != nil {
		panic(err)
	}

	element := p.page.Locator(`.ant-modal-content:has-text("Install Akuity Agent")`)
	if err := element.WaitFor(); err != nil {
		panic(err)
	}

	return &AgentModal{element: element, page: p.page}
}

func (p *KargoDetailsPage) AgentNames() []string {
	if err := p.page.Locator(`h4:has-text("Kargo Agents")`).WaitFor(); err != nil {
		panic(err)
	}

	elements, err := p.page.Locator(`tr[data-row-key] td:nth-child(2)`).All()
	if err != nil {
		panic(err)
	}

	var clusters []string
	for _, element := range elements {
		content, err := element.InnerText()
		if err != nil {
			panic(err)
		}
		clusters = append(clusters, strings.TrimSpace(content))
	}

	return clusters
}

func (p *KargoDetailsPage) AgentExists(name string) (bool, error) {
	agents, err := p.page.Locator(`tr[data-row-key] td:nth-child(2):has-text("` + name + `")`).All()
	if err != nil {
		return false, err
	}
	if len(agents) == 0 {
		return false, nil
	}
	return true, nil
}

func (p *KargoDetailsPage) Agents() *KargoDetailsPage {
	agentsTab := p.page.Locator(`.ant-tabs-tab-btn:has-text('Agents')`)
	if err := agentsTab.Click(); err != nil {
		panic(err)
	}

	return &KargoDetailsPage{page: p.page, AgentModal: &AgentModal{element: agentsTab, page: p.page}}
}

func (p *KargoDetailsPage) ClickAgentSettings(clusterName string) *AgentModal {
	if err := p.page.Locator(fmt.Sprintf(`tr[data-row-key*="%s"] svg.fa-gear`, clusterName)).First().Click(); err != nil {
		panic(err)
	}
	element := p.page.Locator(`.ant-modal-content:visible`)
	if err := element.WaitFor(); err != nil {
		panic(err)
	}
	return &AgentModal{element: element, page: p.page}
}

func (p *KargoDetailsPage) Delete(name string) *KargoDetailsPage {
	deleteBtn := p.page.Locator(`tr:has(td:has-text("` + name + `")) >> .ant-space-item >> button`).Nth(2)
	if err := deleteBtn.WaitFor(); err != nil {
		panic(err)
	}

	if err := deleteBtn.Click(); err != nil {
		panic(err)
	}

	return p
}

func (p *KargoDetailsPage) ConfirmDelete() *KargoDetailsPage {
	confirmBtn := p.page.Locator(`button:has-text("Confirm")`)
	if err := confirmBtn.WaitFor(); err != nil {
		panic(err)
	}

	if err := confirmBtn.Click(); err != nil {
		panic(err)
	}

	return p
}

func (p *KargoDetailsPage) CheckNamespace() *KargoDetailsPage {
	namespace := p.page.Locator(`input[type="checkbox"]`)
	if err := namespace.Click(); err != nil {
		panic(err)
	}
	return p
}

func (p *KargoDetailsPage) ConfirmNamespace() *KargoDetailsPage {
	confirmBtn := p.page.Locator(`button:has-text("Confirm")`).Nth(1)
	if err := confirmBtn.WaitFor(); err != nil {
		panic(err)
	}

	if err := confirmBtn.Click(); err != nil {
		panic(err)
	}

	element := p.page.Locator(`.ant-modal-content:has-text("Are you sure you want to remove agent")`)
	if err := element.WaitFor(); err != nil {
		panic(err)
	}

	return &KargoDetailsPage{page: p.page, AgentModal: &AgentModal{element: element, page: p.page}}
}

func (p *KargoDetailsPage) Confirm() *KargoDetailsPage {
	confirmBtn := p.page.Locator(`button:has-text("Confirm")`).Nth(0)
	if err := confirmBtn.WaitFor(); err != nil {
		panic(err)
	}

	if err := confirmBtn.Click(); err != nil {
		panic(err)
	}

	return p
}

func (k *KargoDetailsPage) EnsureLoaded() *KargoDetailsPage {
	if err := components.WaitForNoLoaders(k.page); err != nil {
		panic(err)
	}
	return k
}

func (m *AgentModal) KargoAgentVersionDropdown() *components.Select {
	return components.NewSelect(m.page, m.page.Locator(`.mb-4:has-text("Agent Version") .ant-select`))
}

type KargoDetailsPageSettings struct {
	settingsEl playwright.Locator
	page       playwright.Page
}

func (p *KargoDetailsPageSettings) Accounts() *KargoDetailsPageAccountsSettings {
	return &KargoDetailsPageAccountsSettings{content: p.goToSettings("System Accounts"), page: p.page, kargoDetailsSettings: &kargoDetailsSettings{p.page}}
}

func (p *KargoDetailsPageSettings) ExternalAccess() *KargoDetailsPageExternalAccessSettings {
	_ = p.goToSettings("External Access")
	return &KargoDetailsPageExternalAccessSettings{page: p.page, kargoDetailsSettings: &kargoDetailsSettings{p.page}}
}

type KargoDetailsPageExternalAccessSettings struct {
	*kargoDetailsSettings
	page playwright.Page
}

type KargoDetailsPageAccountsSettings struct {
	*kargoDetailsSettings
	content playwright.Locator
	page    playwright.Page
}

func (s *KargoDetailsPageExternalAccessSettings) ClickAddNewIPAllowList() *KargoDetailsPageExternalAccessSettings {
	if err := s.page.Locator(`button:has-text("Add New")`).Click(); err != nil {
		panic(err)
	}
	return s
}

func (s *KargoDetailsPageExternalAccessSettings) SetIPAllowListCIDR(index int, val string) *KargoDetailsPageExternalAccessSettings {
	if err := s.page.Locator(`input[name="spec.ipAllowList.` + strconv.Itoa(index) + `.ip"]`).Fill(val); err != nil {
		panic(err)
	}
	return s
}

func (s *KargoDetailsPageExternalAccessSettings) SetIPAllowListCIDRDescription(index int, val string) *KargoDetailsPageExternalAccessSettings {
	if err := s.page.Locator(`input[name="spec.ipAllowList.` + strconv.Itoa(index) + `.description"]`).Fill(val); err != nil {
		panic(err)
	}
	return s
}

func (s *KargoDetailsPageAccountsSettings) AdminToggle() *components.Toggle {
	el := s.content.Locator(`button[name='apiCm.adminAccountEnabled']`)
	if err := el.WaitFor(); err != nil {
		panic(err)
	}
	return components.NewToggle(el)
}

func (s *KargoDetailsPageAccountsSettings) ToggleAdmin() *KargoDetailsPageAccountsSettings {
	s.AdminToggle().Click()
	return s
}

func (s *KargoDetailsPageAccountsSettings) SetPassword(password string) *KargoDetailsPageAccountsSettings {
	input := s.content.Locator(`input[name="apiSecret.adminAccountPasswordHash"]`)
	if err := input.WaitFor(); err != nil {
		panic(err)
	}
	if err := input.Fill(password); err != nil {
		panic(err)
	}
	return s
}

func (p *KargoDetailsPage) Settings() *KargoDetailsPageSettings {
	if err := p.page.Locator(`[data-qe-id="settings-btn"]`).Click(); err != nil {
		panic(err)
	}

	settingsEl := p.page.Locator(`[data-qe-id="settings-page"]`)
	if err := settingsEl.WaitFor(); err != nil {
		panic(err)
	}
	return &KargoDetailsPageSettings{settingsEl: settingsEl, page: p.page}
}

func (p *KargoDetailsPage) HealthStatus() string {
	health := p.page.Locator(`span[data-qe-id="kargo-health-status"]`).First()
	if err := health.WaitFor(); err != nil {
		panic(err)
	}
	status, err := health.TextContent()
	if err != nil {
		panic(err)
	}
	return status
}

func (p *KargoDetailsPage) KargoURL() string {
	urlEl := p.page.Locator(`.content a[target=_blank]`)
	url, err := urlEl.GetAttribute("href")
	if err != nil {
		panic(err)
	}
	return url
}

func (p *KargoDetailsPage) InstanceID() string {
	element := p.page.Locator(`[data-qe-id="kargo-id"]`)
	// Sometimes in CI, it takes a bit longer to load the element. So we increase the timeout.
	timeout := 60000.0
	if err := element.WaitFor(playwright.LocatorWaitForOptions{Timeout: &timeout}); err != nil {
		panic(err)
	}
	content, err := element.TextContent()
	if err != nil {
		panic(err)
	}
	return strings.TrimSpace(content)
}

func (p *KargoDetailsPageSettings) goToSettings(name string) playwright.Locator {
	header := p.settingsEl.Locator(`text=` + name).First()
	if err := header.WaitFor(); err != nil {
		panic(err)
	}
	if err := header.Click(); err != nil {
		panic(err)
	}
	contentEl := p.settingsEl.Locator(`.flex-1`).Nth(0)
	if err := contentEl.WaitFor(); err != nil {
		panic(err)
	}
	return contentEl
}

func (p *KargoDetailsPageSettings) General() *KargoDetailsPageGeneralSettings {
	return &KargoDetailsPageGeneralSettings{content: p.goToSettings("General"), page: p.page, kargoDetailsSettings: &kargoDetailsSettings{p.page}}
}

func (p *KargoDetailsPageSettings) AgentDefault() *KargoDetailsPageAgentDefaultSettings {
	_ = p.goToSettings("Agent Default Settings")
	return &KargoDetailsPageAgentDefaultSettings{page: p.page, kargoDetailsSettings: &kargoDetailsSettings{p.page}}
}

type KargoDetailsPageAgentDefaultSettings struct {
	*kargoDetailsSettings
	page playwright.Page
}

func (p *KargoDetailsPageAgentDefaultSettings) ToggleAutoUpdate() *KargoDetailsPageAgentDefaultSettings {
	toggle := p.page.Locator(`[name="spec.agentCustomizationDefaults.autoUpgradeDisabled"]`)
	if err := toggle.WaitFor(); err != nil {
		panic(err)
	}
	if err := toggle.Click(); err != nil {
		panic(err)
	}
	return p
}

func (p *KargoDetailsPageAgentDefaultSettings) SetKustomization(value string) *KargoDetailsPageAgentDefaultSettings {
	if err := components.TypeInEditor(
		p.page,
		`[data-qe-id="kustomization"]`,
		value,
	); err != nil {
		panic(err)
	}
	return p
}

func (p *KargoDetailsPageAgentDefaultSettings) SetKargoCustomImageRegistry(value string) *KargoDetailsPageAgentDefaultSettings {
	input := p.page.Locator(`input[placeholder="quay.io/akuity"]`).Nth(0)
	if err := input.WaitFor(); err != nil {
		panic(err)
	}
	if err := input.Fill(value); err != nil {
		panic(err)
	}
	return p
}

func (p *KargoDetailsPageAgentDefaultSettings) SetArgoCustomImageRegistry(value string) *KargoDetailsPageAgentDefaultSettings {
	input := p.page.Locator(`input[placeholder="quay.io/akuity"]`).Nth(1)
	if err := input.WaitFor(); err != nil {
		panic(err)
	}
	if err := input.Fill(value); err != nil {
		panic(err)
	}
	return p
}

func (p *KargoDetailsPageAgentDefaultSettings) SetAgentCustomImageRegistry(value string) *KargoDetailsPageAgentDefaultSettings {
	input := p.page.Locator(`input[placeholder="quay.io/akuity"]`).Nth(2)
	if err := input.WaitFor(); err != nil {
		panic(err)
	}
	if err := input.Fill(value); err != nil {
		panic(err)
	}
	return p
}

type KargoDetailsPageGeneralSettings struct {
	*kargoDetailsSettings
	content playwright.Locator
	page    playwright.Page
}

type kargoDetailsSettings struct {
	page playwright.Page
}

func (p *KargoDetailsPageGeneralSettings) SetDescription(desc string) *KargoDetailsPageGeneralSettings {
	input := p.content.Locator(`textarea[name="patch.description"]`)
	if err := input.WaitFor(); err != nil {
		panic(err)
	}
	if err := input.Fill(desc); err != nil {
		panic(err)
	}
	return p
}

func (p *KargoDetailsPageGeneralSettings) SetGlobalCredentialsNamespaces(ns string) *KargoDetailsPageGeneralSettings {
	input := p.content.Locator(`input[aria-owns="rc_select_1_list"]`)
	if err := input.WaitFor(); err != nil {
		panic(err)
	}
	if err := input.Fill(ns); err != nil {
		panic(err)
	}
	return p
}

func (p *KargoDetailsPageGeneralSettings) SetGlobalServiceAccountNamespaces(ns string) *KargoDetailsPageGeneralSettings {
	input := p.content.Locator(`input[aria-owns="rc_select_2_list"]`)
	if err := input.WaitFor(); err != nil {
		panic(err)
	}
	if err := input.Fill(ns); err != nil {
		panic(err)
	}
	return p
}

func (p *KargoDetailsPageGeneralSettings) ToggleDefaultShard(value bool) *KargoDetailsPageGeneralSettings {
	toggle := p.content.Locator(`button[name="defaultShardAgentEnabled"]`)
	if err := toggle.WaitFor(); err != nil {
		panic(err)
	}
	// Get the aria-checked value
	checked, err := toggle.GetAttribute("aria-checked")
	if err != nil {
		panic(err)
	}
	if checked != strconv.FormatBool(value) {
		if err := toggle.Click(); err != nil {
			panic(err)
		}
	}
	return p
}

func (p *KargoDetailsPageGeneralSettings) Delete() *components.ConfirmationDialog {
	danger := p.content.Locator(`section:has-text("Delete Instance")`)
	if err := danger.WaitFor(); err != nil {
		panic(err)
	}
	input := danger.Locator("input")
	val, err := input.GetAttribute("placeholder")
	if err != nil {
		panic(err)
	}
	if err := input.Fill(val); err != nil {
		panic(err)
	}
	deleteButton := danger.Locator(`button:has-text("Delete")`)
	if err := deleteButton.Click(); err != nil {
		panic(err)
	}
	return components.NewConfirmationDialog(p.page)
}

func (p *kargoDetailsSettings) Save() error {
	result := components.WaitForResponse(p.page, components.KargoSettingsRequestURL)
	saveButton := p.page.Locator(`button:has-text("Save")`)
	if err := saveButton.WaitFor(); err != nil {
		panic(err)
	}
	if err := saveButton.Click(); err != nil {
		panic(err)
	}

	tickMark := p.page.Locator(`svg[data-icon="check"], svg[data-icon="floppy-disk"]`)
	if err := tickMark.WaitFor(); err != nil {
		panic(err)
	}
	if err := components.WaitForNoLoaders(p.page); err != nil {
		panic(err)
	}
	// After successfully saving setting, the page receives an update from the server using SSE and
	// there is no easy way to detect it. So we just wait a bit.
	time.Sleep(time.Second)
	return <-result
}
