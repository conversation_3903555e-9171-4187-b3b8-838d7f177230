package ui

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"
	"k8s.io/utils/ptr"

	httpctx "github.com/akuity/grpc-gateway-client/pkg/http/context"
	"github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/akuity-platform/internal/utils/io"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	kargo "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
	featuresv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/features/v1"
	"github.com/akuityio/akuity-platform/test/utils"
	"github.com/akuityio/akuity-platform/test/utils/ui"
)

func TestKargoInstanceAgents(t *testing.T) {
	f, cancel, err := utils.NewFixture(utils.WithAllPaidFeaturesEnabled(), utils.WithFeatureGates(&featuresv1.OrganizationFeatureGates{Kargo: ptr.To(true)}))
	require.NoError(t, err)
	defer cancel(false)
	ctx := httpctx.SetAuthorizationHeader(context.Background(),
		f.PlatformClientset.Creds.Scheme(), f.PlatformClientset.Creds.Credential())

	webapp, closer, err := f.InitPlatformWebApp(ui.WithVideoName(t.Name()))
	require.NoError(t, err)
	defer io.Close(closer)

	versions, err := client.ListKargoVersions()
	require.NoError(t, err)

	argoInstance, err := f.PlatformClientset.ArgoCD.CreateInstance(ctx, &argocdv1.CreateInstanceRequest{Name: "test-argo", OrganizationId: f.CurrentOrg.ID})
	require.NoError(t, err)

	instance, err := f.PlatformClientset.Kargo.CreateKargoInstance(ctx, &kargo.CreateKargoInstanceRequest{Version: versions[0].Version, Name: "test-kargo", OrganizationId: f.CurrentOrg.ID})
	require.NoError(t, err)

	t.Run("Add agent", func(t *testing.T) {
		webapp.GoToKargoDetails("test-kargo").
			ClickRegisterAgent().
			SetName("agent1").
			ToggleSelfHostedKargoAgent().
			SetArgoCDNamespace("argocd").
			ToggleAdvancedSettings().
			SetNamespace("akuity").
			ClickAddLabel().
			SetLabel("key1", "value1").
			ClickAddAnnotation().
			SetAnnotation("key2", "value2").
			ClickKargoConnect()

		agentResp, err := f.PlatformClientset.Kargo.ListKargoInstanceAgents(ctx, &kargo.ListKargoInstanceAgentsRequest{
			InstanceId: instance.Instance.Id, OrganizationId: f.CurrentOrg.ID,
		})

		require.NoError(t, err)
		require.Len(t, agentResp.Agents, 1)
		require.Equal(t, "agent1", agentResp.Agents[0].Name)
		require.Len(t, agentResp.Agents[0].Data.Labels, 1)
		require.Equal(t, "value1", agentResp.Agents[0].Data.Labels["key1"])
		require.Len(t, agentResp.Agents[0].Data.Annotations, 1)
		require.Equal(t, "value2", agentResp.Agents[0].Data.Annotations["key2"])
		require.Equal(t, "argocd", agentResp.Agents[0].Data.ArgocdNamespace)
		require.Equal(t, "akuity", agentResp.Agents[0].Data.Namespace)
	})

	t.Run("Advanced Settings", func(t *testing.T) {
		webapp.GoToKargoDetails("test-kargo").
			ClickRegisterAgent().
			SetName("agent2").
			SelectAkuityManaged("test-argo").
			ToggleAdvancedSettings().
			ClickKargoConnect()

		agentResp, err := f.PlatformClientset.Kargo.ListKargoInstanceAgents(ctx, &kargo.ListKargoInstanceAgentsRequest{
			InstanceId: instance.Instance.Id, OrganizationId: f.CurrentOrg.ID,
		})

		require.NoError(t, err)
		require.Len(t, agentResp.Agents, 2)
		require.Equal(t, "agent2", agentResp.Agents[1].Name)
		require.Equal(t, true, agentResp.Agents[1].Data.AkuityManaged)
		require.Equal(t, argoInstance.Instance.Id, agentResp.Agents[1].Data.RemoteArgocd)
	})
}
