package e2e

import (
	"context"
	"fmt"
	"net/url"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"github.com/volatiletech/sqlboiler/v4/boil"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	rbacv1 "k8s.io/api/rbac/v1"
	"k8s.io/utils/ptr"

	"github.com/akuityio/akuity-platform/controllers/platform/integration"
	"github.com/akuityio/akuity-platform/internal/utils/io"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
	featuresv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/features/v1"
	"github.com/akuityio/akuity-platform/test/utils"
	"github.com/akuityio/akuity-platform/test/utils/expect"
	"github.com/akuityio/akuity-platform/test/utils/ui"
	"github.com/akuityio/akuity-platform/test/utils/ui/akuity/components"
	"github.com/akuityio/akuity-platform/test/utils/ui/akuity/pages"
	"github.com/akuityio/akuity-platform/test/utils/ui/argocd"
	"github.com/akuityio/akuity-platform/test/utils/ui/kargo"
)

const agentKustomization = `patches:
- patch: "- {op: replace, path: /spec/replicas, value: 1}"
  target:
    kind: Deployment
    name: akuity-agent
- patch: "- {op: replace, path: /spec/replicas, value: 1}"
  target:
    kind: Deployment
    name: argocd-redis
- patch: "- {op: replace, path: /spec/replicas, value: 0}"
  target:
    kind: StatefulSet
    name: argocd-redis-ha
- patch: |-
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: argocd-application-controller
    spec:
      template:
        spec:
          containers:
          - name: syncer
            resources:
              requests:
                cpu: 100m
                memory: 100Mi
  target:
    kind: Deployment
    name: argocd-application-controller`

// Test verifies that platform is able to provision ArgoCD instance and deploy an application
func TestAkuityPlatformHappyPath(t *testing.T) {
	checkEnv(t)
	f, clean, err := utils.NewFixture(utils.WithAllPaidFeaturesEnabled(), utils.WithFeatureGates(&featuresv1.OrganizationFeatureGates{Kargo: ptr.To(true)}))
	require.NoError(t, err)
	defer clean(false)

	akp, closer, err := f.InitPlatformWebApp(ui.WithVideoName(t.Name()))
	require.NoError(t, err)
	defer io.Close(closer)

	detailsPage := akp.GoToArgoCD().ClickCreateNewInstance().SetName("argocd1").SelectWorkspace("default").Create()

	instanceID := detailsPage.InstanceID()
	instanceCfg := models.ArgoCDInstanceConfig{InstanceID: instanceID}
	require.NoError(t, instanceCfg.SetInternalSpec(&models.InstanceInternalSpec{
		Values: map[string]interface{}{
			"argo_cd":       map[string]interface{}{"replicas": 1},
			"agent_server":  map[string]interface{}{"replicas": 1},
			"webhook":       map[string]interface{}{"replicas": 1},
			"redis_haproxy": map[string]interface{}{"replicas": 1},
		},
	}))
	_, err = instanceCfg.Update(context.Background(), f.DB, boil.Whitelist(models.ArgoCDInstanceConfigColumns.InternalSpec))
	require.NoError(t, err)

	tenantArgoCDNamespace := fmt.Sprintf("argocd-%s", instanceID)
	deployments := f.K8SClientset.AppsV1().Deployments(tenantArgoCDNamespace)
	tenantInstance, err := f.NewTenant(instanceID)
	require.NoError(t, err)

	adminPassword, err := client.NanoID(8)
	require.NoError(t, err)
	settingsPage := detailsPage.Settings()
	argoCDGeneral := settingsPage.General().ToggleAppInAnyNs()
	argoCDGeneral.Save()
	settingsPage.Accounts().AdminToggle().Click()
	components.NewConfirmationDialog(akp.Page()).Confirm()
	settingsPage.Accounts().SetPassword("admin", adminPassword)
	time.Sleep(5 * time.Second)
	settingsPage.GoBackToDetails()

	createKargoPage := akp.GoToKargo().ClickCreateNewInstance().SetName("kargo1")
	if version := os.Getenv("KARGO_INSTANCE_VERSION"); version != "" {
		createKargoPage = createKargoPage.SetVersion().ChooseVersion("unstable")
	}
	kargoDetailsPage := createKargoPage.Create()

	kargoInstanceID := kargoDetailsPage.InstanceID()
	kargoInstanceCfg := models.KargoInstanceConfig{InstanceID: kargoInstanceID}
	require.NoError(t, kargoInstanceCfg.SetInternalSpec(&models.KargoInstanceInternalSpec{
		Values: map[string]interface{}{
			"kargo":         map[string]interface{}{"replicas": 1},
			"k3s":           map[string]interface{}{"replicas": 1},
			"k3s_webhook":   map[string]interface{}{"replicas": 1},
			"kargo_webhook": map[string]interface{}{"replicas": 1},
		},
	}))
	_, err = kargoInstanceCfg.Update(context.Background(), f.DB, boil.Whitelist(models.KargoInstanceConfigColumns.InternalSpec))
	require.NoError(t, err)

	tenantKargoNamespace := fmt.Sprintf("kargo-%s", kargoInstanceID)
	kargoDeployments := f.K8SClientset.AppsV1().Deployments(tenantKargoNamespace)
	kargoTenantInstance, err := f.NewKargoTenant(kargoInstanceID)
	require.NoError(t, err)

	require.NoError(t, kargoDetailsPage.Settings().Accounts().ToggleAdmin().SetPassword(adminPassword).Save())
	akp.GoToKargoDetails("kargo1")

	// Check if the Argo CD and Kargo instances are created and tenant deployments are healthy
	require.NoError(t, f.WaitFor("argocd and kargo instances to be healthy",
		expect.K8SResource[*corev1.Namespace](f.K8SClientset.CoreV1().Namespaces(), tenantArgoCDNamespace),
		expect.K8SResource[*appsv1.Deployment](deployments, "agent-server", expect.DeploymentIsHealthy),
		expect.K8SResource[*appsv1.Deployment](deployments, "argocd-server", expect.DeploymentIsHealthy),
		expect.K8SResource[*appsv1.Deployment](deployments, "k3s-webhook", expect.DeploymentIsHealthy),
		expect.K8SResource[*appsv1.Deployment](deployments, "argocd-redis-ha-haproxy", expect.DeploymentIsHealthy),
		expect.K8SResource[*appsv1.Deployment](deployments, "k3s", expect.DeploymentIsHealthy),
		expect.DbEntity(f.ArgoCDInstances(), instanceID, func(instance *models.ArgoCDInstance) (bool, string) {
			return !instance.StatusObservedGeneration.IsZero() && instance.StatusObservedGeneration.Int == 9, fmt.Sprintf("Observed Generation %v in db doesn't match expected generation ", instance.StatusObservedGeneration)
		}),
		expect.TenantCheck(tenantInstance, func(ctx context.Context, tenant integration.Tenant) (bool, string) {
			stat, err := tenant.Status(ctx, false, false)
			if err != nil {
				return false, err.Error()
			}
			return stat.MinObservedGeneration == 9, fmt.Sprintf("Observed Generation %v in resources doesn't match expected generation", stat.MinObservedGeneration)
		}),
		expect.K8SResource[*corev1.Namespace](f.K8SClientset.CoreV1().Namespaces(), tenantKargoNamespace),
		expect.K8SResource[*appsv1.Deployment](kargoDeployments, "k3s-webhook", expect.DeploymentIsHealthy),
		expect.K8SResource[*appsv1.Deployment](kargoDeployments, "kargo-webhooks-server", expect.DeploymentIsHealthy),
		expect.K8SResource[*appsv1.Deployment](kargoDeployments, "kargo-api", expect.DeploymentIsHealthy),
		expect.K8SResource[*appsv1.Deployment](kargoDeployments, "k3s", expect.DeploymentIsHealthy),
		expect.DbEntity(f.KargoInstances(), kargoInstanceID, func(instance *models.KargoInstance) (bool, string) {
			return !instance.StatusObservedGeneration.IsZero() && instance.StatusObservedGeneration.Int == 6, fmt.Sprintf("Observed Generation %v in db doesn't match expected generation ", instance.StatusObservedGeneration)
		}),
		expect.KargoTenantCheck(kargoTenantInstance, func(ctx context.Context, tenant integration.KargoTenant) (bool, string) {
			stat, err := tenant.Status(ctx)
			if err != nil {
				return false, err.Error()
			}
			return stat.MinObservedGeneration == 6, fmt.Sprintf("Observed Generation %v in resources doesn't match expected generation", stat.MinObservedGeneration)
		}),
	))

	// Install ArgoCD agents
	detailsPage = akp.GoToArgoCDDetails("test-org", "argocd1")

	installCommands := map[string]string{}

	// Connect two clusters to the ArgoCD instance
	for i := 1; i <= 2; i++ {
		agentNS := fmt.Sprintf("test-%d", i)
		installAgentModal := detailsPage.
			SelectClustersTab().
			ClickConnectCluster().
			SetName(agentNS).
			ToggleAdvancedSettings().
			SetNamespace(agentNS).
			SelectCustomizationTab().
			SetKustomization(agentKustomization).
			ClickConnect()

		var installCommand string
		require.NoError(t, f.WaitFor("agent manifests generated", func(ctx context.Context) (bool, string, error) {
			var ok bool
			installCommand, ok = installAgentModal.AgentCommand("kubectl apply -f", false)
			return ok, "manifests are not generated yet", nil
		}))
		installCommands[agentNS] = installCommand
		installAgentModal.Close("Done")
	}

	settings := detailsPage.Settings()

	settings.ManifestGeneration().SelectManagedCluster("test-2").Save()
	settings.GoBackToDetails()

	detailsPage.SelectClustersTab()
	for agentNS, installCommand := range installCommands {
		require.NoError(t, f.RunCLI("bash", "-c", installCommand))
		createPullSecrets(t, agentNS)
	}

	require.NoError(t, f.WaitFor("agents to be healthy", func(ctx context.Context) (bool, string, error) {
		statusIcon := detailsPage.ClusterStatusIcon("test-1")
		return statusIcon == "heart", fmt.Sprintf("status icon is '%s'", statusIcon), nil
	}, func(ctx context.Context) (bool, string, error) {
		statusIcon := detailsPage.ClusterStatusIcon("test-2")
		return statusIcon == "heart", fmt.Sprintf("status icon is '%s'", statusIcon), nil
	}))

	detailsPage.SelectSummaryTab()

	// Make sure ArgoCD instance is healthy
	require.NoError(t, f.WaitFor("argo cd instance to be healthy", func(ctx context.Context) (bool, string, error) {
		status := detailsPage.HealthStatus()
		return status == "Healthy", fmt.Sprintf("instance status is %s", status), nil
	}))

	// install kargo agent
	kargoDetailsPage = akp.GoToKargoDetails("kargo1")
	kargoDetailsPage.Agents().ClickRegisterAgent().SetName("test1").SelectAkuityManaged("argocd1").ClickKargoConnect()
	installAgentModal := kargoDetailsPage.Agents().
		ClickRegisterAgent().
		SetName("test2").
		ToggleSelfHostedKargoAgent().
		SetArgoCDNamespace("argocd-" + instanceID).
		ToggleDefaultShard("false").
		ClickKargoConnect().
		ClickInstallAgent("test2")

	var installCommand string
	require.NoError(t, f.WaitFor("agent manifests generated", func(ctx context.Context) (bool, string, error) {
		var ok bool
		installCommand, ok = installAgentModal.AgentCommand("kubectl apply -f", false)
		return ok, "manifests are not generated yet", nil
	}))

	kargoAgentNS := "argocd-" + instanceID
	installCommands[kargoAgentNS] = installCommand
	installAgentModal.Close("Done")

	require.NoError(t, f.RunCLI("bash", "-c", installCommand))
	createPullSecrets(t, kargoAgentNS)

	require.NoError(t, f.WaitFor("agents to be healthy", func(ctx context.Context) (bool, string, error) {
		statusIcon := kargoDetailsPage.AgentStatusIcon("test1")
		return statusIcon == "heart", fmt.Sprintf("status icon is '%s'", statusIcon), nil
	}, func(ctx context.Context) (bool, string, error) {
		statusIcon := kargoDetailsPage.AgentStatusIcon("test2")
		return statusIcon == "heart", fmt.Sprintf("status icon is '%s'", statusIcon), nil
	}))

	// Make sure Kargo instance is healthy
	require.NoError(t, f.WaitFor("kargo instance to be healthy", func(ctx context.Context) (bool, string, error) {
		status := kargoDetailsPage.HealthStatus()
		return status == "Healthy", fmt.Sprintf("instance status is %s", status), nil
	}))

	kargoURL, err := url.Parse(kargoDetailsPage.KargoURL())
	require.NoError(t, err)
	kargoURL.Scheme = "http"

	detailsPage = akp.GoToArgoCDDetails("test-org", "argocd1")
	detailsPage.SelectClustersTab().ClickIntegrate().SetName("kargo").SelectKargoInstance("kargo1").ClickConnectKargo()

	argocdURL, err := url.Parse(detailsPage.SelectSummaryTab().ArgoCDURL())
	require.NoError(t, err)
	argocdURL.Scheme = "http"
	argocdApp := argocd.NewArgoCDWebApp(argocdURL.String(), akp.Page())
	_ = argocdApp.GoToLogin().Login("admin", adminPassword)

	require.NoError(t,
		argocdApp.GoToSettingsRepoPage().
			ConnectRepo().
			ChooseHTTPS().
			SetProject("default").
			SetRepoURL("https://github.com/akuityio/kargo-saas-e2e-test").
			SetUsername().
			SetGitHubSecret().
			Connect())

	require.True(t, argocdApp.GoToSettingsRepoPage().RetryCredentials("https://github.com/akuityio/kargo-saas-e2e-test").CheckConnectedRepo("https://github.com/akuityio/kargo-saas-e2e-test"))

	kargoClusterURL := "https://cluster-kargo:6445"
	require.NoError(t,
		argocdApp.GoToAppListPage().
			CreateApp().
			SetName("kargo-bootstrap").
			SetProject("default").
			SetRepoURL("https://github.com/akuityio/kargo-saas-e2e-test").
			SetPath("kargo-bootstrap-e2e").
			SetDestClusterURL(kargoClusterURL).
			Create())

	require.NoError(t, argocdApp.WaitForAppToLoad("kargo-bootstrap"))
	appDetails := argocdApp.GoToAppDetails("kargo-bootstrap")

	require.NoError(t, f.WaitFor("app to be healthy", func(ctx context.Context) (bool, string, error) {
		status := appDetails.HealthStatus()
		return status == "Healthy", fmt.Sprintf("app status is %s", status), nil
	}))

	require.NoError(t, f.WaitFor("sync status to be out of sync", func(ctx context.Context) (bool, string, error) {
		status := appDetails.SyncStatus()
		return status == "OutOfSync", fmt.Sprintf("sync status is %s", status), nil
	}))

	appDetails.Sync().Synchronize()

	require.NoError(t, f.WaitFor("sync status to be synced", func(ctx context.Context) (bool, string, error) {
		status := appDetails.SyncStatus()
		done := status == "Synced"
		if !done {
			appDetails.Refresh()
		}
		return done, fmt.Sprintf("sync status is %s", status), nil
	}))

	require.NoError(t, f.WaitFor("app to be healthy", func(ctx context.Context) (bool, string, error) {
		status := appDetails.HealthStatus()
		return status == "Healthy", fmt.Sprintf("app status is %s", status), nil
	}))

	require.NoError(t,
		argocdApp.GoToAppListPage().
			CreateApp().
			SetName("argocd-bootstrap").
			SetProject("default").
			SetRepoURL("https://github.com/akuityio/kargo-saas-e2e-test").
			SetPath("argocd-app-of-apps").
			SelectHelm().
			SetHelmValues(`testType: "e2e"`).
			SetDestClusterURL("https://kubernetes.default.svc").
			SetDestNamespace("argocd").
			Create())

	require.NoError(t, argocdApp.WaitForAppToLoad("argocd-bootstrap"))
	appDetails = argocdApp.GoToAppDetails("argocd-bootstrap")

	require.NoError(t, f.WaitFor("app to be healthy", func(ctx context.Context) (bool, string, error) {
		status := appDetails.HealthStatus()
		return status == "Healthy", fmt.Sprintf("app status is %s", status), nil
	}))

	require.NoError(t, f.WaitFor("sync status to be out of sync", func(ctx context.Context) (bool, string, error) {
		status := appDetails.SyncStatus()
		return status == "OutOfSync", fmt.Sprintf("sync status is %s", status), nil
	}))

	appDetails.Sync().Synchronize()

	require.NoError(t, f.WaitFor("sync status to be synced", func(ctx context.Context) (bool, string, error) {
		status := appDetails.SyncStatus()
		done := status == "Synced"
		if !done {
			appDetails.Refresh()
		}
		return done, fmt.Sprintf("sync status is %s", status), nil
	}))

	kargoApp := kargo.NewKargoWebApp(kargoURL.String(), akp.Page())
	kargoApp.GoToLogin().Login(adminPassword)

	kargoDetails := kargoApp.GoToProjectDetails("kargo-proj")

	require.NoError(t,
		kargoDetails.Settings().Secrets().
			AddCredentials().
			SetName("akuity-test").
			SetRepoURL("https://github.com/akuityio/kargo-saas-e2e-test").
			SetGitHubUsername().
			SetGitHubSecret().
			Save())

	kargoDetails = kargoApp.GoToProjectDetails("kargo-proj")
	for i := 0; i < 10; i++ {
		kargoDetails.RefreshWarehouse()
		err := kargoDetails.CheckFreight()
		if err == nil {
			break
		}
	}

	kargoDetails.ClickPromoteStage("test1").ReviewPromotion().ConfirmPromotion()
	kargoDetails.OpenStageDetails("test1").PromotionsTab().VerifyPromotion("test1")

	appDetails = argocdApp.GoToAppDetails("kargo-saas-e2e-test")
	require.NoError(t, f.WaitFor("app to be healthy", func(ctx context.Context) (bool, string, error) {
		status := appDetails.HealthStatus()
		return status == "Healthy", fmt.Sprintf("app status is %s", status), nil
	}))

	appDetails = argocdApp.GoToAppDetails("kargo-saas-e2e-test-2", "argocd-2")
	require.NoError(t, f.WaitFor("app to be healthy", func(ctx context.Context) (bool, string, error) {
		status := appDetails.HealthStatus()
		done := status == "Healthy"
		if !done {
			appDetails.Reload()
		}
		return done, fmt.Sprintf("app status is %s", status), nil
	}))

	kargoApp.GoToProjectDetails("kargo-proj").OpenStageDetails("test1").VerificationsTab().VerifyVerification("test1")

	kargoApp.GoToProjectDetails("kargo-proj").ClickPromoteStage("test2").ReviewPromotion().ConfirmPromotion()
	kargoDetails.OpenStageDetails("test2").PromotionsTab().VerifyPromotion("test2").VerificationsTab().VerifyVerification("test2")

	argocdApp.GoToAppListPage().DeleteApp("argocd-bootstrap", true, true)
	// There exists no way in ArgoCD to delete an auto-created namespace. So, we need to delete it manually
	require.NoError(t, f.RunCLI("kubectl", "delete", "namespace", "argocd"))
	require.NoError(t, f.RunCLI("kubectl", "delete", "namespace", "argocd2"))

	// delete kargo instance intg. in argocd before deleting kargo instance
	detailsPage = akp.GoToArgoCDDetails("test-org", "argocd1")
	detailsPage.SelectClustersTab()
	detailsPage.DeleteCluster("kargo")

	// delete kargo instance
	kargoDetailsPage = akp.GoToKargoDetails("kargo1")
	require.NoError(t, kargoDetailsPage.Settings().General().ToggleDefaultShard(false).Save())
	kargoDetailsPage = akp.GoToKargoDetails("kargo1")
	kargoDetailsPage.Agents().Delete("test1").ConfirmDelete()
	deleteAgentModal := kargoDetailsPage.Agents().Delete("test2").CheckNamespace().ConfirmNamespace()
	var ok bool
	installCommand, ok = deleteAgentModal.AgentCommand("kubectl delete -f", false)
	require.True(t, ok)
	deleteAgentModal.Confirm()

	require.NoError(t, f.RunCLI("bash", "-c", installCommand))

	kargoDetailsPage.Settings().General().Delete()

	// Make sure the instance is deleted
	require.NoError(t, f.WaitFor("tenant resources are deleted",
		expect.K8SResourceIsDeleted[*corev1.Namespace](f.K8SClientset.CoreV1().Namespaces(), tenantKargoNamespace),
	))

	detailsPage = akp.GoToArgoCDDetails("test-org", "argocd1")

	settings = detailsPage.Settings()
	settings.ManifestGeneration().SelectDelegate(pages.ALL_MANAGED_CLUSTERS).Save()
	settings.GoBackToDetails()

	detailsPage.SelectClustersTab()
	detailsPage.DeleteCluster("test-1")

	require.NoError(t, err)

	cplaneClient, err := tenantInstance.ControlPlaneKubeClientset(context.Background())
	require.NoError(t, err)

	require.NoError(t, f.WaitFor("cluster resources are deleted",
		expect.K8SResourceIsDeleted[*corev1.Service](f.K8SClientset.CoreV1().Services(tenantArgoCDNamespace), "cluster-test-1"),
		expect.K8SResourceIsDeleted[*corev1.Secret](cplaneClient.CoreV1().Secrets(tenantArgoCDNamespace), "cluster-test-1"),
		expect.K8SResourceIsDeleted[*corev1.ServiceAccount](f.K8SClientset.CoreV1().ServiceAccounts(tenantArgoCDNamespace), "cluster-test-1"),
		expect.K8SResourceIsDeleted[*rbacv1.Role](f.K8SClientset.RbacV1().Roles(tenantArgoCDNamespace), "cluster-test-1"),
		expect.K8SResourceIsDeleted[*rbacv1.RoleBinding](f.K8SClientset.RbacV1().RoleBindings(tenantArgoCDNamespace), "cluster-test-1"),
	))

	detailsPage.Settings().General().Delete().Confirm()

	// Make sure the instance is deleted
	require.NoError(t, f.WaitFor("tenant resources are deleted",
		expect.K8SResourceIsDeleted[*corev1.Namespace](f.K8SClientset.CoreV1().Namespaces(), tenantArgoCDNamespace),
	))

	// Deleting all test Namespaces
	clean(true)
}

// createPullSecrets - creates secret/akuity-pullsecrets from akuityPullsecretsFile
// (created by Smoketest CronJob before the test starts - akuity-platform-deploy/../smoketest-cronjob.yaml)
func createPullSecrets(t require.TestingT, agentNS string) {
	if akuityPullsecretsFile := os.Getenv("AKUITY_PULLSECRETS"); akuityPullsecretsFile != "" {
		require.NotEmpty(t, akuityPullsecretsFile)
		require.NoError(t, utils.RunBash(fmt.Sprintf("kubectl delete secret/akuity-pullsecrets -n '%s' --ignore-not-found=true", agentNS)))
		require.NoError(t, utils.RunBash(fmt.Sprintf("kubectl create secret docker-registry akuity-pullsecrets -n '%s' --from-file=.dockerconfigjson='%s'", agentNS, akuityPullsecretsFile)))
		require.NoError(t, utils.RunBash(fmt.Sprintf("kubectl get    secret/akuity-pullsecrets -n '%s'", agentNS)))
	}
}

func checkEnv(t require.TestingT) {
	ghUsername := os.Getenv("GITHUB_USERNAME")
	if ghUsername == "" {
		t.Errorf("GitHub username is empty. Please ensure the GITHUB_USERNAME environment variable is set.")
	}
	require.NotEmpty(t, ghUsername)
	ghPassword := os.Getenv("GITHUB_TOKEN")
	if ghPassword == "" {
		t.Errorf("GitHub token is empty. Please ensure the GITHUB_TOKEN environment variable is set.")
	}
	require.NotEmpty(t, ghPassword)
}
