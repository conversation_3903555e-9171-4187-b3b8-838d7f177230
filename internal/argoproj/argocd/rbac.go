package argocd

import (
	"encoding/csv"
	"errors"
	"fmt"
	"strings"

	"github.com/casbin/casbin/v2"
	"github.com/casbin/casbin/v2/model"
)

func ValidatePolicy(policy, modelConf string) error {
	m, err := model.NewModelFromString(modelConf)
	if err != nil {
		return err
	}
	if _, err := casbin.NewEnforcer(m, newAdapter(policy)); err != nil {
		return fmt.Errorf("policy syntax error: %w", err)
	}
	return nil
}

// Casbin adapter which satisfies persist.Adapter interface
type argocdAdapter struct {
	policy string
}

func newAdapter(policy string) *argocdAdapter {
	return &argocdAdapter{
		policy: policy,
	}
}

func (a *argocdAdapter) LoadPolicy(model model.Model) error {
	for _, line := range strings.Split(a.policy, "\n") {
		if err := loadPolicyLine(strings.TrimSpace(line), model); err != nil {
			return err
		}
	}
	return nil
}

// The modified version of LoadPolicyLine function defined in "persist" package of github.com/casbin/casbin.
// Uses CVS parser to correctly handle quotes in policy line.
func loadPolicyLine(line string, model model.Model) error {
	if line == "" || strings.HasPrefix(line, "#") {
		return nil
	}

	reader := csv.NewReader(strings.NewReader(line))
	reader.TrimLeadingSpace = true
	tokens, err := reader.Read()
	if err != nil {
		return err
	}

	tokenLen := len(tokens)

	if tokenLen < 1 ||
		tokens[0] == "" ||
		(tokens[0] == "g" && tokenLen != 3) ||
		(tokens[0] == "p" && tokenLen != 6) {
		return fmt.Errorf("invalid RBAC policy: %s", line)
	}

	key := tokens[0]
	sec := key[:1]
	if _, ok := model[sec]; !ok {
		return fmt.Errorf("invalid RBAC policy: %s", line)
	}
	if _, ok := model[sec][key]; !ok {
		return fmt.Errorf("invalid RBAC policy: %s", line)
	}
	model[sec][key].Policy = append(model[sec][key].Policy, tokens[1:])
	return nil
}

func (a *argocdAdapter) SavePolicy(model model.Model) error {
	return errors.New("not implemented")
}

func (a *argocdAdapter) AddPolicy(sec, ptype string, rule []string) error {
	return errors.New("not implemented")
}

func (a *argocdAdapter) RemovePolicy(sec, ptype string, rule []string) error {
	return errors.New("not implemented")
}

func (a *argocdAdapter) RemoveFilteredPolicy(sec, ptype string, fieldIndex int, fieldValues ...string) error {
	return errors.New("not implemented")
}
