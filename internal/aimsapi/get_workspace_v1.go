package aimsapi

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	"github.com/akuityio/akuity-platform/internal/services/workspaces"
	"github.com/akuityio/akuity-platform/models/models"
)

func mapWorkspaceToAIMSResponse(ws workspaces.Workspace) *aimsv1.Workspace {
	argoInstances := make([]*aimsv1.WorkspaceArgoCDInstance, len(ws.R.ArgoCDInstances))
	for i, argoInstance := range ws.R.ArgoCDInstances {
		argoInstances[i] = &aimsv1.WorkspaceArgoCDInstance{
			Id:   argoInstance.ID,
			Name: argoInstance.Name,
		}
	}

	kargoInstances := make([]*aimsv1.WorkspaceKargoInstance, len(ws.R.KargoInstances))
	for i, kargoInstance := range ws.R.KargoInstances {
		kargoInstances[i] = &aimsv1.WorkspaceKargoInstance{
			Id:   kargoInstance.ID,
			Name: kargoInstance.Name,
		}
	}

	var teamMemberCount, userMemberCount uint32
	for _, member := range ws.Members {
		switch member.Ref.(type) {
		case *teams.Team:
			teamMemberCount++
		case *models.AkuityUser:
			userMemberCount++
		}
	}

	return &aimsv1.Workspace{
		Id:              ws.ID,
		Name:            ws.Name,
		Description:     ws.Description.String,
		CreateTime:      timestamppb.New(ws.CreationTimestamp),
		ArgocdInstances: argoInstances,
		KargoInstances:  kargoInstances,
		TeamMemberCount: teamMemberCount,
		UserMemberCount: userMemberCount,
		IsDefault:       ws.IsDefault,
	}
}

func (s *AimsV1Server) GetWorkspace(ctx context.Context, req *aimsv1.GetWorkspaceRequest) (*aimsv1.GetWorkspaceResponse, error) {
	if req.GetWorkspaceId() == "" || req.GetOrganizationId() == "" {
		return nil, status.Error(codes.InvalidArgument, "workspace_id and organization_id are required")
	}

	teamSvc := teams.NewService(s.db)
	workspaceSvc := workspaces.NewService(s.db, teamSvc, s.cfg.FeatureGatesSource)

	ws, err := workspaceSvc.GetWorkspace(ctx, req.GetWorkspaceId())
	if err != nil {
		return nil, status.Errorf(codes.NotFound, "workspace id %s not found", req.GetWorkspaceId())
	}

	return &aimsv1.GetWorkspaceResponse{
		Workspace: mapWorkspaceToAIMSResponse(*ws),
	}, nil
}
