package aimsapi

import (
	"context"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *AimsV1Server) ListOrganizationDomains(ctx context.Context, req *aimsv1.ListOrganizationDomainsRequest) (*aimsv1.ListOrganizationDomainsResponse, error) {
	org, err := models.Organizations(models.OrganizationWhere.ID.EQ(req.OrganizationId)).One(ctx, s.db)
	if err != nil {
		return nil, err
	}
	vd, err := org.GetVerifiedDomains()
	if err != nil {
		return nil, err
	}

	resp := &aimsv1.ListOrganizationDomainsResponse{
		Domains: make([]*organizationv1.DomainVerification, len(vd)),
	}

	for i, d := range vd {
		resp.Domains[i] = &organizationv1.DomainVerification{Domain: d.Domain, Verified: d.Verified}
	}

	return resp, nil
}
