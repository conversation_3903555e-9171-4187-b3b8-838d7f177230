package aimsapi

import (
	"context"
	"time"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	sqltypes "github.com/volatiletech/sqlboiler/v4/types"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

const auditMaxLimit = uint32(1000)

func (s *AimsV1Server) GetInternalAuditLogs(
	ctx context.Context,
	req *aimsv1.GetInternalAuditLogsRequest,
) (*aimsv1.GetInternalAuditLogsResponse, error) {
	repoSet := client.NewRepoSet(s.db)

	filters := req.GetFilters()
	if filters == nil {
		filters = &aimsv1.InternalAuditFilters{}
	}

	objectType := filters.GetObjectType()

	var err error
	if filters.StartTime != nil {
		if _, err = time.Parse(time.RFC3339, *filters.StartTime); err != nil {
			return nil, status.Error(codes.InvalidArgument, err.Error())
		}
	}

	if filters.EndTime != nil {
		if _, err = time.Parse(time.RFC3339, *filters.EndTime); err != nil {
			return nil, status.Error(codes.InvalidArgument, err.Error())
		}
	}

	limit := filters.GetLimit()
	offset := filters.GetOffset()

	if limit == 0 {
		limit = 10
	} else if limit > auditMaxLimit {
		limit = auditMaxLimit
	}

	mods := []qm.QueryMod{}
	mods = getMods(filters, mods, objectType)

	totalCount, err := repoSet.InternalAudits(mods...).Count(ctx)
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}

	mods = append(mods,
		qm.OrderBy(models.InternalAuditColumns.Timestamp+" DESC"),
		qm.Limit(int(limit)),
		qm.Offset(int(offset)),
	)

	auditLogs, err := repoSet.InternalAudits().Filter(mods...).ListAll(ctx)
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}

	items := make([]*aimsv1.AuditLog, 0, len(auditLogs))
	for _, auditLog := range auditLogs {
		auditLogV1, err := mapAuditLogToRPCEntity(auditLog)
		if err != nil {
			return nil, err
		}
		items = append(items, auditLogV1)
	}

	return &aimsv1.GetInternalAuditLogsResponse{
		Items:      items,
		TotalCount: uint32(totalCount),
	}, nil
}

func getMods(filters *aimsv1.InternalAuditFilters, mods []qm.QueryMod, objectType string) []qm.QueryMod {
	if filters.StartTime != nil {
		mods = append(mods,
			qm.Where(models.InternalAuditColumns.Timestamp+" >= ?", filters.StartTime),
		)
	}
	if filters.EndTime != nil {
		mods = append(mods,
			qm.Where(models.InternalAuditColumns.Timestamp+" <= ?", filters.EndTime),
		)
	}
	if len(filters.Action) > 0 {
		mods = append(mods, models.InternalAuditWhere.Action.IN(filters.Action))
	}
	if len(filters.ActorId) > 0 {
		mods = append(mods,
			qm.Where("actor ->> 'id' LIKE ANY (?)", sqltypes.StringArray(filters.ActorId)),
		)
	}
	if objectType != "" {
		mods = append(mods,
			database.WhereJSONin(`object ->> 'type'`, []string{objectType}),
		)
	}
	return mods
}

func mapAuditLogToRPCEntity(auditLog *models.InternalAudit) (*aimsv1.AuditLog, error) {
	if auditLog == nil {
		return nil, nil
	}
	actor, err := auditLog.GetActor()
	if err != nil {
		return nil, err
	}
	object, err := auditLog.GetObject()
	if err != nil {
		return nil, err
	}
	details, err := auditLog.GetDetails()
	if err != nil {
		return nil, err
	}

	detailsV1 := &aimsv1.AuditLog_AuditDetails{}
	if details != nil {
		detailsV1.Message = details.Message
		detailsV1.Patch = details.Patch
	}

	res := &aimsv1.AuditLog{
		Timestamp: auditLog.Timestamp.String(),
		Action:    auditLog.Action,
		Actor:     &aimsv1.AuditLog_AuditActor{Id: actor.ID},
		Object:    &aimsv1.AuditLog_AuditObject{Type: string(object.Type), Id: object.ID},
		Details:   detailsV1,
	}
	return res, nil
}
