package aimsapi

import (
	"context"

	"github.com/pkg/errors"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/internal/services/permissions"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	"github.com/akuityio/akuity-platform/internal/services/workspaces"
	"github.com/akuityio/akuity-platform/models/models"
)

func mapWorkspaceMemberRole(role permissions.Role) aimsv1.WorkspaceMemberRole {
	switch role {
	case permissions.RoleWorkspaceAdmin:
		return aimsv1.WorkspaceMemberRole_WORKSPACE_MEMBER_ROLE_ADMIN
	case permissions.RoleWorkspaceMember:
		return aimsv1.WorkspaceMemberRole_WORKSPACE_MEMBER_ROLE_MEMBER
	default:
		return aimsv1.WorkspaceMemberRole_WORKSPACE_MEMBER_ROLE_UNSPECIFIED
	}
}

func mapWorkspaceMemberToRPCEntity(m workspaces.WorkspaceMember) *aimsv1.WorkspaceMember {
	member := &aimsv1.WorkspaceMember{
		Id:   m.ID,
		Role: mapWorkspaceMemberRole(m.Role),
	}
	switch ref := m.Ref.(type) {
	case *models.AkuityUser:
		member.Member = &aimsv1.WorkspaceMember_User{
			User: &aimsv1.WorkspaceUserMember{
				Id:    ref.ID,
				Email: ref.Email,
			},
		}
	case *teams.Team:
		member.Member = &aimsv1.WorkspaceMember_Team{
			Team: &aimsv1.WorkspaceTeamMember{
				Id:          ref.ID,
				Name:        ref.Name,
				Description: ref.Description,
				CreateTime:  timestamppb.New(ref.CreationTimestamp),
				MemberCount: ref.MemberCount,
			},
		}
	}
	return member
}

func (s *AimsV1Server) ListWorkspaceMembers(ctx context.Context, req *aimsv1.ListWorkspaceMembersRequest) (*aimsv1.ListWorkspaceMembersResponse, error) {
	if req.GetOrganizationId() == "" {
		return nil, status.Error(codes.InvalidArgument, "organization_id is required")
	}
	if req.GetWorkspaceId() == "" {
		return nil, status.Error(codes.InvalidArgument, "workspace_id is required")
	}

	offset := int(req.GetOffset())
	limit := int(req.GetLimit())
	if limit == 0 {
		limit = 100
	}

	teamSvc := teams.NewService(s.db)
	workspaceSvc := workspaces.NewService(s.db, teamSvc, s.cfg.FeatureGatesSource)
	members, err := workspaceSvc.ListWorkspaceMembers(ctx, req.GetWorkspaceId(), offset, limit)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to list workspace members: %v", err)
	}

	if err != nil {
		return nil, errors.Wrap(err, "list workspace members")
	}

	resp := &aimsv1.ListWorkspaceMembersResponse{
		WorkspaceMembers: make([]*aimsv1.WorkspaceMember, len(members)),
		TeamMemberCount:  0,
		UserMemberCount:  0,
	}
	for idx, m := range members {
		resp.WorkspaceMembers[idx] = mapWorkspaceMemberToRPCEntity(m)
		switch m.Ref.(type) {
		case *models.AkuityUser:
			resp.UserMemberCount += 1
		case *teams.Team:
			resp.TeamMemberCount += 1
		}
	}

	return resp, nil
}

func (s *AimsV1Server) CountTeamMembers(ctx context.Context, teamID string) (int64, error) {
	return models.TeamUsers(qm.Where("team_id = ?", teamID)).Count(ctx, s.db)
}
