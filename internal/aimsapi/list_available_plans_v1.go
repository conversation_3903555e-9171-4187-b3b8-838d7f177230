package aimsapi

import (
	"context"

	"google.golang.org/protobuf/types/known/structpb"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/models/client"
)

func (s *AimsV1Server) ListAvailablePlans(ctx context.Context, req *aimsv1.ListAvailablePlansRequest) (*aimsv1.ListAvailablePlansResponse, error) {
	rs := client.NewRepoSet(s.db)

	plans, err := rs.OrganizationPlans().ListAll(ctx)
	if err != nil {
		return nil, err
	}

	rpcPlans := []*aimsv1.Plan{}
	for _, plan := range plans {
		rpcPlan := &aimsv1.Plan{
			Name:      plan.Name,
			ProductId: plan.ProductID,
			Default:   plan.DefaultPlan.Bool,
		}

		features := map[string]interface{}{}
		quotas := map[string]interface{}{}

		if err := plan.Features.Unmarshal(&features); err != nil {
			return nil, err
		}

		featuresStruct, err := structpb.NewStruct(features)
		if err != nil {
			return nil, err
		}

		rpcPlan.Features = featuresStruct

		if err := plan.Quota.Unmarshal(&quotas); err != nil {
			return nil, err
		}

		quotasStruct, err := structpb.NewStruct(quotas)
		if err != nil {
			return nil, err
		}

		rpcPlan.Quotas = quotasStruct

		rpcPlans = append(rpcPlans, rpcPlan)
	}

	return &aimsv1.ListAvailablePlansResponse{
		Plans: rpcPlans,
	}, nil
}
