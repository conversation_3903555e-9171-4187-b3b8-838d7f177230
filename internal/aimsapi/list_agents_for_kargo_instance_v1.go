package aimsapi

import (
	"context"
	"database/sql"
	"fmt"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/internal/portalapi/kargo"
	"github.com/akuityio/akuity-platform/internal/utils/types"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
)

func (s *AimsV1Server) ListAgentsForKargoInstance(ctx context.Context, req *aimsv1.ListAgentsForKargoInstanceRequest) (*aimsv1.ListAgentsForKargoInstanceResponse, error) {
	id := req.GetInstanceId()

	if id == "" {
		return nil, status.Error(codes.InvalidArgument, "instance_id required")
	}

	repoSet := client.NewRepoSet(s.db)

	agents, err := repoSet.KargoAgents(models.KargoAgentWhere.InstanceID.EQ(id)).ListAll(ctx)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, status.Error(codes.NotFound, fmt.Sprintf("kargo agents for id %s not found", id))
		}
	}

	rpcAgents, err := types.MapSlice(agents, func(in *models.KargoAgent) (*kargov1.KargoAgent, error) {
		return kargo.NewKargoAgentV1(in, s.cfg.ClusterProgressingDeadline)
	})
	if err != nil {
		return nil, err
	}

	return &aimsv1.ListAgentsForKargoInstanceResponse{
		Agents: rpcAgents,
	}, nil
}
