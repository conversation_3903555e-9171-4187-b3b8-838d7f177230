package aimsapi

import (
	"context"

	"github.com/volatiletech/sqlboiler/v4/boil"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	aimsutil "github.com/akuityio/akuity-platform/internal/aimsapi/util"
	"github.com/akuityio/akuity-platform/models/models"
)

func (s *AimsV1Server) UpdateOrganizationDomains(ctx context.Context, req *aimsv1.UpdateOrganizationDomainsRequest) (*aimsv1.UpdateOrganizationDomainsResponse, error) {
	audit := req.GetAudit()

	if err := aimsutil.ValidateAuditPayload(audit); err != nil {
		return nil, err
	}

	org, err := models.Organizations(models.OrganizationWhere.ID.EQ(req.OrganizationId)).One(ctx, s.db)
	if err != nil {
		return nil, err
	}

	vd := make([]models.VerifiedDomain, len(req.Domains))
	for i, d := range req.Domains {
		vd[i] = models.VerifiedDomain{Domain: d.Domain, Verified: d.Verified}
	}

	if err := org.SetVerifiedDomains(vd); err != nil {
		return nil, err
	}

	newCtx := s.aimsAuditSvc.SetActor(ctx, audit)
	if _, err := org.Update(newCtx, s.db, boil.Whitelist(models.OrganizationColumns.VerifiedDomains)); err != nil {
		return nil, err
	}

	return &aimsv1.UpdateOrganizationDomainsResponse{Domains: req.Domains}, nil
}
