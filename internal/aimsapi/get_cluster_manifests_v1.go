package aimsapi

import (
	"database/sql"
	"errors"
	"fmt"
	"strings"

	"google.golang.org/genproto/googleapis/api/httpbody"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	aimsutil "github.com/akuityio/akuity-platform/internal/aimsapi/util"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
	"github.com/akuityio/akuity-platform/models/util/encryption"
)

func (s *AimsV1Server) GetClusterManifests(
	req *aimsv1.GetClusterManifestsRequest,
	ws aimsv1.AimsService_GetClusterManifestsServer,
) error {
	ctx := ws.Context()
	repoSet := client.NewRepoSet(s.db)

	// Verify cluster exists and belongs to the instance
	cluster, err := repoSet.ArgoCDClusters(models.ArgoCDClusterNoStatusManifestMod).Filter(
		models.ArgoCDClusterWhere.InstanceID.EQ(req.GetInstanceId()),
		models.ArgoCDClusterWhere.ID.EQ(req.GetClusterId()),
	).One(ctx)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return fmt.Errorf("cluster not found")
		}
		return err
	}

	audit := req.GetAudit()

	if err := aimsutil.ValidateAuditPayload(audit); err != nil {
		return err
	}

	// Add audit logging for manifest download
	if audit != nil {
		if err := s.aimsAuditSvc.AuditArgoCDClusterPatch(ctx, cluster.Name, audit, []byte(fmt.Sprintf(`{"action": "manifest_download", "cluster_id": "%s", "instance_id": "%s"}`, cluster.ID, req.GetInstanceId()))); err != nil {
			s.log.Error(err, "failed to create audit log for cluster manifest download")
		}
	}

	if err := grpc.SendHeader(ctx, metadata.New(map[string]string{
		"Content-Disposition": fmt.Sprintf("attachment; filename=akuity-manifests-%s.yaml", cluster.Name),
	})); err != nil {
		return err
	}

	const chunkSize = 4096 * 16
	cursor := 1
	accumulated := ""
	decoder, err := encryption.NewCTRDecoder()
	if err != nil {
		return err
	}

	for {
		query := fmt.Sprintf("SELECT SUBSTRING(COALESCE(%s, '') FROM %d FOR %d) FROM argo_cd_cluster WHERE %s=$1 AND %s=$2", models.ArgoCDClusterColumns.StatusManifests, cursor, chunkSize, models.ArgoCDClusterColumns.ID, models.ArgoCDClusterColumns.InstanceID)
		row := s.db.QueryRowContext(ctx, query, req.GetClusterId(), req.GetInstanceId())
		var chunk string
		if err := row.Scan(&chunk); err != nil {
			return err
		}
		if chunk == "" {
			break
		}

		decrypted, err := decoder.Next(chunk)
		if err != nil {
			if !errors.Is(err, encryption.ErrNotEncoded) {
				return err
			}
			// backward compatibility for not encrypted status manifest
			decrypted = chunk
		}

		accumulated += decrypted
		lastNewlineIndex := strings.LastIndex(accumulated, "\n")
		if lastNewlineIndex != -1 {
			sendChunk := accumulated[:lastNewlineIndex]
			if err := ws.Send(&httpbody.HttpBody{
				Data:        []byte(sendChunk),
				ContentType: "application/yaml",
			}); err != nil {
				return err
			}
			accumulated = accumulated[lastNewlineIndex+1:]
		}
		cursor += chunkSize
	}

	if accumulated != "" {
		if err := ws.Send(&httpbody.HttpBody{
			Data:        []byte(accumulated),
			ContentType: "application/yaml",
		}); err != nil {
			return err
		}
	}

	if cursor == 1 {
		return fmt.Errorf("manifests not available")
	}

	return nil
}
