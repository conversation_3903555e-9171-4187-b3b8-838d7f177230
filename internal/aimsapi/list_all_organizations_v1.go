package aimsapi

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"time"

	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/internal/utils/aims"
	"github.com/akuityio/akuity-platform/models/models"
)

func convertToInterfaceSlice(s []string) []interface{} {
	res := make([]interface{}, len(s))
	for i, v := range s {
		res[i] = v
	}
	return res
}

func (s *AimsV1Server) getOrganizationFilters(filters *aimsv1.OrganizationFilter) ([]qm.QueryMod, error) {
	var mods []qm.QueryMod

	if filters == nil {
		return mods, nil
	}

	if filters.Billed != nil {
		if filters.GetBilled() {
			mods = append(mods, qm.InnerJoin("billing on billing.organization_id = organization.id"))
		} else {
			mods = append(mods, qm.LeftOuterJoin("billing on billing.organization_id = organization.id"))
			mods = append(mods, qm.Where("billing.organization_id IS NULL"))
		}
	}

	if filters.ManuallyVerified != nil {
		if verifiedOrgMods, err := s.getManualVerificationFilters(filters.GetManuallyVerified()); err != nil {
			s.log.Error(err, "Failed to retrieve internal config for manual verification filter")
		} else {
			mods = append(mods, verifiedOrgMods...)
		}
	}

	if len(filters.GetPlans()) > 0 {
		planStrings := make([]interface{}, len(filters.GetPlans()))
		for i, p := range filters.GetPlans() {
			planStrings[i] = p
		}
		mods = append(mods, qm.WhereIn("organization.plan IN ?", planStrings...))
	}

	if fuzzVal := filters.GetFuzz(); fuzzVal != "" {
		fuzzPattern := "%" + fuzzVal + "%"
		mods = append(mods, qm.Where(
			"(organization.name LIKE ? OR organization.id = ? OR akuity_user.email LIKE ?)",
			fuzzPattern, fuzzVal, fuzzPattern,
		))
	}

	if startTime := filters.GetStartTime(); startTime != "" {
		if _, err := time.Parse(time.RFC3339, startTime); err != nil {
			return nil, status.Error(codes.InvalidArgument, fmt.Sprintf("invalid start_time: %v", err))
		}
		mods = append(mods, qm.Where("organization.creation_timestamp >= ?", startTime))
	}

	if endTime := filters.GetEndTime(); endTime != "" {
		if _, err := time.Parse(time.RFC3339, endTime); err != nil {
			return nil, status.Error(codes.InvalidArgument, fmt.Sprintf("invalid end_time: %v", err))
		}
		mods = append(mods, qm.Where("organization.creation_timestamp <= ?", endTime))
	}

	return mods, nil
}

func (s *AimsV1Server) getManualVerificationFilters(manuallyVerified bool) ([]qm.QueryMod, error) {
	config, err := aims.RetreiveInternalConfig(s.cmInformer)
	if err != nil {
		return nil, err
	}

	verifiedOrgIDs := []string{}
	for orgID, isVerified := range config.VerifiedOrganizationMap {
		if isVerified {
			verifiedOrgIDs = append(verifiedOrgIDs, orgID)
		}
	}

	var mods []qm.QueryMod
	if manuallyVerified {
		if len(verifiedOrgIDs) == 0 {
			mods = append(mods, qm.Where("1=0"))
		} else {
			mods = append(mods, qm.WhereIn("organization.id IN ?", convertToInterfaceSlice(verifiedOrgIDs)...))
		}
	} else {
		if len(verifiedOrgIDs) > 0 {
			mods = append(mods, qm.WhereNotIn("organization.id NOT IN ?", convertToInterfaceSlice(verifiedOrgIDs)...))
		}
	}

	return mods, nil
}

func (s *AimsV1Server) applySortingAndPagination(mods []qm.QueryMod, filters *aimsv1.OrganizationFilter) []qm.QueryMod {
	limit := 50
	offset := 0

	if filters != nil {
		if filters.Limit != nil {
			limit = int(*filters.Limit)
		}
		if filters.Offset != nil {
			offset = int(*filters.Offset)
		}
	}

	sortApplied := false
	if filters != nil {
		switch filters.GetSortByCreation() {
		case aimsv1.Sort_SORT_ASCENDING:
			mods = append(mods,
				qm.GroupBy("organization."+models.OrganizationColumns.ID),
				qm.OrderBy(fmt.Sprintf("%s asc", "organization."+models.OrganizationColumns.CreationTimestamp)))
			sortApplied = true
		case aimsv1.Sort_SORT_DESCENDING:
			mods = append(mods,
				qm.GroupBy("organization."+models.OrganizationColumns.ID),
				qm.OrderBy(fmt.Sprintf("%s desc", "organization."+models.OrganizationColumns.CreationTimestamp)))
			sortApplied = true
		}
	}

	if !sortApplied {
		mods = append(mods,
			qm.GroupBy("organization."+models.OrganizationColumns.ID),
			qm.OrderBy(fmt.Sprintf("%s desc", "organization."+models.OrganizationColumns.CreationTimestamp)))
	}

	mods = append(mods, qm.Limit(limit), qm.Offset(offset))

	return mods
}

func (s *AimsV1Server) ListAllOrganizations(ctx context.Context, req *aimsv1.ListAllOrganizationsRequest) (*aimsv1.ListAllOrganizationsResponse, error) {
	filters := req.GetFilters()
	data := []InternalOrganizationUserData{}

	mods := []qm.QueryMod{
		qm.From(models.TableNames.Organization),
		qm.Select(internalOrganizationUserDataColumns()...),
		qm.InnerJoin("organization_user on organization_user.organization_id = organization.id"),
		qm.InnerJoin("akuity_user on organization_user.user_id = akuity_user.id"),
	}

	filterMods, err := s.getOrganizationFilters(filters)
	if err != nil {
		return nil, err
	}
	mods = append(mods, filterMods...)

	countQuery := models.NewQuery(mods...)
	queries.SetSelect(countQuery, nil)
	queries.SetDistinct(countQuery, "organization.id")
	queries.SetCount(countQuery)
	var count int64
	err = countQuery.QueryRowContext(ctx, s.db).Scan(&count)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return &aimsv1.ListAllOrganizationsResponse{}, nil
		}
		return nil, err
	}

	mods = s.applySortingAndPagination(mods, filters)

	orgQuery := models.NewQuery(mods...)
	err = orgQuery.Bind(ctx, s.db, &data)
	if err != nil {
		return nil, err
	}

	var basicOrgs []*aimsv1.BasicOrganization
	for _, payload := range data {
		basicOrg, err := s.NewBasicOrg(ctx, payload.Organization)
		if err != nil {
			return nil, err
		}
		basicOrgs = append(basicOrgs, basicOrg)
	}

	return &aimsv1.ListAllOrganizationsResponse{
		Organizations: basicOrgs,
		Count:         uint32(count),
	}, nil
}
