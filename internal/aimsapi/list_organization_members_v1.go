package aimsapi

import (
	"context"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/models/models"
)

type InternalOrganizationMembersData struct {
	OrganizationUser models.OrganizationUser `boil:"organization_user,bind"`
	AkuityUser       models.AkuityUser       `boil:"akuity_user,bind"`
	Organization     models.Organization     `boil:"organization,bind"`
}

func internalOrganizationMembersDataColumns() []string {
	return []string{
		"organization_user.organization_id",
		"akuity_user.id", "akuity_user.email",
		"organization.name",
	}
}

func (s *AimsV1Server) ListOrganizationMembers(ctx context.Context, req *aimsv1.ListOrganizationMembersRequest) (*aimsv1.ListOrganizationMembersResponse, error) {
	data := []InternalOrganizationMembersData{}

	filter := []qm.QueryMod{
		qm.Select(internalOrganizationMembersDataColumns()...),
		qm.From(models.TableNames.OrganizationUser),
		qm.InnerJoin("akuity_user on organization_user.user_id = akuity_user.id"),
		qm.InnerJoin("organization on organization.id = organization_user.organization_id"),
	}

	if len(req.GetOrganizationId()) > 0 {
		orgIds := make([]interface{}, 0)
		for _, id := range req.GetOrganizationId() {
			orgIds = append(orgIds, id)
		}
		filter = append(filter, qm.WhereIn("organization_user.organization_id in ? ", orgIds...))
	}

	err := models.NewQuery(filter...).Bind(ctx, s.db, &data)
	if err != nil {
		return nil, err
	}

	resp := &aimsv1.ListOrganizationMembersResponse{
		Members: make(map[string]*aimsv1.OrganizationMembers),
	}

	for _, member := range data {
		_, ok := resp.Members[member.Organization.Name]

		if !ok {
			resp.Members[member.Organization.Name] = &aimsv1.OrganizationMembers{
				Email: make([]string, 0),
			}
		}

		resp.Members[member.Organization.Name].Email = append(resp.Members[member.Organization.Name].Email, member.AkuityUser.Email)
	}

	return resp, nil
}
