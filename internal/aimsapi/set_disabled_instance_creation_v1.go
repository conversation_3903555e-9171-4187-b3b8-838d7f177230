package aimsapi

import (
	"context"
	"strconv"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	aimsutil "github.com/akuityio/akuity-platform/internal/aimsapi/util"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/utils/aims"
)

func (s *AimsV1Server) SetDisabledInstanceCreation(ctx context.Context, req *aimsv1.SetDisabledInstanceCreationRequest) (*aimsv1.SetDisabledInstanceCreationResponse, error) {
	audit := req.GetAudit()
	if err := aimsutil.ValidateAuditPayload(audit); err != nil {
		return nil, err
	}

	cm, err := aims.RetreiveInternalConfigMap(s.cmInformer)
	if err != nil {
		return nil, err
	}

	cm.Data[config.DisabledInstanceCreationKey] = strconv.FormatBool(req.Disabled)
	if _, err := s.kubeclient.CoreV1().ConfigMaps(config.InternalCmNamespace).Update(ctx, cm, metav1.UpdateOptions{}); err != nil {
		return nil, err
	}

	disabled := "false"
	if req.GetDisabled() {
		disabled = "true"
	}

	return &aimsv1.SetDisabledInstanceCreationResponse{}, s.aimsAuditSvc.AuditArgoCDInstancePatch(ctx, "", audit, []byte(`{"free_instance_creation_disabled": "`+disabled+`"}`))
}
