package aimsapi

import (
	"context"
	"database/sql"
	"time"

	"github.com/auth0/go-auth0/management"
	"github.com/go-logr/logr"
	"github.com/go-playground/validator/v10"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8sruntime "k8s.io/apimachinery/pkg/util/runtime"
	"k8s.io/client-go/informers"
	informerv1 "k8s.io/client-go/informers/core/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/cache"

	"github.com/akuityio/agent/pkg/common"
	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/controllers/platform/integration/clusterautoscaler"
	"github.com/akuityio/akuity-platform/internal/aimsapi/handler"
	"github.com/akuityio/akuity-platform/internal/cli"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	aimsservice "github.com/akuityio/akuity-platform/internal/services/aims"
	"github.com/akuityio/akuity-platform/internal/services/apikeys"
	"github.com/akuityio/akuity-platform/internal/services/customroles"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	"github.com/akuityio/akuity-platform/internal/services/workspaces"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/pkg/billing"
)

type AimsV1Server struct {
	aimsv1.AimsServiceServer

	cfg                     config.AimsConfig
	db                      *sql.DB
	acs                     accesscontrol.PolicyService
	billingProviders        map[billing.ProviderName]billing.IProvider
	instanceLister          *handler.InstanceLister
	kubeclient              *kubernetes.Clientset
	cmInformer              informerv1.ConfigMapInformer
	log                     *logr.Logger
	featureSvc              features.Service
	aimsAuditSvc            aimsservice.AimsAuditService
	clusterAutoscalerConfig common.AutoScalerConfig
	auth0Management         *management.Management
}

func NewAimsV1Server(cfg config.AimsConfig, db *sql.DB, v *validator.Validate, billingProviders map[billing.ProviderName]billing.IProvider, kubeclient *kubernetes.Clientset, log *logr.Logger, auth0Management *management.Management) *AimsV1Server {
	clusterAutoscalerConfig, err := clusterautoscaler.NewConfig(log, kubeclient)
	cli.CheckErr(err)

	argocdVersion, err := misc.GetArgoCDVersions(*log)
	cli.CheckErr(err)

	lister := handler.NewInstanceLister(db, cfg.InstanceProgressingDeadline, cfg.ClusterProgressingDeadline, clusterAutoscalerConfig, argocdVersion)

	factory := informers.NewSharedInformerFactoryWithOptions(kubeclient, 5*time.Minute, informers.WithNamespace(config.InternalCmNamespace), informers.WithTweakListOptions(
		func(options *metav1.ListOptions) {
			options.FieldSelector = "metadata.name=" + config.InternalCmName
		},
	))
	configMapInformer := factory.Core().V1().ConfigMaps()

	repoSet := client.NewRepoSet(db)
	aks := apikeys.NewService(repoSet, v)
	crs := customroles.New(repoSet)
	teamSvc := teams.NewService(db)
	workspaceSvc := workspaces.NewService(db, teamSvc, cfg.FeatureGatesSource)
	aimsAuditSvc := aimsservice.NewAimsAuditService(repoSet)

	aimsAuditSvc.AddAuditHooks()

	return &AimsV1Server{
		db:                      db,
		acs:                     accesscontrol.NewPolicyService(v, crs, workspaceSvc, aks, teamSvc),
		cfg:                     cfg,
		billingProviders:        billingProviders,
		instanceLister:          lister,
		kubeclient:              kubeclient,
		cmInformer:              configMapInformer,
		log:                     log,
		featureSvc:              features.NewService(repoSet, db, false, false, cfg.FeatureGatesSource, config.GetLicense(), features.WithLogger(log)),
		aimsAuditSvc:            aimsAuditSvc,
		clusterAutoscalerConfig: clusterAutoscalerConfig,
		auth0Management:         auth0Management,
	}
}

func (s *AimsV1Server) StartInformer(ctx context.Context) {
	informer := s.cmInformer.Informer()
	stopper := make(chan struct{})
	defer k8sruntime.HandleCrash()
	go informer.Run(ctx.Done())
	if !cache.WaitForCacheSync(stopper, informer.HasSynced) {
		panic("failed to sync informer cache")
	}
	<-ctx.Done()
}

func (s *AimsV1Server) DeleteUnpaidInstance(ctx context.Context, req *aimsv1.DeleteUnpaidInstanceRequest) (*aimsv1.DeleteUnpaidInstanceResponse, error) {
	return handler.DeleteUnpaidInstanceV1(s.db, s.aimsAuditSvc)(ctx, req)
}

func (s *AimsV1Server) OnboardManualCustomer(ctx context.Context, req *aimsv1.OnboardManualCustomerRequest) (*aimsv1.OnboardManualCustomerResponse, error) {
	return handler.OnboardManualCustomerV1(s.db, s.billingProviders, s.aimsAuditSvc)(ctx, req)
}

func (s *AimsV1Server) ListUnbilledOrganizations(ctx context.Context, req *aimsv1.ListUnbilledOrganizationsRequest) (*aimsv1.ListUnbilledOrganizationsResponse, error) {
	return handler.ListUnbilledOrganizationsV1(s.db, s.cfg.DomainSuffix)(ctx, req)
}

func (s *AimsV1Server) UpdateOrganizationTrialExpiration(ctx context.Context, req *aimsv1.UpdateOrganizationTrialExpirationRequest) (*aimsv1.UpdateOrganizationTrialExpirationResponse, error) {
	return handler.UpdateOrganizationTrialExpirationV1(s.db)(ctx, req)
}

func (s *AimsV1Server) DecrementInstanceGeneration(ctx context.Context, req *aimsv1.DecrementInstanceGenerationRequest) (*aimsv1.DecrementInstanceGenerationResponse, error) {
	return handler.DecrementInstanceGenerationV1(s.db, s.aimsAuditSvc)(ctx, req)
}

func (s *AimsV1Server) GetInstanceById(ctx context.Context, req *aimsv1.GetInstanceByIdRequest) (*aimsv1.GetInstanceByIdResponse, error) {
	return s.instanceLister.GetInstanceByIdV1()(ctx, req)
}

func (s *AimsV1Server) ListClustersForInstance(ctx context.Context, req *aimsv1.ListClustersForInstanceRequest) (*aimsv1.ListClustersForInstanceResponse, error) {
	return handler.ListClustersForInstanceV1(s.db, s.cfg.ClusterProgressingDeadline, s.clusterAutoscalerConfig)(ctx, req)
}
