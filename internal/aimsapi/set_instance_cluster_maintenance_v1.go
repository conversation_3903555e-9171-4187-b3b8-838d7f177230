package aimsapi

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	aimsutil "github.com/akuityio/akuity-platform/internal/aimsapi/util"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

func (s *AimsV1Server) InstanceClusterMaintenance(ctx context.Context, req *aimsv1.InstanceClusterMaintenanceRequest) (*aimsv1.InstanceClusterMaintenanceResponse, error) {
	audit := req.GetAudit()

	if err := aimsutil.ValidateAuditPayload(audit); err != nil {
		return nil, err
	}

	if req.GetInstanceId() == "" {
		return nil, status.Error(codes.InvalidArgument, "instance_id required")
	}

	if req.GetClusterId() == "" {
		return nil, status.Error(codes.InvalidArgument, "cluster_id required")
	}

	repoSet := client.NewRepoSet(s.db)

	cluster, err := repoSet.ArgoCDClusters().Filter(models.ArgoCDClusterWhere.InstanceID.EQ(req.GetInstanceId())).GetByID(ctx, req.GetClusterId())
	if err != nil {
		return nil, err
	}

	currentSpec, err := cluster.GetSpec()
	if err != nil {
		return nil, err
	}

	currentSpec.MaintenanceMode = req.GetMaintanenceMode()

	if err := cluster.SetSpec(*currentSpec); err != nil {
		return nil, err
	}

	newCtx := s.aimsAuditSvc.SetActor(ctx, audit)

	return &aimsv1.InstanceClusterMaintenanceResponse{}, repoSet.ArgoCDClusters().Update(newCtx, cluster, models.ArgoCDClusterColumns.Spec)
}
