package aimsapi

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/ai"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	"github.com/akuityio/akuity-platform/models/client"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *AimsV1Server) GetAIConversation(
	ctx context.Context,
	req *organizationv1.GetAIConversationRequest,
) (*organizationv1.GetAIConversationResponse, error) {
	repoSet := client.NewRepoSet(s.db)
	orgID := req.GetOrganizationId()
	featureStatuses := s.featureSvc.GetFeatureStatuses(ctx, &orgID)
	aiSvc, err := ai.NewService(s.db, repoSet, featureStatuses, orgID, config.AIConfig{}, nil, logging.Extract(ctx))
	if err != nil {
		return nil, err
	}
	conversation, err := aiSvc.GetConversation(ctx, req.GetId(), nil)
	if err != nil {
		return nil, err
	}
	return &organizationv1.GetAIConversationResponse{
		Conversation: conversation,
	}, nil
}
