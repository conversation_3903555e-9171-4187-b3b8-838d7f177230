package aimsapi

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

func (s *AimsV1Server) GetKargoInstanceById(ctx context.Context, req *aimsv1.GetKargoInstanceByIdRequest) (*aimsv1.GetKargoInstanceByIdResponse, error) {
	id := req.GetInstanceId()

	if id == "" {
		return nil, status.Error(codes.InvalidArgument, "instance_id required")
	}

	data := InternalKargoInstanceData{}

	err := models.NewQuery(
		qm.Select(internalKargoInstanceDataColumns()...),
		qm.From(models.TableNames.KargoInstance),
		qm.InnerJoin("organization on organization.id = kargo_instance.organization_owner"),
		qm.Where("kargo_instance.id = ?", id),
	).Bind(ctx, s.db, &data)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, status.Error(codes.NotFound, fmt.Sprintf("kargo id %s not found", id))
		}
		return nil, err
	}

	workspace, err := models.Workspaces(
		qm.Select("id", "name"),
		qm.Where("id = ?", data.KargoInstance.WorkspaceID),
	).One(ctx, s.db)

	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, err
	}

	var apiWorkspace *aimsv1.Workspace
	if workspace != nil {
		apiWorkspace = &aimsv1.Workspace{
			Id:   workspace.ID,
			Name: workspace.Name,
		}
	}

	instance, err := toRPCKargoInstance(data.KargoInstance, s.cfg.InstanceProgressingDeadline)
	if err != nil {
		return nil, err
	}

	rs := client.NewRepoSet(s.db)

	instanceConfig, err := rs.KargoInstanceConfigs().GetByID(ctx, instance.Id)
	if err != nil {
		return nil, err
	}

	instance.Fqdn = instanceConfig.FQDN.String
	if instanceConfig.Version.Valid {
		instance.Version = instanceConfig.Version.String
	}

	org, err := toRPCKargoInstanceOrganization(data.Organization)
	if err != nil {
		return nil, err
	}

	statusStruct, err := convertNullJSONToStruct(data.KargoInstance.StatusInfo)
	if err != nil {
		return nil, err
	}

	return &aimsv1.GetKargoInstanceByIdResponse{
		Instance: &aimsv1.InternalKargoInstance{
			Instance:          instance,
			Organization:      org,
			Workspace:         apiWorkspace,
			CreationTimestamp: timestamppb.New(data.KargoInstance.CreationTimestamp),
			StatusInfo:        statusStruct,
		},
	}, nil
}
