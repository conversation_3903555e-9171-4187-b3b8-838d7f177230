package aimsapi

import (
	"context"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/internal/utils/aims"
)

func (s *AimsV1Server) GetInternalConfig(ctx context.Context, req *aimsv1.GetInternalConfigRequest) (*aimsv1.GetInternalConfigResponse, error) {
	config, err := aims.RetreiveInternalConfig(s.cmInformer)
	if err != nil {
		return nil, err
	}

	return &aimsv1.GetInternalConfigResponse{
		Config: &aimsv1.InternalConfig{
			DisableFreeInstanceCreation: config.DisableFreeInstanceCreation,
		},
	}, nil
}
