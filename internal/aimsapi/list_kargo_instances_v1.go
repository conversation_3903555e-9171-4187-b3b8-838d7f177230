package aimsapi

import (
	"context"
	"time"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/models/models"
)

func (s *AimsV1Server) ListKargoInstances(ctx context.Context, req *aimsv1.ListKargoInstancesRequest) (*aimsv1.ListKargoInstancesResponse, error) {
	data := []InternalKargoInstanceData{}

	columns := internalKargoInstanceDataColumns()
	columns = append(columns, configVersionColumn())

	filter := []qm.QueryMod{
		qm.Select(columns...),
		qm.From(models.TableNames.KargoInstance),
		qm.InnerJoin("organization on organization.id = kargo_instance.organization_owner"),
		qm.LeftOuterJoin("kargo_instance_config on kargo_instance.id = kargo_instance_config.instance_id"),
	}

	requestedFilters := req.GetFilter()

	// prioritize paid filter
	if requestedFilters.GetPaid() {
		filter = append(filter, qm.Where("org_status ->> 'state' = ?", models.PaidCustomer))
	} else if requestedFilters.GetUnpaid() {
		filter = append(filter, qm.Where("org_status ->> 'state' != ?", models.PaidCustomer))
	}

	if organizationID := requestedFilters.GetOrganizationId(); organizationID != "" {
		filter = append(filter, qm.Where("kargo_instance.organization_owner = ?", organizationID))
	}

	if requestedFilters.GetFuzz() != "" {
		fuzz := requestedFilters.GetFuzz()
		filter = append(filter, qm.Where("kargo_instance.id = ? or kargo_instance.name like ? or organization.name = ?", fuzz, "%"+fuzz+"%", fuzz))
	}

	if requestedFilters.GetTimeFrom() != "" {
		timeFromParsed, err := time.Parse(time.RFC3339, requestedFilters.GetTimeFrom())
		if err != nil {
			return nil, status.Error(codes.InvalidArgument, "invalid time_from format")
		}
		filter = append(filter, qm.Where("kargo_instance.creation_timestamp >= ?", timeFromParsed))
	}

	filter = append(filter, qm.OrderBy("kargo_instance.creation_timestamp DESC"))

	err := models.NewQuery(filter...).Bind(ctx, s.db, &data)
	if err != nil {
		return nil, err
	}

	instancesList := &aimsv1.ListKargoInstancesResponse{
		Instances: []*aimsv1.InternalKargoInstance{},
	}

	for _, d := range data {
		org, err := toRPCKargoInstanceOrganization(d.Organization)
		if err != nil {
			return nil, err
		}

		instance, err := toRPCKargoInstance(d.KargoInstance, s.cfg.InstanceProgressingDeadline)
		if err != nil {
			return nil, err
		}

		instance.Version = d.Version

		statusStruct, err := convertNullJSONToStruct(d.KargoInstance.StatusInfo)
		if err != nil {
			return nil, err
		}

		instancesList.Instances = append(instancesList.Instances, &aimsv1.InternalKargoInstance{
			Instance:          instance,
			Organization:      org,
			CreationTimestamp: timestamppb.New(d.KargoInstance.CreationTimestamp),
			StatusInfo:        statusStruct,
		})
	}

	return instancesList, nil
}
