package aimsapi

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	"github.com/akuityio/akuity-platform/internal/services/workspaces"
	"github.com/akuityio/akuity-platform/models/models"
)

func mapWorkspaceToRPCEntity(ws workspaces.Workspace) *aimsv1.Workspace {
	argoInstances := make([]*aimsv1.WorkspaceArgoCDInstance, 0, len(ws.R.ArgoCDInstances))
	for _, argoInstance := range ws.R.ArgoCDInstances {
		argoInstances = append(argoInstances, &aimsv1.WorkspaceArgoCDInstance{
			Id:   argoInstance.ID,
			Name: argoInstance.Name,
		})
	}

	kargoInstances := make([]*aimsv1.WorkspaceKargoInstance, 0, len(ws.R.KargoInstances))
	for _, kargoInstance := range ws.R.KargoInstances {
		kargoInstances = append(kargoInstances, &aimsv1.WorkspaceKargoInstance{
			Id:   kargoInstance.ID,
			Name: kargoInstance.Name,
		})
	}

	var teamMemberCount, userMemberCount uint32
	for _, member := range ws.Members {
		switch member.Ref.(type) {
		case *teams.Team:
			teamMemberCount++
		case *models.AkuityUser:
			userMemberCount++
		}
	}

	return &aimsv1.Workspace{
		Id:              ws.ID,
		Name:            ws.Name,
		Description:     ws.Description.String,
		CreateTime:      timestamppb.New(ws.CreationTimestamp),
		ArgocdInstances: argoInstances,
		KargoInstances:  kargoInstances,
		TeamMemberCount: teamMemberCount,
		UserMemberCount: userMemberCount,
		IsDefault:       ws.IsDefault,
	}
}

func (s *AimsV1Server) ListWorkspaces(ctx context.Context, req *aimsv1.ListWorkspacesRequest) (*aimsv1.ListWorkspacesResponse, error) {
	if req.GetOrganizationId() == "" {
		return nil, status.Error(codes.InvalidArgument, "organization_id is required")
	}

	offset := int(req.GetOffset())
	limit := int(req.GetLimit())
	if limit == 0 {
		limit = 10
	}

	teamSvc := teams.NewService(s.db)
	workspaceSvc := workspaces.NewService(s.db, teamSvc, s.cfg.FeatureGatesSource)

	totalCount, err := workspaceSvc.CountWorkspaces(ctx, req.GetOrganizationId())
	if err != nil {
		return nil, err
	}

	workspaces, err := workspaceSvc.ListWorkspaces(ctx, req.GetOrganizationId(), offset, limit)
	if err != nil {
		return nil, err
	}

	var apiWorkspaces []*aimsv1.Workspace
	for _, ws := range workspaces {
		apiWorkspaces = append(apiWorkspaces, mapWorkspaceToRPCEntity(ws))
	}

	return &aimsv1.ListWorkspacesResponse{
		Workspaces: apiWorkspaces,
		Count:      uint32(totalCount),
	}, nil
}
