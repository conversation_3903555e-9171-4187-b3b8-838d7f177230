package aimsapi

import (
	"context"

	"github.com/go-playground/validator/v10"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/internal/auth0"
	internalconfig "github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/organizations"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	"github.com/akuityio/akuity-platform/internal/services/workspaces"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

const (
	queryListUserTeams = `
		SELECT tu.user_id, t.id, t.name
		FROM team_user tu
		JOIN team t ON tu.team_id = t.id
		WHERE t.organization_id = $1;
	`
)

func (s *AimsV1Server) ListOrganizationUsers(ctx context.Context, req *aimsv1.ListOrganizationUsersRequest) (*aimsv1.ListOrganizationUsersResponse, error) {
	if req.GetOrganizationId() == "" {
		return nil, status.Error(codes.InvalidArgument, "organization_id is required")
	}

	txDB, txBeginner := database.WithTxBeginner(s.db)
	repoSet := client.NewRepoSet(txDB)

	auth0Client, err := auth0.New(ctx, s.cfg.Auth0)
	if err != nil {
		return nil, err
	}

	v := validator.New()
	orgSvc := organizations.New(txDB, txBeginner, auth0Client, s.billingProviders, repoSet, internalconfig.PortalServerConfig{
		DomainSuffix: s.cfg.DomainSuffix,
	}, v)
	members, err := orgSvc.ListMembers(ctx, req.GetOrganizationId())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to fetch organization members: %v", err)
	}

	usersMap := make(map[string]*aimsv1.OrganizationUser)
	users := make([]*aimsv1.OrganizationUser, 0, len(members))

	for _, member := range members {
		user := &aimsv1.OrganizationUser{
			Id:         member.ID,
			Email:      member.Email,
			Role:       member.Role,
			Workspaces: []*aimsv1.WorkspaceInfo{},
			Teams:      []*aimsv1.TeamInfo{},
		}
		usersMap[member.ID] = user
		users = append(users, user)
	}

	teamSvc := teams.NewService(s.db)
	workspaceSvc := workspaces.NewService(s.db, teamSvc, s.cfg.FeatureGatesSource)

	wsList, err := workspaceSvc.ListWorkspaces(ctx, req.GetOrganizationId(), 0, 1000)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to fetch workspaces: %v", err)
	}

	for _, ws := range wsList {
		for _, member := range ws.Members {
			if user, ok := member.Ref.(*models.AkuityUser); ok {
				userID := user.ID

				if orgUser, exists := usersMap[userID]; exists {
					orgUser.Workspaces = append(orgUser.Workspaces, &aimsv1.WorkspaceInfo{
						Id:   ws.ID,
						Name: ws.Name,
					})
				}
			}
		}
	}

	err = s.fetchUserTeams(ctx, req.OrganizationId, usersMap)
	if err != nil {
		return nil, err
	}

	return &aimsv1.ListOrganizationUsersResponse{Users: users}, nil
}

func (s *AimsV1Server) fetchUserTeams(ctx context.Context, orgID string, usersMap map[string]*aimsv1.OrganizationUser) error {
	rows, err := s.db.QueryContext(ctx, queryListUserTeams, orgID)
	if err != nil {
		return status.Errorf(codes.Internal, "failed to fetch teams: %v", err)
	}
	defer rows.Close()

	for rows.Next() {
		var userID string
		var team aimsv1.TeamInfo
		err := rows.Scan(&userID, &team.Id, &team.Name)
		if err != nil {
			return status.Errorf(codes.Internal, "failed to scan team row: %v", err)
		}
		if user, exists := usersMap[userID]; exists {
			user.Teams = append(user.Teams, &team)
		}
	}

	return nil
}
