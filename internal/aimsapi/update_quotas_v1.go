package aimsapi

import (
	"context"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	aimsutil "github.com/akuityio/akuity-platform/internal/aimsapi/util"
)

func (s *AimsV1Server) UpdateQuotas(ctx context.Context, req *aimsv1.UpdateQuotasRequest) (*aimsv1.UpdateQuotasResponse, error) {
	audit := req.GetAudit()

	if err := aimsutil.ValidateAuditPayload(audit); err != nil {
		return nil, err
	}

	patch := req.GetQuota()

	if patch == nil {
		return &aimsv1.UpdateQuotasResponse{}, nil
	}

	newCtx := s.aimsAuditSvc.SetActor(ctx, audit)

	quotas, _, err := s.featureSvc.PatchQuotas(newCtx, req.GetId(), req.GetQuota())
	if err != nil {
		return nil, err
	}

	return &aimsv1.UpdateQuotasResponse{Quota: quotas}, nil
}
