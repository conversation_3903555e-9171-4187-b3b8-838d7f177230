package aimsapi

import (
	"context"

	"github.com/auth0/go-auth0/management"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	aimsutil "github.com/akuityio/akuity-platform/internal/aimsapi/util"
)

func (s *AimsV1Server) ResetMFA(ctx context.Context, req *aimsv1.ResetMFARequest) (*aimsv1.ResetMFAResponse, error) {
	audit := req.GetAudit()

	if err := aimsutil.ValidateAuditPayload(audit); err != nil {
		return nil, err
	}

	// API to get user ID from email: https://auth0.com/docs/api/management/v2/users-by-email/get-users-by-email
	users, err := s.auth0Management.User.ListByEmail(ctx, req.Email, management.IncludeFields("user_id"))
	if err != nil {
		return nil, err
	}

	for _, user := range users {
		// API to delete all the MFA factors of user: https://auth0.com/docs/api/management/v2/users/delete-authentication-methods
		if err := s.auth0Management.User.DeleteAllAuthenticationMethods(ctx, *user.ID); err != nil {
			return nil, err
		}
	}

	patch := []byte(`{"mfa_reset_user_email": "` + req.Email + `"}`)
	if err := s.aimsAuditSvc.AuditOrganizationPatch(ctx, req.GetOrganizationId(), audit, patch); err != nil {
		return nil, err
	}

	return &aimsv1.ResetMFAResponse{}, nil
}
