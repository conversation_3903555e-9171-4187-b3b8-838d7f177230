package aimsapi

import (
	"database/sql"
	"errors"
	"fmt"
	"strings"

	"google.golang.org/genproto/googleapis/api/httpbody"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	aimsutil "github.com/akuityio/akuity-platform/internal/aimsapi/util"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
	"github.com/akuityio/akuity-platform/models/util/encryption"
)

func (s *AimsV1Server) GetKargoAgentManifests(
	req *aimsv1.GetKargoAgentManifestsRequest,
	ws aimsv1.AimsService_GetKargoAgentManifestsServer,
) error {
	ctx := ws.Context()
	repoSet := client.NewRepoSet(s.db)

	// Verify agent exists and belongs to the instance
	agent, err := repoSet.KargoAgents(models.KargoAgentNoStatusManifestMod).Filter(
		models.KargoAgentWhere.InstanceID.EQ(req.GetInstanceId()),
		models.KargoAgentWhere.ID.EQ(req.GetAgentId()),
	).One(ctx)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return fmt.Errorf("agent not found")
		}
		return err
	}

	audit := req.GetAudit()

	if err := aimsutil.ValidateAuditPayload(audit); err != nil {
		return err
	}

	// Add audit logging for manifest download
	if audit != nil {
		if err := s.aimsAuditSvc.AuditKargoInstancePatch(ctx, req.GetInstanceId(), audit, []byte(fmt.Sprintf(`{"action": "agent_manifest_download", "agent_id": "%s", "instance_id": "%s"}`, agent.ID, req.GetInstanceId()))); err != nil {
			s.log.Error(err, "failed to create audit log for agent manifest download")
		}
	}

	if err := grpc.SendHeader(ctx, metadata.New(map[string]string{
		"Content-Disposition": fmt.Sprintf("attachment; filename=akuity-manifests-%s.yaml", agent.Name),
	})); err != nil {
		return err
	}

	const chunkSize = 4096 * 16
	cursor := 1
	accumulated := ""
	decoder, err := encryption.NewCTRDecoder()
	if err != nil {
		return err
	}

	for {
		query := fmt.Sprintf("SELECT SUBSTRING(COALESCE(%s, '') FROM %d FOR %d) FROM kargo_agent WHERE %s=$1 AND %s=$2", models.KargoAgentColumns.StatusManifests, cursor, chunkSize, models.KargoAgentColumns.ID, models.KargoAgentColumns.InstanceID)
		row := s.db.QueryRowContext(ctx, query, req.GetAgentId(), req.GetInstanceId())
		var chunk string
		if err := row.Scan(&chunk); err != nil {
			return err
		}
		if chunk == "" {
			break
		}

		decrypted, err := decoder.Next(chunk)
		if err != nil {
			if !errors.Is(err, encryption.ErrNotEncoded) {
				return err
			}
			// backward compatibility for not encrypted status manifest
			decrypted = chunk
		}

		accumulated += decrypted
		lastNewlineIndex := strings.LastIndex(accumulated, "\n")
		if lastNewlineIndex != -1 {
			sendChunk := accumulated[:lastNewlineIndex]
			if err := ws.Send(&httpbody.HttpBody{
				Data:        []byte(sendChunk),
				ContentType: "application/yaml",
			}); err != nil {
				return err
			}
			accumulated = accumulated[lastNewlineIndex+1:]
		}
		cursor += chunkSize
	}

	if accumulated != "" {
		if err := ws.Send(&httpbody.HttpBody{
			Data:        []byte(accumulated),
			ContentType: "application/yaml",
		}); err != nil {
			return err
		}
	}

	if cursor == 1 {
		return fmt.Errorf("manifests not available")
	}

	return nil
}
