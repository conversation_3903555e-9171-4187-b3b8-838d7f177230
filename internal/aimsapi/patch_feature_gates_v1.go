package aimsapi

import (
	"context"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	aimsutil "github.com/akuityio/akuity-platform/internal/aimsapi/util"
)

func (s *AimsV1Server) PatchFeatureGates(ctx context.Context, req *aimsv1.PatchFeatureGatesRequest) (*aimsv1.PatchFeatureGatesResponse, error) {
	audit := req.GetAudit()

	if err := aimsutil.ValidateAuditPayload(audit); err != nil {
		return nil, err
	}

	patch := req.GetFeatureGates()

	if patch == nil {
		return &aimsv1.PatchFeatureGatesResponse{}, nil
	}

	newCtx := s.aimsAuditSvc.SetActor(ctx, audit)

	gates, _, err := s.featureSvc.PatchFeatureGates(newCtx, req.GetId(), patch)
	if err != nil {
		return nil, err
	}

	return &aimsv1.PatchFeatureGatesResponse{
		FeatureGates: gates,
	}, nil
}
