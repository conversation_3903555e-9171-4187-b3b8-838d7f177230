package aimsapi

import (
	"context"
	"database/sql"
	"fmt"
	"net"
	"net/http"

	"github.com/auth0/go-auth0/management"
	"github.com/gin-gonic/gin"
	"github.com/go-logr/logr"
	"github.com/go-playground/validator/v10"
	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"k8s.io/client-go/kubernetes"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/internal/config"
	ctxutil "github.com/akuityio/akuity-platform/internal/utils/context"
	akuitygrpc "github.com/akuityio/akuity-platform/internal/utils/grpc"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/gateway"
	httputil "github.com/akuityio/akuity-platform/internal/utils/http"
	"github.com/akuityio/akuity-platform/pkg/billing"
)

type AimsAPIServer interface{}

type apiServer struct {
	bindNetwork string
	bindAddress string

	kubeclient       *kubernetes.Clientset
	db               *sql.DB
	billingProviders map[billing.ProviderName]billing.IProvider
	auth0Management  *management.Management
}

func NewAimsApi(db *sql.DB, bindNetwork, bindAddress string, billingProviders map[billing.ProviderName]billing.IProvider, kubeclient *kubernetes.Clientset, auth0Management *management.Management) *apiServer {
	return &apiServer{bindNetwork, bindAddress, kubeclient, db, billingProviders, auth0Management}
}

func (srv *apiServer) runAPI(cfg config.AimsConfig, log *logr.Logger, errCh chan error) (*grpc.Server, error) {
	grpcSrv := grpc.NewServer(
		akuitygrpc.NewUnaryServerInterceptor(
			log,
			akuitygrpc.WithUnaryServerInterceptors(func(ctx context.Context, req any, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp any, err error) {
				newCtx := ctxutil.SetPatchFuncPlaceholder(ctx)

				return handler(newCtx, req)
			}),
		),
		akuitygrpc.NewStreamServerInterceptor(
			log,
		),
	)

	l, err := net.Listen(srv.bindNetwork, srv.bindAddress)
	if err != nil {
		return nil, fmt.Errorf("new listener: %w", err)
	}

	v := validator.New()

	aimsServer := NewAimsV1Server(cfg, srv.db, v, srv.billingProviders, srv.kubeclient, log, srv.auth0Management)
	aimsv1.RegisterAimsServiceServer(grpcSrv, aimsServer)
	go aimsServer.StartInformer(context.Background())

	go func() {
		log.Info(fmt.Sprintf("api is listening on %s://%s", srv.bindNetwork, srv.bindAddress))
		if err := grpcSrv.Serve(l); err != nil {
			errCh <- fmt.Errorf("serve api: %w", err)
		}
	}()
	return grpcSrv, nil
}

func (srv *apiServer) Proxy() (http.HandlerFunc, error) {
	dialOpts := []grpc.DialOption{
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	}
	cc, err := grpc.NewClient(srv.bindAddress, dialOpts...)
	if err != nil {
		return nil, fmt.Errorf("dial api: %w", err)
	}
	mux := runtime.NewServeMux(gateway.InjectMetadata())
	if err := aimsv1.RegisterAimsServiceHandler(context.Background(), mux, cc); err != nil {
		return nil, fmt.Errorf("register aims service handler: %w", err)
	}
	r := gin.New()
	r.Use(gin.Recovery())
	r.Use(httputil.InjectVersionHeader())
	r.Any("/*any", gin.WrapH(mux))
	return r.ServeHTTP, nil
}

func (srv *apiServer) Start(ctx context.Context, cfg config.AimsConfig, log *logr.Logger) error {
	errCh := make(chan error, 1)
	api, err := srv.runAPI(cfg, log, errCh)
	if err != nil {
		return fmt.Errorf("run api: %w", err)
	}
	select {
	case <-ctx.Done():
		api.GracefulStop()
		return nil
	case err := <-errCh:
		return err
	}
}
