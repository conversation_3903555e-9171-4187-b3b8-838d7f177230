package aimsapi

import (
	"context"
	"database/sql"
	"errors"

	"github.com/volatiletech/null/v8"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	aimsutil "github.com/akuityio/akuity-platform/internal/aimsapi/util"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

func (s *AimsV1Server) UpdateOrganizationBillingPlan(ctx context.Context, req *aimsv1.UpdateOrganizationBillingPlanRequest) (*aimsv1.UpdateOrganizationBillingPlanResponse, error) {
	audit := req.GetAudit()

	if err := aimsutil.ValidateAuditPayload(audit); err != nil {
		return nil, err
	}

	if req.GetOrganizationId() == "" {
		return nil, status.Error(codes.InvalidArgument, "organization id is empty")
	}

	txDB, _ := database.WithTxBeginner(s.db)
	rs := client.NewRepoSet(txDB)

	_, err := rs.Organizations().GetByID(ctx, req.GetOrganizationId())
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, status.Error(codes.NotFound, "organization not found")
		}

		return nil, err
	}

	orgPlan, err := rs.OrganizationPlans().GetByID(ctx, req.GetPlan())
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, status.Error(codes.NotFound, "plan not found")
		}
		return nil, err
	}

	newCtx := s.aimsAuditSvc.SetActor(ctx, audit)

	return &aimsv1.UpdateOrganizationBillingPlanResponse{}, rs.Organizations().Update(newCtx, &models.Organization{
		ID:   req.GetOrganizationId(),
		Plan: null.StringFrom(orgPlan.Name),
	}, models.OrganizationColumns.Plan)
}
