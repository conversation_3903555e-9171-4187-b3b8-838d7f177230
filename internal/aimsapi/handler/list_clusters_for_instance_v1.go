package handler

import (
	"context"
	"database/sql"
	"errors"
	"time"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/agent/pkg/common"
	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	argocdutil "github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/internal/utils/types"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

type ListClustersForInstanceHandlerFunc func(ctx context.Context, req *aimsv1.ListClustersForInstanceRequest) (*aimsv1.ListClustersForInstanceResponse, error)

func ListClustersForInstanceV1(db *sql.DB, clusterProgressingDeadline time.Duration, clusterAutoscalerConfig common.AutoScalerConfig) ListClustersForInstanceHandlerFunc {
	return func(ctx context.Context, req *aimsv1.ListClustersForInstanceRequest) (*aimsv1.ListClustersForInstanceResponse, error) {
		if req.GetInstanceId() == "" {
			return nil, errors.New("instance_id is required")
		}

		mods := []qm.QueryMod{}

		filters := req.GetFilter()
		if filters != nil {
			fuzz := filters.GetFuzz()

			filtersMods := []qm.QueryMod{}

			if fuzz != "" {
				filtersMods = append(filtersMods, qm.Expr(qm.Where(models.ArgoCDClusterColumns.Name+" like ?", "%"+fuzz+"%"),
					qm.Or2(models.ArgoCDClusterWhere.ID.EQ(fuzz)),
					qm.Or2(models.ArgoCDClusterWhere.Namespace.EQ(fuzz))),
				)
			}

			mods = append(mods, filtersMods...)

			timeFrom := filters.GetTimeFrom()

			if timeFrom != "" {
				timeFromParsed, err := time.Parse(time.RFC3339, timeFrom)
				if err != nil {
					return nil, status.Error(codes.InvalidArgument, "invalid time from")
				}
				mods = append(mods, qm.Where("creation_timestamp >= ?", timeFromParsed))
			}
		}

		mods = append(mods, qm.Where("instance_id = ?", req.GetInstanceId()))

		clusterModels, err := models.ArgoCDClusters(mods...).All(ctx, db)
		if err != nil {
			return nil, err
		}

		clusters, err := types.MapSlice(clusterModels, func(in *models.ArgoCDCluster) (*argocdv1.Cluster, error) {
			return argocdutil.NewArgoCDClusterV1(*in, clusterProgressingDeadline, clusterAutoscalerConfig)
		})
		if err != nil {
			return nil, err
		}

		return &aimsv1.ListClustersForInstanceResponse{
			Clusters: clusters,
		}, nil
	}
}
