package handler

import (
	"context"
	"database/sql"
	"time"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/protobuf/types/known/timestamppb"

	agentclient "github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/agent/pkg/common"
	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	argocdutil "github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	healthv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/status/health/v1"
)

type (
	ListAgroInstancesHandlerFunc func(ctx context.Context, req *aimsv1.ListArgoInstancesRequest) (*aimsv1.ListArgoInstancesResponse, error)
)

type OrganizationInfo struct {
	organization *models.Organization
	expiryTime   int64
}

type InstanceLister struct {
	db                          *sql.DB
	instanceProgressingDeadline time.Duration
	clustersProgressingDeadline time.Duration
	clusterAutoscalerConfig     common.AutoScalerConfig
	versions                    []agentclient.ComponentVersion
}

func NewInstanceLister(db *sql.DB, instanceProgressingDeadline, clustersProgressingDeadline time.Duration, clusterAutoscalerConfig common.AutoScalerConfig, versions []agentclient.ComponentVersion) *InstanceLister {
	return &InstanceLister{
		db:                          db,
		instanceProgressingDeadline: instanceProgressingDeadline,
		clustersProgressingDeadline: clustersProgressingDeadline,
		clusterAutoscalerConfig:     clusterAutoscalerConfig,
		versions:                    versions,
	}
}

func UnixTimestampToPbtimestamp(unixTimestamp int64) *timestamppb.Timestamp {
	return timestamppb.New(time.Unix(unixTimestamp, 0))
}

func (l *InstanceLister) GetClusters(ctx context.Context, instanceID string) (*[]*argocdv1.Cluster, error) {
	clusters, err := models.ArgoCDClusters(qm.Where("instance_id = ?", instanceID)).All(ctx, l.db)
	if err != nil {
		return nil, err
	}
	var clusterList []*argocdv1.Cluster
	for _, cluster := range clusters {
		c, err := argocdutil.NewArgoCDClusterV1(*cluster, l.clustersProgressingDeadline, l.clusterAutoscalerConfig)
		if err != nil {
			return nil, err
		}
		clusterList = append(clusterList, c)
	}
	return &clusterList, nil
}

func (l *InstanceLister) ProcessInstance(instanceModel *instances.ArgoCDInstance, orgMap map[string]*OrganizationInfo) (*aimsv1.InternalInstance, error) {
	instance, err := argocdutil.NewArgoCDInstanceV1(*instanceModel, l.instanceProgressingDeadline, argocdutil.IsComponentVersionSupported(instanceModel.Version.String, l.versions) != nil)
	if err != nil {
		return nil, err
	}
	notificationConfig, err := instanceModel.GetArgoCDNotificationsConfigMap()
	if err != nil {
		return nil, err
	}

	internalInstance := &aimsv1.InternalInstance{
		Instance: instance,
	}
	if notificationConfig != nil {
		internalInstance.NotificationConfig = &aimsv1.NotificationConfig{
			Config: notificationConfig,
		}
	}

	ownerOrg := orgMap[instanceModel.OrganizationOwner]
	if ownerOrg != nil {
		instance.OwnerOrganizationName = ownerOrg.organization.Name
		status, err := ownerOrg.organization.GetOrgStatus()
		if err != nil {
			return nil, err
		}
		internalInstance.Expired = status.ExpiryTime < time.Now().Unix()
	}
	processInfoStr := string(instanceModel.StatusRecentProcessedEventInfo.JSON)
	internalInstance.StatusProcessedInfo = &processInfoStr
	internalInstance.CreateTime = timestamppb.New(instanceModel.CreationTimestamp)
	internalInstance.OrgId = instanceModel.OrganizationOwner
	clusters, err := l.GetClusters(context.Background(), instanceModel.ID)
	if err != nil {
		return nil, err
	}
	if clusters != nil {
		instance.ClusterCount = uint32(len(*clusters))
		connectedCount := 0
		for _, cluster := range *clusters {
			if cluster.HealthStatus.Code != healthv1.StatusCode_STATUS_CODE_UNKNOWN && cluster.HealthStatus.Code != healthv1.StatusCode_STATUS_CODE_UNSPECIFIED {
				connectedCount++
			}
		}
		internalInstance.ConnectedClusters = uint32(connectedCount)
	}

	return internalInstance, nil
}

func (l *InstanceLister) ProcessInstances(instancesModel []*instances.ArgoCDInstance, orgMap map[string]*OrganizationInfo) ([]*aimsv1.InternalInstance, error) {
	var instances []*aimsv1.InternalInstance
	for _, instanceModel := range instancesModel {
		internalInstance, err := l.ProcessInstance(instanceModel, orgMap)
		if err != nil {
			return nil, err
		}
		instances = append(instances, internalInstance)
	}
	return instances, nil
}
