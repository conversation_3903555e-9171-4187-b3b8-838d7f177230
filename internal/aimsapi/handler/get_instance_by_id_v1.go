package handler

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/models/models"
)

type GetInstanceByIdHandlerFunc func(ctx context.Context, req *aimsv1.GetInstanceByIdRequest) (*aimsv1.GetInstanceByIdResponse, error)

func (l *InstanceLister) GetInstanceByIdV1() GetInstanceByIdHandlerFunc {
	return func(ctx context.Context, req *aimsv1.GetInstanceByIdRequest) (*aimsv1.GetInstanceByIdResponse, error) {
		id := req.GetInstanceId()
		if id == "" {
			return nil, fmt.Errorf("missing instance id")
		}

		var instanceModel instances.ArgoCDInstance
		if err := models.ArgoCDInstances(qm.Where("id = ?", id)).Bind(ctx, l.db, &instanceModel); err != nil {
			return nil, err
		}

		org, err := models.Organizations(qm.Where("id = ?", instanceModel.OrganizationOwner)).One(ctx, l.db)
		if err != nil {
			return nil, err
		}

		instanceConfig, err := models.ArgoCDInstanceConfigs(qm.Where("instance_id = ?", instanceModel.ID)).One(ctx, l.db)
		if err != nil {
			return nil, err
		}

		instanceModel.ArgoCDInstanceConfig = *instanceConfig

		status, err := org.GetOrgStatus()
		if err != nil {
			return nil, err
		}
		orgMap := map[string]*OrganizationInfo{
			org.ID: {
				organization: org,
				expiryTime:   status.ExpiryTime,
			},
		}

		instance, err := l.ProcessInstance(&instanceModel, orgMap)
		if err != nil {
			return nil, err
		}

		workspace, err := models.Workspaces(
			qm.Select("id", "name"),
			qm.Where("id = ?", instanceModel.WorkspaceID),
		).One(ctx, l.db)

		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return nil, err
		}

		var apiWorkspace *aimsv1.Workspace
		if workspace != nil {
			apiWorkspace = &aimsv1.Workspace{
				Id:   workspace.ID,
				Name: workspace.Name,
			}
		}
		instance.Workspace = apiWorkspace

		return &aimsv1.GetInstanceByIdResponse{
			Instance: instance,
		}, nil
	}
}
