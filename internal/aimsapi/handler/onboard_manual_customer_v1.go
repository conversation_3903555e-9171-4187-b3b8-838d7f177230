package handler

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"

	"github.com/stripe/stripe-go/v74"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	aimsutil "github.com/akuityio/akuity-platform/internal/aimsapi/util"
	"github.com/akuityio/akuity-platform/internal/config"
	aimsservice "github.com/akuityio/akuity-platform/internal/services/aims"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
	"github.com/akuityio/akuity-platform/pkg/billing"
	stripepkg "github.com/akuityio/akuity-platform/pkg/billing/stripe"
)

type OnboardManualCustomerV1HandlerFunc func(ctx context.Context, req *aimsv1.OnboardManualCustomerRequest) (*aimsv1.OnboardManualCustomerResponse, error)

func OnboardManualCustomerV1(db *sql.DB, billingProviders billing.Providers, auditSvc aimsservice.AimsAuditService) OnboardManualCustomerV1HandlerFunc {
	return func(ctx context.Context, req *aimsv1.OnboardManualCustomerRequest) (*aimsv1.OnboardManualCustomerResponse, error) {
		customer := req.GetCustomer()
		if customer == nil || customer.OrganizationId == "" {
			return nil, status.Error(codes.InvalidArgument, "customer and organization ID are required")
		}

		audit := req.GetAudit()
		if err := aimsutil.ValidateAuditPayload(audit); err != nil {
			return nil, err
		}

		stripeCfg, err := config.NewStripeConfig()
		if err != nil {
			return nil, err
		}

		stripe.Key = stripeCfg.Key

		txCtx, cancel := context.WithCancel(ctx)
		defer cancel()

		txDB, txBeginner := database.WithTxBeginner(db)
		tx, err := txBeginner.Begin(txCtx)
		if err != nil {
			return nil, err
		}
		repoSet := client.NewRepoSet(txDB)

		// Check if association already exists
		if err := checkIfOrgExists(txCtx, customer, repoSet, getFiltersForCustomer(customer)); err != nil {
			return nil, err
		}

		if err := commitCustomer(txCtx, tx, customer, repoSet, stripeCfg.Key); err != nil {
			return nil, err
		}

		cus, err := json.Marshal(customer)
		if err != nil {
			return nil, err
		}

		return &aimsv1.OnboardManualCustomerResponse{}, auditSvc.AuditCustomerOnboarding(ctx, customer.OrganizationId, audit, cus)
	}
}

func commitCustomer(ctx context.Context, tx database.Commiter, customer *aimsv1.Customer, repoSet client.RepoSet, stripeKey string) error {
	provider := stripepkg.NewStripeProvider(stripeKey, repoSet)
	stripeData, err := provider.CreateOrUpdateManualCustomer(customer.StripeId, customer.OrganizationId, customer.BillingEmail, customer.BillingName)
	if err != nil {
		return fmt.Errorf("failed to create or update manual customer: %w", err)
	}

	customer.StripeId = stripeData.Id
	billingData, err := newBillingDataFromCustomerRequest(customer)
	if err != nil {
		return err
	}

	if err := repoSet.Billing().Create(ctx, billingData); err != nil {
		return err
	}

	if err := tx.Commit(); err != nil {
		return err
	}

	return nil
}

func newBillingDataFromCustomerRequest(customer *aimsv1.Customer) (*models.Billing, error) {
	billingData := &models.Billing{
		CustomerID:       customer.StripeId,
		OrganizationID:   customer.OrganizationId,
		BillingAuthority: string(billing.StripeBillingProvider),
	}

	if customer.BillingEmail != nil && *customer.BillingEmail != "" {
		billingData.BillingEmail = null.StringFrom(*customer.BillingEmail)
	}

	if customer.BillingName != nil && *customer.BillingName != "" {
		if err := billingData.SetMetadata(models.BillingMetadata{
			Name: *customer.BillingName,
		}); err != nil {
			return nil, err
		}
	}

	return billingData, nil
}

func getFiltersForCustomer(customer *aimsv1.Customer) []qm.QueryMod {
	var filters []qm.QueryMod
	if customer.StripeId != "" {
		filters = append(filters, qm.Or("customer_id = ?", customer.StripeId))
	}
	if customer.OrganizationId != "" {
		filters = append(filters, qm.Or("organization_id = ?", customer.OrganizationId))
	}
	return filters
}

func checkIfOrgExists(ctx context.Context, customer *aimsv1.Customer, repoSet client.RepoSet, filters []qm.QueryMod) error {
	b, err := repoSet.Billing().Filter(filters...).One(ctx)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	} else if b != nil {
		idMsg := ""
		if b.CustomerID == customer.StripeId {
			idMsg = fmt.Sprintf("customer ID %s", customer.StripeId)
		} else if b.OrganizationID == customer.OrganizationId {
			idMsg = fmt.Sprintf("organization ID %s", customer.OrganizationId)
		}
		return fmt.Errorf("billing entry already exists for %s", idMsg)
	}
	return nil
}
