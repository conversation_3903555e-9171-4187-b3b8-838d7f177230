package handler

import (
	"context"
	"database/sql"
	"errors"
	"time"

	"github.com/volatiletech/null/v8"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	aimsutil "github.com/akuityio/akuity-platform/internal/aimsapi/util"
	aimsservice "github.com/akuityio/akuity-platform/internal/services/aims"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

type DeleteUnpaidInstanceHandlerFunc func(ctx context.Context, req *aimsv1.DeleteUnpaidInstanceRequest) (*aimsv1.DeleteUnpaidInstanceResponse, error)

func DeleteUnpaidInstanceV1(db *sql.DB, aimsAuditSvc aimsservice.AimsAuditService) DeleteUnpaidInstanceHandlerFunc {
	return func(ctx context.Context, req *aimsv1.DeleteUnpaidInstanceRequest) (*aimsv1.DeleteUnpaidInstanceResponse, error) {
		audit := req.GetAudit()

		if err := aimsutil.ValidateAuditPayload(audit); err != nil {
			return nil, err
		}

		repoSet := client.NewRepoSet(db)
		instance, err := repoSet.ArgoCDInstances().GetByID(ctx, req.GetInstanceId())
		if err != nil {
			return nil, err
		}
		owner := instance.OrganizationOwner
		org, err := repoSet.Organizations().GetByID(ctx, owner)
		if err != nil {
			return nil, err
		}
		status, err := org.GetOrgStatus()
		if err != nil {
			return nil, err
		}
		if status == nil {
			return nil, errors.New("organization status is nil")
		}

		if !status.Trial {
			return nil, errors.New("organization is a paid customer")
		}

		instance.DeletionTimestamp = null.TimeFrom(time.Now())
		newCtx := aimsAuditSvc.SetActor(ctx, audit)

		if err := repoSet.ArgoCDInstances().Update(newCtx, instance, models.ArgoCDInstanceColumns.DeletionTimestamp); err != nil {
			return nil, err
		}
		return &aimsv1.DeleteUnpaidInstanceResponse{}, nil
	}
}
