package addon

import (
	"fmt"
	"strings"

	"github.com/spf13/cobra"

	"github.com/akuityio/akuity-platform/internal/akuitycli/option"
	"github.com/akuityio/akuity-platform/internal/cli/display"
	gwoption "github.com/akuityio/akuity-platform/pkg/api/gateway/option"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

type SyncFlags struct {
	OrgKey                      option.OrganizationKeyFlag
	InstanceKey                 option.ArgoCDInstanceKeyFlag
	ClusterSelectorNameFilters  option.Option[string]
	ClusterSelectorLabelFilters option.Option[string]
	Revision                    string
	Prune                       bool
	SyncOpsFlags                SyncOptsFlags
}

func NewSyncCommand(opt *option.CLI) (*cobra.Command, error) {
	cfg, err := opt.ConfigClient.Load()
	if err != nil {
		return nil, fmt.Errorf("load config: %w", err)
	}

	flags := SyncFlags{
		OrgKey:                      option.NewOrganizationKeyFlag(cfg.Defaults.OrganizationID, cfg.Defaults.OrganizationName),
		ClusterSelectorNameFilters:  option.NewStringOption(""),
		ClusterSelectorLabelFilters: option.NewStringOption(""),
		SyncOpsFlags:                NewSyncOptsFlags(),
	}
	cmd := &cobra.Command{
		Use:   "sync",
		Args:  cobra.ExactArgs(1),
		Short: "Sync a addon",
		Example: `akuity argocd addon sync \
  --organization-name=<name> \
  --instance-name=<name> \
  <addon-id>`,
		RunE: func(cmd *cobra.Command, args []string) error {
			ctx := cmd.Context()
			gwc := gwoption.NewClient(opt.ServerURL, opt.InsecureSkipTLSVerify)
			orgc := organizationv1.NewOrganizationServiceGatewayClient(gwc)
			org, err := flags.OrgKey.Get(ctx, orgc)
			if err != nil {
				return fmt.Errorf("get organization: %w", err)
			}

			argoc := argocdv1.NewArgoCDServiceGatewayClient(gwc)
			instance, err := flags.InstanceKey.GetInstance(ctx, argoc, org.GetId())
			if err != nil {
				return fmt.Errorf("get instance: %w", err)
			}

			addonID := strings.TrimSpace(args[0])

			addonRes, err := argoc.GetInstanceAddon(ctx, &argocdv1.GetInstanceAddonRequest{
				OrganizationId: org.GetId(),
				InstanceId:     instance.Id,
				WorkspaceId:    instance.WorkspaceId,
				Id:             addonID,
			})
			if err != nil {
				return err
			}

			so := argocdv1.StatusOperation{
				ClusterAddonStatusOperation: &argocdv1.ClusterAddonStatusOperation{
					Revision:    flags.Revision,
					Prune:       flags.Prune,
					SyncOptions: flags.SyncOpsFlags.toSlice(),
				},
			}

			clusterSelector := addonRes.GetAddon().GetSpec().GetClusterSelector()
			if clusterSelector == nil {
				clusterSelector = &argocdv1.ClusterSelector{}
			}
			if params, ok := flags.ClusterSelectorNameFilters.Get(); ok {
				nameFilters, err := parseNameSelector(*params)
				if err != nil {
					return err
				}
				clusterSelector.NameFilters = nameFilters
			}
			if params, ok := flags.ClusterSelectorLabelFilters.Get(); ok {
				labelFilters, err := parseLabelSelector(*params)
				if err != nil {
					return err
				}
				clusterSelector.LabelFilters = labelFilters
			}

			if clusterSelector.NameFilters != nil || clusterSelector.LabelFilters != nil {
				so.ClusterSelector = clusterSelector
			}
			addonRes.GetAddon().StatusOperation = &so

			res, err := argoc.UpdateInstanceAddon(ctx, &argocdv1.UpdateInstanceAddonRequest{
				Id:             addonID,
				OrganizationId: org.GetId(),
				WorkspaceId:    instance.WorkspaceId,
				InstanceId:     instance.Id,
				Addon:          addonRes.GetAddon(),
			})
			if err != nil {
				return fmt.Errorf("update addon: %w", err)
			}

			switch opt.OutputFormat {
			case display.OutputFormatJSON, display.OutputFormatYAML:
				opt.Renderer.Output(newAddonStatsResult(res.GetAddon()))
			default:
				opt.Renderer.Infof("Addon synced.")
			}
			return nil
		},
	}
	option.OutputFormat(&opt.OutputFormat)(cmd.Flags())
	option.OrganizationKey(&flags.OrgKey)(cmd.Flags())
	option.ArgoCDInstanceKey(&flags.InstanceKey)(cmd.Flags())

	cmd.Flags().BoolVar(&flags.Prune, "prune", false, "Allow deleting unexpected resources")
	cmd.Flags().StringVarP(&flags.Revision, "revision", "r", "HEAD", "Sync to a specific revision")

	flags.SyncOpsFlags.addSyncOpsFlags(cmd.Flags())

	option.AddonNameFilter(flags.ClusterSelectorNameFilters)(cmd.Flags())
	option.AddonLabelFilter(flags.ClusterSelectorLabelFilters)(cmd.Flags())

	return cmd, nil
}
