package addon

import (
	"fmt"
	"strconv"

	"github.com/spf13/pflag"

	"github.com/akuityio/akuity-platform/internal/akuitycli/option"
	"github.com/akuityio/akuity-platform/internal/cli/display"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

type AddonResult struct {
	v *argocdv1.Addon
}

func newAddonResult(o *argocdv1.Addon) display.Result {
	return &AddonResult{
		v: o,
	}
}

func (o *AddonResult) TableHeader() []string {
	return []string{
		"ID",
		"NAME",
		"ENABLED",
		"STATUS",
	}
}

func (o *AddonResult) ToTableRow() []string {
	return []string{
		o.v.GetId(),
		o.v.GetSpec().GetName(),
		strconv.FormatBool(o.v.GetSpec().GetEnabled()),
		o.v.GetStatus().GetReconciliationStatus().GetCode().String(),
	}
}

func (o *AddonResult) Data() interface{} {
	return o.v
}

type AddonStatsResult struct {
	v *argocdv1.Addon
}

func newAddonStatsResult(o *argocdv1.Addon) display.Result {
	return &AddonStatsResult{
		v: o,
	}
}

func (o *AddonStatsResult) TableHeader() []string {
	return []string{
		"ID",
		"NAME",
		"ENABLED",
		"STATUS",
	}
}

func (o *AddonStatsResult) ToTableRow() []string {
	return []string{
		o.v.GetId(),
		o.v.GetSpec().GetName(),
		strconv.FormatBool(o.v.GetSpec().GetEnabled()),
		o.v.GetStatus().GetReconciliationStatus().GetCode().String(),
	}
}

func (o *AddonStatsResult) Data() interface{} {
	return o.v
}

type SyncOptsFlags struct {
	SyncOptValidate                 option.Option[bool]
	SyncOptPruneLast                option.Option[bool]
	SyncOptRespectIgnoreDifferences option.Option[bool]
	SyncOptServerSideApply          option.Option[bool]
	SyncOptApplyOutOfSyncOnly       option.Option[bool]
	SyncOptCreateNamespace          option.Option[bool]
	SyncOptReplace                  option.Option[bool]
	SyncOptPrunePropagationPolicy   option.Option[string]
}

func NewSyncOptsFlags() SyncOptsFlags {
	return SyncOptsFlags{
		SyncOptValidate:                 option.NewBoolOption(true),
		SyncOptPruneLast:                option.NewBoolOption(false),
		SyncOptRespectIgnoreDifferences: option.NewBoolOption(false),
		SyncOptServerSideApply:          option.NewBoolOption(false),
		SyncOptApplyOutOfSyncOnly:       option.NewBoolOption(false),
		SyncOptCreateNamespace:          option.NewBoolOption(false),
		SyncOptReplace:                  option.NewBoolOption(false),
		SyncOptPrunePropagationPolicy:   option.NewStringOption("foreground"),
	}
}

func (flags SyncOptsFlags) toSlice() []string {
	syncOptionsSlice := []string{}
	m := flags.toMap()
	for k, v := range m {
		syncOptionsSlice = append(syncOptionsSlice, fmt.Sprintf("%s=%s", k, v))
	}
	return syncOptionsSlice
}

func (flags SyncOptsFlags) toMap() map[string]string {
	syncOptionsMap := map[string]string{}
	if v, ok := flags.SyncOptValidate.Get(); ok {
		syncOptionsMap["Validate"] = strconv.FormatBool(*v)
	}
	if v, ok := flags.SyncOptPruneLast.Get(); ok {
		syncOptionsMap["PruneLast"] = strconv.FormatBool(*v)
	}
	if v, ok := flags.SyncOptRespectIgnoreDifferences.Get(); ok {
		syncOptionsMap["RespectIgnoreDifferences"] = strconv.FormatBool(*v)
	}
	if v, ok := flags.SyncOptServerSideApply.Get(); ok {
		syncOptionsMap["ServerSideApply"] = strconv.FormatBool(*v)
	}
	if v, ok := flags.SyncOptApplyOutOfSyncOnly.Get(); ok {
		syncOptionsMap["ApplyOutOfSyncOnly"] = strconv.FormatBool(*v)
	}
	if v, ok := flags.SyncOptCreateNamespace.Get(); ok {
		syncOptionsMap["CreateNamespace"] = strconv.FormatBool(*v)
	}
	if v, ok := flags.SyncOptReplace.Get(); ok {
		syncOptionsMap["Replace"] = strconv.FormatBool(*v)
	}
	if v, ok := flags.SyncOptPrunePropagationPolicy.Get(); ok {
		syncOptionsMap["PrunePropagationPolicy"] = *v
	}
	return syncOptionsMap
}

func (flags SyncOptsFlags) addSyncOpsFlags(cmdFlags *pflag.FlagSet) {
	cmdFlags.Var(flags.SyncOptApplyOutOfSyncOnly, "apply-out-of-sync-only", "Sync only out-of-sync resources")
	cmdFlags.Var(flags.SyncOptCreateNamespace, "create-namespace", "Namespace Auto-Creation ensures that namespace specified as the application destination exists in the destination cluster.")
	cmdFlags.Var(flags.SyncOptPruneLast, "prune-last", "Allow the ability for resource pruning to happen as a final, implicit wave of a sync operation")
	cmdFlags.Var(flags.SyncOptReplace, "replace", "Use a kubectl create/replace instead apply")
	cmdFlags.Var(flags.SyncOptRespectIgnoreDifferences, "respect-ignore-diff", "When syncing changes, respect fields ignored by the ignoreDifferences configuration")
	cmdFlags.Var(flags.SyncOptServerSideApply, "server-side", "Use server-side apply while syncing the application")
	cmdFlags.Var(flags.SyncOptValidate, "validate", "disables resource validation (equivalent to 'kubectl apply --validate=false')")
	cmdFlags.VarP(flags.SyncOptPrunePropagationPolicy, "propagation-policy", "p", "Specify propagation policy for deletion of application's resources. One of: foreground|background")
}
