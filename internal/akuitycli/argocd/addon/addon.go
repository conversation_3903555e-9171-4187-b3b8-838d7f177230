package addon

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/akuityio/akuity-platform/internal/akuitycli/option"
)

func NewCommand(opt *option.CLI) (*cobra.Command, error) {
	cmd := &cobra.Command{
		Use:   "addon",
		Short: "Interact with ArgoCD Addons",
		Run: func(cmd *cobra.Command, args []string) {
			cmd.HelpFunc()(cmd, args)
		},
	}

	listCmd, err := NewListCommand(opt)
	if err != nil {
		return nil, fmt.Errorf("new list command: %w", err)
	}
	cmd.AddCommand(listCmd)

	updateCmd, err := NewUpdateCommand(opt)
	if err != nil {
		return nil, fmt.Errorf("new update command: %w", err)
	}
	cmd.AddCommand(updateCmd)

	syncCmd, err := NewSyncCommand(opt)
	if err != nil {
		return nil, fmt.Errorf("new sync command: %w", err)
	}
	cmd.AddCommand(syncCmd)

	return cmd, nil
}
