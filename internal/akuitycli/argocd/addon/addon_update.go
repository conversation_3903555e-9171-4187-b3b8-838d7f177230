package addon

import (
	"errors"
	"fmt"
	"strings"

	"github.com/aws/smithy-go/ptr"
	"github.com/spf13/cobra"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/selection"

	"github.com/akuityio/akuity-platform/internal/akuitycli/option"
	"github.com/akuityio/akuity-platform/internal/cli/display"
	gwoption "github.com/akuityio/akuity-platform/pkg/api/gateway/option"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

type UpdateFlags struct {
	OrgKey                      option.OrganizationKeyFlag
	InstanceKey                 option.ArgoCDInstanceKeyFlag
	Enable                      bool
	Disable                     bool
	ClusterSelectorNameFilters  option.Option[string]
	ClusterSelectorLabelFilters option.Option[string]

	NameTemplate      option.Option[string]
	NamespaceTemplate option.Option[string]
	ProjectTemplate   option.Option[string]

	OnConflictAction option.AddonOnConflictAction
	NonCascade       option.Option[bool]

	AutoSync       option.Option[bool]
	AutoHeal       option.Option[bool]
	PruneResources option.Option[bool]

	SyncOpsFlags SyncOptsFlags
}

func NewUpdateCommand(opt *option.CLI) (*cobra.Command, error) {
	cfg, err := opt.ConfigClient.Load()
	if err != nil {
		return nil, fmt.Errorf("load config: %w", err)
	}

	flags := UpdateFlags{
		OrgKey: option.NewOrganizationKeyFlag(cfg.Defaults.OrganizationID, cfg.Defaults.OrganizationName),

		ClusterSelectorNameFilters:  option.NewStringOption(""),
		ClusterSelectorLabelFilters: option.NewStringOption(""),

		NameTemplate:      option.NewStringOption(""),
		NamespaceTemplate: option.NewStringOption(""),
		ProjectTemplate:   option.NewStringOption(""),

		NonCascade: option.NewBoolOption(false),

		AutoSync:       option.NewBoolOption(false),
		AutoHeal:       option.NewBoolOption(false),
		PruneResources: option.NewBoolOption(false),

		SyncOpsFlags: NewSyncOptsFlags(),
	}
	cmd := &cobra.Command{
		Use:   "update",
		Args:  cobra.ExactArgs(1),
		Short: "Update a addon",
		Example: `akuity argocd addon update \
  --organization-name=<name> \
  --instance-name=<name> \
  <addon-id>`,
		RunE: func(cmd *cobra.Command, args []string) error {
			ctx := cmd.Context()
			gwc := gwoption.NewClient(opt.ServerURL, opt.InsecureSkipTLSVerify)
			orgc := organizationv1.NewOrganizationServiceGatewayClient(gwc)
			org, err := flags.OrgKey.Get(ctx, orgc)
			if err != nil {
				return fmt.Errorf("get organization: %w", err)
			}

			argoc := argocdv1.NewArgoCDServiceGatewayClient(gwc)
			instance, err := flags.InstanceKey.GetInstance(ctx, argoc, org.GetId())
			if err != nil {
				return fmt.Errorf("get instance: %w", err)
			}

			addonID := strings.TrimSpace(args[0])

			addonRes, err := argoc.GetInstanceAddon(ctx, &argocdv1.GetInstanceAddonRequest{
				OrganizationId: org.GetId(),
				InstanceId:     instance.Id,
				WorkspaceId:    instance.WorkspaceId,
				Id:             addonID,
			})
			if err != nil {
				return err
			}

			if flags.Enable && flags.Disable {
				return errors.New("both --enable and --disable flags set")
			}
			if flags.Enable {
				addonRes.GetAddon().GetSpec().Enabled = true
			}
			if flags.Disable {
				addonRes.GetAddon().GetSpec().Enabled = false
			}

			clusterSelector := addonRes.GetAddon().GetSpec().GetClusterSelector()
			if clusterSelector == nil {
				clusterSelector = &argocdv1.ClusterSelector{}
			}
			if params, ok := flags.ClusterSelectorNameFilters.Get(); ok {
				nameFilters, err := parseNameSelector(*params)
				if err != nil {
					return err
				}
				clusterSelector.NameFilters = nameFilters
			}
			if params, ok := flags.ClusterSelectorLabelFilters.Get(); ok {
				labelFilters, err := parseLabelSelector(*params)
				if err != nil {
					return err
				}
				clusterSelector.LabelFilters = labelFilters
			}

			if clusterSelector.NameFilters != nil || clusterSelector.LabelFilters != nil {
				addonRes.GetAddon().GetSpec().ClusterSelector = clusterSelector
			}
			// preserve origin status operation
			addonRes.GetAddon().StatusOperation = nil

			appTemplate := addonRes.GetAddon().Spec.GetAppTemplate()
			if appTemplate == nil {
				appTemplate = &argocdv1.AppTemplate{}
			}
			if template, ok := flags.NameTemplate.Get(); ok {
				appTemplate.NameTemplate = *template
			}
			if template, ok := flags.NamespaceTemplate.Get(); ok {
				appTemplate.NamespaceTemplate = *template
			}
			if template, ok := flags.ProjectTemplate.Get(); ok {
				appTemplate.ProjectTemplate = *template
			}

			if v, ok := flags.NonCascade.Get(); ok {
				appTemplate.DeletionOptions = &argocdv1.AppDeletionOptions{
					NonCascade: *v,
				}
			}

			if flags.OnConflictAction != "" {
				onConflicAction, err := mapOnConflictAction(flags.OnConflictAction)
				if err != nil {
					return err
				}
				appTemplate.CreationOptions = &argocdv1.AppCreationOptions{
					OnConflict: onConflicAction,
				}
			}
			syncOptions := appTemplate.GetSyncOptions()
			if syncOptions == nil {
				syncOptions = &argocdv1.AppSyncOptions{}
			}

			if v, ok := flags.AutoSync.Get(); ok {
				syncOptions.AutoSync = *v
			}
			if v, ok := flags.AutoHeal.Get(); ok {
				syncOptions.AutoHeal = *v
			}
			if v, ok := flags.PruneResources.Get(); ok {
				syncOptions.PruneResources = *v
			}

			syncOptionsMap := map[string]string{}
			for _, o := range syncOptions.SyncOptionsList {
				l := strings.Split(o, "=")
				if len(l) != 2 {
					return fmt.Errorf("unexpected sync option: %s, should be in format key=value", o)
				}
				syncOptionsMap[l[0]] = l[1]
			}

			syncOptsFlagsMap := flags.SyncOpsFlags.toMap()
			for k, v := range syncOptsFlagsMap {
				syncOptionsMap[k] = v
			}

			newSyncOptionList := []string{}
			for k, v := range syncOptionsMap {
				newSyncOptionList = append(newSyncOptionList, fmt.Sprintf("%s=%s", k, v))
			}
			syncOptions.SyncOptionsList = newSyncOptionList

			appTemplate.SyncOptions = syncOptions
			addonRes.GetAddon().Spec.AppTemplate = appTemplate

			res, err := argoc.UpdateInstanceAddon(ctx, &argocdv1.UpdateInstanceAddonRequest{
				Id:             addonID,
				OrganizationId: org.GetId(),
				WorkspaceId:    instance.WorkspaceId,
				InstanceId:     instance.Id,
				Addon:          addonRes.GetAddon(),
			})
			if err != nil {
				return fmt.Errorf("update addon: %w", err)
			}

			switch opt.OutputFormat {
			case display.OutputFormatJSON, display.OutputFormatYAML:
				opt.Renderer.Output(newAddonStatsResult(res.GetAddon()))
			default:
				opt.Renderer.Infof("Addon updated.")
			}
			return nil
		},
	}
	option.OutputFormat(&opt.OutputFormat)(cmd.Flags())
	option.OrganizationKey(&flags.OrgKey)(cmd.Flags())
	option.ArgoCDInstanceKey(&flags.InstanceKey)(cmd.Flags())
	cmd.Flags().BoolVar(&flags.Enable, "enable", false, "Enable addon")
	cmd.Flags().BoolVar(&flags.Disable, "disable", false, "Disable addon")
	option.AddonNameFilter(flags.ClusterSelectorNameFilters)(cmd.Flags())
	option.AddonLabelFilter(flags.ClusterSelectorLabelFilters)(cmd.Flags())
	cmd.Flags().Var(flags.NameTemplate, "name-template", "Name tempate")
	cmd.Flags().Var(flags.NamespaceTemplate, "namespace-template", "Namespace tempate")
	cmd.Flags().Var(flags.ProjectTemplate, "project-template", "Project tempate")
	option.AddonOnConflictActionFlag(&flags.OnConflictAction, option.AddonOnConflictActionOverwrite)(cmd.Flags())
	cmd.Flags().Var(flags.NonCascade, "non-cascade", "Perform a non cascaded deletion of all application resources")

	cmd.Flags().Var(flags.AutoSync, "auto-sync", "Auto sync policy")
	cmd.Flags().Var(flags.AutoHeal, "auto-heal", "Set self healing when sync is automated")
	cmd.Flags().Var(flags.PruneResources, "prune-resources", "Set automatic pruning when sync is automated")

	flags.SyncOpsFlags.addSyncOpsFlags(cmd.Flags())

	return cmd, nil
}

func mapOnConflictAction(action option.AddonOnConflictAction) (argocdv1.OnConflictAction, error) {
	switch action {
	case option.AddonOnConflictActionOverwrite:
		return argocdv1.OnConflictAction_ON_CONFLICT_ACTION_OVERWRITE, nil
	case option.AddonOnConflictActionSkip:
		return argocdv1.OnConflictAction_ON_CONFLICT_ACTION_SKIP, nil
	default:
		return argocdv1.OnConflictAction_ON_CONFLICT_ACTION_UNSPECIFIED, fmt.Errorf("invalid on conflict action: %v", action)
	}
}

func parseLabelSelector(param string) ([]*argocdv1.Selector, error) {
	selectors := []*argocdv1.Selector{}
	selector, err := labels.Parse(param)
	if err != nil {
		return selectors, err
	}
	rr, ok := selector.Requirements()
	if !ok {
		return selectors, fmt.Errorf("cluster selector label filter doesn't contain selectable condition: %s", selector)
	}
	for _, r := range rr {
		switch r.Operator() {
		case selection.NotEquals, selection.NotIn:
			s := argocdv1.Selector{
				Key:              ptr.String(r.Key()),
				SelectorOperator: argocdv1.SelectorOperator_SELECTOR_OPERATOR_NOT_IN,
				Values:           r.Values().List(),
			}
			selectors = append(selectors, &s)
		case selection.In, selection.Equals, selection.DoubleEquals:
			s := argocdv1.Selector{
				Key:              ptr.String(r.Key()),
				SelectorOperator: argocdv1.SelectorOperator_SELECTOR_OPERATOR_IN,
				Values:           r.Values().List(),
			}
			selectors = append(selectors, &s)
		}
	}
	return selectors, nil
}

func parseNameSelector(param string) ([]*argocdv1.Selector, error) {
	selectors := []*argocdv1.Selector{}
	selector, err := labels.Parse(param)
	if err != nil {
		return selectors, err
	}
	rr, ok := selector.Requirements()
	if !ok {
		return selectors, fmt.Errorf("cluster selector name filter doesn't contain selectable condition: %s", selector)
	}
	for _, r := range rr {
		switch r.Operator() {
		case selection.DoesNotExist, selection.NotIn:
			s := argocdv1.Selector{
				SelectorOperator: argocdv1.SelectorOperator_SELECTOR_OPERATOR_NOT_IN,
				Values:           []string{r.Key()},
			}
			selectors = append(selectors, &s)
		case selection.In, selection.Exists:
			s := argocdv1.Selector{
				SelectorOperator: argocdv1.SelectorOperator_SELECTOR_OPERATOR_IN,
				Values:           []string{r.Key()},
			}
			selectors = append(selectors, &s)
		}
	}
	return selectors, nil
}
