package cluster

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/akuityio/akuity-platform/internal/akuitycli/option"
	gwoption "github.com/akuityio/akuity-platform/pkg/api/gateway/option"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

type UpgradeFlags struct {
	OrgKey        option.OrganizationKeyFlag
	InstanceKey   option.ArgoCDInstanceKeyFlag
	TargetVersion string
	ClusterNames  []string
}

func NewUpgradeCommand(opt *option.CLI) (*cobra.Command, error) {
	cfg, err := opt.ConfigClient.Load()
	if err != nil {
		return nil, fmt.Errorf("load config: %w", err)
	}

	flags := UpgradeFlags{
		OrgKey: option.NewOrganizationKeyFlag(cfg.Defaults.OrganizationID, cfg.Defaults.OrganizationName),
	}
	cmd := &cobra.Command{
		Use:   "upgrade",
		Args:  cobra.ExactArgs(0),
		Short: "Upgrade a batch of clusters to a particular version",
		Example: `akuity argocd cluster upgrade \
  --organization-name=<name> \
  --instance-name=<name> \
  --version=<version> \
  --clusters=<cluster1>,<cluster2>,..`,
		RunE: func(cmd *cobra.Command, args []string) error {
			ctx := cmd.Context()
			gwc := gwoption.NewClient(opt.ServerURL, opt.InsecureSkipTLSVerify)
			orgc := organizationv1.NewOrganizationServiceGatewayClient(gwc)
			org, err := flags.OrgKey.Get(ctx, orgc)
			if err != nil {
				return fmt.Errorf("get organization: %w", err)
			}

			argoc := argocdv1.NewArgoCDServiceGatewayClient(gwc)
			instanceID, err := flags.InstanceKey.GetInstanceID(ctx, argoc, org.GetId())
			if err != nil {
				return fmt.Errorf("get instance: %w", err)
			}

			if _, err := argoc.UpdateInstanceClustersAgentVersion(ctx, &argocdv1.UpdateInstanceClustersAgentVersionRequest{
				OrganizationId: org.GetId(),
				InstanceId:     instanceID,
				ClusterNames:   flags.ClusterNames,
				NewVersion:     flags.TargetVersion,
			}); err != nil {
				return fmt.Errorf("upgrade cluster: %w", err)
			}

			opt.Renderer.Infof("Clusters upgraded.")

			return nil
		},
	}
	option.OutputFormat(&opt.OutputFormat)(cmd.Flags())
	option.OrganizationKey(&flags.OrgKey)(cmd.Flags())
	option.ArgoCDInstanceKey(&flags.InstanceKey)(cmd.Flags())
	option.ArgoCDClusterTargetVersion(&flags.TargetVersion)(cmd.Flags())
	option.ArgoCDClusterNames(&flags.ClusterNames)(cmd.Flags())
	return cmd, nil
}
