package cluster

import (
	"fmt"
	"strings"

	"github.com/spf13/cobra"

	"github.com/akuityio/akuity-platform/internal/akuitycli/option"
	gwoption "github.com/akuityio/akuity-platform/pkg/api/gateway/option"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

type DeleteFlags struct {
	OrgKey      option.OrganizationKeyFlag
	InstanceKey option.ArgoCDInstanceKeyFlag
}

func NewDeleteCommand(opt *option.CLI) (*cobra.Command, error) {
	cfg, err := opt.ConfigClient.Load()
	if err != nil {
		return nil, fmt.Errorf("load config: %w", err)
	}

	flags := DeleteFlags{
		OrgKey: option.NewOrganizationKeyFlag(cfg.Defaults.OrganizationID, cfg.Defaults.OrganizationName),
	}
	cmd := &cobra.Command{
		Use:   "delete",
		Args:  cobra.ExactArgs(1),
		Short: "Delete a cluster",
		Example: `akuity argocd cluster delete \
  --organization-name=<name> \
  --instance-name=<name> \
  <cluster-name or cluster-id>`,
		RunE: func(cmd *cobra.Command, args []string) error {
			ctx := cmd.Context()
			gwc := gwoption.NewClient(opt.ServerURL, opt.InsecureSkipTLSVerify)
			orgc := organizationv1.NewOrganizationServiceGatewayClient(gwc)
			org, err := flags.OrgKey.Get(ctx, orgc)
			if err != nil {
				return fmt.Errorf("get organization: %w", err)
			}

			argoc := argocdv1.NewArgoCDServiceGatewayClient(gwc)
			instanceID, err := flags.InstanceKey.GetInstanceID(ctx, argoc, org.GetId())
			if err != nil {
				return fmt.Errorf("get instance: %w", err)
			}

			clusterKey := strings.TrimSpace(args[0])
			cluster, err := inferOrganizationInstanceCluster(
				ctx, argoc, org.GetId(), instanceID, clusterKey)
			if err != nil {
				return fmt.Errorf("get cluster: %w", err)
			}

			if _, err := argoc.DeleteInstanceCluster(ctx, &argocdv1.DeleteInstanceClusterRequest{
				OrganizationId: org.GetId(),
				InstanceId:     instanceID,
				Id:             cluster.GetId(),
			}); err != nil {
				return fmt.Errorf("delete cluster: %w", err)
			}

			opt.Renderer.Infof("Cluster deleted.")
			return nil
		},
	}
	option.OrganizationKey(&flags.OrgKey)(cmd.Flags())
	option.ArgoCDInstanceKey(&flags.InstanceKey)(cmd.Flags())
	return cmd, nil
}
