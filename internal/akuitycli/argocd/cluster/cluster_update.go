package cluster

import (
	"fmt"
	"strings"

	"github.com/spf13/cobra"

	"github.com/akuityio/akuity-platform/internal/akuitycli/option"
	"github.com/akuityio/akuity-platform/internal/akuitycli/utils"
	"github.com/akuityio/akuity-platform/internal/cli/display"
	"github.com/akuityio/akuity-platform/internal/utils/types"
	gwoption "github.com/akuityio/akuity-platform/pkg/api/gateway/option"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

type UpdateFlags struct {
	OrgKey                      option.OrganizationKeyFlag
	InstanceKey                 option.ArgoCDInstanceKeyFlag
	Description                 option.Option[string]
	DisableAutoUpgrade          option.Option[bool]
	AkuityCustomImageRegistry   option.Option[string]
	ArgoprojCustomImageRegistry option.Option[string]
	KustomizationPath           option.Option[string]
	AgentSize                   option.ArgoCDClusterAgentSize
	TargetVersion               string
	Labels                      []string
	Annotations                 []string
	Force                       bool
	Project                     option.Option[string]
}

func NewUpdateCommand(opt *option.CLI) (*cobra.Command, error) {
	cfg, err := opt.ConfigClient.Load()
	if err != nil {
		return nil, fmt.Errorf("load config: %w", err)
	}

	flags := UpdateFlags{
		OrgKey:                      option.NewOrganizationKeyFlag(cfg.Defaults.OrganizationID, cfg.Defaults.OrganizationName),
		Description:                 option.NewStringOption(""),
		DisableAutoUpgrade:          option.NewBoolOption(option.DefaultArgoCDDisableAutoUpdate),
		AkuityCustomImageRegistry:   option.NewStringOption(option.DefaultAkuityCustomImageRegistry),
		ArgoprojCustomImageRegistry: option.NewStringOption(option.DefaultArgoprojCustomImageRegistry),
		KustomizationPath:           option.NewStringOption(""),
		Project:                     option.NewStringOption(""),
	}
	cmd := &cobra.Command{
		Use:   "update",
		Args:  cobra.ExactArgs(1),
		Short: "Update a cluster",
		Example: `akuity argocd cluster update \
  --organization-name=<name> \
  --instance-name=<name> \
  <cluster-name>`,
		RunE: func(cmd *cobra.Command, args []string) error {
			ctx := cmd.Context()
			gwc := gwoption.NewClient(opt.ServerURL, opt.InsecureSkipTLSVerify)
			orgc := organizationv1.NewOrganizationServiceGatewayClient(gwc)
			org, err := flags.OrgKey.Get(ctx, orgc)
			if err != nil {
				return fmt.Errorf("get organization: %w", err)
			}

			argoc := argocdv1.NewArgoCDServiceGatewayClient(gwc)
			instanceID, err := flags.InstanceKey.GetInstanceID(ctx, argoc, org.GetId())
			if err != nil {
				return fmt.Errorf("get instance: %w", err)
			}

			clusterKey := strings.TrimSpace(args[0])
			cluster, err := inferOrganizationInstanceCluster(ctx, argoc, org.GetId(), instanceID, clusterKey)
			if err != nil {
				return fmt.Errorf("get cluster: %w", err)
			}

			description := cluster.GetDescription()
			if v, ok := flags.Description.Get(); ok {
				description = *v
			}

			data := cluster.GetData()
			if v, ok := flags.DisableAutoUpgrade.Get(); ok {
				data.AutoUpgradeDisabled = v
			}

			if flags.KustomizationPath.IsSet() || flags.AkuityCustomImageRegistry.IsSet() || flags.ArgoprojCustomImageRegistry.IsSet() {
				kustomizationStruct, err := utils.KustomizationFromFlags(flags.KustomizationPath, flags.AkuityCustomImageRegistry, flags.ArgoprojCustomImageRegistry, nil)
				if err != nil {
					return fmt.Errorf("failed to create kustomization: %w", err)
				}
				data.Kustomization = kustomizationStruct
			}

			if flags.AgentSize != option.ArgoCDClusterAgentSizeUndefined {
				data.Size, err = mapAgentSize(flags.AgentSize)
				if err != nil {
					return err
				}
			}
			if flags.Labels != nil {
				data.Labels = types.MapKVSliceToMap(flags.Labels)
			}
			if flags.Annotations != nil {
				data.Annotations = types.MapKVSliceToMap(flags.Annotations)
			}

			if flags.TargetVersion != "" {
				data.TargetVersion = flags.TargetVersion
			}

			if val, ok := flags.Project.Get(); ok {
				data.Project = *val
			}

			res, err := argoc.UpdateInstanceCluster(ctx, &argocdv1.UpdateInstanceClusterRequest{
				OrganizationId: org.GetId(),
				InstanceId:     instanceID,
				Id:             cluster.GetId(),
				Description:    description,
				Force:          flags.Force,
				Data:           data,
			})
			if err != nil {
				return fmt.Errorf("update cluster: %w", err)
			}

			switch opt.OutputFormat {
			case display.OutputFormatJSON, display.OutputFormatYAML:
				opt.Renderer.Output(utils.NewResult(res.GetCluster()))
			default:
				opt.Renderer.Infof("Cluster updated.")
			}
			return nil
		},
	}
	option.OutputFormat(&opt.OutputFormat)(cmd.Flags())
	option.OrganizationKey(&flags.OrgKey)(cmd.Flags())
	option.ArgoCDInstanceKey(&flags.InstanceKey)(cmd.Flags())
	option.ArgoCDDisableAutoUpdate(flags.DisableAutoUpgrade)(cmd.Flags())
	option.AkuityCustomImageRegistry(flags.AkuityCustomImageRegistry)(cmd.Flags())
	option.ArgoprojCustomImageRegistry(flags.ArgoprojCustomImageRegistry)(cmd.Flags())
	option.ArgoCDClusterAgentSizeFlag(&flags.AgentSize, option.ArgoCDClusterAgentSizeUndefined)(cmd.Flags())
	option.ArgoCDClusterLabels(&flags.Labels)(cmd.Flags())
	option.ArgoCDClusterAnnotations(&flags.Annotations)(cmd.Flags())
	option.ArgoCDClusterTargetVersion(&flags.TargetVersion)(cmd.Flags())
	option.ArgoCDClusterKustomizationPath(flags.KustomizationPath)(cmd.Flags())
	option.ArgoCDClusterForce(&flags.Force)(cmd.Flags())
	option.ArgoCDClusterProject(flags.Project)(cmd.Flags())
	return cmd, nil
}
