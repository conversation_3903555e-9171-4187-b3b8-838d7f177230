package cluster

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/akuityio/akuity-platform/internal/akuitycli/option"
	"github.com/akuityio/akuity-platform/internal/akuitycli/utils"
	"github.com/akuityio/akuity-platform/internal/cli/display"
	"github.com/akuityio/akuity-platform/internal/utils/types"
	gwoption "github.com/akuityio/akuity-platform/pkg/api/gateway/option"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

type ListFlags struct {
	OrgKey      option.OrganizationKeyFlag
	InstanceKey option.ArgoCDInstanceKeyFlag
	Labels      []string
}

func NewListCommand(opt *option.CLI) (*cobra.Command, error) {
	cfg, err := opt.ConfigClient.Load()
	if err != nil {
		return nil, fmt.Errorf("load config: %w", err)
	}

	flags := ListFlags{
		OrgKey: option.NewOrganizationKeyFlag(cfg.Defaults.OrganizationID, cfg.Defaults.OrganizationName),
	}
	cmd := &cobra.Command{
		Use:   "list",
		Args:  cobra.ExactArgs(0),
		Short: "List instance clusters",
		Example: `akuity argocd cluster list \
  --organization-name=<name> \
  --instance-name=<name> \
  --label='key1=val1'`,
		RunE: func(cmd *cobra.Command, args []string) error {
			ctx := cmd.Context()
			gwc := gwoption.NewClient(opt.ServerURL, opt.InsecureSkipTLSVerify)
			orgc := organizationv1.NewOrganizationServiceGatewayClient(gwc)
			org, err := flags.OrgKey.Get(ctx, orgc)
			if err != nil {
				return fmt.Errorf("get organization: %w", err)
			}

			argoc := argocdv1.NewArgoCDServiceGatewayClient(gwc)
			instanceID, err := flags.InstanceKey.GetInstanceID(ctx, argoc, org.GetId())
			if err != nil {
				return fmt.Errorf("get instance: %w", err)
			}
			res, err := argoc.ListInstanceClusters(ctx, &argocdv1.ListInstanceClustersRequest{
				OrganizationId: org.GetId(),
				InstanceId:     instanceID,
				Filter:         &argocdv1.ClusterFilter{Labels: types.MapKVSliceToMap(flags.Labels)},
			})
			if err != nil {
				return fmt.Errorf("list clusters: %w", err)
			}

			output, _ := types.MapSlice(res.GetClusters(), func(in *argocdv1.Cluster) (display.Result, error) {
				return utils.NewResult(in), nil
			})
			opt.Renderer.ListOutput(output...)
			return nil
		},
	}
	option.OutputFormat(&opt.OutputFormat)(cmd.Flags())
	option.OrganizationKey(&flags.OrgKey)(cmd.Flags())
	option.ArgoCDInstanceKey(&flags.InstanceKey)(cmd.Flags())
	option.ArgoCDClusterLabels(&flags.Labels)(cmd.Flags())
	return cmd, nil
}
