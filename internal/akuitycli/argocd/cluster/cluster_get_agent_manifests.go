package cluster

import (
	"fmt"
	"strings"
	"time"

	"github.com/spf13/cobra"

	"github.com/akuityio/akuity-platform/internal/akuitycli/option"
	"github.com/akuityio/akuity-platform/internal/cli"
	gwoption "github.com/akuityio/akuity-platform/pkg/api/gateway/option"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
	idv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/id/v1"
)

type GetAgentManifestsFlags struct {
	OrgKey              option.OrganizationKeyFlag
	InstanceKey         option.ArgoCDInstanceKeyFlag
	UseID               bool
	OfflineInstallation bool
}

func NewGetAgentManifestsCommand(opt *option.CLI) (*cobra.Command, error) {
	cfg, err := opt.ConfigClient.Load()
	if err != nil {
		return nil, fmt.Errorf("load config: %w", err)
	}

	flags := GetAgentManifestsFlags{
		OrgKey: option.NewOrganizationKeyFlag(cfg.Defaults.OrganizationID, cfg.Defaults.OrganizationName),
	}
	cmd := &cobra.Command{
		Use:   "get-agent-manifests",
		Args:  cobra.ExactArgs(1),
		Short: "Get cluster agent manifests",
		Example: `akuity argocd cluster get-agent-manifests \
  --organization-name=<name> \
  --instance-name=<name> \
  <cluster-name or cluster-id>`,
		RunE: func(cmd *cobra.Command, args []string) error {
			ctx := cmd.Context()
			clusterKey := strings.TrimSpace(args[0])
			gwc := gwoption.NewClient(opt.ServerURL, opt.InsecureSkipTLSVerify)
			orgc := organizationv1.NewOrganizationServiceGatewayClient(gwc)
			org, err := flags.OrgKey.Get(ctx, orgc)
			cli.CheckErr(err)

			argoc := argocdv1.NewArgoCDServiceGatewayClient(gwc)
			instanceID, err := flags.InstanceKey.GetInstanceID(ctx, argoc, org.GetId())
			cli.CheckErr(err)

			idType := idv1.Type_NAME
			if flags.UseID {
				idType = idv1.Type_ID
			}

			getClusterReq := &argocdv1.GetInstanceClusterRequest{
				OrganizationId: org.GetId(),
				InstanceId:     instanceID,
				IdType:         idType,
				Id:             clusterKey,
			}
			cluster, err := waitForOrganizationClusterStatus(
				ctx, argoc, getClusterReq, isClusterReconciled, 3*time.Second, time.Minute)
			cli.CheckErr(err)

			resChan, errChan, err := argoc.GetInstanceClusterManifests(ctx, &argocdv1.GetInstanceClusterManifestsRequest{
				OrganizationId:      org.GetId(),
				InstanceId:          instanceID,
				Id:                  cluster.GetId(),
				OfflineInstallation: flags.OfflineInstallation,
			})
			cli.CheckErr(err)
			for {
				select {
				case dataChunk, ok := <-resChan:
					if !ok {
						resChan = nil
					} else {
						opt.Renderer.StringOutput(string(dataChunk.Data))
					}

				case serverErr, ok := <-errChan:
					if !ok {
						errChan = nil
					} else {
						cli.CheckErr(serverErr)
					}
				}

				if resChan == nil || errChan == nil {
					break
				}
			}
			return nil
		},
	}
	option.OrganizationKey(&flags.OrgKey)(cmd.Flags())
	option.ArgoCDInstanceKey(&flags.InstanceKey)(cmd.Flags())
	option.UseID("cluster", &flags.UseID)(cmd.Flags())
	cmd.Flags().BoolVar(&flags.OfflineInstallation, "offline-installation", false, "When set to true the agent manifests will be bundle snapshot of all Argo CD settings including applications and projects")
	return cmd, nil
}
