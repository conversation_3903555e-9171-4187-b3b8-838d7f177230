package cluster

import (
	"fmt"
	"strings"

	"github.com/spf13/cobra"

	"github.com/akuityio/akuity-platform/internal/akuitycli/option"
	"github.com/akuityio/akuity-platform/internal/akuitycli/utils"
	"github.com/akuityio/akuity-platform/internal/cli/display"
	"github.com/akuityio/akuity-platform/internal/utils/types"
	gwoption "github.com/akuityio/akuity-platform/pkg/api/gateway/option"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

type CreateFlags struct {
	OrgKey                      option.OrganizationKeyFlag
	InstanceKey                 option.ArgoCDInstanceKeyFlag
	Namespace                   string
	NamespaceScoped             bool
	DisableAutoUpgrade          option.Option[bool]
	AkuityCustomImageRegistry   option.Option[string]
	ArgoprojCustomImageRegistry option.Option[string]
	KustomizationPath           option.Option[string]
	StateReplication            option.Option[bool]
	RedisTunneling              option.Option[bool]
	AgentSize                   option.ArgoCDClusterAgentSize
	TargetVersion               string
	Upsert                      bool
	Force                       bool
	Labels                      []string
	Annotations                 []string
	Project                     option.Option[string]
}

func NewCreateCommand(opt *option.CLI) (*cobra.Command, error) {
	cfg, err := opt.ConfigClient.Load()
	if err != nil {
		return nil, fmt.Errorf("load config: %w", err)
	}

	flags := CreateFlags{
		OrgKey:                      option.NewOrganizationKeyFlag(cfg.Defaults.OrganizationID, cfg.Defaults.OrganizationName),
		DisableAutoUpgrade:          option.NewBoolOption(option.DefaultArgoCDDisableAutoUpdate),
		AkuityCustomImageRegistry:   option.NewStringOption(option.DefaultAkuityCustomImageRegistry),
		ArgoprojCustomImageRegistry: option.NewStringOption(option.DefaultArgoprojCustomImageRegistry),
		KustomizationPath:           option.NewStringOption(""),
		StateReplication:            option.NewBoolOption(false),
		RedisTunneling:              option.NewBoolOption(false),
		Project:                     option.NewStringOption(""),
	}
	cmd := &cobra.Command{
		Use:   "create",
		Args:  cobra.ExactArgs(1),
		Short: "Create a cluster",
		Example: `akuity argocd cluster create \
  --organization-name=<name> \
  --instance-name=<name> \
  <cluster-name>`,
		RunE: func(cmd *cobra.Command, args []string) error {
			ctx := cmd.Context()
			clusterName := strings.TrimSpace(args[0])

			agentSize, err := mapAgentSize(flags.AgentSize)
			if err != nil {
				return err
			}
			gwc := gwoption.NewClient(opt.ServerURL, opt.InsecureSkipTLSVerify)
			orgc := organizationv1.NewOrganizationServiceGatewayClient(gwc)
			org, err := flags.OrgKey.Get(ctx, orgc)
			if err != nil {
				return fmt.Errorf("get organization: %w", err)
			}

			argoc := argocdv1.NewArgoCDServiceGatewayClient(gwc)
			instanceID, err := flags.InstanceKey.GetInstanceID(ctx, argoc, org.GetId())
			if err != nil {
				return fmt.Errorf("get instance: %w", err)
			}

			var (
				autoUpgradeDisabled *bool
				stateReplication    *bool
				redisTunneling      *bool
			)

			// let it be nil if not set manually by user so that instance defaults can take over
			if flags.DisableAutoUpgrade.IsSet() {
				autoUpgradeDisabled, _ = flags.DisableAutoUpgrade.Get()
			}
			if flags.StateReplication.IsSet() {
				stateReplication, _ = flags.StateReplication.Get()
			}
			if flags.RedisTunneling.IsSet() {
				redisTunneling, _ = flags.RedisTunneling.Get()
			}

			kustomizationStruct, err := utils.KustomizationFromFlags(flags.KustomizationPath, flags.AkuityCustomImageRegistry, flags.ArgoprojCustomImageRegistry, nil)
			if err != nil {
				return fmt.Errorf("failed to create kustomization: %w", err)
			}
			var project string
			if val, ok := flags.Project.Get(); ok {
				project = *val
			}

			res, err := argoc.CreateInstanceCluster(ctx, &argocdv1.CreateInstanceClusterRequest{
				OrganizationId: org.GetId(),
				InstanceId:     instanceID,
				Name:           clusterName,
				Upsert:         flags.Upsert,
				Force:          flags.Force,
				Data: &argocdv1.ClusterData{
					Size:                agentSize,
					AutoUpgradeDisabled: autoUpgradeDisabled,
					Kustomization:       kustomizationStruct,
					Labels:              types.MapKVSliceToMap(flags.Labels),
					Annotations:         types.MapKVSliceToMap(flags.Annotations),
					TargetVersion:       flags.TargetVersion,
					AppReplication:      stateReplication,
					RedisTunneling:      redisTunneling,
					Namespace:           flags.Namespace,
					NamespaceScoped:     flags.NamespaceScoped,
					Project:             project,
				},
			})
			if err != nil {
				return fmt.Errorf("create cluster: %w", err)
			}

			switch opt.OutputFormat {
			case display.OutputFormatJSON, display.OutputFormatYAML:
				opt.Renderer.Output(utils.NewResult(res.GetCluster()))
			default:
				opt.Renderer.Infof("Cluster created.")
			}
			return nil
		},
	}
	option.OutputFormat(&opt.OutputFormat)(cmd.Flags())
	option.UpsertFlag(&flags.Upsert)(cmd.Flags())
	option.OrganizationKey(&flags.OrgKey)(cmd.Flags())
	option.ArgoCDInstanceKey(&flags.InstanceKey)(cmd.Flags())
	option.ArgoCDClusterNamespace(&flags.Namespace)(cmd.Flags())
	option.ArgoCDNamespaceScopedCluster(&flags.NamespaceScoped)(cmd.Flags())
	option.ArgoCDDisableAutoUpdate(flags.DisableAutoUpgrade)(cmd.Flags())
	option.AkuityCustomImageRegistry(flags.AkuityCustomImageRegistry)(cmd.Flags())
	option.ArgoprojCustomImageRegistry(flags.ArgoprojCustomImageRegistry)(cmd.Flags())
	option.ArgoCDClusterAgentSizeFlag(&flags.AgentSize, option.ArgoCDClusterAgentSizeSmall)(cmd.Flags())
	option.ArgoCDClusterLabels(&flags.Labels)(cmd.Flags())
	option.ArgoCDClusterAnnotations(&flags.Annotations)(cmd.Flags())
	option.ArgoCDClusterTargetVersion(&flags.TargetVersion)(cmd.Flags())
	option.ArgoCDClusterKustomizationPath(flags.KustomizationPath)(cmd.Flags())
	option.ArgoCDClusterStateReplication(flags.StateReplication)(cmd.Flags())
	option.ArgoCDClusterRedisTunneling(flags.RedisTunneling)(cmd.Flags())
	option.ArgoCDClusterForce(&flags.Force)(cmd.Flags())
	option.ArgoCDClusterProject(flags.Project)(cmd.Flags())
	return cmd, nil
}

func mapAgentSize(size option.ArgoCDClusterAgentSize) (argocdv1.ClusterSize, error) {
	switch size {
	case option.ArgoCDClusterAgentSizeSmall:
		return argocdv1.ClusterSize_CLUSTER_SIZE_SMALL, nil
	case option.ArgoCDClusterAgentSizeMedium:
		return argocdv1.ClusterSize_CLUSTER_SIZE_MEDIUM, nil
	case option.ArgoCDClusterAgentSizeLarge:
		return argocdv1.ClusterSize_CLUSTER_SIZE_LARGE, nil
	case option.ArgoCDClusterAgentSizeAuto:
		return argocdv1.ClusterSize_CLUSTER_SIZE_AUTO, nil
	default:
		return argocdv1.ClusterSize_CLUSTER_SIZE_UNSPECIFIED, fmt.Errorf("invalid agent size: %v", size)
	}
}
