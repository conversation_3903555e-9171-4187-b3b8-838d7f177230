package cluster

import (
	"context"
	"fmt"
	"strings"

	"github.com/spf13/cobra"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/akuitycli/option"
	"github.com/akuityio/akuity-platform/internal/akuitycli/utils"
	"github.com/akuityio/akuity-platform/internal/cli/display"
	"github.com/akuityio/akuity-platform/internal/utils/types"
	gwoption "github.com/akuityio/akuity-platform/pkg/api/gateway/option"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
	idv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/id/v1"
)

type GetFlags struct {
	OrgKey      option.OrganizationKeyFlag
	InstanceKey option.ArgoCDInstanceKeyFlag
}

func NewGetCommand(opt *option.CLI) (*cobra.Command, error) {
	cfg, err := opt.ConfigClient.Load()
	if err != nil {
		return nil, fmt.Errorf("load config: %w", err)
	}

	flags := GetFlags{
		OrgKey: option.NewOrganizationKeyFlag(cfg.Defaults.OrganizationID, cfg.Defaults.OrganizationName),
	}
	cmd := &cobra.Command{
		Use:   "get",
		Args:  cobra.MaximumNArgs(1),
		Short: "Get instance clusters",
		Example: `akuity argocd cluster get \
  --organization-name=<name> \
  --instance-name=<name> \
  <(optional) cluster-name or cluster-id>`,
		RunE: func(cmd *cobra.Command, args []string) error {
			ctx := cmd.Context()
			gwc := gwoption.NewClient(opt.ServerURL, opt.InsecureSkipTLSVerify)
			orgc := organizationv1.NewOrganizationServiceGatewayClient(gwc)
			org, err := flags.OrgKey.Get(ctx, orgc)
			if err != nil {
				return fmt.Errorf("get organization: %w", err)
			}

			argoc := argocdv1.NewArgoCDServiceGatewayClient(gwc)
			instanceID, err := flags.InstanceKey.GetInstanceID(ctx, argoc, org.GetId())
			if err != nil {
				return fmt.Errorf("get instance: %w", err)
			}

			if len(args) == 0 {
				res, err := argoc.ListInstanceClusters(ctx, &argocdv1.ListInstanceClustersRequest{
					OrganizationId: org.GetId(),
					InstanceId:     instanceID,
				})
				if err != nil {
					return fmt.Errorf("list clusters: %w", err)
				}

				output, _ := types.MapSlice(res.GetClusters(), func(in *argocdv1.Cluster) (display.Result, error) {
					return utils.NewResult(in), nil
				})
				opt.Renderer.ListOutput(output...)
				return nil
			}

			clusterKey := strings.TrimSpace(args[0])
			cluster, err := inferOrganizationInstanceCluster(ctx, argoc, org.GetId(), instanceID, clusterKey)
			if err != nil {
				return fmt.Errorf("get cluster: %w", err)
			}
			opt.Renderer.Output(utils.NewResult(cluster))
			return nil
		},
	}
	option.OutputFormat(&opt.OutputFormat)(cmd.Flags())
	option.OrganizationKey(&flags.OrgKey)(cmd.Flags())
	option.ArgoCDInstanceKey(&flags.InstanceKey)(cmd.Flags())
	return cmd, nil
}

func inferOrganizationInstanceCluster(ctx context.Context, argoc argocdv1.ArgoCDServiceGatewayClient, orgID, instanceID, key string) (*argocdv1.Cluster, error) {
	count := 0
	var permissionError error
	for _, idType := range []idv1.Type{
		idv1.Type_ID,
		idv1.Type_NAME,
	} {
		res, err := argoc.GetInstanceCluster(ctx, &argocdv1.GetInstanceClusterRequest{
			OrganizationId: orgID,
			InstanceId:     instanceID,
			IdType:         idType,
			Id:             key,
		})
		if err != nil {
			if status.Code(err) == codes.PermissionDenied {
				permissionError = err
				count++
				continue
			}
			if status.Code(err) == codes.NotFound {
				continue
			}
			return nil, err
		}
		return res.GetCluster(), nil
	}
	// because we don't know if user provide to us ID or a name, we calling both in a loop
	// and only if both responses are permission denided, we return it, because we will usually got it for name or ID.
	if count == 2 {
		return nil, fmt.Errorf("%w. If you are using custom role please make sure 'get' action is permitted", permissionError)
	}
	return nil, fmt.Errorf("cluster %q not found", key)
}
