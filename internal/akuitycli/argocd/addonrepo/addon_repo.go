package addonrepo

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/akuityio/akuity-platform/internal/akuitycli/option"
)

func NewCommand(opt *option.CLI) (*cobra.Command, error) {
	cmd := &cobra.Command{
		Use:     "addon-repo",
		Aliases: []string{"addonrepo"},
		Short:   "Interact with ArgoCD Addon repos",
		Run: func(cmd *cobra.Command, args []string) {
			cmd.HelpFunc()(cmd, args)
		},
	}

	createCmd, err := NewCreateCommand(opt)
	if err != nil {
		return nil, fmt.Errorf("new create command: %w", err)
	}
	cmd.AddCommand(createCmd)

	deleteCmd, err := NewDeleteCommand(opt)
	if err != nil {
		return nil, fmt.Errorf("new delete command: %w", err)
	}
	cmd.AddCommand(deleteCmd)

	listCmd, err := NewListCommand(opt)
	if err != nil {
		return nil, fmt.Errorf("new list command: %w", err)
	}
	cmd.AddCommand(listCmd)

	return cmd, nil
}
