package addonrepo

import (
	"fmt"
	"strings"

	"github.com/spf13/cobra"

	"github.com/akuityio/akuity-platform/internal/akuitycli/option"
	gwoption "github.com/akuityio/akuity-platform/pkg/api/gateway/option"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

type DeleteFlags struct {
	OrgKey      option.OrganizationKeyFlag
	InstanceKey option.ArgoCDInstanceKeyFlag
}

func NewDeleteCommand(opt *option.CLI) (*cobra.Command, error) {
	cfg, err := opt.ConfigClient.Load()
	if err != nil {
		return nil, fmt.Errorf("load config: %w", err)
	}

	flags := DeleteFlags{
		OrgKey: option.NewOrganizationKeyFlag(cfg.Defaults.OrganizationID, cfg.Defaults.OrganizationName),
	}
	cmd := &cobra.Command{
		Use:   "delete",
		Args:  cobra.ExactArgs(1),
		Short: "Delete a addon repo",
		Example: `akuity argocd addon-repo delete \
  --organization-name=<name> \
  --instance-name=<name> \
  <addon-repo-id>`,
		RunE: func(cmd *cobra.Command, args []string) error {
			ctx := cmd.Context()
			gwc := gwoption.NewClient(opt.ServerURL, opt.InsecureSkipTLSVerify)
			orgc := organizationv1.NewOrganizationServiceGatewayClient(gwc)
			org, err := flags.OrgKey.Get(ctx, orgc)
			if err != nil {
				return fmt.Errorf("get organization: %w", err)
			}

			argoc := argocdv1.NewArgoCDServiceGatewayClient(gwc)
			instance, err := flags.InstanceKey.GetInstance(ctx, argoc, org.GetId())
			if err != nil {
				return fmt.Errorf("get instance: %w", err)
			}

			addonRepoID := strings.TrimSpace(args[0])

			if _, err := argoc.DeleteInstanceAddonRepo(ctx, &argocdv1.DeleteInstanceAddonRepoRequest{
				OrganizationId: org.GetId(),
				InstanceId:     instance.Id,
				WorkspaceId:    instance.WorkspaceId,
				Id:             addonRepoID,
			}); err != nil {
				return fmt.Errorf("delete addon repo: %w", err)
			}

			opt.Renderer.Infof("Addon repo deleted.")
			return nil
		},
	}
	option.OrganizationKey(&flags.OrgKey)(cmd.Flags())
	option.ArgoCDInstanceKey(&flags.InstanceKey)(cmd.Flags())
	return cmd, nil
}
