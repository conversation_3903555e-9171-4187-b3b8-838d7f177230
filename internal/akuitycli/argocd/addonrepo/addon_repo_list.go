package addonrepo

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/akuityio/akuity-platform/internal/akuitycli/option"
	"github.com/akuityio/akuity-platform/internal/cli/display"
	"github.com/akuityio/akuity-platform/internal/utils/types"
	gwoption "github.com/akuityio/akuity-platform/pkg/api/gateway/option"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

type ListFlags struct {
	OrgKey      option.OrganizationKeyFlag
	InstanceKey option.ArgoCDInstanceKeyFlag
}

func NewListCommand(opt *option.CLI) (*cobra.Command, error) {
	cfg, err := opt.ConfigClient.Load()
	if err != nil {
		return nil, fmt.Errorf("load config: %w", err)
	}

	flags := ListFlags{
		OrgKey: option.NewOrganizationKeyFlag(cfg.Defaults.OrganizationID, cfg.Defaults.OrganizationName),
	}
	cmd := &cobra.Command{
		Use:   "list",
		Args:  cobra.ExactArgs(0),
		Short: "List instance addon repos",
		Example: `akuity argocd addon-repo list \
  --organization-name=<name> \
  --instance-name=<name>`,
		RunE: func(cmd *cobra.Command, args []string) error {
			ctx := cmd.Context()
			gwc := gwoption.NewClient(opt.ServerURL, opt.InsecureSkipTLSVerify)
			orgc := organizationv1.NewOrganizationServiceGatewayClient(gwc)
			org, err := flags.OrgKey.Get(ctx, orgc)
			if err != nil {
				return fmt.Errorf("get organization: %w", err)
			}

			argoc := argocdv1.NewArgoCDServiceGatewayClient(gwc)
			instance, err := flags.InstanceKey.GetInstance(ctx, argoc, org.GetId())
			if err != nil {
				return fmt.Errorf("get instance: %w", err)
			}
			limit := int32(1000)
			res, err := argoc.ListInstanceAddonRepos(ctx, &argocdv1.ListInstanceAddonReposRequest{
				OrganizationId: org.GetId(),
				InstanceId:     instance.Id,
				WorkspaceId:    instance.WorkspaceId,
				Limit:          &limit,
			})
			if err != nil {
				return fmt.Errorf("list addon repos: %w", err)
			}

			output, _ := types.MapSlice(res.GetAddonRepos(), func(in *argocdv1.AddonRepo) (display.Result, error) {
				return newAddonRepoResult(in), nil
			})
			opt.Renderer.ListOutput(output...)
			return nil
		},
	}
	option.OutputFormat(&opt.OutputFormat)(cmd.Flags())
	option.OrganizationKey(&flags.OrgKey)(cmd.Flags())
	option.ArgoCDInstanceKey(&flags.InstanceKey)(cmd.Flags())
	return cmd, nil
}
