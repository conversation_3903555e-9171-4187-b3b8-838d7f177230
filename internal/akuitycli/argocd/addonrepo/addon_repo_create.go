package addonrepo

import (
	"fmt"
	"strings"

	"github.com/spf13/cobra"

	"github.com/akuityio/akuity-platform/internal/akuitycli/option"
	"github.com/akuityio/akuity-platform/internal/cli/display"
	gwoption "github.com/akuityio/akuity-platform/pkg/api/gateway/option"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

type CreateFlags struct {
	OrgKey      option.OrganizationKeyFlag
	InstanceKey option.ArgoCDInstanceKeyFlag
	Revision    string
}

func NewCreateCommand(opt *option.CLI) (*cobra.Command, error) {
	cfg, err := opt.ConfigClient.Load()
	if err != nil {
		return nil, fmt.Errorf("load config: %w", err)
	}

	flags := CreateFlags{
		OrgKey: option.NewOrganizationKeyFlag(cfg.Defaults.OrganizationID, cfg.Defaults.OrganizationName),
	}
	cmd := &cobra.Command{
		Use:   "create",
		Args:  cobra.ExactArgs(1),
		Short: "Create a addon repo",
		Example: `akuity argocd addon-repo create \
  --organization-name=<name> \
  --instance-name=<name> \
  --revision=<revision> \
  <url>`,
		RunE: func(cmd *cobra.Command, args []string) error {
			ctx := cmd.Context()
			repoUrl := strings.TrimSpace(args[0])

			gwc := gwoption.NewClient(opt.ServerURL, opt.InsecureSkipTLSVerify)
			orgc := organizationv1.NewOrganizationServiceGatewayClient(gwc)
			org, err := flags.OrgKey.Get(ctx, orgc)
			if err != nil {
				return fmt.Errorf("get organization: %w", err)
			}

			argoc := argocdv1.NewArgoCDServiceGatewayClient(gwc)
			instance, err := flags.InstanceKey.GetInstance(ctx, argoc, org.GetId())
			if err != nil {
				return fmt.Errorf("get instance: %w", err)
			}

			res, err := argoc.CreateInstanceAddonRepo(ctx, &argocdv1.CreateInstanceAddonRepoRequest{
				OrganizationId: org.GetId(),
				InstanceId:     instance.Id,
				WorkspaceId:    instance.WorkspaceId,
				Spec: &argocdv1.RepoSpec{
					RepoUrl:  repoUrl,
					Revision: flags.Revision,
				},
			})
			if err != nil {
				return fmt.Errorf("create addon repo: %w", err)
			}

			switch opt.OutputFormat {
			case display.OutputFormatJSON, display.OutputFormatYAML:
				opt.Renderer.Output(newAddonRepoResult(res.GetAddonRepo()))
			default:
				opt.Renderer.Infof("Addon Repo created.")
			}
			return nil
		},
	}
	option.OutputFormat(&opt.OutputFormat)(cmd.Flags())
	option.OrganizationKey(&flags.OrgKey)(cmd.Flags())
	option.ArgoCDInstanceKey(&flags.InstanceKey)(cmd.Flags())
	cmd.Flags().StringVarP(&flags.Revision, "revision", "r", "HEAD", "Target revision")
	return cmd, nil
}
