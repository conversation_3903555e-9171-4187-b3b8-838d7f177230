package addonrepo

import (
	"github.com/akuityio/akuity-platform/internal/cli/display"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

type AddonRepoResult struct {
	v *argocdv1.AddonRepo
}

func newAddonRepoResult(o *argocdv1.AddonRepo) display.Result {
	return &AddonRepoResult{
		v: o,
	}
}

func (o *AddonRepoResult) TableHeader() []string {
	return []string{
		"ID",
		"URL",
		"REVISION",
		"STATUS",
	}
}

func (o *AddonRepoResult) ToTableRow() []string {
	return []string{
		o.v.GetId(),
		o.v.Spec.GetRepoUrl(),
		o.v.Spec.GetRevision(),
		o.v.GetStatus().GetReconciliationStatus().GetCode().String(),
	}
}

func (o *AddonRepoResult) Data() interface{} {
	return o.v
}
