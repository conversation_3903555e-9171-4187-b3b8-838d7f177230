package agentapi

import (
	"context"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	agentv1 "github.com/akuityio/akuity-platform/agent/pkg/api/gen/agent/v1"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

func (s *AgentService) UpdateClusterKubernetesInfo(
	ctx context.Context, req *agentv1.UpdateClusterKubernetesInfoRequest,
) (*agentv1.UpdateClusterKubernetesInfoResponse, error) {
	rs := client.NewRepoSet(s.db)
	cluster, err := rs.ArgoCDClusters(qm.Load(models.ArgoCDClusterRels.Instance)).GetByID(ctx, req.GetClusterId())
	if err != nil {
		return nil, status.Error(codes.NotFound, err.Error())
	}
	if err = s.checkDashboardEnabled(ctx, cluster, rs); err != nil {
		return nil, err
	}
	resSvc := k8sresource.NewServiceWithOptions(rs, s.db, cluster.R.Instance.OrganizationOwner, k8sresource.WithLogger(logging.Extract(ctx)))
	err = resSvc.UpdateClusterStatusK8sInfo(ctx, cluster, req.GetClusterK8SInfo())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "error updating cluster info: %v", err)
	}

	return &agentv1.UpdateClusterKubernetesInfoResponse{}, nil
}
