package agentapi

import (
	"context"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	agentv1 "github.com/akuityio/akuity-platform/agent/pkg/api/gen/agent/v1"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

func (s *AgentService) UpdateKubernetesResources(
	ctx context.Context,
	req *agentv1.UpdateKubernetesResourcesRequest,
) (*agentv1.UpdateKubernetesResourcesResponse, error) {
	txDB, txBeginner := database.WithTxBeginner(s.db)
	rs := client.NewRepoSet(txDB)

	cluster, err := rs.ArgoCDClusters(qm.Load(models.ArgoCDClusterRels.Instance)).GetByID(ctx, req.GetClusterId())
	if err != nil {
		return nil, status.Error(codes.NotFound, err.Error())
	}
	if err = s.checkDashboardEnabled(ctx, cluster, rs); err != nil {
		return nil, err
	}

	resSvc := k8sresource.NewServiceWithOptions(rs, txDB, cluster.R.Instance.OrganizationOwner, k8sresource.WithLogger(logging.Extract(ctx)))

	tx, err := txBeginner.Begin(ctx)
	if err != nil {
		return nil, err
	}
	defer func() {
		_ = tx.Rollback()
	}()
	var toAdd, toUpdate, toDelete []*agentv1.Resource
	for _, evt := range req.GetEvents() {
		// Skip the event if the resource UID is empty, as it is not a valid resource.
		// Ideally the agent should not send such events, but we should handle it gracefully in case if
		// error events keep coming from the agent.
		if evt.GetResource().GetUid() == "" {
			continue
		}
		switch evt.GetType() {
		case agentv1.ResourceEventType_RESOURCE_EVENT_TYPE_ADDED:
			toAdd = append(toAdd, evt.GetResource())
		case agentv1.ResourceEventType_RESOURCE_EVENT_TYPE_MODIFIED:
			toUpdate = append(toUpdate, evt.GetResource())
		case agentv1.ResourceEventType_RESOURCE_EVENT_TYPE_DELETED:
			toDelete = append(toDelete, evt.GetResource())
		}
	}

	if len(toAdd) == 0 && len(toUpdate) == 0 && len(toDelete) == 0 && !req.OverrideAll {
		return nil, status.Error(codes.InvalidArgument, "no events provided")
	}

	// OverrideAll means to override all existing resources, and the resource events should all be added events
	if req.OverrideAll {
		if len(toUpdate) > 0 || len(toDelete) > 0 {
			return nil, status.Error(codes.InvalidArgument, "when override all, there should be no update or delete events")
		}
		if err = resSvc.ArgoCDClusterK8sObjects(models.ArgoCDClusterK8SObjectWhere.ClusterID.EQ(cluster.GetID())).DeleteAll(ctx); err != nil {
			return nil, err
		}
	}

	toUpsert := append(toAdd, toUpdate...)
	if err := resSvc.UpsertResources(ctx, tx, cluster.InstanceID, req.GetClusterId(), toUpsert...); err != nil {
		return nil, err
	}
	toDeleteIDs := make([]string, 0, len(toDelete))
	for _, res := range toDelete {
		toDeleteIDs = append(toDeleteIDs, res.GetUid())
	}
	if err := resSvc.DeleteResources(ctx, cluster.InstanceID, req.GetClusterId(), toDeleteIDs...); err != nil {
		return nil, err
	}

	if err := resSvc.UpdateClusterRefreshTime(ctx, cluster); err != nil {
		return nil, err
	}

	if err := tx.Commit(); err != nil {
		return nil, err
	}
	return &agentv1.UpdateKubernetesResourcesResponse{}, nil
}
