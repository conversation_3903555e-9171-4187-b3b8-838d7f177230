package agentapi

import (
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"

	commonagent "github.com/akuityio/agent/pkg/common"
	agentv1 "github.com/akuityio/akuity-platform/agent/pkg/api/gen/agent/v1"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/gateway"
	modelClient "github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

const (
	MaxLogSize = 1024 * 1024 * 50  // 50MB
	DefaultTTL = 60 * 60 * 24 * 7  // 7 days
	MaxTTL     = 60 * 60 * 24 * 60 // 60 days
)

func (s *AgentService) CollectLogs(stream agentv1.AgentService_CollectLogsServer) error {
	ctx := stream.Context()

	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return status.Error(codes.Internal, "can't extract metadata from incoming context")
	}

	clusterID, ok := gateway.GetClusterID(md)
	if !ok {
		return status.Error(codes.InvalidArgument, "can't extract cluster id from metadata")
	}

	agentType, ok := gateway.GetAgentType(md)
	if !ok {
		return status.Error(codes.InvalidArgument, "can't extract agent type from metadata")
	}

	// Get the first request to extract cluster info
	req, err := stream.Recv()
	if err != nil {
		return status.Errorf(codes.Internal, "failed to receive initial request: %v", err)
	}

	db, txBeginer := database.WithTxBeginner(s.db)
	tx, err := txBeginer.Begin(ctx)
	if err != nil {
		return err
	}
	defer func() {
		_ = tx.Rollback()
	}()

	repost := modelClient.NewRepoSet(db)

	orgID := ""
	instanceID := ""
	logMetadata := models.LogMetadata{}
	logType := req.LogType
	switch logType {
	case agentv1.LogType_LOG_TYPE_UNSPECIFIED:
		return status.Errorf(codes.InvalidArgument, "unknown log type")
	case agentv1.LogType_LOG_TYPE_KARGO_ANALYSIS_JOB:
		if agentType != gateway.AgentTypeKargo {
			return status.Errorf(codes.InvalidArgument, "invalid agent type for log type")
		}
		agent, err := repost.KargoAgents().GetByID(ctx, clusterID)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				return status.Errorf(codes.NotFound, "related agent not found")
			}
			return status.Errorf(codes.Internal, "failed to get kargo agent: %v", err)
		}
		instance, err := repost.KargoInstances().GetByID(ctx, agent.InstanceID)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				return status.Errorf(codes.NotFound, "related instance not found")
			}
			return status.Errorf(codes.Internal, "failed to get kargo instance: %v", err)
		}
		instanceID = instance.ID
		org, err := repost.Organizations().GetByID(ctx, instance.OrganizationOwner.String)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				return status.Errorf(codes.InvalidArgument, "related org not found")
			}
			return status.Errorf(codes.Internal, "failed to get org: %v", err)
		}
		orgID = org.ID
		if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetKargoAnalysisLogs().Enabled() {
			return status.Errorf(codes.PermissionDenied, "kargo analysis logs feature is not enabled")
		}

		data, err := req.Metadata.MarshalJSON()
		if err != nil {
			return status.Errorf(codes.InvalidArgument, "failed to marshal metadata: %v", err)
		}
		reqMeta := commonagent.KargoAnalysisLogMeta{}
		if err := json.Unmarshal(data, &reqMeta); err != nil {
			return status.Errorf(codes.InvalidArgument, "failed to unmarshal metadata: %v", err)
		}
		logMetadata = models.LogMetadata{
			KargoAnalysisLog: &models.KargoAnalysisLog{
				AgentID:              clusterID,
				Shard:                agent.Name,
				KargoAnalysisLogMeta: reqMeta,
			},
		}

	default:
		return status.Errorf(codes.InvalidArgument, "unknown log type")
	}

	ttlSeconds := req.TtlSeconds
	if ttlSeconds > MaxTTL {
		ttlSeconds = MaxTTL
	} else if ttlSeconds == 0 {
		ttlSeconds = DefaultTTL
	}

	logInput := &models.Log{
		OrganizationID: orgID,
		Type:           logType.String(),
		InstanceID:     instanceID,
		TTLSeconds:     int(ttlSeconds),
	}

	if err := logInput.SetMetadata(logMetadata); err != nil {
		return status.Errorf(codes.Internal, "failed to set log metadata: %v", err)
	}

	if err := repost.Logs().Create(ctx, logInput); err != nil {
		return status.Errorf(codes.Internal, "failed to create log: %v", err)
	}

	totalLogSize := 0

	start := time.Now()
	// Process the stream of log chunks
	for {
		if totalLogSize > MaxLogSize {
			return status.Errorf(codes.InvalidArgument, "log size exceeds limit max limit of %d bytes", MaxLogSize)
		}

		if len(req.LogChunk) > 0 {
			// Dev: Trade off, multiple db updates but server does not have to hold full logs in memory
			// slower compared to holding all logs in memory and updating db once
			_, err = db.ExecContext(ctx,
				"UPDATE logs SET log_dst = log_dst || $1 WHERE id = $2",
				req.LogChunk, logInput.ID)
			if err != nil {
				return status.Errorf(codes.Internal, "failed to append log chunk: %v", err)
			}
			totalLogSize += len(req.LogChunk)
		}

		if req.Finished {
			break
		}

		req, err = stream.Recv()
		if err != nil {
			if err == io.EOF {
				return status.Errorf(codes.InvalidArgument, "log stream closed before finished signal")
			}
			return status.Errorf(codes.Internal, "failed to receive log chunk: %v", err)
		}
	}

	s.log.V(1).Info(fmt.Sprintf("took %v to get all %v bytes data", time.Since(start), totalLogSize))

	// update metadata with total log size
	logMetadata.Size = int64(totalLogSize)
	if err := logInput.SetMetadata(logMetadata); err != nil {
		return status.Errorf(codes.Internal, "failed to update log metadata: %v", err)
	}

	if err := repost.Logs().Update(ctx, logInput, "metadata"); err != nil {
		return status.Errorf(codes.Internal, "failed to update log metadata: %v", err)
	}

	if err := tx.Commit(); err != nil {
		return status.Errorf(codes.Internal, "failed to commit transaction: %v", err)
	}
	return stream.SendAndClose(&agentv1.CollectLogsResponse{})
}
