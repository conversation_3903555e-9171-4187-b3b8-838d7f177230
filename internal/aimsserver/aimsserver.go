package aimsserver

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/auth0/go-auth0/management"
	"github.com/spf13/cobra"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/akuityio/akuity-platform/internal/aimsapi"
	"github.com/akuityio/akuity-platform/internal/cli"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	httputil "github.com/akuityio/akuity-platform/internal/utils/http"
	"github.com/akuityio/akuity-platform/internal/utils/logging"
	"github.com/akuityio/akuity-platform/internal/utils/metrics"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/pkg/billing"
	"github.com/akuityio/akuity-platform/pkg/billing/stripe"
)

const (
	UI_DIR = "./aims/ui/build"
)

func NewUIRequestHandler() http.HandlerFunc {
	fs := http.FileServer(http.Dir(UI_DIR))
	return func(w http.ResponseWriter, req *http.Request) {
		path := UI_DIR + req.URL.Path
		info, err := os.Stat(path)
		if os.IsNotExist(err) || info.IsDir() {
			if w != nil {
				httputil.SetNoCacheHeaders(w, false)
				http.ServeFile(w, req, UI_DIR+"/index.html")
			}
		} else {
			fs.ServeHTTP(w, req)
		}
	}
}

func NewAimsServerCommand() *cobra.Command {
	var (
		metricsPort int
		debug       bool
	)
	cmd := &cobra.Command{
		Use: "aims-server",
		Run: func(c *cobra.Command, args []string) {
			backgroundCtx := context.Background()
			errCh := make(chan error, 1)

			cfg, err := config.NewAimsConfig()
			cli.CheckErr(err)

			portalDBPool, err := database.GetDBPool(cfg.PortalDBConnection, &cfg.DBConnection)
			if err != nil {
				cli.CheckErr(fmt.Errorf("failed to connect to portal db: %w", err))
			}

			var logOpts []logging.Option
			if debug {
				logOpts = append(logOpts, logging.WithDebug())
			}
			log, err := logging.NewLogger(logOpts...)
			cli.CheckErr(err)

			cli.CheckErr(database.InitializeDataKey(cfg.DBDataKey))

			var billingProviders map[billing.ProviderName]billing.IProvider

			stripeConfig, err := config.NewStripeConfig()
			cli.CheckErr(err)

			repoSet := client.NewRepoSet(portalDBPool.DB)

			if stripeConfig.Key != "" {
				billingProviders = map[billing.ProviderName]billing.IProvider{
					billing.StripeBillingProvider: stripe.NewStripeProvider(stripeConfig.Key, repoSet),
				}
			}

			loadingRules := clientcmd.NewDefaultClientConfigLoadingRules()
			loadingRules.DefaultClientConfig = &clientcmd.DefaultClientConfig
			overrides := clientcmd.ConfigOverrides{}

			clientConfig := clientcmd.NewInteractiveDeferredLoadingClientConfig(loadingRules, &overrides, os.Stdin)
			restConfig, err := clientConfig.ClientConfig()
			cli.CheckErr(err)

			kubeclient, err := kubernetes.NewForConfig(restConfig)
			cli.CheckErr(err)

			var auth0Management *management.Management
			auth0Management, err = management.New(cfg.Auth0.Portal().Domain, management.WithClientCredentialsAndAudience(backgroundCtx, cfg.Auth0.Portal().ClientID, cfg.Auth0.Portal().ClientSecret, cfg.Auth0.Portal().ManagementAPIAudience))
			cli.CheckErr(err)

			as := aimsapi.NewAimsApi(portalDBPool.DB, "tcp", fmt.Sprintf("127.0.0.1:%d", config.DefaultAIMSAPIPort), billingProviders, kubeclient, auth0Management)

			grpcAPIRequestHandler, err := as.Proxy()
			cli.CheckErr(err)

			uiRequestHandler := NewUIRequestHandler()

			srv := &http.Server{
				Addr: fmt.Sprintf("%s:%d", "0.0.0.0", config.DefaultAIMSServerPort),
				Handler: http.HandlerFunc(func(w http.ResponseWriter, req *http.Request) {
					switch {
					case strings.HasPrefix(req.URL.Path, "/api/v1"):
						grpcAPIRequestHandler(w, req)
					default:
						uiRequestHandler(w, req)
					}
				}),
				ReadHeaderTimeout: 3 * time.Second,
			}

			go func() { cli.CheckErr(metrics.NewMetricsServer(&log, "aims-server", metricsPort)()) }()

			go func() {
				if err := as.Start(backgroundCtx, cfg, &log); err != nil {
					errCh <- err
				}
			}()

			go func() {
				if err := srv.ListenAndServe(); err != nil {
					log.Error(err, "failed to start grpc api server")
				}
			}()
			err = <-errCh
			log.Error(err, "failed to start aims server")
		},
	}

	cmd.Flags().IntVar(&metricsPort, "metrics-port", config.DefaultAimsMetricsPort, "The metrics server port")
	cmd.Flags().BoolVar(&debug, "debug", false, "Enable debug logging")
	return cmd
}
