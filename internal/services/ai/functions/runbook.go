package functions

import (
	"context"
	"fmt"

	"github.com/akuityio/akuity-platform/internal/utils/ai"
	"github.com/akuityio/akuity-platform/models/models"
)

func (fc *Controller) addRunbookFunctions() error {
	if err := addFunction(fc, Function{
		Tool: ai.Tool{
			Name:        "get-runbooks-by-names",
			Description: `Retrieves runbooks by their names from the ArgoCD instance configuration.`,
		},
		DisplayName: "Get Runbooks by Names",
	}, fc.getRunbooksByNames); err != nil {
		return err
	}

	return nil
}

func (fc *Controller) getRunbooksByNames(ctx context.Context, args struct {
	Names []string `json:"names" description:"Array of runbook names to retrieve" required:"true"`
}) (string, error) {
	// Get conversation to extract instance ID
	conv := GetConversation(ctx)
	if conv == nil {
		return "", fmt.Errorf("context has no conversation")
	}
	if conv.InstanceID.IsZero() {
		return "", fmt.<PERSON>rrorf("runbooks can only be retrieved for conversations related to ArgoCD instances")
	}

	instanceID := conv.InstanceID.String

	// Validate required fields
	if len(args.Names) == 0 {
		return "", &InvalidArgsError{Message: "At least one runbook name is required"}
	}

	// Get the instance configuration directly from the repo
	instanceConfig, err := fc.RepoSet.ArgoCDInstanceConfigs().GetByID(ctx, instanceID)
	if err != nil {
		return "", fmt.Errorf("failed to get instance configuration: %w", err)
	}

	// Get current spec
	spec, err := instanceConfig.GetSpec()
	if err != nil {
		return "", fmt.Errorf("failed to get instance spec: %w", err)
	}

	// Find runbooks by names
	var foundRunbooks []models.Runbook
	nameSet := make(map[string]bool)
	for _, name := range args.Names {
		nameSet[name] = true
	}

	for _, runbook := range spec.KubeVisionConfig.AIConfig.Runbooks {
		if nameSet[runbook.Name] {
			foundRunbooks = append(foundRunbooks, runbook)
		}
	}

	if len(foundRunbooks) == 0 {
		return "No runbooks found with the specified names", nil
	}

	// Format the response
	result := fmt.Sprintf("Found %d runbook(s):\n\n", len(foundRunbooks))
	for i, runbook := range foundRunbooks {
		result += fmt.Sprintf("**Runbook %d: %s**\n", i+1, runbook.Name)
		result += fmt.Sprintf("- **Content:**\n%s\n", runbook.Content)
		if i < len(foundRunbooks)-1 {
			result += "\n---\n\n"
		}
	}

	return result, nil
}
