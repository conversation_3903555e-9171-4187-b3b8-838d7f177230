// Code generated by go generate; DO NOT EDIT.

package k8sresource

import (
	"k8s.io/apimachinery/pkg/runtime/schema"
)

var deprecatedGVKMap = map[schema.GroupVersionKind]APIVersionInfo{
	{Group: "admission.k8s.io", Version: "v1beta1", Kind: "AdmissionReview"}: {
		Group:   "admission.k8s.io",
		Version: "v1beta1",
		Kind:    "AdmissionReview",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 19,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "admission.k8s.io",
			Version: "v1",
			Kind:    "AdmissionReview",
		},
	},
	{Group: "admissionregistration.k8s.io", Version: "v1beta1", Kind: "MutatingWebhookConfiguration"}: {
		Group:   "admissionregistration.k8s.io",
		Version: "v1beta1",
		Kind:    "MutatingWebhookConfiguration",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "admissionregistration.k8s.io",
			Version: "v1",
			Kind:    "MutatingWebhookConfiguration",
		},
	},
	{Group: "admissionregistration.k8s.io", Version: "v1beta1", Kind: "MutatingWebhookConfigurationList"}: {
		Group:   "admissionregistration.k8s.io",
		Version: "v1beta1",
		Kind:    "MutatingWebhookConfigurationList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "admissionregistration.k8s.io",
			Version: "v1",
			Kind:    "MutatingWebhookConfigurationList",
		},
	},
	{Group: "admissionregistration.k8s.io", Version: "v1beta1", Kind: "ValidatingAdmissionPolicy"}: {
		Group:   "admissionregistration.k8s.io",
		Version: "v1beta1",
		Kind:    "ValidatingAdmissionPolicy",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 31,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 34,
		},
	},
	{Group: "admissionregistration.k8s.io", Version: "v1beta1", Kind: "ValidatingAdmissionPolicyBinding"}: {
		Group:   "admissionregistration.k8s.io",
		Version: "v1beta1",
		Kind:    "ValidatingAdmissionPolicyBinding",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 31,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 34,
		},
	},
	{Group: "admissionregistration.k8s.io", Version: "v1beta1", Kind: "ValidatingAdmissionPolicyBindingList"}: {
		Group:   "admissionregistration.k8s.io",
		Version: "v1beta1",
		Kind:    "ValidatingAdmissionPolicyBindingList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 31,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 34,
		},
	},
	{Group: "admissionregistration.k8s.io", Version: "v1beta1", Kind: "ValidatingAdmissionPolicyList"}: {
		Group:   "admissionregistration.k8s.io",
		Version: "v1beta1",
		Kind:    "ValidatingAdmissionPolicyList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 31,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 34,
		},
	},
	{Group: "admissionregistration.k8s.io", Version: "v1beta1", Kind: "ValidatingWebhookConfiguration"}: {
		Group:   "admissionregistration.k8s.io",
		Version: "v1beta1",
		Kind:    "ValidatingWebhookConfiguration",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "admissionregistration.k8s.io",
			Version: "v1",
			Kind:    "ValidatingWebhookConfiguration",
		},
	},
	{Group: "admissionregistration.k8s.io", Version: "v1beta1", Kind: "ValidatingWebhookConfigurationList"}: {
		Group:   "admissionregistration.k8s.io",
		Version: "v1beta1",
		Kind:    "ValidatingWebhookConfigurationList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "admissionregistration.k8s.io",
			Version: "v1",
			Kind:    "ValidatingWebhookConfigurationList",
		},
	},
	{Group: "apidiscovery.k8s.io", Version: "v2beta1", Kind: "APIGroupDiscovery"}: {
		Group:   "apidiscovery.k8s.io",
		Version: "v2beta1",
		Kind:    "APIGroupDiscovery",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 32,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 35,
		},
	},
	{Group: "apidiscovery.k8s.io", Version: "v2beta1", Kind: "APIGroupDiscoveryList"}: {
		Group:   "apidiscovery.k8s.io",
		Version: "v2beta1",
		Kind:    "APIGroupDiscoveryList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 32,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 35,
		},
	},
	{Group: "apps", Version: "v1beta1", Kind: "ControllerRevision"}: {
		Group:   "apps",
		Version: "v1beta1",
		Kind:    "ControllerRevision",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 8,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "apps",
			Version: "v1",
			Kind:    "ControllerRevision",
		},
	},
	{Group: "apps", Version: "v1beta1", Kind: "ControllerRevisionList"}: {
		Group:   "apps",
		Version: "v1beta1",
		Kind:    "ControllerRevisionList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 8,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "apps",
			Version: "v1",
			Kind:    "ControllerRevisionList",
		},
	},
	{Group: "apps", Version: "v1beta1", Kind: "Deployment"}: {
		Group:   "apps",
		Version: "v1beta1",
		Kind:    "Deployment",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 8,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "apps",
			Version: "v1",
			Kind:    "Deployment",
		},
	},
	{Group: "apps", Version: "v1beta1", Kind: "DeploymentList"}: {
		Group:   "apps",
		Version: "v1beta1",
		Kind:    "DeploymentList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 8,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "apps",
			Version: "v1",
			Kind:    "DeploymentList",
		},
	},
	{Group: "apps", Version: "v1beta1", Kind: "DeploymentRollback"}: {
		Group:   "apps",
		Version: "v1beta1",
		Kind:    "DeploymentRollback",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 8,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "apps",
			Version: "v1",
			Kind:    "DeploymentRollback",
		},
	},
	{Group: "apps", Version: "v1beta1", Kind: "Scale"}: {
		Group:   "apps",
		Version: "v1beta1",
		Kind:    "Scale",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 8,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "autoscaling",
			Version: "v1",
			Kind:    "Scale",
		},
	},
	{Group: "apps", Version: "v1beta1", Kind: "StatefulSet"}: {
		Group:   "apps",
		Version: "v1beta1",
		Kind:    "StatefulSet",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 8,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "apps",
			Version: "v1",
			Kind:    "StatefulSet",
		},
	},
	{Group: "apps", Version: "v1beta1", Kind: "StatefulSetList"}: {
		Group:   "apps",
		Version: "v1beta1",
		Kind:    "StatefulSetList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 8,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "apps",
			Version: "v1",
			Kind:    "StatefulSetList",
		},
	},
	{Group: "apps", Version: "v1beta2", Kind: "ControllerRevision"}: {
		Group:   "apps",
		Version: "v1beta2",
		Kind:    "ControllerRevision",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 9,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "apps",
			Version: "v1",
			Kind:    "ControllerRevision",
		},
	},
	{Group: "apps", Version: "v1beta2", Kind: "ControllerRevisionList"}: {
		Group:   "apps",
		Version: "v1beta2",
		Kind:    "ControllerRevisionList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 9,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "apps",
			Version: "v1",
			Kind:    "ControllerRevisionList",
		},
	},
	{Group: "apps", Version: "v1beta2", Kind: "DaemonSet"}: {
		Group:   "apps",
		Version: "v1beta2",
		Kind:    "DaemonSet",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 9,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "apps",
			Version: "v1",
			Kind:    "DaemonSet",
		},
	},
	{Group: "apps", Version: "v1beta2", Kind: "DaemonSetList"}: {
		Group:   "apps",
		Version: "v1beta2",
		Kind:    "DaemonSetList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 9,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "apps",
			Version: "v1",
			Kind:    "DaemonSetList",
		},
	},
	{Group: "apps", Version: "v1beta2", Kind: "Deployment"}: {
		Group:   "apps",
		Version: "v1beta2",
		Kind:    "Deployment",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 9,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "apps",
			Version: "v1",
			Kind:    "Deployment",
		},
	},
	{Group: "apps", Version: "v1beta2", Kind: "DeploymentList"}: {
		Group:   "apps",
		Version: "v1beta2",
		Kind:    "DeploymentList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 9,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "apps",
			Version: "v1",
			Kind:    "DeploymentList",
		},
	},
	{Group: "apps", Version: "v1beta2", Kind: "ReplicaSet"}: {
		Group:   "apps",
		Version: "v1beta2",
		Kind:    "ReplicaSet",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 9,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "apps",
			Version: "v1",
			Kind:    "ReplicaSet",
		},
	},
	{Group: "apps", Version: "v1beta2", Kind: "ReplicaSetList"}: {
		Group:   "apps",
		Version: "v1beta2",
		Kind:    "ReplicaSetList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 9,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "apps",
			Version: "v1",
			Kind:    "ReplicaSetList",
		},
	},
	{Group: "apps", Version: "v1beta2", Kind: "Scale"}: {
		Group:   "apps",
		Version: "v1beta2",
		Kind:    "Scale",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 9,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "autoscaling",
			Version: "v1",
			Kind:    "Scale",
		},
	},
	{Group: "apps", Version: "v1beta2", Kind: "StatefulSet"}: {
		Group:   "apps",
		Version: "v1beta2",
		Kind:    "StatefulSet",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 9,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "apps",
			Version: "v1",
			Kind:    "StatefulSet",
		},
	},
	{Group: "apps", Version: "v1beta2", Kind: "StatefulSetList"}: {
		Group:   "apps",
		Version: "v1beta2",
		Kind:    "StatefulSetList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 9,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "apps",
			Version: "v1",
			Kind:    "StatefulSetList",
		},
	},
	{Group: "authentication.k8s.io", Version: "v1alpha1", Kind: "SelfSubjectReview"}: {
		Group:   "authentication.k8s.io",
		Version: "v1alpha1",
		Kind:    "SelfSubjectReview",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 29,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 32,
		},
	},
	{Group: "authentication.k8s.io", Version: "v1beta1", Kind: "SelfSubjectReview"}: {
		Group:   "authentication.k8s.io",
		Version: "v1beta1",
		Kind:    "SelfSubjectReview",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 30,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 33,
		},
	},
	{Group: "authentication.k8s.io", Version: "v1beta1", Kind: "TokenReview"}: {
		Group:   "authentication.k8s.io",
		Version: "v1beta1",
		Kind:    "TokenReview",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 19,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "authentication.k8s.io",
			Version: "v1",
			Kind:    "TokenReview",
		},
	},
	{Group: "authorization.k8s.io", Version: "v1beta1", Kind: "LocalSubjectAccessReview"}: {
		Group:   "authorization.k8s.io",
		Version: "v1beta1",
		Kind:    "LocalSubjectAccessReview",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 19,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "authorization.k8s.io",
			Version: "v1",
			Kind:    "LocalSubjectAccessReview",
		},
	},
	{Group: "authorization.k8s.io", Version: "v1beta1", Kind: "SelfSubjectAccessReview"}: {
		Group:   "authorization.k8s.io",
		Version: "v1beta1",
		Kind:    "SelfSubjectAccessReview",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 19,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "authorization.k8s.io",
			Version: "v1",
			Kind:    "SelfSubjectAccessReview",
		},
	},
	{Group: "authorization.k8s.io", Version: "v1beta1", Kind: "SelfSubjectRulesReview"}: {
		Group:   "authorization.k8s.io",
		Version: "v1beta1",
		Kind:    "SelfSubjectRulesReview",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 19,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "authorization.k8s.io",
			Version: "v1",
			Kind:    "SelfSubjectRulesReview",
		},
	},
	{Group: "authorization.k8s.io", Version: "v1beta1", Kind: "SubjectAccessReview"}: {
		Group:   "authorization.k8s.io",
		Version: "v1beta1",
		Kind:    "SubjectAccessReview",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 19,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "authorization.k8s.io",
			Version: "v1",
			Kind:    "SubjectAccessReview",
		},
	},
	{Group: "autoscaling", Version: "v2beta1", Kind: "HorizontalPodAutoscaler"}: {
		Group:   "autoscaling",
		Version: "v2beta1",
		Kind:    "HorizontalPodAutoscaler",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 25,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "autoscaling",
			Version: "v2",
			Kind:    "HorizontalPodAutoscaler",
		},
	},
	{Group: "autoscaling", Version: "v2beta1", Kind: "HorizontalPodAutoscalerList"}: {
		Group:   "autoscaling",
		Version: "v2beta1",
		Kind:    "HorizontalPodAutoscalerList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 25,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "autoscaling",
			Version: "v2beta2",
			Kind:    "HorizontalPodAutoscalerList",
		},
	},
	{Group: "autoscaling", Version: "v2beta2", Kind: "HorizontalPodAutoscaler"}: {
		Group:   "autoscaling",
		Version: "v2beta2",
		Kind:    "HorizontalPodAutoscaler",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 23,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 26,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "autoscaling",
			Version: "v2",
			Kind:    "HorizontalPodAutoscaler",
		},
	},
	{Group: "autoscaling", Version: "v2beta2", Kind: "HorizontalPodAutoscalerList"}: {
		Group:   "autoscaling",
		Version: "v2beta2",
		Kind:    "HorizontalPodAutoscalerList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 25,
		},
	},
	{Group: "batch", Version: "v1beta1", Kind: "CronJob"}: {
		Group:   "batch",
		Version: "v1beta1",
		Kind:    "CronJob",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 21,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 25,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "batch",
			Version: "v1",
			Kind:    "CronJob",
		},
	},
	{Group: "batch", Version: "v1beta1", Kind: "CronJobList"}: {
		Group:   "batch",
		Version: "v1beta1",
		Kind:    "CronJobList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 21,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 25,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "batch",
			Version: "v1",
			Kind:    "CronJobList",
		},
	},
	{Group: "certificates.k8s.io", Version: "v1alpha1", Kind: "ClusterTrustBundle"}: {
		Group:   "certificates.k8s.io",
		Version: "v1alpha1",
		Kind:    "ClusterTrustBundle",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 29,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 32,
		},
	},
	{Group: "certificates.k8s.io", Version: "v1alpha1", Kind: "ClusterTrustBundleList"}: {
		Group:   "certificates.k8s.io",
		Version: "v1alpha1",
		Kind:    "ClusterTrustBundleList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 29,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 32,
		},
	},
	{Group: "certificates.k8s.io", Version: "v1beta1", Kind: "CertificateSigningRequest"}: {
		Group:   "certificates.k8s.io",
		Version: "v1beta1",
		Kind:    "CertificateSigningRequest",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 19,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "certificates.k8s.io",
			Version: "v1",
			Kind:    "CertificateSigningRequest",
		},
	},
	{Group: "certificates.k8s.io", Version: "v1beta1", Kind: "CertificateSigningRequestList"}: {
		Group:   "certificates.k8s.io",
		Version: "v1beta1",
		Kind:    "CertificateSigningRequestList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 19,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "certificates.k8s.io",
			Version: "v1",
			Kind:    "CertificateSigningRequestList",
		},
	},
	{Group: "coordination.k8s.io", Version: "v1alpha1", Kind: "LeaseCandidate"}: {
		Group:   "coordination.k8s.io",
		Version: "v1alpha1",
		Kind:    "LeaseCandidate",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 34,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 37,
		},
	},
	{Group: "coordination.k8s.io", Version: "v1alpha1", Kind: "LeaseCandidateList"}: {
		Group:   "coordination.k8s.io",
		Version: "v1alpha1",
		Kind:    "LeaseCandidateList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 34,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 37,
		},
	},
	{Group: "coordination.k8s.io", Version: "v1beta1", Kind: "Lease"}: {
		Group:   "coordination.k8s.io",
		Version: "v1beta1",
		Kind:    "Lease",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 19,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "coordination.k8s.io",
			Version: "v1",
			Kind:    "Lease",
		},
	},
	{Group: "coordination.k8s.io", Version: "v1beta1", Kind: "LeaseList"}: {
		Group:   "coordination.k8s.io",
		Version: "v1beta1",
		Kind:    "LeaseList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 19,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "coordination.k8s.io",
			Version: "v1",
			Kind:    "LeaseList",
		},
	},
	{Group: "discovery.k8s.io", Version: "v1beta1", Kind: "EndpointSlice"}: {
		Group:   "discovery.k8s.io",
		Version: "v1beta1",
		Kind:    "EndpointSlice",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 21,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 25,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "discovery.k8s.io",
			Version: "v1",
			Kind:    "EndpointSlice",
		},
	},
	{Group: "discovery.k8s.io", Version: "v1beta1", Kind: "EndpointSliceList"}: {
		Group:   "discovery.k8s.io",
		Version: "v1beta1",
		Kind:    "EndpointSliceList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 21,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 25,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "discovery.k8s.io",
			Version: "v1",
			Kind:    "EndpointSlice",
		},
	},
	{Group: "events.k8s.io", Version: "v1beta1", Kind: "Event"}: {
		Group:   "events.k8s.io",
		Version: "v1beta1",
		Kind:    "Event",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 25,
		},
	},
	{Group: "events.k8s.io", Version: "v1beta1", Kind: "EventList"}: {
		Group:   "events.k8s.io",
		Version: "v1beta1",
		Kind:    "EventList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 25,
		},
	},
	{Group: "extensions", Version: "v1beta1", Kind: "DaemonSet"}: {
		Group:   "extensions",
		Version: "v1beta1",
		Kind:    "DaemonSet",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 8,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "apps",
			Version: "v1",
			Kind:    "DaemonSet",
		},
	},
	{Group: "extensions", Version: "v1beta1", Kind: "DaemonSetList"}: {
		Group:   "extensions",
		Version: "v1beta1",
		Kind:    "DaemonSetList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 8,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "apps",
			Version: "v1",
			Kind:    "DaemonSetList",
		},
	},
	{Group: "extensions", Version: "v1beta1", Kind: "Deployment"}: {
		Group:   "extensions",
		Version: "v1beta1",
		Kind:    "Deployment",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 8,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "apps",
			Version: "v1",
			Kind:    "Deployment",
		},
	},
	{Group: "extensions", Version: "v1beta1", Kind: "DeploymentList"}: {
		Group:   "extensions",
		Version: "v1beta1",
		Kind:    "DeploymentList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 8,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "apps",
			Version: "v1",
			Kind:    "DeploymentList",
		},
	},
	{Group: "extensions", Version: "v1beta1", Kind: "DeploymentRollback"}: {
		Group:   "extensions",
		Version: "v1beta1",
		Kind:    "DeploymentRollback",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 8,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
	},
	{Group: "extensions", Version: "v1beta1", Kind: "Ingress"}: {
		Group:   "extensions",
		Version: "v1beta1",
		Kind:    "Ingress",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 14,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "networking.k8s.io",
			Version: "v1",
			Kind:    "Ingress",
		},
	},
	{Group: "extensions", Version: "v1beta1", Kind: "IngressList"}: {
		Group:   "extensions",
		Version: "v1beta1",
		Kind:    "IngressList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 14,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "networking.k8s.io",
			Version: "v1",
			Kind:    "IngressList",
		},
	},
	{Group: "extensions", Version: "v1beta1", Kind: "NetworkPolicy"}: {
		Group:   "extensions",
		Version: "v1beta1",
		Kind:    "NetworkPolicy",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 9,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "networking.k8s.io",
			Version: "v1",
			Kind:    "NetworkPolicy",
		},
	},
	{Group: "extensions", Version: "v1beta1", Kind: "NetworkPolicyList"}: {
		Group:   "extensions",
		Version: "v1beta1",
		Kind:    "NetworkPolicyList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 9,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "networking.k8s.io",
			Version: "v1",
			Kind:    "NetworkPolicyList",
		},
	},
	{Group: "extensions", Version: "v1beta1", Kind: "ReplicaSet"}: {
		Group:   "extensions",
		Version: "v1beta1",
		Kind:    "ReplicaSet",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 8,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "apps",
			Version: "v1",
			Kind:    "ReplicaSet",
		},
	},
	{Group: "extensions", Version: "v1beta1", Kind: "ReplicaSetList"}: {
		Group:   "extensions",
		Version: "v1beta1",
		Kind:    "ReplicaSetList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 8,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "apps",
			Version: "v1",
			Kind:    "ReplicaSetList",
		},
	},
	{Group: "extensions", Version: "v1beta1", Kind: "Scale"}: {
		Group:   "extensions",
		Version: "v1beta1",
		Kind:    "Scale",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 2,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 16,
		},
	},
	{Group: "flowcontrol.apiserver.k8s.io", Version: "v1beta1", Kind: "FlowSchema"}: {
		Group:   "flowcontrol.apiserver.k8s.io",
		Version: "v1beta1",
		Kind:    "FlowSchema",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 23,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 26,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "flowcontrol.apiserver.k8s.io",
			Version: "v1beta3",
			Kind:    "FlowSchema",
		},
	},
	{Group: "flowcontrol.apiserver.k8s.io", Version: "v1beta1", Kind: "FlowSchemaList"}: {
		Group:   "flowcontrol.apiserver.k8s.io",
		Version: "v1beta1",
		Kind:    "FlowSchemaList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 23,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 26,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "flowcontrol.apiserver.k8s.io",
			Version: "v1beta3",
			Kind:    "FlowSchemaList",
		},
	},
	{Group: "flowcontrol.apiserver.k8s.io", Version: "v1beta1", Kind: "PriorityLevelConfiguration"}: {
		Group:   "flowcontrol.apiserver.k8s.io",
		Version: "v1beta1",
		Kind:    "PriorityLevelConfiguration",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 23,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 26,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "flowcontrol.apiserver.k8s.io",
			Version: "v1beta3",
			Kind:    "PriorityLevelConfiguration",
		},
	},
	{Group: "flowcontrol.apiserver.k8s.io", Version: "v1beta1", Kind: "PriorityLevelConfigurationList"}: {
		Group:   "flowcontrol.apiserver.k8s.io",
		Version: "v1beta1",
		Kind:    "PriorityLevelConfigurationList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 23,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 26,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "flowcontrol.apiserver.k8s.io",
			Version: "v1beta3",
			Kind:    "PriorityLevelConfigurationList",
		},
	},
	{Group: "flowcontrol.apiserver.k8s.io", Version: "v1beta2", Kind: "FlowSchema"}: {
		Group:   "flowcontrol.apiserver.k8s.io",
		Version: "v1beta2",
		Kind:    "FlowSchema",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 26,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 29,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "flowcontrol.apiserver.k8s.io",
			Version: "v1beta3",
			Kind:    "FlowSchema",
		},
	},
	{Group: "flowcontrol.apiserver.k8s.io", Version: "v1beta2", Kind: "FlowSchemaList"}: {
		Group:   "flowcontrol.apiserver.k8s.io",
		Version: "v1beta2",
		Kind:    "FlowSchemaList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 26,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 29,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "flowcontrol.apiserver.k8s.io",
			Version: "v1beta3",
			Kind:    "FlowSchemaList",
		},
	},
	{Group: "flowcontrol.apiserver.k8s.io", Version: "v1beta2", Kind: "PriorityLevelConfiguration"}: {
		Group:   "flowcontrol.apiserver.k8s.io",
		Version: "v1beta2",
		Kind:    "PriorityLevelConfiguration",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 26,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 29,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "flowcontrol.apiserver.k8s.io",
			Version: "v1beta3",
			Kind:    "PriorityLevelConfiguration",
		},
	},
	{Group: "flowcontrol.apiserver.k8s.io", Version: "v1beta2", Kind: "PriorityLevelConfigurationList"}: {
		Group:   "flowcontrol.apiserver.k8s.io",
		Version: "v1beta2",
		Kind:    "PriorityLevelConfigurationList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 26,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 29,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "flowcontrol.apiserver.k8s.io",
			Version: "v1beta3",
			Kind:    "PriorityLevelConfigurationList",
		},
	},
	{Group: "flowcontrol.apiserver.k8s.io", Version: "v1beta3", Kind: "FlowSchema"}: {
		Group:   "flowcontrol.apiserver.k8s.io",
		Version: "v1beta3",
		Kind:    "FlowSchema",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 29,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 32,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "flowcontrol.apiserver.k8s.io",
			Version: "v1",
			Kind:    "FlowSchema",
		},
	},
	{Group: "flowcontrol.apiserver.k8s.io", Version: "v1beta3", Kind: "FlowSchemaList"}: {
		Group:   "flowcontrol.apiserver.k8s.io",
		Version: "v1beta3",
		Kind:    "FlowSchemaList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 29,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 32,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "flowcontrol.apiserver.k8s.io",
			Version: "v1",
			Kind:    "FlowSchemaList",
		},
	},
	{Group: "flowcontrol.apiserver.k8s.io", Version: "v1beta3", Kind: "PriorityLevelConfiguration"}: {
		Group:   "flowcontrol.apiserver.k8s.io",
		Version: "v1beta3",
		Kind:    "PriorityLevelConfiguration",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 29,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 32,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "flowcontrol.apiserver.k8s.io",
			Version: "v1",
			Kind:    "PriorityLevelConfiguration",
		},
	},
	{Group: "flowcontrol.apiserver.k8s.io", Version: "v1beta3", Kind: "PriorityLevelConfigurationList"}: {
		Group:   "flowcontrol.apiserver.k8s.io",
		Version: "v1beta3",
		Kind:    "PriorityLevelConfigurationList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 29,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 32,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "flowcontrol.apiserver.k8s.io",
			Version: "v1",
			Kind:    "PriorityLevelConfigurationList",
		},
	},
	{Group: "networking.k8s.io", Version: "v1alpha1", Kind: "IPAddress"}: {
		Group:   "networking.k8s.io",
		Version: "v1alpha1",
		Kind:    "IPAddress",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 30,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 33,
		},
	},
	{Group: "networking.k8s.io", Version: "v1alpha1", Kind: "IPAddressList"}: {
		Group:   "networking.k8s.io",
		Version: "v1alpha1",
		Kind:    "IPAddressList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 30,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 33,
		},
	},
	{Group: "networking.k8s.io", Version: "v1alpha1", Kind: "ServiceCIDR"}: {
		Group:   "networking.k8s.io",
		Version: "v1alpha1",
		Kind:    "ServiceCIDR",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 30,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 33,
		},
	},
	{Group: "networking.k8s.io", Version: "v1alpha1", Kind: "ServiceCIDRList"}: {
		Group:   "networking.k8s.io",
		Version: "v1alpha1",
		Kind:    "ServiceCIDRList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 30,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 33,
		},
	},
	{Group: "networking.k8s.io", Version: "v1beta1", Kind: "IPAddress"}: {
		Group:   "networking.k8s.io",
		Version: "v1beta1",
		Kind:    "IPAddress",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 34,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 37,
		},
	},
	{Group: "networking.k8s.io", Version: "v1beta1", Kind: "IPAddressList"}: {
		Group:   "networking.k8s.io",
		Version: "v1beta1",
		Kind:    "IPAddressList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 34,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 37,
		},
	},
	{Group: "networking.k8s.io", Version: "v1beta1", Kind: "Ingress"}: {
		Group:   "networking.k8s.io",
		Version: "v1beta1",
		Kind:    "Ingress",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 19,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "networking.k8s.io",
			Version: "v1",
			Kind:    "Ingress",
		},
	},
	{Group: "networking.k8s.io", Version: "v1beta1", Kind: "IngressClass"}: {
		Group:   "networking.k8s.io",
		Version: "v1beta1",
		Kind:    "IngressClass",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 19,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "networking.k8s.io",
			Version: "v1",
			Kind:    "IngressClassList",
		},
	},
	{Group: "networking.k8s.io", Version: "v1beta1", Kind: "IngressClassList"}: {
		Group:   "networking.k8s.io",
		Version: "v1beta1",
		Kind:    "IngressClassList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 19,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "networking.k8s.io",
			Version: "v1",
			Kind:    "IngressClassList",
		},
	},
	{Group: "networking.k8s.io", Version: "v1beta1", Kind: "IngressList"}: {
		Group:   "networking.k8s.io",
		Version: "v1beta1",
		Kind:    "IngressList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 19,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "networking.k8s.io",
			Version: "v1",
			Kind:    "IngressList",
		},
	},
	{Group: "networking.k8s.io", Version: "v1beta1", Kind: "ServiceCIDR"}: {
		Group:   "networking.k8s.io",
		Version: "v1beta1",
		Kind:    "ServiceCIDR",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 34,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 37,
		},
	},
	{Group: "networking.k8s.io", Version: "v1beta1", Kind: "ServiceCIDRList"}: {
		Group:   "networking.k8s.io",
		Version: "v1beta1",
		Kind:    "ServiceCIDRList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 34,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 37,
		},
	},
	{Group: "node.k8s.io", Version: "v1beta1", Kind: "RuntimeClass"}: {
		Group:   "node.k8s.io",
		Version: "v1beta1",
		Kind:    "RuntimeClass",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 25,
		},
	},
	{Group: "node.k8s.io", Version: "v1beta1", Kind: "RuntimeClassList"}: {
		Group:   "node.k8s.io",
		Version: "v1beta1",
		Kind:    "RuntimeClassList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 25,
		},
	},
	{Group: "policy", Version: "v1beta1", Kind: "Eviction"}: {
		Group:   "policy",
		Version: "v1beta1",
		Kind:    "Eviction",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 25,
		},
	},
	{Group: "policy", Version: "v1beta1", Kind: "PodDisruptionBudget"}: {
		Group:   "policy",
		Version: "v1beta1",
		Kind:    "PodDisruptionBudget",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 21,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 25,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "policy",
			Version: "v1",
			Kind:    "PodDisruptionBudget",
		},
	},
	{Group: "policy", Version: "v1beta1", Kind: "PodDisruptionBudgetList"}: {
		Group:   "policy",
		Version: "v1beta1",
		Kind:    "PodDisruptionBudgetList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 21,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 25,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "policy",
			Version: "v1",
			Kind:    "PodDisruptionBudgetList",
		},
	},
	{Group: "rbac.authorization.k8s.io", Version: "v1beta1", Kind: "ClusterRole"}: {
		Group:   "rbac.authorization.k8s.io",
		Version: "v1beta1",
		Kind:    "ClusterRole",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 17,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "rbac.authorization.k8s.io",
			Version: "v1",
			Kind:    "ClusterRole",
		},
	},
	{Group: "rbac.authorization.k8s.io", Version: "v1beta1", Kind: "ClusterRoleBinding"}: {
		Group:   "rbac.authorization.k8s.io",
		Version: "v1beta1",
		Kind:    "ClusterRoleBinding",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 17,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "rbac.authorization.k8s.io",
			Version: "v1",
			Kind:    "ClusterRoleBinding",
		},
	},
	{Group: "rbac.authorization.k8s.io", Version: "v1beta1", Kind: "ClusterRoleBindingList"}: {
		Group:   "rbac.authorization.k8s.io",
		Version: "v1beta1",
		Kind:    "ClusterRoleBindingList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 17,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "rbac.authorization.k8s.io",
			Version: "v1",
			Kind:    "ClusterRoleBindingList",
		},
	},
	{Group: "rbac.authorization.k8s.io", Version: "v1beta1", Kind: "ClusterRoleList"}: {
		Group:   "rbac.authorization.k8s.io",
		Version: "v1beta1",
		Kind:    "ClusterRoleList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 17,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "rbac.authorization.k8s.io",
			Version: "v1",
			Kind:    "ClusterRoleList",
		},
	},
	{Group: "rbac.authorization.k8s.io", Version: "v1beta1", Kind: "Role"}: {
		Group:   "rbac.authorization.k8s.io",
		Version: "v1beta1",
		Kind:    "Role",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 17,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "rbac.authorization.k8s.io",
			Version: "v1",
			Kind:    "Role",
		},
	},
	{Group: "rbac.authorization.k8s.io", Version: "v1beta1", Kind: "RoleBinding"}: {
		Group:   "rbac.authorization.k8s.io",
		Version: "v1beta1",
		Kind:    "RoleBinding",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 17,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "rbac.authorization.k8s.io",
			Version: "v1",
			Kind:    "RoleBinding",
		},
	},
	{Group: "rbac.authorization.k8s.io", Version: "v1beta1", Kind: "RoleBindingList"}: {
		Group:   "rbac.authorization.k8s.io",
		Version: "v1beta1",
		Kind:    "RoleBindingList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 17,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "rbac.authorization.k8s.io",
			Version: "v1",
			Kind:    "RoleBindingList",
		},
	},
	{Group: "rbac.authorization.k8s.io", Version: "v1beta1", Kind: "RoleList"}: {
		Group:   "rbac.authorization.k8s.io",
		Version: "v1beta1",
		Kind:    "RoleList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 17,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "rbac.authorization.k8s.io",
			Version: "v1",
			Kind:    "RoleList",
		},
	},
	{Group: "scheduling.k8s.io", Version: "v1beta1", Kind: "PriorityClass"}: {
		Group:   "scheduling.k8s.io",
		Version: "v1beta1",
		Kind:    "PriorityClass",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 14,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "scheduling.k8s.io",
			Version: "v1",
			Kind:    "PriorityClass",
		},
	},
	{Group: "scheduling.k8s.io", Version: "v1beta1", Kind: "PriorityClassList"}: {
		Group:   "scheduling.k8s.io",
		Version: "v1beta1",
		Kind:    "PriorityClassList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 14,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "scheduling.k8s.io",
			Version: "v1",
			Kind:    "PriorityClassList",
		},
	},
	{Group: "storage.k8s.io", Version: "v1alpha1", Kind: "CSIStorageCapacity"}: {
		Group:   "storage.k8s.io",
		Version: "v1alpha1",
		Kind:    "CSIStorageCapacity",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 21,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 24,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "storage.k8s.io",
			Version: "v1beta1",
			Kind:    "CSIStorageCapacity",
		},
	},
	{Group: "storage.k8s.io", Version: "v1alpha1", Kind: "CSIStorageCapacityList"}: {
		Group:   "storage.k8s.io",
		Version: "v1alpha1",
		Kind:    "CSIStorageCapacityList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 21,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 24,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "storage.k8s.io",
			Version: "v1beta1",
			Kind:    "CSIStorageCapacityList",
		},
	},
	{Group: "storage.k8s.io", Version: "v1alpha1", Kind: "VolumeAttachment"}: {
		Group:   "storage.k8s.io",
		Version: "v1alpha1",
		Kind:    "VolumeAttachment",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 21,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 24,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "storage.k8s.io",
			Version: "v1",
			Kind:    "VolumeAttachment",
		},
	},
	{Group: "storage.k8s.io", Version: "v1alpha1", Kind: "VolumeAttachmentList"}: {
		Group:   "storage.k8s.io",
		Version: "v1alpha1",
		Kind:    "VolumeAttachmentList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 21,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 24,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "storage.k8s.io",
			Version: "v1",
			Kind:    "VolumeAttachmentList",
		},
	},
	{Group: "storage.k8s.io", Version: "v1alpha1", Kind: "VolumeAttributesClass"}: {
		Group:   "storage.k8s.io",
		Version: "v1alpha1",
		Kind:    "VolumeAttributesClass",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 32,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 35,
		},
	},
	{Group: "storage.k8s.io", Version: "v1alpha1", Kind: "VolumeAttributesClassList"}: {
		Group:   "storage.k8s.io",
		Version: "v1alpha1",
		Kind:    "VolumeAttributesClassList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 32,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 35,
		},
	},
	{Group: "storage.k8s.io", Version: "v1beta1", Kind: "CSIDriver"}: {
		Group:   "storage.k8s.io",
		Version: "v1beta1",
		Kind:    "CSIDriver",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 19,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "storage.k8s.io",
			Version: "v1",
			Kind:    "CSIDriver",
		},
	},
	{Group: "storage.k8s.io", Version: "v1beta1", Kind: "CSIDriverList"}: {
		Group:   "storage.k8s.io",
		Version: "v1beta1",
		Kind:    "CSIDriverList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 19,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "storage.k8s.io",
			Version: "v1",
			Kind:    "CSIDriverList",
		},
	},
	{Group: "storage.k8s.io", Version: "v1beta1", Kind: "CSINode"}: {
		Group:   "storage.k8s.io",
		Version: "v1beta1",
		Kind:    "CSINode",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 17,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "storage.k8s.io",
			Version: "v1",
			Kind:    "CSINode",
		},
	},
	{Group: "storage.k8s.io", Version: "v1beta1", Kind: "CSINodeList"}: {
		Group:   "storage.k8s.io",
		Version: "v1beta1",
		Kind:    "CSINodeList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 17,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "storage.k8s.io",
			Version: "v1",
			Kind:    "CSINode",
		},
	},
	{Group: "storage.k8s.io", Version: "v1beta1", Kind: "CSIStorageCapacity"}: {
		Group:   "storage.k8s.io",
		Version: "v1beta1",
		Kind:    "CSIStorageCapacity",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 24,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 27,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "storage.k8s.io",
			Version: "v1",
			Kind:    "CSIStorageCapacity",
		},
	},
	{Group: "storage.k8s.io", Version: "v1beta1", Kind: "CSIStorageCapacityList"}: {
		Group:   "storage.k8s.io",
		Version: "v1beta1",
		Kind:    "CSIStorageCapacityList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 24,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 27,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "storage.k8s.io",
			Version: "v1",
			Kind:    "CSIStorageCapacityList",
		},
	},
	{Group: "storage.k8s.io", Version: "v1beta1", Kind: "StorageClass"}: {
		Group:   "storage.k8s.io",
		Version: "v1beta1",
		Kind:    "StorageClass",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 19,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "storage.k8s.io",
			Version: "v1",
			Kind:    "StorageClass",
		},
	},
	{Group: "storage.k8s.io", Version: "v1beta1", Kind: "StorageClassList"}: {
		Group:   "storage.k8s.io",
		Version: "v1beta1",
		Kind:    "StorageClassList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 19,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "storage.k8s.io",
			Version: "v1",
			Kind:    "StorageClassList",
		},
	},
	{Group: "storage.k8s.io", Version: "v1beta1", Kind: "VolumeAttachment"}: {
		Group:   "storage.k8s.io",
		Version: "v1beta1",
		Kind:    "VolumeAttachment",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 19,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "storage.k8s.io",
			Version: "v1",
			Kind:    "VolumeAttachment",
		},
	},
	{Group: "storage.k8s.io", Version: "v1beta1", Kind: "VolumeAttachmentList"}: {
		Group:   "storage.k8s.io",
		Version: "v1beta1",
		Kind:    "VolumeAttachmentList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 19,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 22,
		},
		Replacement: schema.GroupVersionKind{
			Group:   "storage.k8s.io",
			Version: "v1",
			Kind:    "VolumeAttachmentList",
		},
	},
	{Group: "storage.k8s.io", Version: "v1beta1", Kind: "VolumeAttributesClass"}: {
		Group:   "storage.k8s.io",
		Version: "v1beta1",
		Kind:    "VolumeAttributesClass",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 34,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 37,
		},
	},
	{Group: "storage.k8s.io", Version: "v1beta1", Kind: "VolumeAttributesClassList"}: {
		Group:   "storage.k8s.io",
		Version: "v1beta1",
		Kind:    "VolumeAttributesClassList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 34,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 37,
		},
	},
	{Group: "storagemigration.k8s.io", Version: "v1alpha1", Kind: "StorageVersionMigration"}: {
		Group:   "storagemigration.k8s.io",
		Version: "v1alpha1",
		Kind:    "StorageVersionMigration",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 33,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 36,
		},
	},
	{Group: "storagemigration.k8s.io", Version: "v1alpha1", Kind: "StorageVersionMigrationList"}: {
		Group:   "storagemigration.k8s.io",
		Version: "v1alpha1",
		Kind:    "StorageVersionMigrationList",
		DeprecatedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 33,
		},
		RemovedVersion: Version{
			VersionMajor: 1,
			VersionMinor: 36,
		},
	},
}
