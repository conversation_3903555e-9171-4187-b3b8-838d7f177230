package k8sresource

import (
	"fmt"
	"strings"

	semverparse "github.com/Masterminds/semver"
	"github.com/samber/lo"
	"k8s.io/apimachinery/pkg/runtime/schema"
)

type DeprecatedGVKs struct {
	GVKs map[string]map[schema.GroupVersionKind]APIVersionInfo
}

func (d *DeprecatedGVKs) GetDeprecatedGVK(instanceID string, gvk schema.GroupVersionKind) (APIVersionInfo, bool) {
	if _, ok := d.GVKs[instanceID]; !ok {
		return APIVersionInfo{}, false
	}
	apiInfo, ok := d.GVKs[instanceID][gvk]
	return apiInfo, ok
}

func (d *DeprecatedGVKs) SetDeprecatedGVK(instanceID string, gvk schema.GroupVersionKind, apiInfo APIVersionInfo) {
	if _, ok := d.GVKs[instanceID]; !ok {
		d.GVKs[instanceID] = make(map[schema.GroupVersionKind]APIVersionInfo)
	}
	d.GVKs[instanceID][gvk] = apiInfo
}

func (d *DeprecatedGVKs) GetDeprecatedGVKs(instanceID string) map[schema.GroupVersionKind]APIVersionInfo {
	return d.GVKs[instanceID]
}

func (d *DeprecatedGVKs) SetDeprecatedGVKs(instanceID string, gvks map[schema.GroupVersionKind]APIVersionInfo) {
	d.GVKs[instanceID] = gvks
}

func (s *Service) GetDeprecatedGVKs(enabledClusters []*EnabledCluster) (*DeprecatedGVKs, error) {
	deprecatedGVKs := &DeprecatedGVKs{
		GVKs: make(map[string]map[schema.GroupVersionKind]APIVersionInfo),
	}
	instanceIds := lo.Map(enabledClusters, func(cluster *EnabledCluster, _ int) string {
		return cluster.InstanceID
	})
	for _, instanceId := range instanceIds {
		for gvk, apiInfo := range deprecatedGVKMap {
			deprecatedGVKs.SetDeprecatedGVK(instanceId, gvk, apiInfo)
		}
	}
	for _, cluster := range enabledClusters {
		k8sInfo, err := cluster.GetK8sInfo()
		if err != nil {
			return nil, err
		}
		for _, resourceType := range k8sInfo.ResourceTypes {
			if resourceType == nil {
				continue
			}
			customDeprecatedAPIs, err := cluster.GetCustomDeprecatedAPIs()
			if err != nil {
				return nil, err
			}
			for _, customDeprecatedAPI := range customDeprecatedAPIs {
				chunks := strings.Split(customDeprecatedAPI.APIVersion, "/")
				if len(chunks) != 2 {
					return nil, fmt.Errorf("invalid custom deprecated API: %s", customDeprecatedAPI.APIVersion)
				}
				group := chunks[0]
				version := chunks[1]
				newChunks := strings.Split(customDeprecatedAPI.NewAPIVersion, "/")
				if len(newChunks) != 2 {
					return nil, fmt.Errorf("invalid custom deprecated API: %s", customDeprecatedAPI.NewAPIVersion)
				}
				gvk := schema.GroupVersionKind{
					Group:   group,
					Version: version,
					Kind:    resourceType.GroupVersionKind.Kind,
				}
				if _, ok := deprecatedGVKs.GetDeprecatedGVK(cluster.InstanceID, gvk); ok {
					continue
				}
				newGroup := newChunks[0]
				newVersion := newChunks[1]
				deprecatedVersion := Version{}
				deprecatedParsedVersion, err := semverparse.NewVersion(customDeprecatedAPI.DeprecatedInKubernetesVersion)
				if err == nil {
					deprecatedVersion.VersionMajor = int(deprecatedParsedVersion.Major())
					deprecatedVersion.VersionMinor = int(deprecatedParsedVersion.Minor())
				}
				removedVersion := Version{}
				removedParsedVersion, err := semverparse.NewVersion(customDeprecatedAPI.UnavailableInKubernetesVersion)
				if err == nil {
					removedVersion.VersionMajor = int(removedParsedVersion.Major())
					removedVersion.VersionMinor = int(removedParsedVersion.Minor())
				}
				deprecatedGVKs.SetDeprecatedGVK(cluster.InstanceID, gvk, APIVersionInfo{
					Group:             group,
					Version:           version,
					Kind:              resourceType.GroupVersionKind.Kind,
					DeprecatedVersion: deprecatedVersion,
					RemovedVersion:    removedVersion,
					Replacement: schema.GroupVersionKind{
						Group:   newGroup,
						Version: newVersion,
						Kind:    resourceType.GroupVersionKind.Kind,
					},
				})
			}
		}
	}
	return deprecatedGVKs, nil
}
