package organization

import (
	"context"
	"fmt"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/spf13/cobra"
	"github.com/xhit/go-str2duration/v2"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/akuityio/akuity-platform/internal/akputil/shared"
	"github.com/akuityio/akuity-platform/internal/akuitycli/option"
	"github.com/akuityio/akuity-platform/internal/cli"
	"github.com/akuityio/akuity-platform/internal/cli/display"
	"github.com/akuityio/akuity-platform/internal/services/apikeys"
	"github.com/akuityio/akuity-platform/internal/services/permissions"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

func createKey(ctx context.Context, org *models.Organization, service apikeys.Service, reCreate bool, opts apikeys.CreateOpts) (string, string, error) {
	keys, err := service.ListOrganizationKeys(ctx, org.ID)
	if err != nil {
		return "", "", err
	}
	for _, key := range keys {
		if key.Description == opts.Description {
			if reCreate {
				if err := service.Delete(ctx, key.ID); err != nil {
					return "", "", err
				}
			} else {
				return "", "", fmt.Errorf("key with description %s already exists", opts.Description)
			}
		}
	}
	key, secret, err := service.CreateAPIKey(ctx, org.ID, "", opts)
	return key.ID, secret, err
}

type keyResult struct {
	key    string
	secret string
}

func (k keyResult) TableHeader() []string {
	return []string{"Key", "Secret"}
}

func (k keyResult) ToTableRow() []string {
	return []string{k.key, k.secret}
}

func (k keyResult) Data() interface{} {
	return map[string]string{
		"key":    k.key,
		"secret": k.secret,
	}
}

func NewAPIKeyCreateCommand() *cobra.Command {
	var (
		orgName      string
		desc         string
		role         string
		expiryIn     string
		reCreate     bool
		clientConfig clientcmd.ClientConfig
		outputFormat display.OutputFormat
	)
	cmd := &cobra.Command{
		Use:   "create-api-key",
		Short: "Create an API key for an organization",
		Run: func(cmd *cobra.Command, args []string) {
			akpUtil := shared.NewAKPUtil(clientConfig, cmd.Context(), time.Minute)
			defer func() { _ = akpUtil.Close() }()

			repoSet := client.NewRepoSet(akpUtil.PortalDBPool.DB)
			apiKeysSrv := apikeys.NewService(repoSet, validator.New())

			org, err := repoSet.Organizations().Filter(models.OrganizationWhere.Name.EQ(orgName)).One(akpUtil.Ctx)
			cli.CheckErr(err)
			duration, err := str2duration.ParseDuration(expiryIn)
			cli.CheckErr(err)

			key, secret, err := createKey(akpUtil.Ctx, org, apiKeysSrv, reCreate, apikeys.CreateOpts{
				Description: desc,
				Permissions: permissions.Permissions{Roles: []permissions.Role{permissions.Role(role)}},
				Expiry:      duration,
			})
			cli.CheckErr(err)
			display.NewSystemRenderer(outputFormat).Output(&keyResult{
				key:    key,
				secret: secret,
			})
		},
	}

	cmd.Flags().StringVar(&orgName, "org-name", "", "organization name")
	cmd.Flags().StringVar(&desc, "description", "", "API key description")
	cmd.Flags().StringVar(&role, "role", "organization/owner", "API key role (one of: organization/owner, organization/admin, organization/member)")
	cmd.Flags().StringVar(&expiryIn, "expiry-in", "0", "API key expiry. Valid time units are \"s\", \"m\", \"h\", \"d\", \"w\". Put 0 for no expiry.")
	cmd.Flags().BoolVar(&reCreate, "recreate", false, "recreate the key if it already exists")
	option.OutputFormat(&outputFormat)(cmd.Flags())
	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cli.CheckErr(cmd.MarkFlagRequired("org-name"))
	cli.CheckErr(cmd.MarkFlagRequired("description"))
	return cmd
}
