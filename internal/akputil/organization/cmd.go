package organization

import "github.com/spf13/cobra"

func NewOrganizationCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "organization",
		Short: "Organization command",
		Run: func(cmd *cobra.Command, args []string) {
			cmd.HelpFunc()(cmd, args)
		},
	}

	cmd.AddCommand(NewOrganizationCreateCommand())
	cmd.AddCommand(NewAddMembersCommand())
	cmd.AddCommand(NewRemoveMembersCommand())
	cmd.AddCommand(NewAPIKeyCreateCommand())
	cmd.AddCommand(NewBillingUpdateStateCommand())
	cmd.AddCommand(NewBillingUpdateLimitsCommand())

	return cmd
}
