package organization

import (
	"database/sql"
	"errors"
	"fmt"
	"time"

	"github.com/spf13/cobra"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/akuityio/akuity-platform/internal/akputil/shared"
	"github.com/akuityio/akuity-platform/internal/cli"
	"github.com/akuityio/akuity-platform/internal/services/organizations"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

func NewAddMembersCommand() *cobra.Command {
	var clientConfig clientcmd.ClientConfig
	cmd := &cobra.Command{
		Use:   "add-member ORGANIZATION_NAME EMAIL ROLE",
		Short: "Add user as a member to organization",
		Run: func(cmd *cobra.Command, args []string) {
			if len(args) != 3 {
				cmd.HelpFunc()(cmd, args)
				return
			}
			orgName := args[0]
			email := args[1]
			role := args[2]

			if !organizations.IsValidRole(organizations.Role(role)) {
				cli.CheckErr(fmt.Errorf("unknown role: %s", role))
			}

			akpUtil := shared.NewAKPUtil(clientConfig, cmd.Context(), time.Minute)
			defer func() { _ = akpUtil.Close() }()

			repoSet := client.NewRepoSet(akpUtil.PortalDBPool.DB)
			user, err := repoSet.Users().Filter(models.AkuityUserWhere.Email.EQ(email)).One(akpUtil.Ctx)
			cli.CheckErr(err)

			org, err := repoSet.Organizations().Filter(models.OrganizationWhere.Name.EQ(orgName)).One(akpUtil.Ctx)
			cli.CheckErr(err)

			membership, err := repoSet.OrganizationUsers().Filter(
				models.OrganizationUserWhere.OrganizationID.EQ(org.ID),
				models.OrganizationUserWhere.UserID.EQ(user.ID)).One(akpUtil.Ctx)

			if err == nil {
				membership.OrganizationRole = role
				cli.CheckErr(repoSet.OrganizationUsers().Update(akpUtil.Ctx, membership))
			} else if errors.Is(err, sql.ErrNoRows) {
				cli.CheckErr(repoSet.OrganizationUsers().Create(akpUtil.Ctx, &models.OrganizationUser{
					OrganizationID:   org.ID,
					UserID:           user.ID,
					OrganizationRole: role,
				}))
			} else {
				cli.CheckErr(err)
			}

			println("Successfully added user to organization")
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	return cmd
}

func NewRemoveMembersCommand() *cobra.Command {
	var clientConfig clientcmd.ClientConfig
	cmd := &cobra.Command{
		Use:   "remove-member ORGANIZATION_NAME EMAIL",
		Short: "Remove user as a member from organization",
		Run: func(cmd *cobra.Command, args []string) {
			if len(args) != 2 {
				cmd.HelpFunc()(cmd, args)
				return
			}
			orgName := args[0]
			email := args[1]

			akpUtil := shared.NewAKPUtil(clientConfig, cmd.Context(), time.Minute)
			defer func() { _ = akpUtil.Close() }()

			repoSet := client.NewRepoSet(akpUtil.PortalDBPool.DB)
			user, err := repoSet.Users().Filter(models.AkuityUserWhere.Email.EQ(email)).One(akpUtil.Ctx)
			cli.CheckErr(err)

			org, err := repoSet.Organizations().Filter(models.OrganizationWhere.Name.EQ(orgName)).One(akpUtil.Ctx)
			cli.CheckErr(err)

			cli.CheckErr(repoSet.OrganizationUsers().Filter(
				models.OrganizationUserWhere.UserID.EQ(user.ID),
				models.OrganizationUserWhere.OrganizationID.EQ(org.ID),
			).DeleteAll(akpUtil.Ctx))
			println("Successfully removed user from organization")
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	return cmd
}
