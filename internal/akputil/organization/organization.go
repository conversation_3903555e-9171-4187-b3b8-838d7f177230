package organization

import (
	"database/sql"
	"errors"
	"strings"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/spf13/cobra"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/akuityio/akuity-platform/internal/akputil/shared"
	"github.com/akuityio/akuity-platform/internal/cli"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/organizations"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

func NewOrganizationCreateCommand() *cobra.Command {
	var (
		ownerEmail   string
		clientConfig clientcmd.ClientConfig
	)

	cmd := &cobra.Command{
		Use:   "create NAME",
		Short: "Create an organization",
		Run: func(cmd *cobra.Command, args []string) {
			if len(args) != 1 {
				cmd.HelpFunc()(cmd, args)
				return
			}
			orgName := args[0]
			akpUtil := shared.NewAKPUtil(clientConfig, cmd.Context(), time.Minute)
			defer func() { _ = akpUtil.Close() }()

			repoSet := client.NewRepoSet(akpUtil.PortalDBPool.DB)
			ownerEmail = strings.ToLower(ownerEmail)

			user, err := repoSet.Users().Filter(models.AkuityUserWhere.Email.EQ(ownerEmail)).One(akpUtil.Ctx)
			if err != nil {
				if errors.Is(err, sql.ErrNoRows) {
					user = &models.AkuityUser{Email: ownerEmail}
					cli.CheckErr(repoSet.Users().Create(akpUtil.Ctx, user))
				} else {
					cli.CheckErr(err)
				}
			}

			org, err := repoSet.Organizations().Filter(models.OrganizationWhere.Name.EQ(orgName)).One(akpUtil.Ctx)
			if err != nil {
				if errors.Is(err, sql.ErrNoRows) {
					txDB, txBeginner := database.WithTxBeginner(akpUtil.PortalDBPool.DB)
					rs := client.NewRepoSet(txDB)
					orgSrv := organizations.New(txDB, txBeginner, nil, nil, rs, config.PortalServerConfig{}, validator.New())
					org, err = orgSrv.Create(akpUtil.Ctx, *user, models.Organization{Name: orgName}, 0)
					cli.CheckErr(err)
				} else {
					cli.CheckErr(err)
				}
			}

			membership, err := repoSet.OrganizationUsers().Filter(
				models.OrganizationUserWhere.OrganizationID.EQ(org.ID),
				models.OrganizationUserWhere.UserID.EQ(user.ID)).One(akpUtil.Ctx)

			if err == nil {
				membership.OrganizationRole = string(organizations.RoleOwner)
				cli.CheckErr(repoSet.OrganizationUsers().Update(akpUtil.Ctx, membership))
			} else if errors.Is(err, sql.ErrNoRows) {
				cli.CheckErr(repoSet.OrganizationUsers().Create(akpUtil.Ctx, &models.OrganizationUser{
					OrganizationID:   org.ID,
					UserID:           user.ID,
					OrganizationRole: string(organizations.RoleOwner),
				}))
			} else {
				cli.CheckErr(err)
			}
		},
	}
	cmd.Flags().StringVar(&ownerEmail, "owner", "", "Email of the owner of the organization")
	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	return cmd
}
