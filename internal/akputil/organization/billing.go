package organization

import (
	"context"
	"fmt"
	"time"

	"github.com/spf13/cobra"
	"github.com/xhit/go-str2duration/v2"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/akuityio/akuity-platform/internal/akputil/shared"
	"github.com/akuityio/akuity-platform/internal/cli"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

func getOrg(ctx context.Context, orgID, orgName string, repoSet client.RepoSet) (*models.Organization, error) {
	var org *models.Organization
	var err error
	if orgID != "" {
		org, err = repoSet.Organizations().Filter(models.OrganizationWhere.ID.EQ(orgID)).One(ctx)
	} else if orgName != "" {
		org, err = repoSet.Organizations().Filter(models.OrganizationWhere.Name.EQ(orgName)).One(ctx)
	} else {
		return nil, fmt.Errorf("must specify either org-id or org-name")
	}
	if err != nil {
		return nil, err
	}
	if org == nil {
		return nil, fmt.Errorf("organization not found")
	}
	return org, nil
}

func NewBillingUpdateStateCommand() *cobra.Command {
	var (
		clientConfig              clientcmd.ClientConfig
		billingState              string
		expirationExtensionInDays int64
		orgID                     string
		orgName                   string
	)
	cmd := &cobra.Command{
		Use:   "billing-state",
		Short: "Manage billing",
		Run: func(cmd *cobra.Command, args []string) {
			akpUtil := shared.NewAKPUtil(clientConfig, cmd.Context(), time.Minute)
			defer func() { _ = akpUtil.Close() }()

			repoSet := client.NewRepoSet(akpUtil.PortalDBPool.DB)

			org, err := getOrg(akpUtil.Ctx, orgID, orgName, repoSet)
			cli.CheckErr(err)

			if org == nil {
				cli.CheckErr(fmt.Errorf("organization does not exist"))
			}

			status, err := org.GetOrgStatus()
			cli.CheckErr(err)

			if expirationExtensionInDays != -1 {
				duration, err := str2duration.ParseDuration(fmt.Sprintf("%dd", expirationExtensionInDays))
				cli.CheckErr(err)

				status.ExpiryTime = time.Now().Add(duration).Unix()
			}

			if billingState != "" {
				if billingState == "poc" || billingState == "trial" || billingState == "paid_customer" {
					status.State = models.BillingState(billingState)
				} else {
					cli.CheckErr(fmt.Errorf("invalid billing state"))
				}
			}

			err = org.SetOrgStatus(*status)
			cli.CheckErr(err)

			cli.CheckErr(repoSet.Organizations().Update(akpUtil.Ctx, org))

			fmt.Printf("Updated billing state for organization\n")
		},
		DisableAutoGenTag: true,
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().StringVar(&billingState, "state", "", "billing state (trial, poc, paid_customer)")
	cmd.Flags().Int64Var(&expirationExtensionInDays, "expiration-extension", -1, "extend trial by X days")
	cmd.Flags().StringVar(&orgID, "org-id", "", "organization id")
	cmd.Flags().StringVar(&orgName, "org-name", "", "organization name")

	return cmd
}

func NewBillingUpdateLimitsCommand() *cobra.Command {
	var (
		clientConfig    clientcmd.ClientConfig
		orgID           string
		orgName         string
		maxClusters     int64
		maxInstances    int64
		maxApplications int64
	)
	cmd := &cobra.Command{
		Use:   "billing-limits",
		Short: "Manage billing",
		Run: func(cmd *cobra.Command, args []string) {
			akpUtil := shared.NewAKPUtil(clientConfig, cmd.Context(), time.Minute)
			defer func() { _ = akpUtil.Close() }()

			repoSet := client.NewRepoSet(akpUtil.PortalDBPool.DB)

			org, err := getOrg(akpUtil.Ctx, orgID, orgName, repoSet)
			cli.CheckErr(err)

			if maxClusters == -1 && maxInstances == -1 && maxApplications == -1 {
				cli.CheckErr(fmt.Errorf("must specify at least one limit to update"))
			}

			if maxClusters != -1 {
				org.MaxClusters = int(maxClusters)
			}
			if maxInstances != -1 {
				org.MaxInstances = int(maxInstances)
			}
			if maxApplications != -1 {
				org.MaxApplications = int(maxApplications)
			}

			cli.CheckErr(repoSet.Organizations().Update(akpUtil.Ctx, org))

			fmt.Printf("Updated billing limits for organization\n")
		},
		DisableAutoGenTag: true,
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().Int64Var(&maxClusters, "max-clusters", -1, "max clusters")
	cmd.Flags().Int64Var(&maxInstances, "max-instances", -1, "max instances")
	cmd.Flags().Int64Var(&maxApplications, "max-applications", -1, "max applications")
	cmd.Flags().StringVar(&orgID, "org-id", "", "organization id")
	cmd.Flags().StringVar(&orgName, "org-name", "", "organization name")

	return cmd
}
