package cluster

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"net/http/httputil"
	"net/url"
	"os"
	"time"

	"github.com/samber/lo"
	"github.com/spf13/cobra"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/cli-runtime/pkg/genericclioptions"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	clientcmdapi "k8s.io/client-go/tools/clientcmd/api"
	"k8s.io/client-go/tools/portforward"
	"k8s.io/client-go/transport/spdy"
	cmdutil "k8s.io/kubectl/pkg/cmd/util"
	"k8s.io/kubectl/pkg/polymorphichelpers"

	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/internal/cli"
	"github.com/akuityio/akuity-platform/internal/utils/io"
)

type injectHeaderTransport struct {
	http.RoundTripper
	hostname string
}

func (ct *injectHeaderTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	req.Host = ct.hostname
	return ct.RoundTripper.RoundTrip(req)
}

func writeProxyKubeconfig(clusterNS, configPath string, port int) error {
	server := fmt.Sprintf("localhost:%d", port)
	clusterName := "akuity-managed-cluster"

	cfg := &clientcmdapi.Config{
		CurrentContext: clusterName,
		Contexts: map[string]*clientcmdapi.Context{
			clusterName: {Cluster: clusterName, Namespace: clusterNS},
		},
		Clusters: map[string]*clientcmdapi.Cluster{clusterName: {Server: server}},
	}
	return clientcmd.WriteToFile(*cfg, configPath)
}

func startProxy(ctx context.Context, util *AKPUtilCluster) (int, error) {
	kubeConfigFlags := genericclioptions.NewConfigFlags(true)
	kubeConfigFlags.WithWrapConfigFn(func(_ *rest.Config) *rest.Config {
		return util.RestConfig
	})
	matchVersionKubeConfigFlags := cmdutil.NewMatchVersionFlags(kubeConfigFlags)
	f := cmdutil.NewFactory(matchVersionKubeConfigFlags)

	namespace := common.ArgoCDHostNamespace(util.instanceID)
	serviceName := fmt.Sprintf("cluster-%s", util.clusterName)
	clusterSVC, err := util.K8sClient.CoreV1().Services(namespace).Get(ctx, serviceName, metav1.GetOptions{})
	if err != nil {
		return -1, err
	}
	k8sPort, ok := lo.Find(clusterSVC.Spec.Ports, func(item v1.ServicePort) bool {
		return item.Name == "kube-server"
	})
	if !ok {
		return -1, fmt.Errorf("cannot find kube-server port in cluster service :%s", clusterSVC.Name)
	}
	pod, err := polymorphichelpers.AttachablePodForObjectFn(f, clusterSVC, time.Minute)
	if err != nil {
		return -1, err
	}

	transport, upgrader, err := spdy.RoundTripperFor(util.RestConfig)
	if err != nil {
		return -1, err
	}
	restClient, err := rest.RESTClientFor(util.RestConfig)
	if err != nil {
		return -1, err
	}

	req := restClient.Post().
		Resource("pods").
		Namespace(namespace).
		Name(pod.Name).
		SubResource("portforward")

	dialer := spdy.NewDialer(upgrader, &http.Client{Transport: &injectHeaderTransport{transport, serviceName}}, "POST", req.URL())

	readyChan := make(chan struct{}, 1)
	failedChan := make(chan error, 1)

	ln, err := net.Listen("tcp", "localhost:0")
	if err != nil {
		return -1, err
	}
	port := ln.Addr().(*net.TCPAddr).Port
	io.Close(ln)

	forwarder, err := portforward.NewOnAddresses(
		dialer,
		[]string{"localhost"},
		[]string{fmt.Sprintf("%d:%s", port, k8sPort.TargetPort.String())},
		ctx.Done(), readyChan, os.Stdout, os.Stderr)
	if err != nil {
		return -1, err
	}
	go func() {
		err = forwarder.ForwardPorts()
		if err != nil {
			failedChan <- err
		}
	}()

	select {
	case err = <-failedChan:
		return -1, err
	case <-readyChan:
		return port, nil
	}
}

func newPortForwardCommand() *cobra.Command {
	var (
		akpUtilCluster = &AKPUtilCluster{}
		port           int
		clusterNS      string
		kubeconfigPath string
	)
	cmd := &cobra.Command{
		Use:   "port-forward",
		Short: "Connect to a cluster",
		Run: func(cmd *cobra.Command, args []string) {
			cli.CheckErr(akpUtilCluster.Init(cmd.Context(), time.Hour))
			defer func() { _ = akpUtilCluster.Close() }()

			forwardedPort, err := startProxy(akpUtilCluster.Ctx, akpUtilCluster)
			cli.CheckErr(err)
			proxyURL, err := url.Parse(fmt.Sprintf("http://localhost:%d", forwardedPort))
			cli.CheckErr(err)
			proxy := httputil.NewSingleHostReverseProxy(proxyURL)
			proxy.Transport = &injectHeaderTransport{http.DefaultTransport, "cluster-" + akpUtilCluster.clusterName + ":8001"}
			if akpUtilCluster.cluster.Namespace != "" {
				clusterNS = akpUtilCluster.cluster.Namespace
			}
			if kubeconfigPath != "" {
				cli.CheckErr(writeProxyKubeconfig(clusterNS, kubeconfigPath, port))
			}
			cli.CheckErr(http.ListenAndServe(fmt.Sprintf(":%d", port), proxy))
		},
	}
	akpUtilCluster.AddFlags(cmd.Flags())
	cmd.Flags().IntVar(&port, "port", 8001, "Port to forward to")
	cmd.Flags().StringVar(&kubeconfigPath, "kubeconfig-output-path", "/tmp/akpconfig", "Output kubeconfig path")
	cmd.Flags().StringVar(&clusterNS, "cluster-namespace", "akuity", "Managed cluster Namespace")
	return cmd
}
