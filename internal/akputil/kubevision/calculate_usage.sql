WITH akp_counts AS (
    SELECT
        organization.id AS organization_id,
        COUNT(DISTINCT CASE WHEN argo_cd_instance_config.spec->>'multi_cluster_k8s_dashboard_enabled' = 'true' THEN argo_cd_instance.id END) AS instance_count,
        COUNT(DISTINCT CASE WHEN argo_cd_cluster.spec->>'multiClusterK8SDashboardEnabled' = 'true' THEN argo_cd_cluster.id END) AS cluster_count,
        SUM(COALESCE((argo_cd_cluster.status_k8s_info->>'api_resource_count')::int, 0)) as api_resource_count,
        SUM(COALESCE((argo_cd_cluster.status_k8s_info->>'object_count')::int, 0)) as object_count,
        organization.ai_usage
    FROM organization
    LEFT JOIN argo_cd_instance ON organization.id = argo_cd_instance.organization_owner
    LEFT JOIN argo_cd_instance_config ON argo_cd_instance.id = argo_cd_instance_config.instance_id
    LEFT JOIN argo_cd_cluster ON argo_cd_instance.id = argo_cd_cluster.instance_id
    GROUP BY organization.id
    HAVING COUNT(DISTINCT CASE WHEN argo_cd_instance_config.spec->>'multi_cluster_k8s_dashboard_enabled' = 'true' THEN argo_cd_instance.id END) > 0
),
object_counts AS (
    SELECT 
        organization_id,
        COUNT(*) FILTER (WHERE kind = 'Node' AND "group" = '') AS node_count,
        COUNT(*) FILTER (WHERE kind = 'Pod' AND "group" = '') AS pod_count,
        COUNT(*) FILTER (WHERE kind = 'Container' AND "group" = 'dashboard.akuity.io') AS container_count
    FROM argo_cd_cluster_k8s_object
    WHERE (kind = 'Node' AND "group" = '') OR (kind = 'Pod' AND "group" = '') OR (kind = 'Container' AND "group" = 'dashboard.akuity.io')
    GROUP BY
        organization_id
)
INSERT INTO organization_kubevision_usage (
    id,
    organization_id,
    instance_count,
    cluster_count,
    api_resource_count,
    object_count,
    node_count,
    pod_count,
    container_count,
    ai_usage
)
SELECT
    gen_random_uuid(),
    akp.organization_id,
    akp.instance_count,
    akp.cluster_count,
    akp.api_resource_count,
    akp.object_count,
    COALESCE(oc.node_count, 0) AS node_count,
    COALESCE(oc.pod_count, 0) AS pod_count,
    COALESCE(oc.container_count, 0) AS container_count,
    akp.ai_usage
FROM akp_counts akp
LEFT JOIN object_counts oc ON akp.organization_id = oc.organization_id
