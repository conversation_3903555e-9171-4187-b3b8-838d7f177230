package plans

import (
	"context"
	"fmt"
	"reflect"
	"strconv"
	"time"

	"github.com/volatiletech/null/v8"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/akuityio/akuity-platform/internal/akputil/shared"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

type PlansInitializer struct {
	*shared.AKPUtil
	txBeginner database.TxBeginner
	repoSet    client.RepoSet
}

func NewPlansInitializer(clientConfig clientcmd.ClientConfig, ctx context.Context, contextTimeout time.Duration) (*PlansInitializer, error) {
	akpUtil := shared.NewAKPUtil(clientConfig, ctx, contextTimeout)
	txDB, txBeginner := database.WithTxBeginner(akpUtil.PortalDBPool.DB)

	return &PlansInitializer{
		AKPUtil:    akpUtil,
		repoSet:    client.NewRepoSet(txDB),
		txBeginner: txBeginner,
	}, nil
}

func getHeaderMaps(records [][]string) (map[int]string, map[string]bool) {
	headerForIndex := make(map[int]string)
	headerExists := make(map[string]bool)
	for i, header := range records[0] {
		headerForIndex[i] = header
		headerExists[header] = true

	}
	return headerForIndex, headerExists
}

func mapFromArray(arr []string) map[string]bool {
	m := make(map[string]bool, len(arr))
	for _, s := range arr {
		m[s] = true
	}
	return m
}

var plansColumns = []string{
	"name",
	"display_name",
	"default_plan",
	"billing_authority",
	"product_id",
	"quota",
	"features",
	"deprecated",
}

func (a *PlansInitializer) InitializePlans(records [][]string) error {
	if len(records) < 2 {
		return fmt.Errorf("CSV file should have at least 2 rows")
	}

	headerForIndex, columns := getHeaderMaps(records)
	if len(columns) != len(plansColumns) || !reflect.DeepEqual(columns, mapFromArray(plansColumns)) {
		return fmt.Errorf("CSV file should have the following columns: %v", plansColumns)
	}

	txCtx, cancel := context.WithCancel(a.Ctx)
	defer cancel()
	tx, err := a.txBeginner.Begin(txCtx)
	if err != nil {
		return err
	}

	existingPlans, err := a.repoSet.OrganizationPlans().ListAll(txCtx)
	if err != nil {
		return err
	}

	planMap := make(map[string]bool, len(existingPlans))
	for _, plan := range existingPlans {
		planMap[plan.Name] = true
	}

	for _, record := range records[1:] {
		plan := &models.OrganizationPlan{}
		for j, col := range record {
			if field, ok := headerForIndex[j]; ok {
				switch field {
				case "name":
					plan.Name = col
				case "display_name":
					plan.DisplayName = col
				case "default_plan":
					plan.DefaultPlan = null.BoolFrom(col == "true")
				case "billing_authority":
					plan.BillingAuthority = col
				case "product_id":
					plan.ProductID = col
				case "quota":
					plan.Quota = null.JSONFrom([]byte(col))
				case "features":
					plan.Features = null.JSONFrom([]byte(col))
				case "deprecated":
					plan.Deprecated = null.BoolFrom(col == "true")
				default: // ignore
					continue
				}
			}
		}

		var err error
		if planMap[plan.Name] {
			err = a.repoSet.OrganizationPlans().Update(txCtx, plan, plansColumns...)
			delete(planMap, plan.Name)
		} else {
			err = a.repoSet.OrganizationPlans().Create(txCtx, plan)
		}
		if err != nil {
			return err
		}
	}

	for planName := range planMap {
		fmt.Printf("Plan %s is in the database but not in the imported file. It should be pruned.\n", planName)
	}

	if err := tx.Commit(); err != nil {
		return err
	}

	return nil
}

var addonColumns = []string{
	"included_with_plan",
	"included_quantity",
	"name",
	"product_id",
	"min_quantity",
	"max_quantity",
	"description",
	"quota",
	"display_name",
}

func (a *PlansInitializer) InitializeAddons(records [][]string) error {
	if len(records) < 2 {
		return fmt.Errorf("CSV file should have at least 2 rows")
	}

	headerIdx, columns := getHeaderMaps(records)
	if len(columns) != len(addonColumns) || !reflect.DeepEqual(columns, mapFromArray(addonColumns)) {
		return fmt.Errorf("CSV file should have the following columns: %v", addonColumns)
	}

	txCtx, cancel := context.WithCancel(a.Ctx)
	defer cancel()
	tx, err := a.txBeginner.Begin(txCtx)
	if err != nil {
		return err
	}

	existingAddons, err := a.repoSet.AddonPlans().ListAll(txCtx)
	if err != nil {
		return err
	}

	addonMap := make(map[string]bool, len(existingAddons))
	for _, addon := range existingAddons {
		addonMap[addon.Name] = true
	}

	for _, record := range records[1:] {
		addon := &models.AddonPlan{}
		for j, col := range record {
			if field, ok := headerIdx[j]; ok {
				switch field {
				case "included_with_plan":
					addon.IncludedWithPlan = null.StringFrom(col)
				case "included_quantity":
					i, err := strconv.Atoi(col)
					if err != nil {
						return err
					}
					addon.IncludedQuantity = null.IntFrom(i)
				case "name":
					addon.Name = col
				case "product_id":
					addon.ProductID = col
				case "min_quantity":
					i, err := strconv.Atoi(col)
					if err != nil {
						return err
					}
					addon.MinQuantity = i
				case "max_quantity":
					i, err := strconv.Atoi(col)
					if err != nil {
						return err
					}
					addon.MaxQuantity = i
				case "description":
					addon.Description = null.StringFrom(col)
				case "quota":
					addon.Quota = null.JSONFrom([]byte(col))
				case "display_name":
					addon.DisplayName = col
				}
			}
		}

		var err error
		if addonMap[addon.Name] {
			err = a.repoSet.AddonPlans().Update(txCtx, addon, addonColumns...)
			delete(addonMap, addon.Name)
		} else {
			err = a.repoSet.AddonPlans().Create(txCtx, addon)
		}
		if err != nil {
			return err
		}
	}

	for addonName := range addonMap {
		fmt.Printf("Addon %s is in the database but not in the imported file. It should be pruned.\n", addonName)
	}

	if err := tx.Commit(); err != nil {
		return err
	}

	return nil
}
