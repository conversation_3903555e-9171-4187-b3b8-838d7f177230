package plans

import (
	"encoding/csv"
	"os"
	"time"

	"github.com/spf13/cobra"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/akuityio/akuity-platform/internal/cli"
)

var plansCmd = &cobra.Command{
	Use:   "plans",
	Short: "Plans command",
	Run: func(cmd *cobra.Command, args []string) {
		cmd.HelpFunc()(cmd, args)
	},
}

func NewPlansCommand() *cobra.Command {
	plansCmd.AddCommand(NewInitPlansCommand())
	plansCmd.AddCommand(NewInitAddonsCommand())
	return plansCmd
}

func NewInitPlansCommand() *cobra.Command {
	var (
		clientConfig clientcmd.ClientConfig
		file         string
	)

	cmd := &cobra.Command{
		Use:   "init",
		Short: "Initializes plans",
		Run: func(cmd *cobra.Command, args []string) {
			akpPlans, err := NewPlansInitializer(clientConfig, cmd.Context(), time.Minute)
			cli.CheckErr(err)
			defer func() { _ = akpPlans.Close() }()

			f, err := os.Open(file)
			cli.CheckErr(err)
			defer f.Close()

			csvReader := csv.NewReader(f)
			records, err := csvReader.ReadAll()
			cli.CheckErr(err)
			cli.CheckErr(akpPlans.InitializePlans(records))
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().StringVarP(&file, "file", "f", "", "CSV file path to initialize plans data")
	cli.CheckErr(cmd.MarkFlagRequired("file"))
	return cmd
}

func NewInitAddonsCommand() *cobra.Command {
	var (
		clientConfig clientcmd.ClientConfig
		file         string
	)

	cmd := &cobra.Command{
		Use:   "init-addons",
		Short: "Initializes addons",
		Run: func(cmd *cobra.Command, args []string) {
			akpPlans, err := NewPlansInitializer(clientConfig, cmd.Context(), time.Minute)
			cli.CheckErr(err)
			defer func() { _ = akpPlans.Close() }()

			f, err := os.Open(file)
			cli.CheckErr(err)
			defer f.Close()

			csvReader := csv.NewReader(f)
			records, err := csvReader.ReadAll()
			cli.CheckErr(err)
			cli.CheckErr(akpPlans.InitializeAddons(records))
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().StringVarP(&file, "file", "f", "", "CSV file path to initialize addons data")
	cli.CheckErr(cmd.MarkFlagRequired("file"))
	return cmd
}
