package verify

import (
	"context"
	"fmt"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/playwright-community/playwright-go"
	"github.com/stretchr/testify/require"
	"k8s.io/apimachinery/pkg/util/sets"
	"k8s.io/utils/ptr"

	httpctx "github.com/akuity/grpc-gateway-client/pkg/http/context"
	"github.com/akuityio/akuity-platform/pkg/api/gateway/accesscontrol"
	gwoption "github.com/akuityio/akuity-platform/pkg/api/gateway/option"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
	idv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/id/v1"
	"github.com/akuityio/akuity-platform/test/utils"
	"github.com/akuityio/akuity-platform/test/utils/ui/akuity"
	"github.com/akuityio/akuity-platform/test/utils/ui/akuity/components"
	"github.com/akuityio/akuity-platform/test/utils/ui/akuity/pages"
	"github.com/akuityio/akuity-platform/test/utils/ui/argocd"
	"github.com/akuityio/akuity-platform/test/utils/ui/kargo"
)

// auth0LoginWithValidation - utils.WaitFor() wrapper around s.auth0Login() with post-login navigation validation
func (s *smokeTest) auth0LoginWithValidation(t *testFake, page playwright.Page, auth0Username, auth0Password, argoCdRootURL string) string {
	var authToken string

	require.NoError(t, utils.WaitFor("auth0 login", s.timeout, s.log, func(_ context.Context) (bool, string, error) {
		if err := page.Context().ClearCookies(); err != nil {
			return false, "Clearing cookies failed", nil
		}
		if token, err := s.auth0Login(auth0Username, auth0Password); err != nil {
			return false, "Auth0 login failed", nil
		} else {
			authToken = token
		}

		if title, err := page.Title(); err != nil || title != "Akuity Platform Portal" {
			return false, "'Akuity Platform Portal' title is missing", nil
		}

		if response, err := page.Goto(argoCdRootURL + "/organizations"); err != nil || response == nil || !response.Ok() {
			return false, "Organizations navigation failed (Goto)", nil
		}

		if err := page.Locator(`h1:text("Organizations")`).WaitFor(playwright.LocatorWaitForOptions{Timeout: ptr.To(5000.0)}); err != nil {
			return false, "Organizations navigation failed (WaitFor <h1>Organizations</h1>)", nil
		}

		if response, err := page.Goto(argoCdRootURL + "/instances/argocd"); err != nil || response == nil || !response.Ok() {
			return false, "Argo CD instances navigation failed (Goto)", nil
		}

		if err := page.Locator(`span:text("Create")`).WaitFor(playwright.LocatorWaitForOptions{Timeout: ptr.To(5000.0)}); err != nil {
			return false, "Argo CD instances navigation failed (WaitFor <span>Create</span>)", nil
		}

		return true, "", nil
	}))

	return authToken
}

func (s *smokeTest) auth0Login(username, password string) (string, error) {
	loginURL := s.rootURL + "/api/auth/login"
	if _, err := s.page.Goto(loginURL); err != nil {
		return "", err
	}
	if err := s.page.Locator("input[name='username']").Fill(username); err != nil {
		return "", err
	}
	if err := s.page.Locator("button[name='action'][type='submit'][data-action-button-primary='true']").Click(); err != nil {
		return "", err
	}
	if err := s.page.Locator("input[name='password']").Fill(password); err != nil {
		return "", err
	}
	if err := s.page.Locator("button[name='action'][type='submit'][data-action-button-primary='true']").Click(); err != nil {
		return "", err
	}

	cookies, err := s.page.Context().Cookies(s.rootURL)
	if err != nil {
		return "", err
	}

	s.log.Info(fmt.Sprintf("%d %q cookies:", len(cookies), loginURL))

	for _, cookie := range cookies {
		s.log.Info("", "cookie.Name", cookie.Name)
	}

	for _, cookie := range cookies {
		if cookie.Name == "token" {
			return cookie.Value, nil
		}
	}
	return "", fmt.Errorf("'token' cookie not found")
}

func (s *smokeTest) logout(t *testFake, app *akuity.PlatformWebApp) {
	app.Logout()

	// playwright wait for url to have path /u/login/identifier, use regex matching to ignore base url
	require.NoError(t, app.Page().WaitForURL(logoutUrlRegex))
	s.log.Info("Logged out successfully")
}

func (s *smokeTest) createInstance(t require.TestingT, app *akuity.PlatformWebApp, instanceName, adminPassword string) (string, string) {
	s.log.Info("Creating instance", "instance", instanceName)
	detailsPage := app.GoToArgoCD().
		ClickCreateNewInstance().
		SetName(instanceName).
		SelectWorkspace("default").
		Create()

	instanceID := detailsPage.InstanceID()

	settings := detailsPage.Settings()
	settings.General().ToggleAppInAnyNs().Save()
	accountsSettings := settings.Accounts()
	adminToggle := accountsSettings.AdminToggle()
	require.False(t, adminToggle.IsChecked())
	adminToggle.Click()
	components.NewConfirmationDialog(app.Page()).Confirm()

	accountsSettings.SetPassword("admin", adminPassword)
	settings.GoBackToDetails()

	require.NoError(t, utils.WaitFor("instance is healthy", s.timeout, s.log, func(ctx context.Context) (bool, string, error) {
		status := detailsPage.HealthStatus()
		return status == "Healthy", fmt.Sprintf("instance status is %s", status), nil
	}))

	return detailsPage.SelectSummaryTab().ArgoCDURL(), instanceID
}

func (s *smokeTest) createKargoInstance(t require.TestingT, app *akuity.PlatformWebApp, instanceName, adminPassword, kargoVersion string) string {
	s.log.Info("Creating Kargo instance", "instance", instanceName)

	createKargoPage := app.GoToKargo().ClickCreateNewInstance().SetName(instanceName)
	createKargoPage = createKargoPage.ReplaceVersion(kargoVersion)
	kargoDetailsPage := createKargoPage.Create()

	require.NoError(t, kargoDetailsPage.Settings().Accounts().ToggleAdmin().SetPassword(adminPassword).Save())
	app.GoToKargoDetails(instanceName)

	require.NoError(t, utils.WaitFor("kargo instance to be healthy", s.timeout, s.log, func(ctx context.Context) (bool, string, error) {
		status := kargoDetailsPage.HealthStatus()
		return status == "Healthy", fmt.Sprintf("instance status is %s", status), nil
	}))

	return kargoDetailsPage.KargoURL()
}

func (s *smokeTest) checkInstanceExists(token, orgName, instanceName string) (bool, error) {
	s.log.Info("Checking instance exists", "instance", instanceName)
	gwc := gwoption.NewClient(s.rootURL, false)
	argocdClient := argocdv1.NewArgoCDServiceGatewayClient(gwc)
	orgClient := organizationv1.NewOrganizationServiceGatewayClient(gwc)
	cred := accesscontrol.NewUserCredential(token)
	ctx := httpctx.SetAuthorizationHeader(context.Background(), cred.Scheme(), cred.Credential())
	org, err := orgClient.GetOrganization(ctx, &organizationv1.GetOrganizationRequest{Id: orgName, IdType: idv1.Type_NAME})
	if err != nil {
		return false, err
	}
	instance, err := argocdClient.GetInstance(ctx, &argocdv1.GetInstanceRequest{OrganizationId: org.Organization.Id, IdType: idv1.Type_NAME, Id: instanceName})
	if err != nil {
		if strings.Contains(err.Error(), "NotFound") || strings.Contains(err.Error(), "PermissionDenied") {
			return false, nil
		}
		return false, err
	}
	return instance != nil, nil
}

func (s *smokeTest) checkKargoInstanceExists(token, orgName, kargoInstanceName string) (bool, *kargov1.KargoInstance, error) {
	s.log.Info("Checking Kargo instance exists", "kargoInstance", kargoInstanceName)
	gwc := gwoption.NewClient(s.rootURL, false)
	kargoClient := kargov1.NewKargoServiceGatewayClient(gwc)
	orgClient := organizationv1.NewOrganizationServiceGatewayClient(gwc)
	cred := accesscontrol.NewUserCredential(token)
	ctx := httpctx.SetAuthorizationHeader(context.Background(), cred.Scheme(), cred.Credential())
	org, err := orgClient.GetOrganization(ctx, &organizationv1.GetOrganizationRequest{Id: orgName, IdType: idv1.Type_NAME})
	if err != nil {
		return false, nil, err
	}
	kargoInstance, err := kargoClient.GetKargoInstance(ctx, &kargov1.GetKargoInstanceRequest{
		OrganizationId: org.Organization.Id,
		Name:           kargoInstanceName,
	})
	if err != nil {
		if strings.Contains(err.Error(), "NotFound") || strings.Contains(err.Error(), "PermissionDenied") {
			return false, nil, nil
		}
		return false, nil, err
	}
	return kargoInstance != nil, kargoInstance.Instance, nil
}

func (s *smokeTest) getInstanceDetails(t require.TestingT, app *akuity.PlatformWebApp, orgName, instanceName string) (string, string) {
	s.log.Info("Getting instance details", "instance", instanceName)
	detailsPage := app.GoToArgoCDDetails(orgName, instanceName)
	instanceID := detailsPage.InstanceID()

	require.NoError(t, utils.WaitFor("instance is healthy", s.timeout, s.log, func(ctx context.Context) (bool, string, error) {
		status := detailsPage.HealthStatus()
		return status == "Healthy", fmt.Sprintf("instance status is %s", status), nil
	}))

	return detailsPage.ArgoCDURL(), instanceID
}

func (s *smokeTest) getKargoInstanceDetails(t require.TestingT, app *akuity.PlatformWebApp, kargoInstanceName string) string {
	s.log.Info("Getting Kargo instance details", "kargoInstance", kargoInstanceName)
	detailsPage := app.GoToKargoDetails(kargoInstanceName)
	require.NoError(t, utils.WaitFor("kargo instance is healthy", s.timeout, s.log, func(ctx context.Context) (bool, string, error) {
		status := detailsPage.HealthStatus()
		return status == "Healthy", fmt.Sprintf("kargo instance status is %s", status), nil
	}))

	return detailsPage.KargoURL()
}

// cleanUpEnv cleans up the environment by deleting agents, instances, and Kargo instances.
func (s *smokeTest) cleanUpEnv(t require.TestingT, app *akuity.PlatformWebApp, token, orgName, instanceName, kargoInstanceName string, kargoEnabled bool) error {
	s.log.Info("Cleaning up environment", "instance", instanceName, "kargoInstance", kargoInstanceName)

	s.log.Info("Deleting instance", "instance", instanceName)
	gwc := gwoption.NewClient(s.rootURL, false)
	argocdClient := argocdv1.NewArgoCDServiceGatewayClient(gwc)
	orgClient := organizationv1.NewOrganizationServiceGatewayClient(gwc)
	cred := accesscontrol.NewUserCredential(token)
	ctx := httpctx.SetAuthorizationHeader(context.Background(), cred.Scheme(), cred.Credential())
	org, err := orgClient.GetOrganization(ctx, &organizationv1.GetOrganizationRequest{Id: orgName, IdType: idv1.Type_NAME})
	if err != nil {
		return err
	}

	kargoExists, kargoInstance, err := s.checkKargoInstanceExists(token, orgName, kargoInstanceName)
	if err != nil {
		return err
	}

	argoCDExists := true
	instance, err := argocdClient.GetInstance(ctx, &argocdv1.GetInstanceRequest{OrganizationId: org.Organization.Id, IdType: idv1.Type_NAME, Id: instanceName})
	if err != nil {
		if strings.Contains(err.Error(), "NotFound") || strings.Contains(err.Error(), "PermissionDenied") {
			argoCDExists = false
			if !kargoExists {
				s.log.Info("skip delete, no instance found", "instance", instanceName)
				return nil
			}
		} else {
			return err
		}
	}

	if argoCDExists {
		if err := s.deleteAgents(app, org.Organization.Id, instanceName); err != nil {
			return err
		}
	}
	if kargoExists && kargoEnabled {
		if err := s.deleteKargoAgents(t, ctx, app, org.Organization, kargoInstance); err != nil {
			return err
		}
	}

	if argoCDExists {
		if _, err := argocdClient.DeleteInstance(ctx, &argocdv1.DeleteInstanceRequest{Id: instance.Instance.Id, OrganizationId: org.Organization.Id}); err != nil {
			return err
		}
	}

	if err := utils.WaitFor("argo cd instance to be deleted", s.timeout, s.log, func(_ context.Context) (bool, string, error) {
		instance, err := argocdClient.GetInstance(ctx, &argocdv1.GetInstanceRequest{OrganizationId: org.Organization.Id, IdType: idv1.Type_NAME, Id: instanceName})
		if err != nil {
			if strings.Contains(err.Error(), "NotFound") || strings.Contains(err.Error(), "PermissionDenied") {
				return true, "", nil
			}
			return false, "", err
		}
		return false, fmt.Sprintf("%s remained", instance.Instance.Name), nil
	}); err != nil {
		return err
	}

	if !kargoEnabled {
		s.log.Info("Skipping Kargo instance deletion as Kargo is not enabled")
		return nil
	}

	if err := s.deleteKargoInstance(token, orgName, kargoInstanceName); err != nil {
		return err
	}

	return nil
}

func (s *smokeTest) deleteInstance(token, orgName, instanceName string) error {
	s.log.Info("Deleting instance", "instance", instanceName)
	gwc := gwoption.NewClient(s.rootURL, false)
	argocdClient := argocdv1.NewArgoCDServiceGatewayClient(gwc)
	orgClient := organizationv1.NewOrganizationServiceGatewayClient(gwc)
	cred := accesscontrol.NewUserCredential(token)
	ctx := httpctx.SetAuthorizationHeader(context.Background(), cred.Scheme(), cred.Credential())
	org, err := orgClient.GetOrganization(ctx, &organizationv1.GetOrganizationRequest{Id: orgName, IdType: idv1.Type_NAME})
	if err != nil {
		return err
	}
	instance, err := argocdClient.GetInstance(ctx, &argocdv1.GetInstanceRequest{OrganizationId: org.Organization.Id, IdType: idv1.Type_NAME, Id: instanceName})
	if err != nil {
		if strings.Contains(err.Error(), "NotFound") || strings.Contains(err.Error(), "PermissionDenied") {
			s.log.Info("skip delete, no instance found", "instance", instanceName)
			return nil
		}
		return err
	}

	if _, err := argocdClient.DeleteInstance(ctx, &argocdv1.DeleteInstanceRequest{Id: instance.Instance.Id, OrganizationId: org.Organization.Id}); err != nil {
		return err
	}

	return utils.WaitFor("argo cd instance to be deleted", s.timeout, s.log, func(_ context.Context) (bool, string, error) {
		instance, err := argocdClient.GetInstance(ctx, &argocdv1.GetInstanceRequest{OrganizationId: org.Organization.Id, IdType: idv1.Type_NAME, Id: instanceName})
		if err != nil {
			if strings.Contains(err.Error(), "NotFound") || strings.Contains(err.Error(), "PermissionDenied") {
				return true, "", nil
			}
			return false, "", err
		}
		return false, fmt.Sprintf("%s remained", instance.Instance.Name), nil
	})
}

func (s *smokeTest) deleteKargoInstance(token, orgName, kargoInstanceName string) error {
	s.log.Info("Deleting Kargo instance", "instance", kargoInstanceName)
	gwc := gwoption.NewClient(s.rootURL, false)
	kargoClient := kargov1.NewKargoServiceGatewayClient(gwc)
	orgClient := organizationv1.NewOrganizationServiceGatewayClient(gwc)
	cred := accesscontrol.NewUserCredential(token)
	ctx := httpctx.SetAuthorizationHeader(context.Background(), cred.Scheme(), cred.Credential())
	org, err := orgClient.GetOrganization(ctx, &organizationv1.GetOrganizationRequest{Id: orgName, IdType: idv1.Type_NAME})
	if err != nil {
		return err
	}
	kargoInstance, err := kargoClient.GetKargoInstance(ctx, &kargov1.GetKargoInstanceRequest{
		OrganizationId: org.Organization.Id,
		Name:           kargoInstanceName,
	})
	if err != nil {
		if strings.Contains(err.Error(), "NotFound") || strings.Contains(err.Error(), "PermissionDenied") {
			s.log.Info("skip delete, no kargo instance found", "kargo instance", kargoInstanceName)
			return nil
		}
		return err
	}

	if _, err := kargoClient.DeleteInstance(ctx, &kargov1.DeleteInstanceRequest{Id: kargoInstance.Instance.Id, OrganizationId: org.Organization.Id}); err != nil {
		return err
	}

	return utils.WaitFor("kargo instance to be deleted", s.timeout, s.log, func(_ context.Context) (bool, string, error) {
		kargoInstance, err := kargoClient.GetKargoInstance(ctx, &kargov1.GetKargoInstanceRequest{OrganizationId: org.Organization.Id, Name: kargoInstanceName})
		if err != nil {
			if strings.Contains(err.Error(), "NotFound") || strings.Contains(err.Error(), "PermissionDenied") {
				return true, "", nil
			}
			return false, "", err
		}
		return false, fmt.Sprintf("%s remained", kargoInstance.Instance.Name), nil
	})
}

func (s *smokeTest) upgradeInstance(t require.TestingT, app *akuity.PlatformWebApp, orgName, instanceName, agentName string) string {
	s.log.Info("Upgrading instance", "instance", instanceName)
	detailsPage := app.GoToArgoCDDetails(orgName, instanceName)
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	clusterSettigns := detailsPage.SelectClustersTab().ClickClusterSettings(agentName)
	clusterSettigns.AgentVersionDropdown().Open().SelectOptionByIndex(0)

	clusterSettigns.
		RemoveLabel("timestamp").
		AddLabel("timestamp", timestamp).
		RemoveAnnotation("timestamp").
		AddAnnotation("timestamp", timestamp).
		Close("Update Cluster")

	require.NoError(t, utils.WaitFor("agent to be healthy", s.timeout, s.log, func(ctx context.Context) (bool, string, error) {
		agentStatusIcon := detailsPage.ClusterStatusIcon(agentName)
		return agentStatusIcon == "heart", fmt.Sprintf("agent status icon is '%s'", agentStatusIcon), nil
	}))

	generalSettings := detailsPage.Settings().General()
	generalSettings.ToggleWebTerminal()
	generalSettings.ToggleArgoCDVersion()
	generalSettings.Save()

	detailsPage = app.GoToArgoCDDetails(orgName, instanceName)
	detailsPage.SelectSummaryTab()

	require.NoError(t, utils.WaitFor("instance to be healthy", s.timeout, s.log, func(ctx context.Context) (bool, string, error) {
		instanceStatus := detailsPage.HealthStatus()
		return instanceStatus == "Healthy", fmt.Sprintf("agent status icon is '%s'", instanceStatus), nil
	}))

	detailsPage.SelectClustersTab()
	require.NoError(t, utils.WaitFor("agent to be healthy", s.timeout, s.log, func(ctx context.Context) (bool, string, error) {
		agentStatusIcon := detailsPage.ClusterStatusIcon(agentName)
		return agentStatusIcon == "heart", fmt.Sprintf("instance status is '%s'", agentStatusIcon), nil
	}))

	detailsPage.SelectSummaryTab()

	return detailsPage.ArgoCDURL()
}

func (s *smokeTest) upgradeKargoInstance(t require.TestingT, app *akuity.PlatformWebApp, orgName, kargoInstanceName, adminPassword string) string {
	s.log.Info("Upgrading Kargo instance", "instance", kargoInstanceName)
	kargoDetailsPage := app.GoToKargoDetails(kargoInstanceName)
	agentSettings := kargoDetailsPage.Agents().ClickAgentSettings("test2")
	agentSettings.KargoAgentVersionDropdown().Open().SelectOptionByIndex(0)
	agentSettings.Close("Save")

	require.NoError(t, utils.WaitFor("kargo agent to be healthy", s.timeout, s.log, func(ctx context.Context) (bool, string, error) {
		statusIcon := kargoDetailsPage.AgentStatusIcon("test1")
		return statusIcon == "heart", fmt.Sprintf("kargo agent status icon is '%s'", statusIcon), nil
	}))
	require.NoError(t, utils.WaitFor("kargo agent to be healthy", s.timeout, s.log, func(ctx context.Context) (bool, string, error) {
		statusIcon := kargoDetailsPage.AgentStatusIcon("test2")
		return statusIcon == "heart", fmt.Sprintf("kargo agent status icon is '%s'", statusIcon), nil
	}))

	require.NoError(t, utils.WaitFor("kargo instance to be healthy", s.timeout, s.log, func(ctx context.Context) (bool, string, error) {
		status := kargoDetailsPage.HealthStatus()
		return status == "Healthy", fmt.Sprintf("kargo instance status is %s", status), nil
	}))

	kargoDetailsPage.Agents()

	return kargoDetailsPage.KargoURL()
}

func (s *smokeTest) installAgent(t require.TestingT, app *akuity.PlatformWebApp, orgName, instanceName, agentName string) {
	s.log.Info("Installing agent", "agent", agentName)

	detailsPage := app.GoToArgoCDDetails(orgName, instanceName).SelectClustersTab()

	var installAgentModal *pages.AgentModal
	if !slices.Contains(detailsPage.AgentNames(), agentName) {
		installAgentModal = detailsPage.
			ClickConnectCluster().
			SetName(agentName).
			ClickAdvancedSettings().
			SetNamespace(s.config.appNS).
			SetNsScoped(s.config.nsScopedAgent).
			SelectCustomizationTab().
			SetKustomization(fmt.Sprintf(agentKustomization, s.config.agentNS, s.config.agentNS)).
			ClickConnect()
	} else {
		installAgentModal = detailsPage.ClickInstallAgent(agentName)
	}

	var installCommand string
	require.NoError(t, utils.WaitFor("agent manifests generated", s.timeout, s.log, func(ctx context.Context) (bool, string, error) {
		var ok bool
		installCommand, ok = installAgentModal.AgentCommand("kubectl apply -f", false)
		return ok, "manifests are not generated yet", nil
	}))
	installAgentModal.Close("Done")
	require.NoError(t, utils.RunBash(installCommand), installCommand)
	s.createPullSecrets(t)

	require.NoError(t, utils.WaitFor("agent to be healthy", s.timeout, s.log, func(ctx context.Context) (bool, string, error) {
		statusIcon := detailsPage.ClusterStatusIcon(agentName)
		return statusIcon == "heart", fmt.Sprintf("status icon is '%s'", statusIcon), nil
	}))
}

func (s *smokeTest) connectArgoAgentWithKargoInstance(t require.TestingT, app *akuity.PlatformWebApp, orgName, instanceName, kargoInstanceName string) {
	s.log.Info("Connect Argo CD agent with Kargo instance", "kargoInstance", kargoInstanceName)

	detailsPage := app.GoToArgoCDDetails(orgName, instanceName)
	detailsPage.SelectClustersTab()

	if !slices.Contains(detailsPage.AgentNames(), "kargo") {
		detailsPage.SelectClustersTab().ClickIntegrate().SetName("kargo").SelectKargoInstance(kargoInstanceName).ClickConnectKargo()
	}

	require.True(t, slices.Contains(detailsPage.AgentNames(), "kargo"), "Kargo agent is not connected")
}

func (s *smokeTest) installRemoteKargoAgent(t require.TestingT, app *akuity.PlatformWebApp, orgName, instanceName, kargoInstanceName string) {
	s.log.Info("Installing Remote Kargo agent", "agent", "test1")

	kargoDetailsPage := app.GoToKargoDetails(kargoInstanceName)
	if !slices.Contains(kargoDetailsPage.AgentNames(), "test1") {
		kargoDetailsPage.Agents().ClickRegisterAgent().SetName("test1").SelectAkuityManaged(instanceName).ClickKargoConnect()
	}

	require.NoError(t, utils.WaitFor("agents to be healthy", s.timeout, s.log, func(ctx context.Context) (bool, string, error) {
		statusIcon := kargoDetailsPage.AgentStatusIcon("test1")
		return statusIcon == "heart", fmt.Sprintf("status icon is '%s'", statusIcon), nil
	}))
}

func (s *smokeTest) installSelfHostedKargoAgent(t require.TestingT, app *akuity.PlatformWebApp, orgName, instanceName, kargoInstanceName, argoInstanceID string) {
	s.log.Info("Installing Self-Hosted Kargo agent", "agent", "test2")

	kargoDetailsPage := app.GoToKargoDetails(kargoInstanceName)

	var installAgentModal *pages.AgentModal
	if !slices.Contains(kargoDetailsPage.AgentNames(), "test2") {
		installAgentModal = kargoDetailsPage.Agents().
			ClickRegisterAgent().
			SetName("test2").
			ToggleSelfHostedKargoAgent().
			SelectAkuityManaged(instanceName).
			ToggleDefaultShard("false").
			ToggleAdvancedSettings().
			SetNamespace(s.config.agentNS).
			ClickKargoConnect().
			ClickInstallAgent("test2")
	} else {
		installAgentModal = kargoDetailsPage.Agents().ClickInstallAgent("test2")
	}

	var installCommand string
	require.NoError(t, utils.WaitFor("agent manifests generated", s.timeout, s.log, func(ctx context.Context) (bool, string, error) {
		var ok bool
		installCommand, ok = installAgentModal.AgentCommand("kubectl apply -f", false)
		return ok, "manifests are not generated yet", nil
	}))
	installAgentModal.Close("Done")

	require.NoError(t, utils.RunCLI("bash", "-c", installCommand))
	s.createPullSecrets(t)

	require.NoError(t, utils.WaitFor("agents to be healthy", s.timeout, s.log, func(ctx context.Context) (bool, string, error) {
		statusIcon := kargoDetailsPage.AgentStatusIcon("test2")
		return statusIcon == "heart", fmt.Sprintf("status icon is '%s'", statusIcon), nil
	}))
}

func (s *smokeTest) deleteApps(argocdApp *argocd.ArgoCDWebApp, cascade bool, excludeNames ...string) {
	done := components.WaitForSuccessfulResponse(s.page, components.ArgoCDAppsRequestURL)
	applicationsList := argocdApp.GoToAppListPage()
	<-done

	for _, appName := range sets.NewString(applicationsList.AppNames()...).List() {
		if slices.Contains(excludeNames, appName) {
			s.log.Info("Skipping app deletion", "appName", appName)
			continue
		}
		s.log.Info("Deleting app", "appName", appName)
		applicationsList.DeleteApp(appName, cascade, false)
	}
}

func (s *smokeTest) deleteAgents(app *akuity.PlatformWebApp, orgName, instanceName string) error {
	detailsPage := app.GoToArgoCDDetails(orgName, instanceName).SelectClustersTab()
	s.log.Info("Deleting all agents", "instanceName", instanceName, "orgName", orgName)
	for _, agentName := range detailsPage.AgentNames() {
		s.log.Info("Deleting agent", "agentName", agentName)
		detailsPage.ClickDeleteAgent(agentName).Close("Confirm")
		err := utils.WaitFor("agent to be deleted", s.timeout, s.log, func(ctx context.Context) (bool, string, error) {
			agentExists, err := detailsPage.AgentExists(agentName)
			if err != nil {
				return false, "", err
			}
			if !agentExists {
				return true, "", nil
			}
			return false, fmt.Sprintf("agent %s still exists", agentName), nil
		})
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *smokeTest) deleteKargoAgents(t require.TestingT, ctx context.Context, app *akuity.PlatformWebApp, org *organizationv1.Organization, kargo *kargov1.KargoInstance) error {
	kargoDetailsPage := app.GoToKargoDetails(kargo.Name)
	s.log.Info("Deleting all Kargo agents", "kargoInstanceName", kargo.Name, "orgName", org.Name)

	gwc := gwoption.NewClient(s.rootURL, false)
	kargoClient := kargov1.NewKargoServiceGatewayClient(gwc)
	agents, err := kargoClient.ListKargoInstanceAgents(ctx, &kargov1.ListKargoInstanceAgentsRequest{
		OrganizationId: org.Id,
		InstanceId:     kargo.Id,
	})
	if err != nil {
		return fmt.Errorf("failed to list Kargo agents: %w", err)
	}

	for _, agent := range agents.Agents {
		if err := kargoDetailsPage.Settings().General().ToggleDefaultShard(false).Save(); err != nil {
			return err
		}
		kargoDetailsPage = app.GoToKargoDetails(kargo.Name)
		if agent.Name == "test2" {
			deleteAgentModal := kargoDetailsPage.Agents().Delete(agent.Name)
			deleteAgentModal.Confirm()
		} else {
			kargoDetailsPage.Agents().Delete(agent.Name).ConfirmDelete()
		}

		err := utils.WaitFor("kargo agent to be deleted", s.timeout, s.log, func(ctx context.Context) (bool, string, error) {
			agentExists, err := kargoDetailsPage.AgentExists(agent.Name)
			if err != nil {
				return false, "", err
			}
			if !agentExists {
				return true, "", nil
			}
			return false, fmt.Sprintf("kargo agent %s still exists", agent.Name), nil
		})
		if err != nil {
			return err
		}

	}
	return nil
}

func (s *smokeTest) deployApp(t require.TestingT, argocdApp *argocd.ArgoCDWebApp, agentName, appName string) {
	// Delete old applications if any without deleting resources (cascade=false)
	s.deleteApps(argocdApp, false)

	s.log.Info("Deploying app", "appName", appName)

	yamlStr := `
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: %s
spec:
  destination:
    namespace: %s
    server: http://cluster-%s:8001
  source:
    path: helm-guestbook
    repoURL: https://github.com/argoproj/argocd-example-apps.git
    targetRevision: HEAD
    helm:
      parameters:
        - name: replicaCount
          value: "0"
  project: default
`
	require.NoError(t, utils.WaitFor("app to be created", appCreationTimeout, s.log, func(ctx context.Context) (res bool, message string, err error) {
		// Check if the app finally detected by Argo CD
		if err := argocdApp.EnsureAppExists(appName); err == nil {
			return true, "", nil
		}

		if err := argocdApp.GoToAppListPage().
			CreateApp().
			SetYaml(fmt.Sprintf(yamlStr, appName, s.config.appNS, agentName)).
			Create(); err != nil {
			return false, err.Error(), nil
		}
		time.Sleep(5 * time.Second) // wait for app creation before rechecking

		// application is detected by Argo CD with delay
		return false, "waiting for app to be created", nil
	}))

	defer func() { s.deleteApps(argocdApp, true) }()

	require.NoError(t, utils.WaitFor("app to be available and reconciled", s.timeout, s.log, func(ctx context.Context) (bool, string, error) {
		status := argocdApp.GoToAppDetails(appName).HealthStatus()
		return status != "Unknown", fmt.Sprintf("App %s status is %s", appName, status), nil
	}))

	appDetails := argocdApp.GoToAppDetails(appName)
	require.NoError(t, utils.WaitFor("app to start syncing", s.timeout, s.log, func(ctx context.Context) (bool, string, error) {
		syncPanel := appDetails.Sync()
		time.Sleep(5 * time.Second)
		if syncPanel.HashNoResourceSelectedError() {
			syncPanel.Cancel()
			return false, "No resources selected", nil
		}
		syncPanel.Synchronize()
		time.Sleep(time.Second)
		status := appDetails.LastSyncStatus()
		return status == "Sync OK" || status == "Syncing", fmt.Sprintf("App %s status is %s", appName, status), nil
	}))

	require.NoError(t, utils.WaitFor("app to be synced and healthy", s.timeout, s.log, func(ctx context.Context) (bool, string, error) {
		status := appDetails.HealthStatus()
		return status == "Healthy", fmt.Sprintf("App %s status is %s", appName, status), nil
	}))
}

func (s *smokeTest) deployKargoApp(t require.TestingT, argocdApp *argocd.ArgoCDWebApp) {
	s.log.Info("Checking if repo is connected in ArgoCD")
	if !argocdApp.GoToSettingsRepoPage().CheckConnectedRepo("https://github.com/akuityio/kargo-saas-e2e-test") {
		require.NoError(t,
			argocdApp.GoToSettingsRepoPage().
				ConnectRepo().
				ChooseHTTPS().
				SetProject("default").
				SetRepoURL("https://github.com/akuityio/kargo-saas-e2e-test").
				SetUsername().
				SetGitHubSecret().
				Connect())
		require.True(t, argocdApp.GoToSettingsRepoPage().RetryCredentials("https://github.com/akuityio/kargo-saas-e2e-test").CheckConnectedRepo("https://github.com/akuityio/kargo-saas-e2e-test"))
	}

	s.log.Info("Deploying kargo-bootstrap app in ArgoCD")
	kargoClusterURL := "https://cluster-kargo:6445"
	require.NoError(t,
		argocdApp.GoToAppListPage().
			CreateApp().
			SetName("kargo-bootstrap").
			SetProject("default").
			SetRepoURL("https://github.com/akuityio/kargo-saas-e2e-test").
			SetPath("kargo-bootstrap-smoketests").
			SetDestClusterURL(kargoClusterURL).
			Create())

	require.NoError(t, argocdApp.WaitForAppToLoad("kargo-bootstrap"))
	appDetails := argocdApp.GoToAppDetails("kargo-bootstrap")

	require.NoError(t, utils.WaitFor("app to be healthy", s.timeout, s.log, func(ctx context.Context) (bool, string, error) {
		status := appDetails.HealthStatus()
		return status == "Healthy", fmt.Sprintf("app status is %s", status), nil
	}))

	require.NoError(t, utils.WaitFor("sync status to be out of sync", s.timeout, s.log, func(ctx context.Context) (bool, string, error) {
		status := appDetails.SyncStatus()
		return status == "OutOfSync", fmt.Sprintf("sync status is %s", status), nil
	}))

	appDetails.Sync().Synchronize()

	require.NoError(t, utils.WaitFor("sync status to be synced", s.timeout, s.log, func(ctx context.Context) (bool, string, error) {
		status := appDetails.SyncStatus()
		done := status == "Synced"
		if !done {
			appDetails.Refresh()
		}
		return done, fmt.Sprintf("sync status is %s", status), nil
	}))

	require.NoError(t, utils.WaitFor("app to be healthy", s.timeout, s.log, func(ctx context.Context) (bool, string, error) {
		status := appDetails.HealthStatus()
		return status == "Healthy", fmt.Sprintf("app status is %s", status), nil
	}))
}

func (s *smokeTest) deployArgoApp(t require.TestingT, argocdApp *argocd.ArgoCDWebApp, agentName, appName string) {
	s.log.Info("Deploying argocd-bootstrap app")

	yamlStr := `apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: argocd-bootstrap
spec:
  destination:
    namespace: argocd
    server: https://kubernetes.default.svc
  source:
    path: argocd-app-of-apps
    repoURL: https://github.com/akuityio/kargo-saas-e2e-test
    targetRevision: HEAD
    helm:
      values: |-
        destinationServer: "http://cluster-%s:8001"
        destinationNamespace: "%s"
  sources: []
  project: default
`
	require.NoError(t, argocdApp.GoToAppListPage().CreateApp().SetYaml(fmt.Sprintf(yamlStr, agentName, s.config.appNS)).Create())
	time.Sleep(7 * time.Second)

	require.NoError(t, argocdApp.WaitForAppToLoad("argocd-bootstrap"))
	appDetails := argocdApp.GoToAppDetails("argocd-bootstrap")

	require.NoError(t, utils.WaitFor("app to be healthy", s.timeout, s.log, func(ctx context.Context) (bool, string, error) {
		status := appDetails.HealthStatus()
		return status == "Healthy", fmt.Sprintf("app status is %s", status), nil
	}))

	require.NoError(t, utils.WaitFor("sync status to be out of sync", s.timeout, s.log, func(ctx context.Context) (bool, string, error) {
		status := appDetails.SyncStatus()
		return status == "OutOfSync", fmt.Sprintf("sync status is %s", status), nil
	}))

	appDetails.Sync().Synchronize()

	require.NoError(t, utils.WaitFor("sync status to be synced", s.timeout, s.log, func(ctx context.Context) (bool, string, error) {
		status := appDetails.SyncStatus()
		done := status == "Synced"
		if !done {
			appDetails.Refresh()
		}
		return done, fmt.Sprintf("sync status is %s", status), nil
	}))
}

func (s *smokeTest) promoteAndVerifyWithKargo(t require.TestingT, app *akuity.PlatformWebApp, argocdApp *argocd.ArgoCDWebApp, kargoApp *kargo.KargoWebApp) {
	s.log.Info("Promoting and verifying with Kargo")
	kargoDetails := kargoApp.GoToProjectDetails("kargo-proj")

	require.NoError(t,
		kargoDetails.Settings().Secrets().
			AddCredentials().
			SetName("akuity-test").
			SetRepoURL("https://github.com/akuityio/kargo-saas-e2e-test").
			SetGitHubUsername().
			SetGitHubSecret().
			Save())

	kargoDetails = kargoApp.GoToProjectDetails("kargo-proj")
	for i := 0; i < 10; i++ {
		kargoDetails.RefreshWarehouse()
		err := kargoDetails.CheckFreight()
		if err == nil {
			break
		}
	}

	kargoDetails.ClickPromoteStage("test1").ReviewPromotion().ConfirmPromotion()
	kargoDetails.OpenStageDetails("test1").PromotionsTab().VerifyPromotion("test1")

	appDetails := argocdApp.GoToAppDetails("kargo-saas-e2e-test")
	require.NoError(t, utils.WaitFor("app to be healthy", s.timeout, s.log, func(ctx context.Context) (bool, string, error) {
		status := appDetails.HealthStatus()
		return status == "Healthy", fmt.Sprintf("app status is %s", status), nil
	}))

	kargoApp.GoToProjectDetails("kargo-proj").OpenStageDetails("test1").VerificationsTab().VerifyVerification("test1")

	kargoApp.GoToProjectDetails("kargo-proj").ClickPromoteStage("test2").ReviewPromotion().ConfirmPromotion()
	kargoDetails.OpenStageDetails("test2").PromotionsTab().VerifyPromotion("test2").VerificationsTab().VerifyVerification("test2")
}
