package verify

import (
	"bytes"
	"fmt"
	"os"
	"regexp"
	"strings"
	"time"

	"github.com/go-logr/logr"
	"github.com/playwright-community/playwright-go"
	"github.com/spf13/cobra"
	"github.com/stretchr/testify/require"
	"github.com/tonglil/buflogr"

	"github.com/akuityio/akuity-platform/internal/cli"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/utils/consts"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	akpIO "github.com/akuityio/akuity-platform/internal/utils/io"
	"github.com/akuityio/akuity-platform/internal/utils/logging"
	"github.com/akuityio/akuity-platform/test/utils"
	"github.com/akuityio/akuity-platform/test/utils/ui"
	"github.com/akuityio/akuity-platform/test/utils/ui/akuity"
	"github.com/akuityio/akuity-platform/test/utils/ui/argocd"
	"github.com/akuityio/akuity-platform/test/utils/ui/kargo"
)

const (
	smoketestTimeout   = 6 * time.Minute
	playwrightTimeout  = 1 * time.Minute
	appCreationTimeout = 1 * time.Minute
	akuityNS           = consts.AkuityPlatformNamespace
	argoCdAdminUser    = "admin"

	defaultAkuityAgentNS      = "akuity"
	envNameEnvVariable        = "ENV_NAME"
	podNameEnvVariable        = "POD_NAME"
	hookUrlEnvVariable        = "SLACK_INCOMING_WEBHOOK"
	slackChannelIdEnvVariable = "SLACK_CHANNEL_ID"
	defaultInstanceName       = "canary-test-instance"
	defaultKargoInstanceName  = "canary-test-kargo-instance"

	// decreases amount of time agent needs to stay connected to considered healthy
	// timeout longer in production (50s) but acceptable for smoketest
	agentKustomization = `apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
patches:
  - patch: |-
      - op: replace
        path: /spec/template/spec/containers/0/args/1
        value: --health-time-delta=10s
    target:
      kind: Deployment
      name: akuity-agent
      namespace: %s
  - patch: |-
      apiVersion: apps/v1
      kind: Deployment
      metadata:
        name: akuity-agent
        namespace: %s
      spec:
        template:
          spec:
            imagePullSecrets:
            - name: akuity-pullsecrets
`
)

// "smoketest-prober-28415400-h4h2n" => "smoketest-prober-28415400"
var podToJobRegex = regexp.MustCompile("-[^-]+$")

// delete all workloads but keep the namespace around as we might have other custom resources in it
var deleteAllWorkloads = "kubectl delete daemonsets,replicasets,statefulsets,deployments,pods,jobs,secrets --all -n %s 2>/dev/null || echo \"%q Namespace is already cleaned\""

var logoutUrlRegex = regexp.MustCompile(`(.*)/u/login/identifier(.*)`)

type testFake struct{}

type smokeTest struct {
	log     logr.Logger
	page    playwright.Page
	rootURL string
	timeout time.Duration
	config  smokeTestInput
}

func (t testFake) Errorf(format string, args ...interface{}) {
	panic(fmt.Errorf(format, args...))
}

func (t testFake) FailNow() {
	t.Errorf("FailNow")
}

func NewSmokeTestCommand() *cobra.Command {
	var (
		orgName, instanceName, kargoInstanceName, agentName, browser, agentNS, appNS, kargoVersion                 string
		testCreate, testUpgrade, testKargo, testKargoUpgrade, reinstallAgents, nsScopedAgent, skipDevClusterVerify bool
	)
	cmd := &cobra.Command{
		Use: "smoketest URL",
		Run: func(cmd *cobra.Command, args []string) {
			if len(args) != 1 {
				cmd.HelpFunc()(cmd, args)
				os.Exit(1)
			}

			log, err := logging.NewLogger()
			cli.CheckErr(err)
			var buf bytes.Buffer
			log = logging.NewDuplicateLogger(log, buflogr.NewWithBuffer(&buf))
			if err := smoketestRun(log, smokeTestInput{
				url:                  args[0],
				orgName:              orgName,
				instanceName:         instanceName,
				kargoInstanceName:    kargoInstanceName,
				agentName:            agentName,
				browser:              browser,
				testCreate:           testCreate,
				testUpgrade:          testUpgrade,
				testKargo:            testKargo,
				testKargoUpgrade:     testKargoUpgrade,
				reinstallAgents:      reinstallAgents,
				agentNS:              agentNS,
				appNS:                appNS,
				kargoVersion:         kargoVersion,
				nsScopedAgent:        nsScopedAgent,
				skipDevClusterVerify: skipDevClusterVerify,
			}); err != nil {
				notifyError(&log, err, buf.String())
				cli.CheckErr(err)
			}
		},
	}

	cmd.Flags().StringVar(&orgName, "org", consts.SmoketestOrgName, "organization name")
	cmd.Flags().StringVar(&instanceName, "instance", defaultInstanceName, "instance name")
	cmd.Flags().StringVar(&kargoInstanceName, "kargo-instance", defaultKargoInstanceName, "Kargo instance name")
	cmd.Flags().StringVar(&agentName, "agent", "canary-test-agent", "agent name")
	cmd.Flags().StringVar(&browser, "browser", "chromium", "Browser name: firefox, webkit, chromium")
	cmd.Flags().StringVar(&kargoVersion, "kargo-version", "", "Kargo version: v1.6.0-ak.0, v1.6.0-ak.1")
	cmd.Flags().BoolVar(&testCreate, "argocd-instance-creation", true, "Verify argocd instance creation")
	cmd.Flags().BoolVar(&testUpgrade, "argocd-instance-upgrade", true, "Verify argocd instance upgrade")
	cmd.Flags().BoolVar(&testKargo, "kargo-instance-creation", true, "Verify Kargo creation")
	cmd.Flags().BoolVar(&testKargoUpgrade, "kargo-instance-upgrade", false, "Verify Kargo upgrade")
	cmd.Flags().BoolVar(&reinstallAgents, "reinstall-agents", true, "Re-install Akuity Agents for instance upgrade test")
	cmd.Flags().StringVar(&agentNS, "agent-ns", defaultAkuityAgentNS, "Akuity Agent namespace")
	cmd.Flags().StringVar(&appNS, "app-ns", defaultAkuityAgentNS, "Test app namespace")
	cmd.Flags().BoolVar(&nsScopedAgent, "ns-scoped-agent", false, "Akuity Agent is namespace-scoped")
	cmd.Flags().BoolVar(&skipDevClusterVerify, "skip-dev-cluster-verify", false, "Skip dev cluster verification")
	return cmd
}

// notifyError - posts a "Smoketest Prober failed" text message to Slack
func notifyError(log *logr.Logger, err error, logs string) {
	envName := strings.TrimSpace(os.Getenv(envNameEnvVariable))
	podName := strings.TrimSpace(os.Getenv(podNameEnvVariable))
	slackHookURL := strings.TrimSpace(os.Getenv(hookUrlEnvVariable))
	slackChannelID := strings.TrimSpace(os.Getenv(slackChannelIdEnvVariable))

	if envName == "" || podName == "" || slackHookURL == "" || slackChannelID == "" {
		log.Info(fmt.Sprintf("%q, %q, %q or %q env avariables are missing - unable to send a Slack mesage", envNameEnvVariable, podNameEnvVariable, hookUrlEnvVariable, slackChannelIdEnvVariable))
		return
	}

	// "smoketest-prober-28415400-h4h2n" => "smoketest-prober-28415400"
	jobName := podToJobRegex.ReplaceAllString(podName, "")
	// https://corp.cd.stage.akuity.io/applications/argocd/04-prod-aws.akuity-platform/akuity-platform/prober/logs?podName=&group=batch&kind=Job&name=smoketest-prober-28415340
	jobLogURL := fmt.Sprintf("https://corp.cd.stage.akuity.io/applications/argocd/04-%s-aws.akuity-platform/%s/prober/logs?group=batch&kind=Job&name=%s", envName, akuityNS, jobName)
	message := fmt.Sprintf("Smoketest Prober failed attempts, see `%s` <%s|Job logs>:\n```\n%s\n%v```", jobName, jobLogURL, logs, err)
	collectAndUploadFiles(log, slackHookURL, message, slackChannelID, map[string][]string{
		ui.VideosDirName(): {ui.VideoFileExtension},
		ui.TracesDirName(): {".zip"},
	})
}

func collectAndUploadFiles(log *logr.Logger, slackHookURL, message, slackChannelID string, files map[string][]string) {
	// File name => file content
	filesMap := map[string][]byte{}
	for parentDir, extensions := range files {
		parentDirFiles, err := ui.CollectFiles(parentDir, extensions)
		if err != nil {
			log.Error(err, fmt.Sprintf("failed to collect %s files in %q", extensions, parentDir))
		}
		for fileName, fileBytes := range parentDirFiles {
			filesMap[fileName] = fileBytes
		}
	}

	shared.PostSlackMessageAsText(log, slackHookURL, message)
	shared.UploadFiles(log, slackChannelID, filesMap)
}

type smokeTestInput struct {
	url                  string
	orgName              string
	instanceName         string
	kargoInstanceName    string
	agentName            string
	kargoVersion         string
	browser              string
	testCreate           bool
	testUpgrade          bool
	testKargo            bool
	testKargoUpgrade     bool
	reinstallAgents      bool
	agentNS              string
	appNS                string
	nsScopedAgent        bool
	skipDevClusterVerify bool
}

func smoketestRun(log logr.Logger, input smokeTestInput) (smoketestErr error) {
	defer func() {
		if r := recover(); r != nil {
			smoketestErr = fmt.Errorf("smoketestRun panic: %v", r)
		}
	}()

	rootURL := strings.TrimRight(input.url, "/")
	auth0Username := os.Getenv("AKUITY_USERNAME")
	auth0Password := os.Getenv("AKUITY_PASSWORD")
	if auth0Username == "" || auth0Password == "" {
		_, _ = fmt.Fprintln(os.Stderr, "AKUITY_USERNAME and AKUITY_PASSWORD must be set")
		os.Exit(-1)
	}

	t := &testFake{}

	if !input.skipDevClusterVerify {
		// Verifying the dev cluster
		if err := utils.RunBash(fmt.Sprintf("kubectl get service/postgres -n %s > /dev/null", akuityNS)); err != nil {
			return fmt.Errorf("service/postgres is missing in the \"%s\" namespace, is this a dev cluster? %w", akuityNS, err)
		}
	}

	log.Info("Smoketest started",
		"rootURL", rootURL,
		"orgName", input.orgName,
		"instanceName", input.instanceName,
		"agentName", input.agentName,
		"browser", input.browser,
		"testCreate", input.testCreate,
		"testUpgrade", input.testUpgrade,
		"kargoInstanceName", input.kargoInstanceName,
		"kargoVersion", input.kargoVersion,
		"testKargo", input.testKargo,
		"testKargoUpgrade", input.testKargoUpgrade,
		"reinstallAgents", input.reinstallAgents,
		"videosDir", ui.VideosDirName(),
		"tracesDir", ui.TracesDirName(),
	)

	pw, err := ui.NewPlaywright(&log, input.browser)
	if err != nil {
		return err
	}

	randomString, _ := database.RandomAlphabetString(8)
	videoFileName := fmt.Sprintf("video-run-%s", randomString)
	page, closer, err := ui.InitBrowserPage(pw, &log, ui.WithBrowser(input.browser), ui.WithVideoName(videoFileName))
	if err != nil {
		return err
	}
	page.SetDefaultTimeout(float64(playwrightTimeout.Milliseconds()))

	log.Info("Smoketest browser initialized")

	defer akpIO.Close(closer)
	defer func() {
		_ = page.Close()
	}()
	test := &smokeTest{page: page, log: log, rootURL: rootURL, timeout: smoketestTimeout, config: input}

	app := akuity.NewPlatformWebApp(rootURL, page)
	appName := fmt.Sprintf("test-app-%d", time.Now().Unix())

	authToken := test.auth0LoginWithValidation(t, app.Page(), auth0Username, auth0Password, rootURL)

	// We can't use a random admin password for a long-lived Argo CD testUpgrade instance
	adminPassword := auth0Password

	if input.reinstallAgents {
		require.NoError(t, utils.RunBash(fmt.Sprintf(deleteAllWorkloads, input.agentNS, input.agentNS)))
	}

	var argocdURL string
	var kargoURL string
	if input.testCreate {
		err := func() error {
			// Delete if an instance with the same name exists already.
			if err := test.cleanUpEnv(t, app, authToken, input.orgName, input.instanceName, input.kargoInstanceName, input.testKargo); err != nil {
				log.Error(err, "failed to clean up environment", "instance", input.instanceName,
					"kargoInstance", input.kargoInstanceName)
				return err
			}

			// if instance name is not default (kargo verification uses random name)
			// delete it after the test to avoid accumulating instances
			if input.instanceName != defaultInstanceName {
				defer func() {
					// CleanUp
					if err := test.deleteInstance(authToken, input.orgName, input.instanceName); err != nil {
						log.Error(err, "failed to delete instance", "instance", input.instanceName)
					}
				}()
			}

			argocdURL, _ = test.createInstance(t, app, input.instanceName, adminPassword)

			log.Info("==> Argo CD testCreate instance", "url", argocdURL)

			test.installAgent(t, app, input.orgName, input.instanceName, input.agentName)

			argocdApp := argocd.NewArgoCDWebApp(argocdURL, test.page)
			_ = argocdApp.GoToLogin().Login(argoCdAdminUser, adminPassword)

			test.deployApp(t, argocdApp, input.agentName, appName)

			// Reuse the same instance for Kargo test
			if input.testKargo {
				return nil
			}

			if err := test.deleteAgents(app, input.orgName, input.instanceName); err != nil {
				log.Error(err, "failed to delete agents", "instance", input.instanceName)
			}

			// CleanUp
			if err := test.deleteInstance(authToken, input.orgName, input.instanceName); err != nil {
				log.Error(err, "failed to delete instance", "instance", input.instanceName)
			}

			if input.agentNS != defaultAkuityAgentNS {
				// delete agent workloads if not default akuity ns
				if err := utils.RunBash(fmt.Sprintf(deleteAllWorkloads, input.agentNS, input.agentNS)); err != nil {
					log.Error(err, "failed to delete agent ns workloads", "namespace", input.agentNS)
				}
			}
			return nil
		}()
		if err != nil {
			return err
		}
	}

	if input.testUpgrade {
		err := func() error {
			input.instanceName = fmt.Sprintf("%s-upgrade", input.instanceName)
			instanceExists, err := test.checkInstanceExists(authToken, input.orgName, input.instanceName)
			if err != nil {
				return err
			}

			if instanceExists {
				argocdURL, _ = test.getInstanceDetails(t, app, input.orgName, input.instanceName)
			} else {
				argocdURL, _ = test.createInstance(t, app, input.instanceName, adminPassword)
			}

			log.Info("==> Argo CD testUpgrade instance", "url", argocdURL)

			test.installAgent(t, app, input.orgName, input.instanceName, input.agentName)

			argocdApp := argocd.NewArgoCDWebApp(argocdURL, test.page)
			_ = argocdApp.GoToLogin().Login(argoCdAdminUser, adminPassword)

			// Smoketests before upgrade
			test.deployApp(t, argocdApp, input.agentName, appName)

			// Smoketests after upgrade
			argocdURL = test.upgradeInstance(t, akuity.NewPlatformWebApp(rootURL, page), input.orgName, input.instanceName, input.agentName)
			argocdApp = argocd.NewArgoCDWebApp(argocdURL, test.page)
			_ = argocdApp.GoToLogin().Login(argoCdAdminUser, adminPassword)

			appName = fmt.Sprintf("test-app-%d", time.Now().Unix())
			test.deployApp(t, argocdApp, input.agentName, appName)

			if input.reinstallAgents {
				if err := test.deleteAgents(app, input.orgName, input.instanceName); err != nil {
					log.Error(err, "failed to delete agents", "instance", input.instanceName)
				}
			}
			if input.agentNS != defaultAkuityAgentNS {
				// delete agent workloads
				if err := utils.RunBash(fmt.Sprintf(deleteAllWorkloads, input.agentNS, input.agentNS)); err != nil {
					log.Error(err, "failed to delete agent ns workloads", "namespace", input.agentNS)
				}
			}
			return nil
		}()
		if err != nil {
			return err
		}
	}

	if input.testKargo {
		err := func() error {
			if err := testKargo(test, t, app, authToken, input, log, adminPassword, appName); err != nil {
				log.Error(err, "failed to run Kargo test")
				return err
			}

			log.Info("==> Kargo 'testKargo' completed successfully")

			// CleanUp
			if err := test.cleanUpEnv(t, app, authToken, input.orgName, input.instanceName, input.kargoInstanceName, input.testKargo); err != nil {
				log.Error(err, "failed to clean up environment", "instance", input.instanceName,
					"kargoInstance", input.kargoInstanceName)
				return err
			}

			if input.agentNS != defaultAkuityAgentNS {
				// delete agent workloads if not default akuity ns
				if err := utils.RunBash(fmt.Sprintf(deleteAllWorkloads, input.agentNS, input.agentNS)); err != nil {
					log.Error(err, "failed to delete agent ns workloads", "namespace", input.agentNS)
				}
			}

			return nil
		}()
		if err != nil {
			return err
		}
	}

	if input.testKargoUpgrade {
		err := func() error {
			// Pre Upgrade Checks
			input.instanceName = fmt.Sprintf("%s-upgrade", input.instanceName)
			input.kargoInstanceName = fmt.Sprintf("%s-upgrade", input.kargoInstanceName)

			instanceExists, err := test.checkInstanceExists(authToken, input.orgName, input.instanceName)
			if err != nil {
				return err
			}
			kargoInstanceExists, _, err := test.checkKargoInstanceExists(authToken, input.orgName, input.kargoInstanceName)
			if err != nil {
				return err
			}

			var argoInstanceID string
			if instanceExists {
				argocdURL, argoInstanceID = test.getInstanceDetails(t, app, input.orgName, input.instanceName)
			} else {
				argocdURL, argoInstanceID = test.createInstance(t, app, input.instanceName, adminPassword)
			}

			if kargoInstanceExists {
				kargoURL = test.getKargoInstanceDetails(t, app, input.kargoInstanceName)
			} else {
				kargoURL = test.createKargoInstance(t, app, input.kargoInstanceName, adminPassword, input.kargoVersion)
			}

			log.Info("==> Argo CD testUpgrade instance", "url", argocdURL)
			log.Info("==> Kargo testUpgrade instance", "url", kargoURL)

			test.installAgent(t, app, input.orgName, input.instanceName, input.agentName)

			test.installRemoteKargoAgent(t, app, input.orgName, input.instanceName, input.kargoInstanceName)
			test.installSelfHostedKargoAgent(t, app, input.orgName, input.instanceName, input.kargoInstanceName, argoInstanceID)

			test.connectArgoAgentWithKargoInstance(t, app, input.orgName, input.instanceName, input.kargoInstanceName)

			argocdApp := argocd.NewArgoCDWebApp(argocdURL, test.page)
			_ = argocdApp.GoToLogin().Login(argoCdAdminUser, adminPassword)

			test.deleteApps(argocdApp, true, "kargo-saas-e2e-test", "kargo-saas-e2e-test-2")

			// Upgrade Argo CD and Kargo instances
			log.Info("==> Upgrading Argo CD and Kargo instances")
			argocdURL = test.upgradeInstance(t, akuity.NewPlatformWebApp(rootURL, page), input.orgName, input.instanceName, input.agentName)
			kargoURL = test.upgradeKargoInstance(t, akuity.NewPlatformWebApp(rootURL, page), input.orgName, input.kargoInstanceName, adminPassword)

			argocdApp = argocd.NewArgoCDWebApp(argocdURL, test.page)
			_ = argocdApp.GoToLogin().Login(argoCdAdminUser, adminPassword)

			appName = fmt.Sprintf("argocd-bootstrap-%d", time.Now().Unix())
			test.deployKargoApp(t, argocdApp)
			test.deployArgoApp(t, argocdApp, input.agentName, appName)

			kargoApp := kargo.NewKargoWebApp(kargoURL, test.page)
			kargoApp.GoToLogin().Login(adminPassword)

			test.promoteAndVerifyWithKargo(t, app, argocdApp, kargoApp)
			app.GoToKargoDetails(input.kargoInstanceName)

			return nil
		}()
		if err != nil {
			return err
		}
	}

	test.logout(t, app)

	return nil
}

func testKargo(test *smokeTest, t *testFake, app *akuity.PlatformWebApp, authToken string, input smokeTestInput, log logr.Logger, adminPassword, appName string) error {
	instanceExists, err := test.checkInstanceExists(authToken, input.orgName, input.instanceName)
	if err != nil {
		return err
	}

	var argocdURL, argoInstanceID string
	if instanceExists {
		argocdURL, argoInstanceID = test.getInstanceDetails(t, app, input.orgName, input.instanceName)
	} else {
		argocdURL, argoInstanceID = test.createInstance(t, app, input.instanceName, adminPassword)
	}

	log.Info("==> Argo CD testKargo instance", "url", argocdURL)

	test.installAgent(t, app, input.orgName, input.instanceName, input.agentName)

	kargoInstanceExists, _, err := test.checkKargoInstanceExists(authToken, input.orgName, input.kargoInstanceName)
	if err != nil {
		return err
	}
	var kargoURL string
	if kargoInstanceExists {
		kargoURL = test.getKargoInstanceDetails(t, app, input.kargoInstanceName)
	} else {
		kargoURL = test.createKargoInstance(t, app, input.kargoInstanceName, adminPassword, input.kargoVersion)
	}

	log.Info("==> Kargo testKargo instance", "url", kargoURL)

	test.installRemoteKargoAgent(t, app, input.orgName, input.instanceName, input.kargoInstanceName)
	test.installSelfHostedKargoAgent(t, app, input.orgName, input.instanceName, input.kargoInstanceName, argoInstanceID)

	test.connectArgoAgentWithKargoInstance(t, app, input.orgName, input.instanceName, input.kargoInstanceName)

	argocdApp := argocd.NewArgoCDWebApp(argocdURL, test.page)
	_ = argocdApp.GoToLogin().Login(argoCdAdminUser, adminPassword)

	test.deployKargoApp(t, argocdApp)
	test.deployArgoApp(t, argocdApp, input.agentName, appName)

	kargoApp := kargo.NewKargoWebApp(kargoURL, test.page)
	kargoApp.GoToLogin().Login(adminPassword)

	test.promoteAndVerifyWithKargo(t, app, argocdApp, kargoApp)

	return nil
}

// createPullSecrets - creates secret/akuity-pullsecrets from akuityPullsecretsFile
// (created by Smoketest CronJob before the test starts - akuity-platform-deploy/../smoketest-cronjob.yaml)
func (s *smokeTest) createPullSecrets(t require.TestingT) {
	akuityPullsecretsFile := os.Getenv("AKUITY_PULLSECRETS")
	require.NotEmpty(t, akuityPullsecretsFile)
	require.NoError(t, utils.RunBash(fmt.Sprintf("kubectl delete secret/akuity-pullsecrets -n '%s' --ignore-not-found=true", s.config.agentNS)))
	require.NoError(t, utils.RunBash(fmt.Sprintf("kubectl create secret docker-registry akuity-pullsecrets -n '%s' --from-file=.dockerconfigjson='%s'", s.config.agentNS, akuityPullsecretsFile)))
	require.NoError(t, utils.RunBash(fmt.Sprintf("kubectl get    secret/akuity-pullsecrets -n '%s'", s.config.agentNS)))
}
