package sheets

import (
	"time"

	"github.com/spf13/cobra"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/akuityio/akuity-platform/internal/cli"
)

var sheetsCmd = &cobra.Command{
	Use:   "sheets",
	Short: "Google Sheets command",
	Run: func(cmd *cobra.Command, args []string) {
		cmd.HelpFunc()(cmd, args)
	},
}

func NewSheetsCommand() *cobra.Command {
	sheetsCmd.AddCommand(SyncFreeTrialUsers())
	return sheetsCmd
}

func SyncFreeTrialUsers() *cobra.Command {
	var (
		clientConfig        clientcmd.ClientConfig
		sheetId             string
		credentialsJsonFile string
	)

	cmd := &cobra.Command{
		Use:   "sync-free-trial-users",
		Short: "Fetch free trial users and write it to Google Sheet",
		Run: func(cmd *cobra.Command, args []string) {
			akpSheets, err := NewAKPUtilSheets(clientConfig, credentialsJsonFile, cmd.Context(), time.Minute)
			cli.CheckErr(err)
			defer func() { _ = akpSheets.Close() }()

			cli.CheckErr(akpSheets.SyncFreeTrialUsers(sheetId))
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().StringVar(&sheetId, "sheet-id", "", "Google Sheet ID to write users")
	cli.CheckErr(cmd.MarkFlagRequired("sheet-id"))
	cmd.Flags().StringVar(&credentialsJsonFile, "creds-filepath", "/credentials/credentials.json", "Google service account credentials file path")

	return cmd
}
