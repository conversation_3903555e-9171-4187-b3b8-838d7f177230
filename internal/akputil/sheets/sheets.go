package sheets

import (
	"context"
	"fmt"
	"time"

	"github.com/auth0/go-auth0/management"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/api/option"
	"google.golang.org/api/sheets/v4"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/akuityio/akuity-platform/internal/akputil/shared"
	"github.com/akuityio/akuity-platform/models/models"
)

type AKPUtilSheets struct {
	*shared.AKPUtil
	sheetsSvc       *sheets.Service
	auth0Management *management.Management
}

// Grafana free trial users query
const query = `
select
    akuity_user.email as user_email,
    org.creation_timestamp as free_trial_start,
    org.name as organization_name,
    count(distinct argo_cd_instance.id) as instances,
    count(distinct argo_cd_cluster.id) as clusters,
    COALESCE(argo_cd_instance.apps, 0) as applications,
    count(distinct kargo_instance.id) as kargo_instances,
    count(distinct kargo_agent.id) as kargo_agents,
    COALESCE(kargo_instance.stages, 0) as stages
from
    organization org
left join
    (
        select
            argo_cd_instance.*,
            coalesce(sum((argo_cd_instance.status_info->'applicationsStatus'->>'count')::int), 0) as apps
        from argo_cd_instance
        group by argo_cd_instance.id, argo_cd_instance.organization_owner
    ) as argo_cd_instance
    on argo_cd_instance.organization_owner = org.id
left join
    argo_cd_cluster
    on argo_cd_cluster.instance_id = argo_cd_instance.id
left join
    (
        select 
            kargo_instance.*,
            coalesce(sum((kargo_instance.status_info->'kargoStats'->>'stageCount')::int), 0) as stages
        from kargo_instance
        group by kargo_instance.id, kargo_instance.organization_owner
    ) as kargo_instance
    on kargo_instance.organization_owner = org.id
left join
    kargo_agent
    on kargo_agent.instance_id = kargo_instance.id
inner join
    organization_user
    on organization_user.organization_id = org.id
inner join
    akuity_user
    on akuity_user.id = organization_user.user_id
where
    org.org_status->>'trial' = 'true'
	AND (org.name NOT IN ('akuity','akuity-test-org','demo','terraform-provider-test','morey-tech','canary-test-org')) 
	AND (akuity_user.email NOT LIKE '%@akuity.io')
group by 
    org.id, 
    argo_cd_instance.apps, 
    kargo_instance.stages, 
    akuity_user.email,
    akuity_user.creation_timestamp
order by
    org.creation_timestamp`

func NewAKPUtilSheets(
	clientConfig clientcmd.ClientConfig,
	credsFile string,
	ctx context.Context,
	contextTimeout time.Duration,
) (*AKPUtilSheets, error) {
	sheetsSvc, err := sheets.NewService(ctx, option.WithCredentialsFile(credsFile))
	if err != nil {
		return nil, err
	}

	akpSheets := AKPUtilSheets{
		AKPUtil:   shared.NewAKPUtil(clientConfig, ctx, contextTimeout),
		sheetsSvc: sheetsSvc,
	}

	auth0Management, err := management.New(
		akpSheets.AKPUtil.Auth0Config.Portal().Domain,
		management.WithClientCredentialsAndAudience(
			ctx,
			akpSheets.AKPUtil.Auth0Config.Portal().ClientID,
			akpSheets.AKPUtil.Auth0Config.Portal().ClientSecret,
			akpSheets.AKPUtil.Auth0Config.Portal().ManagementAPIAudience,
		),
	)
	if err != nil {
		return nil, err
	}

	akpSheets.auth0Management = auth0Management

	return &akpSheets, nil
}

type FreeTrialUser struct {
	Email          string    `boil:"user_email"`
	FreeTrialStart null.Time `boil:"free_trial_start"`
	Organization   string    `boil:"organization_name"`
	Instances      int32     `boil:"instances"`
	Clusters       int32     `boil:"clusters"`
	Applications   int32     `boil:"applications"`
	KargoInstances int32     `boil:"kargo_instances"`
	KargoAgents    int32     `boil:"kargo_agents"`
	KargoStages    int32     `boil:"stages"`
}

func (a *AKPUtilSheets) SyncFreeTrialUsers(sheetId string) error {
	var freeTrialUsers []FreeTrialUser
	if err := models.NewQuery(qm.SQL(query)).Bind(
		a.Ctx, a.PortalDBPool.DB, &freeTrialUsers); err != nil {
		return err
	}

	a.Log.Info(fmt.Sprintf("%d free trial users found", len(freeTrialUsers)))

	if len(freeTrialUsers) == 0 {
		return nil
	}

	rangeData := "!A:J"

	var values [][]interface{}

	// generate header row
	header := []string{
		"email",
		"full_name",
		"free_trial_start",
		"organization_name",
		"instances",
		"clusters",
		"applications",
		"kargo_instances",
		"kargo_agents",
		"stages",
	}

	sheetHeader := []interface{}{}

	for _, headerName := range header {
		sheetHeader = append(sheetHeader, headerName)
	}

	values = append(values, sheetHeader)

	emails := make([]string, len(freeTrialUsers))

	for _, freeTrialUser := range freeTrialUsers {
		row := []interface{}{
			freeTrialUser.Email,
			"",
			freeTrialUser.FreeTrialStart.Time.Format(time.RFC3339),
			freeTrialUser.Organization,
			freeTrialUser.Instances,
			freeTrialUser.Clusters,
			freeTrialUser.Applications,
			freeTrialUser.KargoInstances,
			freeTrialUser.KargoAgents,
			freeTrialUser.KargoStages,
		}

		values = append(values, row)
		emails = append(emails, freeTrialUser.Email)
	}

	auth0MngmntUsers, err := a.getAuth0UserNameByEmail(emails)
	if err != nil {
		return err
	}

	emailsNameMap := map[string]string{}

	for _, user := range auth0MngmntUsers {
		if user != nil && user.Email != nil && user.Name != nil {
			emailsNameMap[*user.Email] = *user.Name
		}
	}

	for idx, row := range values[1:] {
		email := row[0].(string)

		if name, ok := emailsNameMap[email]; ok {
			values[idx+1][1] = name
		}
	}

	updateValueResponse, err := a.sheetsSvc.Spreadsheets.Values.Update(sheetId, rangeData, &sheets.ValueRange{
		Values: values,
	}).ValueInputOption("USER_ENTERED").Do()
	if err != nil {
		return err
	}

	a.Log.Info(fmt.Sprintf("%d rows updated", updateValueResponse.UpdatedRows))

	return nil
}

func (a *AKPUtilSheets) getAuth0UserNameByEmail(emails []string) ([]*management.User, error) {
	auth0MngmntUsers := []*management.User{}

	batchStart := 0
	batchSize := 50

	var batchEmails []string
	for batchStart < len(emails) {
		if batchStart+batchSize >= len(emails) {
			batchEmails = emails[batchStart:]
		} else {
			batchEmails = emails[batchStart : batchStart+batchSize]
		}

		usersList, err := a.auth0Management.User.List(a.Ctx, management.Query(auth0UserSearchQuery(batchEmails)))
		if err != nil {
			return nil, err
		}

		auth0MngmntUsers = append(auth0MngmntUsers, usersList.Users...)
		batchStart = batchStart + batchSize
	}

	return auth0MngmntUsers, nil
}

func auth0UserSearchQuery(emails []string) string {
	if len(emails) == 0 {
		return ""
	}

	inner := ""
	for i, email := range emails {
		if i == 0 {
			inner = fmt.Sprintf(`"%s"`, email)
			continue
		}
		inner = fmt.Sprintf(`%s OR "%s"`, inner, email)
	}

	return fmt.Sprintf("email:(%s)", inner)
}
