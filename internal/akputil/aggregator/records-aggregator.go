package aggregator

import (
	"context"
	"fmt"
	"time"

	"github.com/akuityio/akuity-platform/internal/akputil/shared"
	"github.com/akuityio/akuity-platform/internal/utils/database"

	_ "embed"
)

//go:embed audit_logs_aggregate.sql
var auditLogsAggregateQuery string

//go:embed sync_aggregate.sql
var syncOpsAggregateQuery string

type RecordsAggregator struct {
	*shared.AKPUtil
	aggregationWindow int
}

func NewRecordsAggregator(akputil *shared.AKPUtil, aggregationWindow int) *RecordsAggregator {
	return &RecordsAggregator{
		AKPUtil:           akputil,
		aggregationWindow: aggregationWindow,
	}
}

func (a *RecordsAggregator) AggregateAuditSyncPrevDay(ctx context.Context) error {
	now := time.Now().UTC()
	endTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.UTC)
	startTime := endTime.Add(-24 * time.Hour)
	return a.AggregateAuditSyncStartEndPeriod(ctx, startTime, endTime)
}

func (a *RecordsAggregator) AggregateAuditSyncStartEndPeriod(ctx context.Context, startTime, endTime time.Time) error {
	txDB, txBeginner := database.WithTxBeginner(a.PortalDBPool.DB)
	tx, err := txBeginner.Begin(ctx)
	if err != nil {
		return err
	}
	defer func() { _ = tx.Rollback() }()

	a.Log.Info("starting aggregation audit_logs", "start time", startTime, "end time", endTime, "aggregation window", a.aggregationWindow)
	res, err := txDB.ExecContext(ctx, auditLogsAggregateQuery, startTime, endTime, a.aggregationWindow)
	if err != nil {
		return fmt.Errorf("error executing audit_logs aggregation query: %w", err)
	}
	rowsAffected, err := res.RowsAffected()
	if err != nil {
		return err
	}
	a.Log.Info("audit_logs affected", "rows", rowsAffected)

	a.Log.Info("starting aggregation sync_operations", "start time", startTime, "end time", endTime, "aggregation window", a.aggregationWindow)
	res, err = txDB.ExecContext(ctx, syncOpsAggregateQuery, startTime, endTime, a.aggregationWindow)
	if err != nil {
		return fmt.Errorf("error executing sync_operations aggregation query: %w", err)
	}
	rowsAffected, err = res.RowsAffected()
	if err != nil {
		return err
	}
	a.Log.Info("sync_operations affected", "rows", rowsAffected)

	return tx.Commit()
}
