package image

import (
	"context"
	"encoding/json"
	"time"

	"github.com/regclient/regclient"
	"github.com/regclient/regclient/config"
	"github.com/regclient/regclient/types/manifest"
	"github.com/regclient/regclient/types/platform"
	"github.com/regclient/regclient/types/ref"
	"github.com/spf13/cobra"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/akuityio/akuity-platform/internal/akputil/shared"
	"github.com/akuityio/akuity-platform/internal/cli"
	"github.com/akuityio/akuity-platform/internal/utils/consts"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
)

const (
	pullSecretsName          = "akuity-pullsecrets"
	dockerImageAgeMetricName = "docker_image_age_days"
)

func NewImageCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "image",
		Short: "Docker images commands",
		Run: func(cmd *cobra.Command, args []string) {
			cmd.HelpFunc()(cmd, args)
		},
	}
	cmd.AddCommand(newImagesAgeCommand())
	return cmd
}

type ImgConfig struct {
	Created time.Time `json:"created"`
}

func newImagesAgeCommand() *cobra.Command {
	var (
		images         []string
		pushgatewayURL string
		timeout        time.Duration
		clientConfig   clientcmd.ClientConfig
		imageAgeMetric *misc.PrometheusGaugeMetric
	)

	cmd := &cobra.Command{
		Use:   "age",
		Short: "Prints the age of images.",
		Run: func(cmd *cobra.Command, args []string) {
			akpUtil := shared.NewAKPUtil(clientConfig, cmd.Context(), time.Minute)
			defer func() { _ = akpUtil.Close() }()

			// When isPushgatewayEnabled is false - we only print out images details
			isPushgatewayEnabled := pushgatewayURL != ""

			if isPushgatewayEnabled {
				imageAgeMetric = misc.NewPrometheusGaugeMetric(dockerImageAgeMetricName,
					"Docker image age (in days)",
					[]string{"image", "repo", "tag"})
			}

			// "quay.io/prometheus/prometheus:v2.54.0"
			for _, image := range images {
				// "quay.io/prometheus/prometheus", "v2.54.0", 615
				imageRepo, imageTag, imageAgeDays, err := readImageDetails(akpUtil, image, timeout)
				if err != nil {
					akpUtil.Log.Error(err, "failed to read image details", "image", image)
					continue
				}
				akpUtil.Log.Info("Image details",
					"image", image,
					"repo", imageRepo,
					"tag", imageTag,
					"ageDays", imageAgeDays)
				if isPushgatewayEnabled {
					imageAgeMetric.AddValue(float64(imageAgeDays), image, imageRepo, imageTag)
				}
			}

			if isPushgatewayEnabled {
				prometheusPayload, err := imageAgeMetric.PlainTextPayload()
				cli.CheckErr(err, misc.PutPayload(pushgatewayURL, prometheusPayload, timeout))
				akpUtil.Log.Info("Pushed payload to Prometheus Pushgateway",
					"pushgatewayURL", pushgatewayURL, "payload", string(prometheusPayload))
			}
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().StringArrayVar(&images, "image", nil, "Docker images to display their age")
	cmd.Flags().StringVar(&pushgatewayURL, "pushgatewayURL", "", "Prometheus Pushgateway URL to push age metrics to")
	cmd.Flags().DurationVar(&timeout, "timeout", 10*time.Second, "Timeout to fetch an image details and push to Prometheus Pushgateway")
	cli.CheckErr(cmd.MarkFlagRequired("image"))
	return cmd
}

// readImageDetails - retrieves the details (repo, tag, age in days) of the image specified
func readImageDetails(akpUtil *shared.AKPUtil, image string, imageTimeout time.Duration) (repo, tag string, ageInDays int, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), imageTimeout)
	defer cancel()

	regClient, err := authenticatedRegClient(akpUtil.Ctx, akpUtil.K8sClient,
		consts.AkuityPlatformNamespace, pullSecretsName)
	if err != nil {
		return "", "", 0, err
	}

	imageRef, err := ref.New(image)
	if err != nil {
		return "", "", 0, err
	}

	imageManifest, err := regClient.ManifestGet(ctx, imageRef)
	if err != nil {
		return "", "", 0, err
	}

	if imageManifest.IsList() {
		platformDesc, err := manifest.GetPlatformDesc(imageManifest, &platform.Platform{OS: "linux", Architecture: "amd64"})
		if err != nil {
			return "", "", 0, err
		}
		imageManifest, err = regClient.ManifestGet(ctx, imageRef.SetDigest(platformDesc.Digest.String()))
		if err != nil {
			return "", "", 0, err
		}

	}

	manifestConfig, err := imageManifest.(manifest.Imager).GetConfig()
	if err != nil {
		return "", "", 0, err
	}

	configBlob, err := regClient.BlobGet(ctx, imageRef, manifestConfig)
	if err != nil {
		return "", "", 0, err
	}

	configBody, err := configBlob.RawBody()
	if err != nil {
		return "", "", 0, err
	}
	var imgConfig ImgConfig
	if err := json.Unmarshal(configBody, &imgConfig); err != nil {
		return "", "", 0, err
	}

	return imageRef.Registry + "/" + imageRef.Repository,
		imageRef.Tag,
		int(time.Since(imgConfig.Created).Hours() / consts.Day.Hours()),
		nil
}

// authenticatedRegClient - retrieves [regclient.RegClient] authenticated based on the pullsecrets K8s Secret specified
func authenticatedRegClient(ctx context.Context, k8sClient *kubernetes.Clientset,
	pullSecretsNamespace, pullSecretsName string,
) (*regclient.RegClient, error) {
	pullsecrets, err := k8sClient.CoreV1().Secrets(pullSecretsNamespace).Get(ctx, pullSecretsName, v1.GetOptions{})
	if err != nil {
		return nil, err
	}

	var regclientOpts []regclient.Opt

	if dockerConfig, ok := pullsecrets.Data[".dockerconfigjson"]; ok {
		var dockerConfigMap map[string]any
		if err := json.Unmarshal(dockerConfig, &dockerConfigMap); err != nil {
			return nil, err
		}

		if auths, ok := dockerConfigMap["auths"]; ok {
			for repoKey, repoValue := range auths.(map[string]any) {
				if username, ok := repoValue.(map[string]any)["username"]; ok {
					if password, ok := repoValue.(map[string]any)["password"]; ok {
						regclientOpts = append(regclientOpts, regclient.WithConfigHost(config.Host{
							Hostname: repoKey,
							Name:     repoKey,
							User:     username.(string),
							Pass:     password.(string),
						}))
					}
				}
			}
		}
	}

	return regclient.New(regclientOpts...), nil
}
