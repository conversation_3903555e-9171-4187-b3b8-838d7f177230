package instance

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"os"
	"strings"

	"github.com/spf13/cobra"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/tools/remotecommand"
	"k8s.io/kubectl/pkg/scheme"

	"github.com/akuityio/akuity-platform/internal/cli"
)

// PoolData represents the structure of each row in the SHOW POOLS output
type PoolData struct {
	Database           string `json:"database"`
	User               string `json:"user"`
	ClActive           int    `json:"cl_active"`
	ClWaiting          int    `json:"cl_waiting"`
	ClActiveCancelReq  int    `json:"cl_active_cancel_req"`
	ClWaitingCancelReq int    `json:"cl_waiting_cancel_req"`
	SvActive           int    `json:"sv_active"`
	SvActiveCancel     int    `json:"sv_active_cancel"`
	SvBeingCanceled    int    `json:"sv_being_canceled"`
	SvIdle             int    `json:"sv_idle"`
	SvUsed             int    `json:"sv_used"`
	SvTested           int    `json:"sv_tested"`
	SvLogin            int    `json:"sv_login"`
	MaxWait            int    `json:"maxwait"`
	MaxWaitUs          int    `json:"maxwait_us"`
	PoolMode           string `json:"pool_mode"`
	LoadBalanceHosts   string `json:"load_balance_hosts"`
}

// NamespacePodPools represents pools data for each pod in a namespace
type NamespacePodPools struct {
	Namespace       string                `json:"namespace"`
	NoPgBouncerPods bool                  `json:"no_pgbouncer_pods"`
	PodData         map[string][]PoolData `json:"pod_data"`
}

func NewAKPUtilInstancePgBouncerStatCommand() *cobra.Command {
	var (
		clientConfig   clientcmd.ClientConfig
		instanceNsList []string
	)
	cmd := &cobra.Command{
		Use:   "pgbouncer-stats",
		Short: "Collect pgBouncer Stats for instance",
		Run: func(cmd *cobra.Command, args []string) {
			res, err := collectPgBouncerPoolStatus(cmd.Context(), clientConfig, instanceNsList)
			cli.CheckErr(err)
			parsePrint(res)
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().StringSliceVar(&instanceNsList, "instance-namespaces", []string{}, "Argo CD/Kargo Instance Namespaces, default : checks all instances")
	return cmd
}

func collectPgBouncerPoolStatus(ctx context.Context, clientConfig clientcmd.ClientConfig, instanceNs []string) ([]NamespacePodPools, error) {
	// Setup Kubernetes client
	config, err := clientConfig.ClientConfig()
	if err != nil {
		return nil, fmt.Errorf("failed to build kubeconfig: %v", err)
	}

	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create Kubernetes client: %v", err)
	}

	var results []NamespacePodPools

	if len(instanceNs) == 0 {
		// Get all namespaces
		namespaces, err := clientset.CoreV1().Namespaces().List(ctx, metav1.ListOptions{})
		if err != nil {
			return nil, fmt.Errorf("failed to list namespaces: %v", err)
		}

		// Filter namespaces with argocd-* or kargo-* prefix
		for _, ns := range namespaces.Items {
			if !strings.HasPrefix(ns.Name, "argocd-") && !strings.HasPrefix(ns.Name, "kargo-") {
				continue
			}
			instanceNs = append(instanceNs, ns.Name)
		}
	}

	resChan := make(chan []NamespacePodPools)
	// start at most 10 workers to process the namespaces concurrently, each worker should get a equal/almost equal number of namespaces
	workerCount := 10
	if len(instanceNs) < workerCount {
		workerCount = len(instanceNs)
	}
	nsPerWorker := len(instanceNs) / workerCount
	fmt.Printf("Processing %d namespaces with %d workers\n", len(instanceNs), workerCount)
	for i := 0; i < workerCount; i++ {
		startIdx := i * nsPerWorker
		endIdx := startIdx + nsPerWorker
		if i == workerCount-1 {
			endIdx = len(instanceNs)
		}
		go pgBouncerPoolStatWorker(ctx, instanceNs[startIdx:endIdx], clientset, config, resChan)
	}

	// collect results from workers
	for i := 0; i < workerCount; i++ {
		select {
		case res := <-resChan:
			if len(res) == 0 {
				continue
			}
			results = append(results, res...)
		case <-ctx.Done():
			return nil, ctx.Err()
		}
	}
	return results, nil
}

func pgBouncerPoolStatWorker(ctx context.Context, instanceNs []string, clientset *kubernetes.Clientset, config *rest.Config, resChan chan<- []NamespacePodPools) {
	var results []NamespacePodPools
	for _, namespaceName := range instanceNs {
		select {
		case <-ctx.Done():
			return
		default:
		}
		// List k3s deployment pods in the namespace
		pods, err := clientset.CoreV1().Pods(namespaceName).List(ctx, metav1.ListOptions{
			LabelSelector: "app.kubernetes.io/name=k3s",
		})
		if err != nil {
			fmt.Fprintf(os.Stderr, "Warning: Failed to list pods in namespace %s: %v\n", namespaceName, err)
			continue
		}

		if len(pods.Items) == 0 {
			fmt.Fprintf(os.Stderr, "Warning: No k3s pods found in namespace %s\n", namespaceName)
			continue
		}

		podData := make(map[string][]PoolData)
		for _, pod := range pods.Items {
			podName := pod.Name

			// Check if the pod has a pgbouncer container
			hasPgBouncer := false
			for _, container := range pod.Spec.InitContainers {
				if container.Name == "pgbouncer" {
					hasPgBouncer = true
					break
				}
			}

			if !hasPgBouncer {
				for _, container := range pod.Spec.Containers {
					if container.Name == "pgbouncer" {
						hasPgBouncer = true
						break
					}
				}
			}

			if !hasPgBouncer {
				fmt.Fprintf(os.Stderr, "Warning: Pod %s in ns %s does not have a PgBouncer container\n", podName, namespaceName)
				continue
			}

			// Execute the command to get PgBouncer pool status
			stdout, stderr, err := ExecCmd(ctx, clientset, config, namespaceName, podName, "pgbouncer", []string{
				"psql", "-h", "/run/pgpool", "-p", "9999", "-U", "pgbouncer", "pgbouncer", "-P", "pager=off", "-c", "SHOW POOLS;",
			}, nil)
			if err != nil {
				fmt.Fprintf(os.Stderr, "Warning: failed to run show pools in pgbouncer pod %v in ns %v, error=%v\n", podName, namespaceName, err)
				continue
			}
			if stderr != "" {
				fmt.Fprintf(os.Stderr, "Warning: failed to run show pools in pgbouncer pod %v in ns %v, error=%v\n", podName, namespaceName, stderr)
				continue
			}
			if stdout == "" {
				fmt.Fprintf(os.Stderr, "Warning: failed to run show pools in pgbouncer pod %v in ns %v, error=no output found\n", podName, namespaceName)
				continue
			}

			// Parse the output
			pools, err := parsePgBouncerPoolOutput(stdout)
			if err != nil {
				fmt.Fprintf(os.Stderr, "Warning: Failed to parse output for pod %s: %v\n", podName, err)
				continue
			}

			podData[podName] = pools
		}

		if len(podData) > 0 {
			results = append(results, NamespacePodPools{
				Namespace: namespaceName,
				PodData:   podData,
			})
		} else {
			results = append(results, NamespacePodPools{
				Namespace:       namespaceName,
				NoPgBouncerPods: true,
			})
		}
	}
	resChan <- results
}

func parsePgBouncerPoolOutput(output string) ([]PoolData, error) {
	lines := strings.Split(output, "\n")

	// Need at least header + separator + one row
	if len(lines) < 3 {
		return nil, fmt.Errorf("unexpected output format: not enough lines")
	}

	// Find where the data rows start (after the header and separator lines)
	dataStartIdx := -1
	for i, line := range lines {
		if strings.Contains(line, "---") {
			dataStartIdx = i + 1
			break
		}
	}

	if dataStartIdx == -1 || dataStartIdx >= len(lines) {
		return nil, fmt.Errorf("could not find data rows in output")
	}

	var pools []PoolData

	// Process each data row
	for i := dataStartIdx; i < len(lines); i++ {
		line := strings.TrimSpace(lines[i])
		if line == "" || strings.Contains(line, "rows)") {
			continue
		}

		// Split the line by |
		fields := strings.Split(line, "|")
		if len(fields) < 17 {
			return nil, fmt.Errorf("invalid row format: expected at least 17 fields, got %d", len(fields))
		}

		// Parse each field
		pool := PoolData{
			Database:           strings.TrimSpace(fields[0]),
			User:               strings.TrimSpace(fields[1]),
			ClActive:           parseInt(strings.TrimSpace(fields[2])),
			ClWaiting:          parseInt(strings.TrimSpace(fields[3])),
			ClActiveCancelReq:  parseInt(strings.TrimSpace(fields[4])),
			ClWaitingCancelReq: parseInt(strings.TrimSpace(fields[5])),
			SvActive:           parseInt(strings.TrimSpace(fields[6])),
			SvActiveCancel:     parseInt(strings.TrimSpace(fields[7])),
			SvBeingCanceled:    parseInt(strings.TrimSpace(fields[8])),
			SvIdle:             parseInt(strings.TrimSpace(fields[9])),
			SvUsed:             parseInt(strings.TrimSpace(fields[10])),
			SvTested:           parseInt(strings.TrimSpace(fields[11])),
			SvLogin:            parseInt(strings.TrimSpace(fields[12])),
			MaxWait:            parseInt(strings.TrimSpace(fields[13])),
			MaxWaitUs:          parseInt(strings.TrimSpace(fields[14])),
			PoolMode:           strings.TrimSpace(fields[15]),
			LoadBalanceHosts:   strings.TrimSpace(fields[16]),
		}

		if pool.Database == "pgbouncer" {
			// ignore pgbouncer connections those are always local only
			continue
		}

		pools = append(pools, pool)
	}

	return pools, nil
}

func parseInt(s string) int {
	var result int
	_, err := fmt.Sscanf(s, "%d", &result)
	if err != nil {
		return 0
	}
	return result
}

func parsePrint(data []NamespacePodPools) {
	totalClientConnections := 0
	totalServerConnections := 0
	notPgBouncerNs := 0
	for _, ns := range data {
		if ns.NoPgBouncerPods {
			notPgBouncerNs++
			continue
		}
		fmt.Println("- Namespace:", ns.Namespace)
		nsTotalClientConnections := 0
		nsTotalServerConnections := 0
		totalWaiting := 0
		totalServerActive := 0
		for pod, pools := range ns.PodData {
			fmt.Println("  - Pod:", pod)
			clientConnections := 0
			serverConnections := 0
			for _, pool := range pools {
				clientConnections += pool.ClActive + pool.ClWaiting + pool.ClActiveCancelReq + pool.ClWaitingCancelReq
				totalWaiting += pool.ClWaiting
				serverConnections += pool.SvActive + pool.SvIdle + pool.SvUsed + pool.SvActiveCancel + pool.SvBeingCanceled + pool.SvTested + pool.SvLogin
				totalServerActive += pool.SvActive
			}
			nsTotalClientConnections += clientConnections
			nsTotalServerConnections += serverConnections
			fmt.Printf("     Total Client Connections: %d\n", clientConnections)
			fmt.Printf("     Total Server Connections: %d\n", serverConnections)
		}
		fmt.Printf("  Total Client(cl_*) Connections in Namespace: %d\n", nsTotalClientConnections)
		fmt.Printf("  Total Server(sv_*) Connections in Namespace: %d\n", nsTotalServerConnections)
		fmt.Printf("  Total Client Waiting(cl_waiting) Connections in Namespace (this should not be too high or else clients are throtlling): %d\n", totalWaiting)
		fmt.Printf("  Total Server Active(sv_active) Connections in Namespace (alert if this value hits max pool_size*replicas and waiting count): %d\n", totalServerActive)
		totalServerConnections += nsTotalServerConnections
		totalClientConnections += nsTotalClientConnections
	}
	fmt.Println("Total Client Connections(k3s->pgbouncer):", totalClientConnections)
	fmt.Println("Total Server Connections(pgbouncer->rds):", totalServerConnections)
	fmt.Println("Namespaces without PgBouncer pods:", notPgBouncerNs)
}

func ExecCmd(ctx context.Context, client kubernetes.Interface, config *rest.Config, ns, podName, containerName string, cmd []string, stdin io.Reader) (string, string, error) {
	req := client.CoreV1().RESTClient().Post().Resource("pods").Name(podName).
		Namespace(ns).SubResource("exec")
	option := &corev1.PodExecOptions{
		Container: containerName,
		Command:   cmd,
		Stdin:     true,
		Stdout:    true,
		Stderr:    true,
		TTY:       false,
	}
	if stdin == nil {
		option.Stdin = false
	}
	req.VersionedParams(
		option,
		scheme.ParameterCodec,
	)
	exec, err := remotecommand.NewSPDYExecutor(config, "POST", req.URL())
	if err != nil {
		return "", "", fmt.Errorf("error while creating Executor: %v", err)
	}
	var stdout, stderr bytes.Buffer

	err = exec.StreamWithContext(ctx, remotecommand.StreamOptions{
		Stdin:  stdin,
		Stdout: &stdout,
		Stderr: &stderr,
	})
	if err != nil {
		return "", "", fmt.Errorf("error in Stream: %v", err)
	}

	return stdout.String(), stderr.String(), nil
}
