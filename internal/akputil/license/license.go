package license

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/spf13/cobra"

	"github.com/akuityio/akuity-platform/internal/cli"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/license"
	"github.com/akuityio/akuity-platform/internal/utils/consts"
)

func NewLicenseCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use: "license",
		Run: func(cmd *cobra.Command, args []string) {
			cmd.HelpFunc()(cmd, args)
		},
	}
	cmd.AddCommand(NewGenerateLicenseCommand())
	cmd.AddCommand(NewPemFilesCommand())
	return cmd
}

func NewPemFilesCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use: "pem-files",
		Run: func(cmd *cobra.Command, args []string) {
			priv<PERSON>ey, pub<PERSON>ey, err := license.GeneratePemFiles()
			cli.CheckErr(err)

			_, _ = os.Stdout.WriteString(string(privKey) + "\n" + string(pubKey))
		},
	}
	return cmd
}

func NewGenerateLicenseCommand() *cobra.Command {
	var (
		privateKeyPath string

		applications uint64
		instances    uint64
		clusters     uint64

		// Deprecated, use KargoStages instead
		kargoProjects uint64

		kargoStages    uint64
		kargoInstances uint64
		kargoAgents    uint64

		gracePeriodDays int
		durationDays    int
		issuedFor       string
		description     string

		onlyArgocd bool
		onlyKargo  bool
		kubeVision bool
	)
	cmd := &cobra.Command{
		Use: "generate",
		Run: func(cmd *cobra.Command, args []string) {
			privateKeyPath = filepath.Clean(privateKeyPath)
			privateKey, err := os.ReadFile(privateKeyPath)
			cli.CheckErr(err)
			gracePeriod := time.Duration(gracePeriodDays) * consts.Day
			if kargoProjects != 0 {
				cli.CheckErr(fmt.Errorf("ERROR: kargo-projects flag is deprecated and no longer enforced, use kargo-stages instead"))
			}
			licenseData := &license.License{
				Version:           license.VersionV2,
				Applications:      applications,
				Clusters:          clusters,
				Instances:         instances,
				KargoStages:       kargoStages,
				KargoInstances:    kargoInstances,
				KargoAgents:       kargoAgents,
				GracePeriod:       int64(gracePeriod.Seconds()),
				ExpirationTime:    time.Now().Add(time.Duration(durationDays) * consts.Day).Unix(),
				IssuedFor:         issuedFor,
				Description:       description,
				KargoEnabled:      true,
				KubeVisionEnabled: kubeVision,
			}

			if onlyKargo && onlyArgocd {
				cli.CheckErr(fmt.Errorf("only one of --only-argocd or --only-kargo can be set"))
			}

			if onlyArgocd {
				licenseData.KargoStages = config.DefaultLicense.KargoStages
				licenseData.KargoInstances = config.DefaultLicense.KargoInstances
				licenseData.KargoAgents = config.DefaultLicense.KargoAgents
				licenseData.KargoEnabled = false
			}
			if onlyKargo {
				licenseData.Applications = config.DefaultLicense.Applications
				licenseData.Clusters = config.DefaultLicense.Clusters
				licenseData.Instances = config.DefaultLicense.Instances
			}

			ldata, err := licenseData.PrettyPrint()
			cli.CheckErr(err)

			fmt.Printf("\n\n--> License data (if a resource limit is 0 it is unlimited): \n%s\n", string(ldata))
			if licenseData.KargoEnabled {
				fmt.Println("\n--> ⚠️ Kargo is enabled in the license")
			} else {
				fmt.Println("\n--> ⚠️ Kargo is disabled in the license")
			}
			fmt.Print("\n\n--> Do you want to generate license with the above specifications? [y/n] : ")
			var confirm string
			_, err = fmt.Scanln(&confirm)
			cli.CheckErr(err)
			if strings.ToLower(confirm) != "y" {
				fmt.Println("Exiting...")
				return
			}
			fmt.Println("--> Generating License...")
			jwt, err := license.CreateLicense(privateKey, licenseData)
			cli.CheckErr(err)
			fmt.Printf("--> License generated successfully, license Key : \n%v\n", jwt)
		},
	}

	cmd.Flags().StringVar(&privateKeyPath, "private-key-path", "", "Path to private key")
	cli.CheckErr(cmd.MarkFlagRequired("private-key-path"))

	cmd.Flags().Uint64Var(&applications, "applications", 0, "Number of applications (default: 0, unlimited)")
	cmd.Flags().Uint64Var(&instances, "instances", 0, "Number of instances (default: 0, unlimited)")
	cmd.Flags().Uint64Var(&clusters, "clusters", 0, "Number of clusters (default: 0, unlimited)")
	cmd.Flags().BoolVar(&kubeVision, "kube-vision", false, "Enables KubeVision feature")
	cmd.Flags().BoolVar(&onlyArgocd, "only-argocd", false, "if true, kargo is disabled in license and default limits are applied for kargo resources")

	cmd.Flags().Uint64Var(&kargoProjects, "kargo-projects", 0, "Number of kargo projects (default: 0, unlimited)")
	cmd.Flags().Uint64Var(&kargoStages, "kargo-stages", 0, "Number of kargo stages (default: 0, unlimited)")
	cmd.Flags().Uint64Var(&kargoInstances, "kargo-instances", 0, "Number of kargo instances (default: 0, unlimited)")
	cmd.Flags().Uint64Var(&kargoAgents, "kargo-agents", 0, "Number of kargo agents (default: 0, unlimited)")
	cmd.Flags().BoolVar(&onlyKargo, "only-kargo", false, "if true, default limits are automatically applied for argocd resources")

	cmd.Flags().IntVar(&durationDays, "duration-days", 0, "Duration in days")
	cli.CheckErr(cmd.MarkFlagRequired("duration-days"))
	cmd.Flags().StringVar(&issuedFor, "issued-for", "", "Customer name the license is issued for")
	cli.CheckErr(cmd.MarkFlagRequired("issued-for"))
	cmd.Flags().StringVar(&description, "description", "", "Additional information about the license")

	cmd.Flags().IntVar(&gracePeriodDays, "grace-period-days", 30, "Grace period in days")

	return cmd
}
