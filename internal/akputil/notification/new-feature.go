package notification

import (
	"github.com/volatiletech/null/v8"

	"github.com/akuityio/akuity-platform/internal/akputil/shared"
	"github.com/akuityio/akuity-platform/internal/services/notifications"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

func CreateNewFeatureEvent(akp *shared.AKPUtil, feature string) error {
	event := &models.Event{
		EventType: null.StringFrom(models.EventTypeNewFeature),
	}
	if err := event.SetMetadata(models.EventMetadata{
		NewFeature: &models.NewFeature{
			Feature: feature,
		},
	}); err != nil {
		return err
	}
	repoSet := client.NewRepoSet(akp.PortalDBPool.DB)
	// we do not perform dedups for features/marketing events
	if err, _ := notifications.CreateEventWithDeDuplication(akp.Ctx, repoSet, event, notifications.NoOpEventExistsChecker); err != nil {
		return err
	}
	return nil
}
