package notification

import (
	"time"

	"github.com/spf13/cobra"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/akuityio/akuity-platform/internal/akputil/shared"
	"github.com/akuityio/akuity-platform/internal/cli"
)

func NewNotificationEventCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use: "event",
		Run: func(cmd *cobra.Command, args []string) {
			cmd.HelpFunc()(cmd, args)
		},
	}
	cmd.AddCommand(NewFreeTrialExpiringEventCommand())
	cmd.AddCommand(NewFeatureEventCommand())
	return cmd
}

func NewFreeTrialExpiringEventCommand() *cobra.Command {
	var (
		clientConfig        clientcmd.ClientConfig
		daysUntilExpiration []int
		portalURL           string
	)
	cmd := &cobra.Command{
		Use:   "free-trial-expiring",
		Short: "Create an event to send notifications to user if the free trial is expiring",
		Run: func(cmd *cobra.Command, args []string) {
			akpUtil := shared.NewAKPUtil(clientConfig, cmd.Context(), 10*time.Minute)
			defer func() { _ = akpUtil.Close() }()
			cli.CheckErr(CreateFreeTrialExpiringEvent(akpUtil, daysUntilExpiration, portalURL))
		},
	}
	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().IntSliceVar(&daysUntilExpiration, "days-until-expiration", []int{1, 3, 7}, "Days until expiration to notify users")
	cmd.Flags().StringVar(&portalURL, "portal-url", "http://portal-server.akuity-platform:9090", "Portal URL")
	return cmd
}

func NewFeatureEventCommand() *cobra.Command {
	var (
		clientConfig clientcmd.ClientConfig
		feature      string
	)
	cmd := &cobra.Command{
		Use: "new-feature",
		Run: func(cmd *cobra.Command, args []string) {
			akpUtil := shared.NewAKPUtil(clientConfig, cmd.Context(), time.Minute)
			defer func() { _ = akpUtil.Close() }()
			cli.CheckErr(CreateNewFeatureEvent(akpUtil, feature))
		},
	}
	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().StringVar(&feature, "feature", "", "Feature name")
	return cmd
}
