package notification

import (
	"database/sql"
	"encoding/json"
	"errors"
	"math"
	"time"

	"github.com/volatiletech/null/v8"

	"github.com/akuityio/akuity-platform/internal/akputil/shared"
	"github.com/akuityio/akuity-platform/internal/services/notifications"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

func CreateFreeTrialExpiringEvent(akp *shared.AKPUtil, daysUntilExpiration []int, portalURL string) error {
	txDB, txBeginner := database.WithTxBeginner(akp.PortalDBPool.DB)
	tx, err := txBeginner.Begin(akp.Ctx)
	if err != nil {
		return err
	}
	defer func() {
		_ = tx.Rollback()
	}()
	repoSet := client.NewRepoSet(txDB)
	orgs, err := repoSet.Organizations().ListAll(akp.Ctx)
	if err != nil {
		return err
	}
	expirationDays := map[int]bool{}
	for _, day := range daysUntilExpiration {
		expirationDays[day] = true
	}
	for _, org := range orgs {
		status, err := org.GetOrgStatus()
		if err != nil {
			return err
		}
		day := 24 * time.Hour
		expiryTime := time.Unix(status.ExpiryTime, 0)
		days := int(math.Floor(time.Until(expiryTime).Hours() / day.Hours()))
		if expirationDays[days] {
			metadata := models.EventMetadata{
				FreeTrialExpiring: &models.FreeTrialExpiring{
					Days: days,
				},
			}
			bytes, err := json.Marshal(metadata)
			if err != nil {
				return err
			}
			_, err = repoSet.Events(
				models.EventWhere.OrganizationID.EQ(null.StringFrom(org.GetID())),
				models.EventWhere.EventType.EQ(null.StringFrom(models.EventTypeFreeTrialExpiring)),
				models.EventWhere.Metadata.EQ(null.JSONFrom(bytes)),
			).One(akp.Ctx)
			if err == nil {
				// The event already exists
				continue
			}
			if !errors.Is(err, sql.ErrNoRows) {
				return err
			}
			event := &models.Event{
				EventType:      null.StringFrom(models.EventTypeFreeTrialExpiring),
				OrganizationID: null.StringFrom(org.GetID()),
			}
			if err := event.SetMetadata(metadata); err != nil {
				return err
			}
			if err, _ := notifications.CreateEventWithDeDuplication(akp.Ctx, repoSet, event); err != nil {
				return err
			}
		}
	}
	return tx.Commit()
}
