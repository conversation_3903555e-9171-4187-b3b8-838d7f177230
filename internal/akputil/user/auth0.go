package user

import (
	"context"
	"fmt"
	"time"

	"github.com/auth0/go-auth0/management"
	"github.com/go-logr/logr"

	"github.com/akuityio/akuity-platform/internal/utils/database"
)

func processForAuth0(ctx context.Context, log logr.Logger, dbPool *database.DBPool, am *management.Management, dryRun bool, inactiveDays, batchSize int) (*summary, error) {
	inactiveUnverifiedUsers, err := fetchInactiveUnverifiedUsersFromAuth0(ctx, am, inactiveDays)
	if err != nil {
		log.Error(err, "failed to fetch inactive unverified users")
		return nil, err
	}
	if len(inactiveUnverifiedUsers) == 0 {
		log.Info(fmt.Sprintf("No users found who are inactive for more than %d days and have unverified emails.\n", inactiveDays))
		return &summary{}, nil
	}

	sum := &summary{numberOfInactiveUsersAuth0: len(inactiveUnverifiedUsers)}
	totalUsers := len(inactiveUnverifiedUsers)

	log.Info(fmt.Sprintf("Processing %d inactive users in batches of %d", totalUsers, batchSize))

	// Process users in batches
	for i := 0; i < totalUsers; i += batchSize {
		batch := inactiveUnverifiedUsers[i:min(i+batchSize, totalUsers)]
		log.Info(fmt.Sprintf("Processing batch %d - %d out of %d", i+1, i+len(batch), totalUsers))

		// Process the batch
		processAuth0Batch(ctx, log, dbPool, am, dryRun, batch, sum)
		time.Sleep(time.Duration(1) * time.Second)

	}
	return sum, nil
}

// processBatch handles batch processing of users sequentially.
func processAuth0Batch(ctx context.Context, log logr.Logger, dbPool *database.DBPool, am *management.Management, dryRun bool, batch []*management.User, sum *summary) {
	start := time.Now()
	for _, user := range batch {
		if err := processAuth0User(ctx, log, dbPool, am, dryRun, user, sum); err != nil {
			log.Error(err, "failed to process user", "email", *user.Email)
			sum.numberOfUsersErroredWhileDeletionAuth0++
		}
	}
	log.Info(fmt.Sprintf("Total time taken to process the batch of size %d : %v\n", len(batch), time.Since(start)))
}

func processAuth0User(ctx context.Context, log logr.Logger, dbPool *database.DBPool, am *management.Management, dryRun bool, user *management.User, sum *summary) error {
	present, err := auth0UserExistsInDB(dbPool, *user.Email)
	if err != nil {
		// error to get user from db does not mean user is not present
		// ignore this user for next time.
		return fmt.Errorf("error checking user in DB: %w", err)
	}

	// if user exists in DB, ignore deletion
	if present {
		sum.numberOfUsersIgnoredForDeletionAuth0++
		return nil
	}

	// Delete user from Auth0
	if err := deleteUserFromAuth0(ctx, log, am, user, dryRun); err != nil {
		return fmt.Errorf("error deleting user from Auth0: %w", err)
	}
	sum.numberOfUsersDeletedAuth0++
	return nil
}

// fetchInactiveUnverifiedUsersFromAuth0 fetch users inactive for more than X days & with unverified emails who does not have sso based login
func fetchInactiveUnverifiedUsersFromAuth0(ctx context.Context, am *management.Management, inactiveDays int) ([]*management.User, error) {
	inactiveSince := time.Now().AddDate(0, 0, -inactiveDays).Format("2006-01-02")

	query := fmt.Sprintf("last_login:[* TO %s] AND email_verified:false", inactiveSince)
	userList, err := am.User.List(ctx, management.Query(query))
	if err != nil {
		return nil, err
	}
	socialProviders := map[string]bool{
		"google-oauth2": true,
		"facebook":      true,
		"github":        true,
		"linkedin":      true,
		"twitter":       true,
		"windowslive":   true,
	}
	// filter out sso users only include social or auth0 users
	filteredUsers := make([]*management.User, 0, len(userList.Users))
	for _, user := range userList.Users {
		hasSSOIdentity := false
		if user.Identities != nil {
			for _, identity := range user.Identities {
				connection := ""
				if identity.Connection != nil {
					connection = *identity.Connection
				}
				provider := ""
				if identity.Provider != nil {
					provider = *identity.Provider
				}

				if connection != userNamePasswordAuthentication && !socialProviders[provider] {
					hasSSOIdentity = true
					// do not include user who have even one sso login
					break
				}
			}
			if !hasSSOIdentity {
				filteredUsers = append(filteredUsers, user)
			}
		}
	}
	return filteredUsers, nil
}

// deleteUserFromAuth0 deletes a user from Auth0
func deleteUserFromAuth0(ctx context.Context, log logr.Logger, am *management.Management, user *management.User, isDryRun bool) error {
	if user.ID == nil {
		return fmt.Errorf("user id is nil")
	}
	// marks the user deleted in auth0 metadata
	if isDryRun {
		userMeta := map[string]interface{}{}
		if user.UserMetadata != nil {
			// user previously marked deleted this helps reduce unnecessary same update.
			if user.GetUserMetadata()[dryRunUserRemoved] == true {
				log.Info(fmt.Sprintf("DryRun: user : %s email : %s already marked deleted in Auth0", *user.Name, *user.Email))
				return nil
			}
			userMeta = *user.UserMetadata
		}
		userMeta[dryRunUserRemoved] = true
		update := &management.User{
			UserMetadata: &userMeta,
		}

		if err := am.User.Update(ctx, *user.ID, update); err != nil {
			log.Error(err, fmt.Sprintf("DryRun: failed to update user : %s email : %s deleted metadata in Auth0", *user.Name, *user.Email))
			return err
		}
		log.Info(fmt.Sprintf("DryRun: Would have deleted user : %s email : %s Auth0", *user.Name, *user.Email))
		return nil
	}

	if err := am.User.Delete(ctx, *user.ID); err != nil {
		log.Error(err, fmt.Sprintf("failed to delete user : %s email : %s in Auth0", *user.Name, *user.Email))
		return err
	}
	log.Info(fmt.Sprintf("Deleted user : %s email : %s successfully from Auth0", *user.Name, *user.Email))
	return nil
}

// auth0UserExistsInDB to check if Auth0 user exists in akuity_user
func auth0UserExistsInDB(dbPool *database.DBPool, userEmail string) (bool, error) {
	var exists bool
	query := `SELECT EXISTS (SELECT 1 FROM akuity_user WHERE UPPER(email) = UPPER($1))`
	err := dbPool.DB.QueryRow(query, userEmail).Scan(&exists)
	if err != nil {
		return false, fmt.Errorf("DB query error: %w", err)
	}
	return exists, nil
}
