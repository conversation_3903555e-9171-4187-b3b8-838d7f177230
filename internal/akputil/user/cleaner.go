package user

import (
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/auth0/go-auth0/management"
	"github.com/getsentry/sentry-go"
	"github.com/spf13/cobra"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/akuityio/akuity-platform/internal/akputil/shared"
	"github.com/akuityio/akuity-platform/internal/cli"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/utils/aims"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/utils/logging"
	"github.com/akuityio/akuity-platform/internal/version"
)

const (
	dryRunUserRemoved              = "dryrun_user_removed"
	userNamePasswordAuthentication = "Username-Password-Authentication"
)

type summary struct {
	numberOfInactiveUsersAuth0             int
	numberOfUsersDeletedAuth0              int
	numberOfUsersIgnoredForDeletionAuth0   int
	numberOfUsersErroredWhileDeletionAuth0 int
}

func NewUserCleaner() *cobra.Command {
	var (
		auth0InactiveDays   int
		auth0BatchSize      int
		dbInactiveDays      int
		dbDeletionGraceDays int
		s3Bucket            string
		dryRun              bool
		clientConfig        clientcmd.ClientConfig
	)

	ver := version.GetVersion()
	env := os.Getenv("ENV")
	if env == "" {
		env = "development"
	}

	sentryDSN := os.Getenv("PLATFORM_SENTRY_DSN")
	if sentryDSN == "" {
		fmt.Println("'PLATFORM_SENTRY_DSN' is missing, Sentry integration is disabled")
	} else {
		if sentryErr := sentry.Init(sentry.ClientOptions{
			AttachStacktrace: true,
			Dsn:              sentryDSN,
			Environment:      env,
			Release:          ver.Version,
		}); sentryErr != nil {
			fmt.Printf("Sentry init has failed: %s\n", sentryErr.Error())
		}
		defer sentry.Flush(2 * time.Second)
		defer sentry.Recover()
	}

	cmd := &cobra.Command{
		Use:   "clean",
		Short: "Clean all the users from DB and Auth0 if inactive for more than <inactive-for> days",
		Run: func(cmd *cobra.Command, args []string) {
			ctx, cancel := signal.NotifyContext(cmd.Context(), syscall.SIGINT, syscall.SIGTERM)
			defer cancel()
			cfg, err := config.NewPortalServerConfig()
			cli.CheckErr(err)

			akp := shared.NewAKPUtil(clientConfig, cmd.Context(), 10*time.Minute)
			defer func() { _ = akp.Close() }()

			logger, err := logging.NewLogger()
			cli.CheckErr(err)

			portalDBPool, err := database.GetDBPool(cfg.PortalDBConnection, &cfg.DBConnection)
			if err != nil {
				cli.CheckErr(fmt.Errorf("failed to connect to portal DB: %w", err))
			}
			defer func() {
				_ = database.CloseDBPool(portalDBPool)
			}()

			k3sDBPool, err := database.GetDBPool(cfg.TenantDBConnection, &cfg.DBConnection)
			if err != nil {
				cli.CheckErr(fmt.Errorf("failed to connect to tenant DB: %w", err))
			}
			defer func() {
				_ = database.CloseDBPool(k3sDBPool)
			}()

			cm, err := akp.K8sClient.CoreV1().ConfigMaps(config.InternalCmNamespace).Get(ctx, config.InternalCmName, metav1.GetOptions{})
			if err != nil {
				cli.CheckErr(fmt.Errorf("failed to get configmap : %w ", err))
			}

			orgVerificationMap, err := aims.ParseVerifiedOrganizationMap(cm)
			if err != nil {
				cli.CheckErr(fmt.Errorf("failed to parse verified organization map: %w", err))
			}

			var auth0Management *management.Management
			auth0Management, err = management.New(cfg.Auth0.Portal().Domain,
				management.WithClientCredentialsAndAudience(ctx,
					cfg.Auth0.Portal().ClientID, cfg.Auth0.Portal().ClientSecret,
					cfg.Auth0.Portal().ManagementAPIAudience))
			cli.CheckErr(err)
			authOProcessSummary, err := processForAuth0(ctx, logger, portalDBPool, auth0Management, dryRun, auth0InactiveDays, auth0BatchSize)
			if err != nil {
				cli.CheckErr(fmt.Errorf("failed to complete user cleanup in Auth0: %w", err))
			}

			if authOProcessSummary != nil {
				logger.Info("Auth0 User Cleanup Summary")
				logger.Info(fmt.Sprintf("Number of users inactive and unverified for %d days in Auth0 : %d", auth0InactiveDays, authOProcessSummary.numberOfInactiveUsersAuth0))
				logger.Info(fmt.Sprintf("Number of users deleted from Auth0: %d", authOProcessSummary.numberOfUsersDeletedAuth0))
				logger.Info(fmt.Sprintf("Number of users skipped (present in DB or ineligible): %d", authOProcessSummary.numberOfUsersIgnoredForDeletionAuth0))
				logger.Info(fmt.Sprintf("Number of errors while deleting users: %d", authOProcessSummary.numberOfUsersErroredWhileDeletionAuth0))
			}
			dbProcess := NewDBCleaner(ctx, logger, akp.K8sClient, portalDBPool, k3sDBPool, auth0Management, s3Bucket, &cfg.SMTP, dryRun, dbInactiveDays, dbDeletionGraceDays, orgVerificationMap)
			dbProcessSummary, err := dbProcess.BackupAndClean()
			if err != nil {
				cli.CheckErr(fmt.Errorf("failed to complete user cleanup in DB: %w", err))
			}

			if dbProcessSummary != nil {
				logger.Info("DB User Cleanup Summary")
				logger.Info(fmt.Sprintf("Number of users inactive for %d days : %d", dbInactiveDays, dbProcessSummary.NumberOfInactiveUserDB))
				logger.Info(fmt.Sprintf("Number of users qualified for deletion : %d", dbProcessSummary.NumberOfDeletionQualifiedUsersDB))
				logger.Info(fmt.Sprintf("Number of users ignored : %d", dbProcessSummary.NumberOfUsersIgnoredDB))
				logger.Info(fmt.Sprintf("Number of users deleted from DB : %d", dbProcessSummary.NumberOfDeletedUserDB))
				logger.Info(fmt.Sprintf("Number of errors processing users in DB : %d", dbProcessSummary.NumberOfErroredUserWhileDeleteDB))
				logger.Info(fmt.Sprintf("Number of new users marked for deletion : %d", dbProcessSummary.NumberOfNewUserMarkedDeletionDB))
				logger.Info(fmt.Sprintf("Number of users deletion mark removed after some activity : %d", dbProcessSummary.NumberOfUserExpiryTimeRemovedDB))
			}
		},
	}
	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().IntVar(&auth0InactiveDays, "auth0-inactive-days", 60, "Days of inactivity in Auth0 before deleting a user")
	cmd.Flags().IntVar(&auth0BatchSize, "auth0-batch-size", 5, "Number of user in a batch to be processed for Auth0, Batch size")
	cmd.Flags().IntVar(&dbInactiveDays, "db-inactive-days", 180, "Days of inactivity in DB before deleting a user")
	cmd.Flags().IntVar(&dbDeletionGraceDays, "db-deletion-grace-days", 60, "Number of grace days after which user is deleted from DB.")
	cmd.Flags().StringVar(&s3Bucket, "s3-bucket", "", "S3 bucket name to upload the archives (required)")
	cmd.Flags().BoolVar(&dryRun, "dry-run", true, "If true, only mark deleted without actually deleting users")
	cli.CheckErr(cmd.MarkFlagRequired("s3-bucket"))
	return cmd
}
