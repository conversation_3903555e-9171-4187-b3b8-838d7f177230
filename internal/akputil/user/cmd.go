package user

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/spf13/cobra"
	"github.com/volatiletech/sqlboiler/v4/boil"

	"github.com/akuityio/akuity-platform/internal/cli"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/models/util/encryption"
)

const (
	defaultDbName = "postgres"
	defaultDbUser = "masteruser"
)

var userCmd = &cobra.Command{
	Use:   "user",
	Short: "User command",
	Run: func(cmd *cobra.Command, args []string) {
		cmd.HelpFunc()(cmd, args)
	},
}

func NewUserCommand() *cobra.Command {
	userCmd.AddCommand(NewMigrateUsersCommand())
	userCmd.AddCommand(NewUserCleaner())
	return userCmd
}

func NewMigrateUsersCommand() *cobra.Command {
	var sourceDbEncryptionKey, sourceDbHost, sourceDbPassword, targetDbHost, targetDbPassword string

	cmd := &cobra.Command{
		Use:   "migrate-users",
		Short: "Migrate Postgres user between source and target Aurora Cluster",
		Run: func(cmd *cobra.Command, args []string) {
			akpUtilUser, err := NewAKPUtilUser(sourceDbEncryptionKey, nil, cmd.Context(), time.Hour)
			cli.CheckErr(err)
			defer func() { _ = akpUtilUser.Close() }()

			sourceDbPool, err := database.GetDBPool(dbConnection(sourceDbHost, sourceDbPassword), nil)
			cli.CheckErr(err)

			targetDbPool, err := database.GetDBPool(dbConnection(targetDbHost, targetDbPassword), nil)
			cli.CheckErr(err)

			defer func() {
				_ = database.CloseDBPool(sourceDbPool)
				_ = database.CloseDBPool(targetDbPool)
			}()

			_, err = sourceDbPool.DB.Exec("select version(); select current_database();")
			cli.CheckErr(err)

			_, err = targetDbPool.DB.Exec("select version(); select current_database();")
			cli.CheckErr(err)

			migrateUsers(sourceDbPool.DB, targetDbPool.DB)
		},
	}

	cmd.Flags().StringVar(&sourceDbEncryptionKey, "source-db-encryption-key", "", "Source portal DB Base64-encoded encryption key")
	cli.CheckErr(cmd.MarkFlagRequired("source-db-encryption-key"))

	cmd.Flags().StringVar(&sourceDbHost, "source-db-host", "", "Source portal DB host")
	cli.CheckErr(cmd.MarkFlagRequired("source-db-host"))

	cmd.Flags().StringVar(&sourceDbPassword, "source-db-password", "", "Source portal DB password")
	cli.CheckErr(cmd.MarkFlagRequired("source-db-password"))

	cmd.Flags().StringVar(&targetDbHost, "target-db-host", "", "Target tenant DB host")
	cli.CheckErr(cmd.MarkFlagRequired("target-db-host"))

	cmd.Flags().StringVar(&targetDbPassword, "target-db-password", "", "Target tenant DB password")
	cli.CheckErr(cmd.MarkFlagRequired("target-db-password"))

	return cmd
}

func migrateUsers(sourceDb, targetDb boil.ContextExecutor) {
	var createCancels []context.CancelFunc
	selectContext, selectCancel := context.WithTimeout(context.Background(), time.Minute)
	defer selectCancel()
	defer func() {
		for _, createCancel := range createCancels {
			createCancel()
		}
	}()

	result, err := sourceDb.QueryContext(selectContext, "SELECT instance_id, private_spec FROM argo_cd_instance_config")
	cli.CheckErr(err)
	defer func() { _ = result.Close() }()

	usersCounter := 0
	for result.Next() {
		var instanceId, privateSpec string
		cli.CheckErr(result.Scan(&instanceId, &privateSpec))

		fmt.Printf("Instance %q: ", instanceId)

		privateSpecDecrypted, err := encryption.Decrypt(privateSpec)
		cli.CheckErr(err)
		var privateSpecMap map[string]any
		cli.CheckErr(json.Unmarshal([]byte(privateSpecDecrypted), &privateSpecMap))

		username := privateSpecMap["k3s_username"].(string)
		password := privateSpecMap["k3s_password"].(string)

		fmt.Printf("private_spec decrypted, ")
		createContext, createCancel := context.WithTimeout(context.Background(), time.Minute)
		createCancels = append(createCancels, createCancel)
		cli.CheckErr(database.CreateTenantDatabaseSchema(targetDb, createContext, username, username, password, false))
		fmt.Printf("USER %q created\n", username)
		usersCounter++
	}

	if err := result.Err(); err != nil {
		cli.CheckErr(err)
	}

	fmt.Printf("%d users created\n", usersCounter)
}

func dbConnection(dbHost, dbPassword string) string {
	// https://pkg.go.dev/github.com/lib/pq#hdr-Connection_String_Parameters
	return fmt.Sprintf("dbname=%s user=%s password=%s host=%s port=5432 sslmode=require", defaultDbName, defaultDbUser, dbPassword, dbHost)
}
