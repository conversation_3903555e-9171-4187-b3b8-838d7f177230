package user

import (
	"context"
	"encoding/base64"
	"time"

	"k8s.io/client-go/tools/clientcmd"

	"github.com/akuityio/akuity-platform/internal/akputil/shared"
	"github.com/akuityio/akuity-platform/models/util/encryption"
)

type AKPUtilUser struct {
	*shared.AKPUtil
}

func NewAKPUtilUser(encryptionKey string, clientConfig clientcmd.ClientConfig, ctx context.Context, contextTimeout time.Duration) (*AKPUtilUser, error) {
	key, err := base64.StdEncoding.DecodeString(encryptionKey)
	if err != nil {
		return nil, err
	}

	if err := encryption.SetCryptKey(key); err != nil {
		return nil, err
	}

	return &AKPUtilUser{AKPUtil: shared.NewAKPUtil(clientConfig, ctx, contextTimeout)}, nil
}
