package user

import (
	"bytes"
	"compress/gzip"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/auth0/go-auth0/management"
	"github.com/go-logr/logr"
	"github.com/lib/pq"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"gopkg.in/gomail.v2"
	"k8s.io/client-go/kubernetes"

	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/notifications"
	"github.com/akuityio/akuity-platform/internal/utils/aws"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

const (
	expiryTime        = "expiry_time"
	dryRunExpiryTime  = "dryrun_expiry_time"
	emailTemplateYaml = "inactivity_user_removal"
	doesNotExists     = "42P01"
)

type TableTarget struct {
	table  string
	column string
}

var (
	tableWithDeletionTimestamp = map[string]struct{}{
		"kargo_agent":      {},
		"kargo_instance":   {},
		"argo_cd_cluster":  {},
		"argo_cd_instance": {},
	}
	kargoOrder = []TableTarget{
		{"kargo_agent", "instance_id"},
		{"kargo_instance_config", "instance_id"},
		{"kargo_promotions", "instance_id"},
	}
	argoCDOrder = []TableTarget{
		{"argo_cd_cluster", "instance_id"},
		{"argo_cd_instance_config", "instance_id"},
		{"argo_cd_sync_operation", "instance_id"},
	}
	orgDeletionOrder = []TableTarget{
		{"workspace_member", "organization_id"},
		{"workspace", "organization_id"},
		{"team_user", "organization_id"},
		{"team", "organization_id"},
		{"organization_user", "organization_id"},
		{"organization_sso_realm", "organization_id"},
		{"organization_notification_config", "organization_id"},
		{"organization_kubevision_usage", "organization_id"},
		{"organization_invite", "organization_id"},
		{"logs", "organization_id"},
		{"event", "organization_id"},
		{"custom_role", "organization_id"},
		{"cluster_addons", "organization_id"},
		{"billing", "organization_id"},
		{"audit_log_archive", "organization_id"},
		{"audit_log", "organization_id"},
		{"argo_cd_cluster_k8s_object", "organization_id"},
		{"argo_cd_cluster_k8s_event", "organization_id"},
		{"api_key", "organization"},
		{"ai_conversation", "organization_id"},
		{"addons", "organization_id"},
		{"addon_repo", "organization_id"},
		{"addon_marketplace_installs", "organization_id"},
		// new safe order
		{"kargo_instance", "organization_owner"},
		{"argo_cd_instance", "organization_owner"},
		{"organization", "id"}, // to be deleted last
	}

	userDeletionOrder = []TableTarget{
		{"workspace_member", "user_id"},
		{"team_user", "user_id"},
		{"instance_role", "user_id"},
		{"blacklisted_tokens", "akuity_user_id"},
		{"organization_user", "user_id"},
		{"akuity_user", "id"}, // to be deleted last
	}
)

type BackupData struct {
	OrgBackupData  []*OrgBackup `json:"org_backup_data"`
	UserBackupData *UserBackup  `json:"user_backup_data"`
	Timestamp      time.Time    `json:"timestamp"`
}

type OrgBackup struct {
	Organization      models.Organization `json:"org"`
	SingleMember      bool                `json:"single_members"`
	KargoInstanceIDs  []string            `json:"kargo_instance_ids"`
	ArgoCDInstanceIDs []string            `json:"argo_cd_instance_ids"`
	OrgRefData        map[string]string   `json:"org_ref_data"`
	Timestamp         time.Time           `json:"timestamp"`
}

type UserBackup struct {
	User        models.AkuityUser `json:"user"`
	UserRefData map[string]string `json:"user_ref_data"`
	Auth0Data   management.User   `json:"auth0_data"`
	Timestamp   time.Time         `json:"timestamp"`
}

type DBCleanupSummary struct {
	NumberOfInactiveUserDB           int `json:"number_of_inactive_user"`
	NumberOfDeletedUserDB            int `json:"number_of_deleted_user"`
	NumberOfDeletionQualifiedUsersDB int `json:"number_of_deletion_qualified_users"`
	NumberOfUsersIgnoredDB           int `json:"number_of_users_ignored"`
	NumberOfErroredUserWhileDeleteDB int `json:"number_of_errored_user_while_delete"`
	NumberOfNewUserMarkedDeletionDB  int `json:"number_of_new_user_marked_deletion"`
	NumberOfUserExpiryTimeRemovedDB  int `json:"number_of_user_expiry_time_removed"`
}
type DBCleaner struct {
	ctx                    context.Context
	logger                 logr.Logger
	kubeClient             *kubernetes.Clientset
	dbPool                 *database.DBPool
	auth0Management        *management.Management
	k3sDBPool              *database.DBPool
	smtpConfig             *config.SMTPConfig
	dryRun                 bool
	inactiveDays           int
	deletionWaitDays       int
	s3Bucket               string
	orgValidationConfigMap map[string]bool
}

func NewDBCleaner(
	ctx context.Context,
	logger logr.Logger,
	kubeClient *kubernetes.Clientset,
	dbPool *database.DBPool,
	k3sDBPool *database.DBPool,
	auth0Management *management.Management,
	s3Bucket string,
	smtpConfig *config.SMTPConfig,
	dryRun bool,
	inactiveDays int,
	deletionWaitDays int,
	orgValidationConfigMap map[string]bool,
) *DBCleaner {
	return &DBCleaner{
		ctx:                    ctx,
		logger:                 logger,
		dbPool:                 dbPool,
		k3sDBPool:              k3sDBPool,
		kubeClient:             kubeClient,
		auth0Management:        auth0Management,
		smtpConfig:             smtpConfig,
		dryRun:                 dryRun,
		inactiveDays:           inactiveDays,
		deletionWaitDays:       deletionWaitDays,
		s3Bucket:               s3Bucket,
		orgValidationConfigMap: orgValidationConfigMap,
	}
}

func (cl *DBCleaner) BackupAndClean() (*DBCleanupSummary, error) {
	summaryDB := &DBCleanupSummary{}
	users, err := cl.fetchInactiveUsersDB()
	if err != nil {
		cl.logger.Error(err, "failed to fetch inactive users")
		return nil, err
	}
	summaryDB.NumberOfInactiveUserDB = len(users)

	for _, user := range users {
		orgs, err := cl.getOrganizationsForUser(user.ID)
		if err != nil {
			cl.logger.Error(err, "failed to fetch organizations for user id : %s", user.ID)
			summaryDB.NumberOfErroredUserWhileDeleteDB++
			return summaryDB, err
		}
		userOrgState, err := cl.getUserOrgState(orgs)
		if err != nil {
			cl.logger.Error(err, "failed to get org status for user id : %s", user.ID)
			summaryDB.NumberOfErroredUserWhileDeleteDB++
			return summaryDB, err
		}
		if userOrgState.eligibleForRevive {
			userInfo, err := user.GetUserInfoPublic()
			if err != nil {
				cl.logger.Error(err, "failed to get userInfoPublic for user id : %s", user.ID)
				summaryDB.NumberOfErroredUserWhileDeleteDB++
				return summaryDB, err
			}
			if userInfo.DryRunExpiryTime != 0 || userInfo.ExpiryTime != 0 {
				if err := cl.removeExpiryForUser(user.ID); err != nil {
					cl.logger.Error(err, "failed to remove expiry for user id : %s", user.ID)
					summaryDB.NumberOfErroredUserWhileDeleteDB++
					return summaryDB, err
				}
				summaryDB.NumberOfUserExpiryTimeRemovedDB += 1
			}
			summaryDB.NumberOfUsersIgnoredDB++
			continue
		}

		if userOrgState.hasValidOrgTrial {
			summaryDB.NumberOfUsersIgnoredDB++
			continue
		}

		state, err := cl.userDeletionState(user)
		if err != nil {
			cl.logger.Error(err, fmt.Sprintf("failed to fetch user id : %s, email : %s  deletion state from db.", user.ID, user.Email))
			summaryDB.NumberOfErroredUserWhileDeleteDB++
			continue // unknown deletion state check other users
		}

		if state.qualifiedForDeletion {
			summaryDB.NumberOfDeletionQualifiedUsersDB++
			backupData, err := cl.backupUserWithDependencies(user)
			if err != nil {
				summaryDB.NumberOfErroredUserWhileDeleteDB++
				return summaryDB, fmt.Errorf("failed to backup user data for user id : %s, email : %s : error : %w", user.ID, user.Email, err)
			}

			if backupData != nil {
				err := cl.deleteUserWithDependencies(user, backupData)
				if err != nil {
					summaryDB.NumberOfErroredUserWhileDeleteDB++
					return summaryDB, fmt.Errorf("failed to delete user data for user : %s, email :%s : error : %w", user.ID, user.Email, err)
				} else {
					summaryDB.NumberOfDeletedUserDB++
				}
			}
			continue
		}

		if !state.markedForDeletion {
			if cl.dryRun {
				cl.logger.Info(fmt.Sprintf("DryRun : would have email notification sent to %s", user.Email))
			} else {
				if err := cl.emailNotification(user.Email); err != nil {
					cl.logger.Error(err, fmt.Sprintf("failed to send email notification to user id : %s , email : %s", user.ID, user.Email))
					// there was error sending notification to the user so we do not want to delete without the notification.
					continue
				}
				cl.logger.Info(fmt.Sprintf("email notification sent to %s", user.Email))
			}

			if err := cl.markForDeletionInDB(user.ID, cl.deletionWaitDays); err != nil {
				cl.logger.Error(err, fmt.Sprintf("failed to mark user id : %s, email :%s for deletion in db", user.ID, user.Email))
			}
			summaryDB.NumberOfNewUserMarkedDeletionDB++
		} else {
			summaryDB.NumberOfUsersIgnoredDB++
		}
	}

	// this takes care of users who came back after getting marked for deletion in db.
	numberOfTagsRemoved, err := cl.removeDeletionTagForReactiveUsers(cl.deletionWaitDays)
	if err != nil {
		cl.logger.Error(err, "failed to remove tags for reactive users")
	}
	summaryDB.NumberOfUserExpiryTimeRemovedDB = numberOfTagsRemoved
	return summaryDB, nil
}

func (cl *DBCleaner) getAuth0User(email string) (*management.User, error) {
	query := fmt.Sprintf(`email:"%s"`, email)
	users, err := cl.auth0Management.User.List(cl.ctx, management.Query(query))
	if err != nil {
		return nil, fmt.Errorf("failed to fetch user from Auth0: %w", err)
	}
	if users.Length == 0 {
		return nil, nil
	}
	return users.Users[0], nil
}

func (cl *DBCleaner) markForDeletionInDB(userId string, waitDays int) error {
	markingDate := time.Now().Add(time.Duration(waitDays) * 24 * time.Hour).Unix()
	deletionMark := expiryTime
	if cl.dryRun {
		deletionMark = dryRunExpiryTime
	}

	query := fmt.Sprintf(`UPDATE akuity_user 
    SET user_info_public = jsonb_set(
        COALESCE(user_info_public, '{}'::jsonb), 
        '{%s}', 
        to_jsonb($1::bigint)
    ) 
    WHERE id = $2;`, deletionMark)

	cl.logger.Info(fmt.Sprintf("update querry marking user id %s for deletion in db , querry : %s", userId, query))

	_, err := cl.dbPool.DB.Exec(query, markingDate, userId)
	if err != nil {
		return fmt.Errorf("failed to mark user id : %s for deletion: %w", userId, err)
	}

	cl.logger.Info(fmt.Sprintf("successfully marked user id : %s for deletion in db", userId))
	return nil
}

func (cl *DBCleaner) getOrganizationsForUser(userID string) ([]*models.Organization, error) {
	if userID == "" {
		return nil, fmt.Errorf("user ID cannot be empty")
	}

	rs := client.NewRepoSet(cl.dbPool.DB)

	orgs, err := rs.Organizations().Filter(
		qm.InnerJoin("organization_user on organization_user.organization_id = organization.id"),
		qm.Where("organization_user.user_id = ?", userID),
	).ListAll(cl.ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get organizations for user %s: %w", userID, err)
	}

	return orgs, nil
}

type deletionState struct {
	qualifiedForDeletion bool
	markedForDeletion    bool
}

// userDeletionState return with current deletionState for user.
// markedForDeletion : true if user is already marked with an expiry date in db.
// qualifiedForDeletion : true if user has crossed the expiry date and should be deleted in this attempt.
func (cl *DBCleaner) userDeletionState(user *models.AkuityUser) (*deletionState, error) {
	userInfoPublic, err := user.GetUserInfoPublic()
	if err != nil {
		// instead of nil, return all default values : false
		return &deletionState{}, err
	}

	date := userInfoPublic.DryRunExpiryTime
	if !cl.dryRun {
		date = userInfoPublic.ExpiryTime
	}

	if date != 0 {
		if date < time.Now().Unix() {
			return &deletionState{qualifiedForDeletion: true, markedForDeletion: true}, nil
		}
		return &deletionState{qualifiedForDeletion: false, markedForDeletion: true}, nil
	}

	return &deletionState{}, nil
}

// fetchInactiveUsers fetches users inactive for more than X days
func (cl *DBCleaner) fetchInactiveUsersDB() ([]*models.AkuityUser, error) {
	rs := client.NewRepoSet(cl.dbPool.DB)
	return rs.Users().Filter(
		qm.Where("last_activity_timestamp < NOW() - ($1 * INTERVAL '1 day')", cl.inactiveDays)).
		ListAll(cl.ctx)
}

func (cl *DBCleaner) emailNotification(userEmail string) error {
	templates, err := notifications.LoadTemplates()
	if err != nil {
		return fmt.Errorf("failed to load templates: %w", err)
	}
	template, exists := templates[emailTemplateYaml]
	if !exists {
		return fmt.Errorf("email template : %s not found", emailTemplateYaml)
	}
	vars := map[string]interface{}{
		"days_remaining": cl.deletionWaitDays,
	}
	email, err := template.Email.Format(vars)
	if err != nil {
		return err
	}
	mailer := gomail.NewDialer(cl.smtpConfig.Host, cl.smtpConfig.Port, cl.smtpConfig.User, cl.smtpConfig.Password)

	m := gomail.NewMessage()
	m.SetAddressHeader("From", cl.smtpConfig.NotificationEmail, "Akuity Platform")
	m.SetHeader("To", userEmail)
	// https://us-west-2.console.aws.amazon.com/ses/home?region=us-west-2#/configuration-sets/ses-history
	m.SetHeader("X-SES-CONFIGURATION-SET", "ses-history")

	m.SetHeader("Subject", email.Subject)
	m.SetBody("text/html", email.ParseBody)

	if err := mailer.DialAndSend(m); err != nil {
		return err
	}
	return nil
}

func (cl *DBCleaner) backupUserWithDependencies(user *models.AkuityUser) (*BackupData, error) {
	backup, err := cl.createCompleteDBBackup(user.ID)
	if err != nil {
		return nil, fmt.Errorf("backup failed for user_id : %s, err :  %w", user.ID, err)
	}

	auth0User, err := cl.getAuth0User(user.Email)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch user from Auth0 : %s error : %w", user.Email, err)
	}
	if auth0User != nil {
		backup.UserBackupData.Auth0Data = *auth0User
	}
	if err := cl.uploadBackupToS3(backup, user.ID); err != nil {
		return nil, fmt.Errorf("backup upload failed: %w", err)
	}
	cl.logger.Info(fmt.Sprintf("Backup data for user with email : %s uploaded to s3 successfully", user.Email))
	return backup, nil
}

func (cl *DBCleaner) deleteUserWithDependencies(user *models.AkuityUser, backup *BackupData) error {
	tx, err := cl.dbPool.DB.BeginTx(cl.ctx, nil)
	if err != nil {
		return fmt.Errorf("transaction failed: %w", err)
	}

	defer func() {
		if err := tx.Rollback(); err != nil && !errors.Is(err, sql.ErrTxDone) {
			cl.logger.Error(err, "failed to rollback transaction")
		}
	}()

	// Process organizations
	for _, org := range backup.OrgBackupData {
		if org.SingleMember {
			// this is out of transaction
			// can not perform with transaction as the timestamp has to be visible for other sessions

			// set deletion_timestamp for Argo and Kargo auto deletion tables.
			for tableName := range tableWithDeletionTimestamp {
				if err := cl.setDeletionTimestampAndVerify(tableName, []byte(org.OrgRefData[tableName]), 5); err != nil {
					return fmt.Errorf("failed to mark deletion timestamp for table : %s : error : %w", tableName, err)
				}
			}

			// here we actually start using tx transaction.
			if err := cl.deleteOrganizationCascade(tx, user.ID, org.Organization.ID); err != nil {
				return fmt.Errorf("failed to delete org %s : error : %w", org.Organization.ID, err)
			}
		}

		if cl.dryRun {
			cl.logger.Info(fmt.Sprintf("DryRun : Removed user id : %s from organization", user.ID))
		} else {
			if _, err := tx.ExecContext(cl.ctx, `DELETE FROM organization_user WHERE organization_id = $1 AND user_id = $2`, org.Organization.ID, user.ID); err != nil {
				return fmt.Errorf("failed to remove from org %s: %w", org.Organization.ID, err)
			}
			cl.logger.Info(fmt.Sprintf("Removed user id : %s from organization", user.ID))
		}
	}

	// Delete user and all references
	if err := cl.deleteUserReferences(tx, user.ID); err != nil {
		return fmt.Errorf("failed to delete user: %w", err)
	}
	if backup.UserBackupData.Auth0Data.ID != nil {
		if err := deleteUserFromAuth0(cl.ctx, cl.logger, cl.auth0Management, &backup.UserBackupData.Auth0Data, cl.dryRun); err != nil {
			return fmt.Errorf("failed to delete user from Auth0 : %s : error : %w", user.Email, err)
		}
	}
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("commit failed: %w", err)
	}

	cl.logger.Info("UserBackupData deleted with all dependencies", "user_id", user.ID)
	return nil
}

func (cl *DBCleaner) createCompleteDBBackup(userID string) (*BackupData, error) {
	data := &BackupData{
		Timestamp: time.Now().UTC(),
	}

	rs := client.NewRepoSet(cl.dbPool.DB)
	user, err := rs.Users().GetByID(cl.ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("backup failed, error to get user : %w", err)
	}

	data.UserBackupData = &UserBackup{
		User: *user,
	}

	orgs, err := cl.getOrganizationsForUser(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get organizations: %w", err)
	}

	data.OrgBackupData = make([]*OrgBackup, 0, len(orgs))
	for _, org := range orgs {
		orgBackup := &OrgBackup{
			Organization: *org,
			OrgRefData:   make(map[string]string),
			Timestamp:    time.Now().UTC(),
		}
		isSingleUser, err := cl.isSingleUserOrg(org.ID)
		if err != nil {
			return nil, fmt.Errorf("failed to check org membership: %w", err)
		}

		if isSingleUser {
			orgBackup.SingleMember = true
			if err := cl.backupOrgDependencies(org.ID, orgBackup); err != nil {
				return nil, fmt.Errorf("failed to backup org %s: %w", org.ID, err)
			}
		}
		data.OrgBackupData = append(data.OrgBackupData, orgBackup)
	}

	data.UserBackupData.Timestamp = time.Now().UTC()
	if err := cl.backupUserDependencies(userID, data.UserBackupData); err != nil {
		return nil, fmt.Errorf("failed to backup user dependencies: %w", err)
	}

	return data, nil
}

// Simplified isSingleUserOrg check (no transaction needed for backup)
func (cl *DBCleaner) isSingleUserOrg(orgID string) (bool, error) {
	var count int
	err := cl.dbPool.DB.QueryRowContext(cl.ctx, `SELECT COUNT(*) FROM organization_user WHERE organization_id = $1`, orgID).Scan(&count)
	return count == 1, err
}

// Backup only the org's tables that will be deleted
func (cl *DBCleaner) backupOrgDependencies(orgID string, backup *OrgBackup) error {
	if backup.OrgRefData == nil {
		backup.OrgRefData = make(map[string]string)
	}

	for _, item := range orgDeletionOrder {
		jsonData, err := cl.backupTableData(item.table, item.column, orgID)
		if err != nil {
			return err
		}

		backup.OrgRefData[item.table] = string(jsonData)

		if item.table == "kargo_instance" {
			kargoIDs, err := idsFromJSON(jsonData)
			if err != nil {
				return fmt.Errorf("backup id from json failed: %w", err)
			}
			backup.KargoInstanceIDs = kargoIDs
			for _, kargoID := range kargoIDs {
				if err := cl.backupReferences(kargoID, backup, kargoOrder); err != nil {
					return fmt.Errorf("backup query failed for kargo references: %w", err)
				}

				// k3s table kargo_instance_<id>.kine dump
				tableName := fmt.Sprintf("kargo_instance_%s.kine", kargoID)
				k3sKargoDump, err := cl.backupK3STable(tableName)
				if err != nil {
					return fmt.Errorf("backup k3s table failed: %w", err)
				}
				backup.OrgRefData[tableName] = string(k3sKargoDump)
			}

		}

		if item.table == "argo_cd_instance" {
			argoIDs, err := idsFromJSON(jsonData)
			if err != nil {
				return fmt.Errorf("backup id from json failed: %w", err)
			}
			backup.ArgoCDInstanceIDs = argoIDs
			for _, argoID := range argoIDs {
				if err := cl.backupReferences(argoID, backup, argoCDOrder); err != nil {
					return fmt.Errorf("backup query failed for argo_cd references: %w", err)
				}

				// k3s table instance_<id>.kine dump
				tableName := fmt.Sprintf("instance_%s.kine", argoID)
				k3sKargoDump, err := cl.backupK3STable(tableName)
				if err != nil {
					return fmt.Errorf("backup k3s table failed: %w", err)
				}
				backup.OrgRefData[tableName] = string(k3sKargoDump)
			}

		}
	}
	return nil
}

func idsFromJSON(data []byte) ([]string, error) {
	var rows []map[string]any
	if err := json.Unmarshal(data, &rows); err != nil {
		return nil, err
	}
	var ids []string
	for _, row := range rows {
		if id, ok := row["id"].(string); ok {
			ids = append(ids, id)
		}
	}
	return ids, nil
}

// Backup only the user's direct dependencies
func (cl *DBCleaner) backupUserDependencies(userID string, backup *UserBackup) error {
	backup.UserRefData = make(map[string]string)
	for _, item := range userDeletionOrder {
		jsonData, err := cl.backupTableData(item.table, item.column, userID)
		if err != nil {
			return err
		}

		if len(jsonData) != 0 {
			backup.UserRefData[item.table] = string(jsonData)
		}
	}
	return nil
}

func (cl *DBCleaner) deleteOrganizationCascade(tx *sql.Tx, userID, orgID string) error {
	for _, item := range orgDeletionOrder {
		if cl.dryRun {
			cl.logger.Info(fmt.Sprintf("DryRun : would have deleted organization id : %s from table %s for user id : %s", orgID, item.table, userID))
		} else {
			if _, err := tx.ExecContext(cl.ctx, fmt.Sprintf(`DELETE FROM %s WHERE %s = $1`, item.table, item.column), orgID); err != nil {
				return fmt.Errorf("failed to delete for user id : %s from %s: %w", userID, item.table, err)
			}
			cl.logger.Info(fmt.Sprintf("Deleted org : %s reference from table : %s for user id %s", orgID, item.table, userID))
		}
	}
	return nil
}

func (cl *DBCleaner) deleteUserReferences(tx *sql.Tx, userID string) error {
	for _, item := range userDeletionOrder {
		if cl.dryRun {
			cl.logger.Info(fmt.Sprintf("DryRun : would have deleted user id %s from table %s", userID, item.table))
		} else {
			if _, err := tx.ExecContext(cl.ctx, fmt.Sprintf(`DELETE FROM %s WHERE %s = $1`, item.table, item.column), userID); err != nil {
				return fmt.Errorf("failed to delete from %s: %w", item.table, err)
			}
			cl.logger.Info(fmt.Sprintf("Deleted user : %s data from table : %s", userID, item.table))
		}
	}
	return nil
}

func compressData(data any) ([]byte, error) {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal backup data: %w", err)
	}

	var buf bytes.Buffer
	gz := gzip.NewWriter(&buf)
	_, err = gz.Write(jsonData)
	if err != nil {
		return nil, fmt.Errorf("failed to compress data: %w", err)
	}
	gz.Close()
	return buf.Bytes(), nil
}

// ToDo need to find if any metadata is required for this .
func (cl *DBCleaner) uploadBackupToS3(backup *BackupData, userID string) error {
	info, err := backup.UserBackupData.User.GetUserInfoPublic()
	if err != nil {
		return fmt.Errorf("failed to get user info while backup upload : %w", err)
	}
	if info.DryRunBackupExists {
		cl.logger.Info(fmt.Sprintf("Backup upload skipped because backup already exists for user id : %s", userID))
		return nil
	}
	compressedUserData, err := compressData(backup.UserBackupData)
	if err != nil {
		return fmt.Errorf("failed to compress user backup data: %w", err)
	}

	folder := "user-archive"
	if cl.dryRun {
		folder = fmt.Sprintf("dry-run-%s", folder)
	}

	// user-archive/user/<email>_<id>_<timestamp>.gz , user-archive/org/<orgName>_<orgID>_<timestamp>.gz
	// dry-run-user-archive/user/<email>_<id>_<timestamp>.gz  , dry-run-user-archive/org/<orgName>_<orgID>_<timestamp>.gz
	// dry-run-user-archive/org/test-org_okyx6vsowl62ehsg_20250410_185614.gz
	// dry-run-user-archive/user/xiaopeng@akuity.io_76ktjtzx2tcnbsm1_20250410_185614.gz
	// Add timestamp in format: YYYYMMDD_HHMMSS
	timestamp := time.Now().UTC().Format("20060102_150405")
	extension := ".gz"
	userS3Key := fmt.Sprintf("%s/user/%s_%s_%s%s", folder, backup.UserBackupData.User.Email, userID, timestamp, extension)
	if err := aws.S3PutObject(cl.ctx, &compressedUserData, cl.s3Bucket, userS3Key, "", map[string]string{}); err != nil {
		return fmt.Errorf("failed to upload user backup to s3 with file name %s : %w", userS3Key, err)
	}
	cl.logger.Info(fmt.Sprintf("Upload user backup to S3 successful with file name %s", userS3Key))

	for _, data := range backup.OrgBackupData {
		orgS3Key := fmt.Sprintf("%s/org/%s_%s_%s%s", folder, data.Organization.Name, data.Organization.ID, timestamp, extension)
		compressedOrgData, err := compressData(data)
		if err != nil {
			return fmt.Errorf("failed to compress org backup data: %w", err)
		}

		if err := aws.S3PutObject(cl.ctx, &compressedOrgData, cl.s3Bucket, orgS3Key, "", map[string]string{}); err != nil {
			return fmt.Errorf("failed to upload org backup to s3 with file name %s : %w", orgS3Key, err)
		}
		cl.logger.Info(fmt.Sprintf("Upload org backup to S3 successful with file name %s", orgS3Key))
	}
	if cl.dryRun {
		if err := cl.markDryRunLastBackup(userID); err != nil {
			return fmt.Errorf("failed to mark dry run last backup for user : %w", err)
		}
	}
	return nil
}

func (cl *DBCleaner) isVerifiedOrganization(orgID string) bool {
	return cl.orgValidationConfigMap[orgID]
}

// removeDeletionTagForReactiveUsers removes expiry time for users who did some activity after they were
// marked for deletion in db and return its count
func (cl *DBCleaner) removeDeletionTagForReactiveUsers(gracePeriodDays int) (int, error) {
	gracePeriodSeconds := int64((time.Duration(gracePeriodDays) * 24 * time.Hour).Seconds())

	updateQuery := `
UPDATE akuity_user
SET user_info_public = user_info_public::jsonb - 'expiry_time' - 'dryrun_expiry_time' - 'dryrun_backup_exists'
WHERE user_info_public IS NOT NULL
  AND last_activity_timestamp IS NOT NULL
  AND (
    (
      user_info_public::jsonb->>'expiry_time' IS NOT NULL AND 
      (user_info_public::jsonb->>'expiry_time')::bigint > 0 AND
      last_activity_timestamp > to_timestamp((user_info_public::jsonb->>'expiry_time')::bigint - $1)
    ) 
    OR 
    (
      user_info_public::jsonb->>'dryrun_expiry_time' IS NOT NULL AND 
      (user_info_public::jsonb->>'dryrun_expiry_time')::bigint > 0 AND
      last_activity_timestamp > to_timestamp((user_info_public::jsonb->>'dryrun_expiry_time')::bigint - $1)
    )
  )
`
	result, err := cl.dbPool.DB.ExecContext(cl.ctx, updateQuery, gracePeriodSeconds)
	if err != nil {
		return 0, fmt.Errorf("failed to remove expiry timestamps: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return 0, fmt.Errorf("failed to get rows affected count: %w", err)
	}

	cl.logger.Info(fmt.Sprintf("Removed expiry timestamps for %d users", rowsAffected))
	return int(rowsAffected), nil
}

func (cl *DBCleaner) backupTableData(table, column, id string) ([]byte, error) {
	var jsonData []byte
	err := cl.dbPool.DB.QueryRowContext(cl.ctx, fmt.Sprintf(`SELECT COALESCE(json_agg(row_to_json(t)),'[]'::json) FROM ( SELECT * FROM %s WHERE %s = $1 ) t`, table, column), id).Scan(&jsonData)

	if errors.Is(err, sql.ErrNoRows) {
		return []byte{}, nil
	}
	if err != nil {
		return nil, fmt.Errorf("%s backup query failed: %w", table, err)
	}

	return jsonData, nil
}

func (cl *DBCleaner) backupReferences(id string, backup *OrgBackup, order []TableTarget) error {
	for _, item := range order {
		newJsonData, err := cl.backupTableData(item.table, item.column, id)
		if err != nil {
			return err
		}
		if existing, exists := backup.OrgRefData[item.table]; exists {
			mergedJson, err := mergeJsonArrays([]byte(existing), newJsonData)
			if err != nil {
				return fmt.Errorf("failed merging %s : %w", item.table, err)
			}
			backup.OrgRefData[item.table] = string(mergedJson)
		} else {
			backup.OrgRefData[item.table] = string(newJsonData)
		}
	}
	return nil
}

func mergeJsonArrays(existing, new []byte) ([]byte, error) {
	var existingArr, newArr []json.RawMessage
	if err := json.Unmarshal(existing, &existingArr); err != nil {
		return nil, err
	}
	if err := json.Unmarshal(new, &newArr); err != nil {
		return nil, err
	}
	merged := append(existingArr, newArr...)
	return json.Marshal(merged)
}

func (cl *DBCleaner) backupK3STable(fullTableName string) ([]byte, error) {
	var jsonData []byte

	query := fmt.Sprintf(`SELECT COALESCE(json_agg(row_to_json(t)),'[]'::json)FROM (SELECT * FROM %s) t`, fullTableName)

	err := cl.k3sDBPool.DB.QueryRowContext(cl.ctx, query).Scan(&jsonData)
	if err != nil {
		var pqErr *pq.Error
		if errors.As(err, &pqErr) && pqErr.Code == doesNotExists {
			cl.logger.Info(fmt.Sprintf("Table %s does not exist, skipping...", fullTableName))
			return jsonData, nil
		}
		return jsonData, fmt.Errorf("querying table %s failed: %w", fullTableName, err)
	}

	return jsonData, nil
}

func (cl *DBCleaner) setDeletionTimestampAndVerify(tableName string, tableData []byte, maxAttempts int) error {
	if tableName == "" || len(tableData) == 0 {
		return nil
	}

	ids, err := idsFromJSON(tableData)
	if err != nil {
		return fmt.Errorf("failed to extract IDs from %s: %w", tableName, err)
	}

	if len(ids) == 0 {
		return nil
	}

	if cl.dryRun {
		cl.logger.Info(fmt.Sprintf("DryRun : we would have marked deletion_timestamp for table %s", tableName))
		return nil
	}

	if _, err := cl.dbPool.DB.ExecContext(cl.ctx, fmt.Sprintf(`UPDATE %s SET deletion_timestamp = $1 WHERE id = ANY($2)`, tableName), time.Now().UTC(), pq.Array(ids)); err != nil {
		return fmt.Errorf("failed to mark %s for deletion: %w", tableName, err)
	}

	const (
		sleepTime = 60 * time.Second
	)

	for attempt := 1; attempt <= maxAttempts; attempt++ {
		var remaining int
		if err := cl.dbPool.DB.QueryRowContext(cl.ctx, fmt.Sprintf(`SELECT COUNT(*) FROM %s WHERE id = ANY($1)`, tableName), pq.Array(ids)).Scan(&remaining); err != nil {
			return fmt.Errorf("verification failed: %w", err)
		}

		if remaining == 0 {
			return nil // all deleted
		}

		if attempt < maxAttempts {
			time.Sleep(sleepTime)
		}
	}

	return fmt.Errorf("timeout: %d rows in %s not deleted after 5 minutes", len(ids), tableName)
}

func (cl *DBCleaner) removeExpiryForUser(userID string) error {
	updateQuery := `UPDATE akuity_user SET user_info_public = user_info_public::jsonb - 'expiry_time' - 'dryrun_expiry_time' - 'dryrun_backup_exists' WHERE id = $1 AND user_info_public IS NOT NULL`
	_, err := cl.dbPool.DB.ExecContext(cl.ctx, updateQuery, userID)
	if err != nil {
		return fmt.Errorf("failed to remove expiry timestamps for user id : %s : %w", userID, err)
	}
	return nil
}

// `eligibleForRevive` is true if the organization has been verified,
// trail has been changed to false,
// changed to paid customer
// `hasValidOrgTrial` is true if at least one org has not expired
type orgState struct {
	eligibleForRevive bool
	hasValidOrgTrial  bool
}

func (cl *DBCleaner) getUserOrgState(orgs []*models.Organization) (*orgState, error) {
	state := &orgState{hasValidOrgTrial: false}
	for _, org := range orgs {
		if cl.isVerifiedOrganization(org.ID) {
			state.eligibleForRevive = true
			return state, nil
		}

		orgStatus, err := org.GetOrgStatus()
		if err != nil {
			return nil, err
		}

		if orgStatus.State == models.PaidCustomer {
			state.eligibleForRevive = true
			return state, nil
		}

		if orgStatus.Trial {
			now := time.Now().Unix()
			if orgStatus.ExpiryTime > now {
				state.hasValidOrgTrial = true
			}
		} else {
			state.eligibleForRevive = true
			return state, nil
		}
	}
	return state, nil
}

func (cl *DBCleaner) markDryRunLastBackup(userID string) error {
	query := `UPDATE akuity_user SET user_info_public = jsonb_set(user_info_public::jsonb, '{dryrun_backup_exists}', 'true'::jsonb, true) WHERE id = $1;`

	_, err := cl.dbPool.DB.ExecContext(cl.ctx, query, userID)
	if err != nil {
		return fmt.Errorf("failed to set dryrun_backup_exists for user id : %s : %w", userID, err)
	}

	cl.logger.Info(fmt.Sprintf("Set dryrun_backup_exists = true for user id : %s", userID))
	return nil
}
