package hubspot

import (
	"context"
	"strconv"
	"time"

	corev1 "k8s.io/api/core/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	"github.com/akuityio/akuity-platform/internal/utils/consts"
)

type PersistConfigMap struct {
	configMapName       string
	defaultTimestampGap time.Duration
	kubeClient          *kubernetes.Clientset
}

type PersistOperations interface {
	ReadTimestamp(ctx context.Context) (time.Time, error)
	WriteTimestamp(ctx context.Context, t time.Time) error
}

func NewConfigMapPersistence(name string, defaultTimestampGap time.Duration, hostKubeClient *kubernetes.Clientset) PersistOperations {
	return &PersistConfigMap{
		configMapName:       name,
		defaultTimestampGap: defaultTimestampGap,
		kubeClient:          hostKubeClient,
	}
}

func (p *PersistConfigMap) getCM(ctx context.Context) (*corev1.ConfigMap, error) {
	return p.kubeClient.CoreV1().ConfigMaps(consts.AkuityPlatformNamespace).Get(ctx, p.configMapName, v1.GetOptions{})
}

func (p *PersistConfigMap) ReadTimestamp(ctx context.Context) (time.Time, error) {
	defaultTime := time.Now().Add(-p.defaultTimestampGap).UTC()
	cm, err := p.getCM(ctx)
	if err != nil {
		return defaultTime, err
	}

	if cm.Data == nil || cm.Data["timestamp"] == "" {
		return defaultTime, nil
	}
	t, err := strconv.ParseInt(cm.Data["timestamp"], 10, 64)
	if err != nil {
		return defaultTime, err
	}
	return time.Unix(t, 0).UTC(), nil
}

func (p *PersistConfigMap) WriteTimestamp(ctx context.Context, t time.Time) error {
	cm, err := p.getCM(ctx)
	if err != nil {
		return err
	}
	timestamp := strconv.FormatInt(t.UTC().Unix(), 10)
	cm.Data = map[string]string{"timestamp": timestamp}

	_, err = p.kubeClient.CoreV1().ConfigMaps(consts.AkuityPlatformNamespace).Update(ctx, cm, v1.UpdateOptions{})

	return err
}
