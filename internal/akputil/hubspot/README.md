# Hubspot

Hubspot integration using CLI.

```sh
akputil hubspot
```

## Environment Variables

- HUBSPOT_ACCESS_TOKEN
> Create a new private app in Hubspot with permissions `crm.lists.read`, `crm.objects.contacts.read`, `crm.objects.contacts.write`, `crm.lists.write`

- DB_DATA_KEY
> Base64 Encoded key used to encrypt database.

## Commands

### add-new-free-trial-users-to-list

- Add new free trial users to hubspot list.

```sh
akputil hubspot add-new-free-trial-users-to-list --list-id=1
```

**Flags**

### list-id: `string`

Hubspot List Id where you want to add users.

### create-free-trial-active-users-task

- Creates tasks in Hubspot of Active users on Akuity Platform who are on Trial Period.

```sh
akputil hubspot create-free-trial-active-users-task --config-map=hubspot-storage
```

**Flags**

### hubspot-storage: `string`

File path used to read and write the timestamp of execution. It is necessary to have a timestamp to avoid duplication of event "Application creation".