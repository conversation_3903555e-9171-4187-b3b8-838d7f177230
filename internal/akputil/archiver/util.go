package archiver

import (
	"archive/zip"
	"bytes"
	"crypto/sha1"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"time"

	"github.com/akuityio/akuity-platform/internal/utils/consts"
)

// formatDate - formats the date using auditLogsDateFormat
func formatDate(t time.Time) string {
	return t.Format(auditLogsDateFormat)
}

// truncateTime - truncates the time portion from the time.Time specified
func truncateTime(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.UTC().Location())
}

// addDays - adds the number of days provided to the date specified
func addDays(date time.Time, daysToAdd int) time.Time {
	return date.Add(time.Duration(daysToAdd) * consts.Day)
}

// subtractDays - subtracts the number of days provided from the date specified
func subtractDays(date time.Time, daysToSubtract int) time.Time {
	return addDays(date, -daysToSubtract)
}

// mondayBackInTime - returns Monday's date at midnight, back in time days ago
func mondayBackInTime(baseTime time.Time, backInTimeDays uint16) (time.Time, error) {
	// Going back in time days
	startDate := subtractDays(truncateTime(baseTime), int(backInTimeDays))

	// Going to the same week's Monday
	if startDate.Weekday() == time.Sunday {
		startDate = subtractDays(startDate, 6)
	} else {
		startDate = subtractDays(startDate, int(startDate.Weekday()-time.Monday))
	}
	if startDate.Weekday() != time.Monday {
		return time.Time{}, fmt.Errorf("start date %q should have fallen on Monday", formatDate(startDate))
	}

	return startDate, nil
}

// unmarshal - JSON unmarshalls the string specified into a data structure provided
func unmarshal(s string, v any) error {
	if s == "" || s == "{}" || s == "[]" {
		return nil
	}
	if err := json.Unmarshal([]byte(s), v); err != nil {
		return fmt.Errorf("failed to json.Unmarshal(%q): %w", s, err)
	}
	return nil
}

// groupByDate - groups Audit Logs records by date
func groupByDate(auditLogRecords *[]auditLogDBRecord) (*map[string][]auditLogDBRecord, error) {
	if auditLogRecords == nil {
		return nil, fmt.Errorf("nil auditLogRecords passed")
	}

	// date => []auditLogDBRecord, sorted chronologically (from the earliest to the latest for that date)
	auditLogRecordsByDate := map[string][]auditLogDBRecord{}

	for _, auditLogRecord := range *auditLogRecords {
		auditLogDate := formatDate(auditLogRecord.Timestamp)
		if _, ok := auditLogRecordsByDate[auditLogDate]; !ok {
			auditLogRecordsByDate[auditLogDate] = []auditLogDBRecord{}
		}

		auditLogRecordsByDate[auditLogDate] = append(auditLogRecordsByDate[auditLogDate], auditLogRecord)
	}

	return &auditLogRecordsByDate, nil
}

// jsonEncodeAuditLogRecords - encodes Audit Log records provided to JSON and returns its bytes
func jsonEncodeAuditLogRecords(auditLogRecords *[]auditLogDBRecord) ([]byte, error) {
	if auditLogRecords == nil {
		return nil, fmt.Errorf("missing audit log entries to convert")
	}

	var auditLogArchiveRecords []auditLogArchiveRecord

	for _, auditLogRecord := range *auditLogRecords {
		auditLogArchiveRecord, err := convertAuditLogRecord(auditLogRecord)
		if err != nil {
			return nil, err
		}
		auditLogArchiveRecords = append(auditLogArchiveRecords, auditLogArchiveRecord)
	}

	auditLogJsonBytes, err := json.MarshalIndent(auditLogArchiveRecords, "  ", "  ")
	if err != nil {
		return nil, fmt.Errorf("failed to json.MarshalIndent() Audit Log records: %w", err)
	}

	return auditLogJsonBytes, nil
}

// convertAuditLogRecord - converts Audit Log record from DB format (auditLogDBRecord)
// to archive format (auditLogArchiveRecord), to be JSON-encoded later
func convertAuditLogRecord(auditLogRecord auditLogDBRecord) (auditLogArchiveRecord, error) {
	auditLogArchiveRecord := auditLogArchiveRecord{
		Timestamp:             auditLogRecord.Timestamp,
		Action:                "",
		Object:                map[string]interface{}{},
		Details:               map[string]interface{}{},
		Actor:                 map[string]interface{}{},
		Count:                 auditLogRecord.Count,
		LastOccurredTimestamp: auditLogRecord.LastOccurredTimestamp.Time,
	}

	if auditLogRecord.LastOccurredTimestamp.Time.IsZero() {
		auditLogArchiveRecord.LastOccurredTimestamp = auditLogRecord.Timestamp
	}

	if !auditLogRecord.Action.IsZero() {
		auditLogArchiveRecord.Action = auditLogRecord.Action.String
	}

	if !auditLogRecord.Object.IsZero() {
		if err := unmarshal(auditLogRecord.Object.String, &auditLogArchiveRecord.Object); err != nil {
			return auditLogArchiveRecord, err
		}
	}

	if !auditLogRecord.Details.IsZero() {
		if err := unmarshal(auditLogRecord.Details.String, &auditLogArchiveRecord.Details); err != nil {
			return auditLogArchiveRecord, err
		}
	}

	if patch, ok := auditLogArchiveRecord.Details["patch"]; ok {
		if patchString, ok := patch.(string); ok {
			// patch is a string like "{\"version\":\"v2.8.4\"}" or "{\"argocd_cm\":{\"exec.enabled\":\"true\"}}"
			var patchMap map[string]interface{}
			if err := unmarshal(patchString, &patchMap); err != nil {
				return auditLogArchiveRecord, err
			}
			auditLogArchiveRecord.Details["patch"] = patchMap
		}
	}

	if !auditLogRecord.Actor.IsZero() {
		if err := unmarshal(auditLogRecord.Actor.String, &auditLogArchiveRecord.Actor); err != nil {
			return auditLogArchiveRecord, err
		}
	}

	return auditLogArchiveRecord, nil
}

// validateStartEndDate - validates start and end dates for pulling Audit Log records are valid
func validateStartEndDate(startDate, endDate time.Time) error {
	startDateString := formatDate(startDate)
	endDateString := formatDate(endDate)

	if startDate.Year() < archivalPeriodStartYear {
		return fmt.Errorf("archival start date %q should not be earlier than %d", startDateString, archivalPeriodStartYear)
	}

	currentDate := truncateTime(time.Now().UTC())
	if !startDate.Before(currentDate) || !endDate.Before(currentDate) {
		return fmt.Errorf("both start %q and end dates %q should be in the past", startDateString, endDateString)
	}

	archivalPeriodInDays := endDate.Sub(startDate) / consts.Day

	if archivalPeriodInDays < 0 {
		return fmt.Errorf("archival end date %q should not be earlier than the start date %q", endDateString, startDateString)
	}

	if archivalPeriodInDays > maxArchivalPeriodInDays {
		return fmt.Errorf("archival period \"%s - %s\" spans over %d days, should not be longer than %d days",
			startDateString, endDateString, archivalPeriodInDays, maxArchivalPeriodInDays)
	}

	return nil
}

// zipAuditLogRecords - returns Zip archive bytes and its SHA-1 checksum for the Audit Log records provided
func zipAuditLogRecords(zipFileName string, auditLogRecords *[]auditLogDBRecord, archivalPeriodInDays int) ([]byte, string, error) {
	if zipFileName == "" {
		return nil, "", fmt.Errorf("missing zipFileName")
	}

	if auditLogRecords == nil {
		return nil, "", fmt.Errorf("nil auditLogRecords")
	}

	// Grouping Audit Logs records by date: date ("YYYY-MM-DD") => []auditLogDBRecord

	auditLogRecordsByDate, err := groupByDate(auditLogRecords)
	if err != nil {
		return nil, "", err
	}

	datedEntries := len(*auditLogRecordsByDate)

	if datedEntries > archivalPeriodInDays {
		// For a period of N days we produced more than N dated entries
		return nil, "", fmt.Errorf("archiving Audit Logs for %q resulted in %d dated entries which is more than %d days",
			zipFileName, datedEntries, archivalPeriodInDays)
	}

	// Creating Zip file entries mapping: entry name ("YYYY-MM-DD.json") => bytes of Audit Log JSON for that date
	// Each "YYYY-MM-DD.json" entry is a file with Audit Logs records for that date,
	// sorted chronologically (from the earliest to the latest for that date)

	bytesBuffer := bytes.NewBuffer(make([]byte, 0))
	zipWriter := zip.NewWriter(bytesBuffer)

	for auditLogDate, auditLogRecords := range *auditLogRecordsByDate {
		zipEntryName := fmt.Sprintf("%s.json", auditLogDate) // 2023-10-11.json
		zipEntryWriter, err := zipWriter.Create(zipEntryName)
		if err != nil {
			return nil, "", fmt.Errorf("failed to create %s/%s zip entry: %w", zipFileName, zipEntryName, err)
		}
		zipEntryBytes, err := jsonEncodeAuditLogRecords(&auditLogRecords)
		if err != nil {
			return nil, "", fmt.Errorf("failed to JSON-encode %s/%s zip entry records: %w", zipFileName, zipEntryName, err)
		}
		if _, err := zipEntryWriter.Write(zipEntryBytes); err != nil {
			return nil, "", fmt.Errorf("failed to write %s/%s zip entry: %w", zipFileName, zipEntryName, err)
		}
	}

	if err := zipWriter.Close(); err != nil {
		return nil, "", fmt.Errorf("failed to close %q zip writer: %w", zipFileName, err)
	}

	zipContentBytes := bytesBuffer.Bytes()
	if len(zipContentBytes) < 1 {
		return nil, "", fmt.Errorf("%q content is empty", zipFileName)
	}

	hasher := sha1.New()
	hasher.Write(zipContentBytes)
	sha1Checksum := base64.StdEncoding.EncodeToString(hasher.Sum(nil))

	return zipContentBytes, sha1Checksum, nil
}
