package archiver

import (
	"fmt"
	"time"

	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"github.com/akuityio/akuity-platform/internal/akputil/shared"
	"github.com/akuityio/akuity-platform/internal/utils/aws"
	"github.com/akuityio/akuity-platform/internal/utils/consts"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

// AKPUtilAuditLogsArchiver - archives Audit Log records: "audit_log" table => S3 bucket
type AKPUtilAuditLogsArchiver struct {
	*shared.AKPUtil
	s3Bucket string
}

// auditLogDBRecord - single Audit Log DB record in the "audit_log" table
type auditLogDBRecord struct {
	Id                    string      `boil:"id"`
	OrgId                 string      `boil:"organization_id"`
	Timestamp             time.Time   `boil:"timestamp"`
	Action                null.String `boil:"action"`
	Object                null.String `boil:"object"`
	Details               null.String `boil:"details"`
	Actor                 null.String `boil:"actor"`
	Count                 int         `boil:"count"`
	LastOccurredTimestamp null.Time   `boil:"last_occurred_timestamp"`
}

// auditLogArchiveRecord - single Audit Log archive JSON record
type auditLogArchiveRecord struct {
	Timestamp             time.Time              `json:"timestamp"`
	Action                string                 `json:"action"`
	Object                map[string]interface{} `json:"object"`
	Details               map[string]interface{} `json:"details"`
	Actor                 map[string]interface{} `json:"actor"`
	Count                 int                    `json:"count"`
	LastOccurredTimestamp time.Time              `json:"last_occurred_timestamp"`
}

func NewAuditLogsArchiver(akputil *shared.AKPUtil, s3Bucket string) *AKPUtilAuditLogsArchiver {
	return &AKPUtilAuditLogsArchiver{
		AKPUtil:  akputil,
		s3Bucket: s3Bucket,
	}
}

// ArchiveAuditLogsRecentPeriod - archives Audit Logs from the recent period
func (a *AKPUtilAuditLogsArchiver) ArchiveAuditLogsRecentPeriod(orgId string) error {
	startDate, err := mondayBackInTime(time.Now().UTC(), backInTimeDays)
	if err != nil {
		return err
	}
	return a.ArchiveAuditLogsWeeklyIncrements(orgId, startDate)
}

// ArchiveAuditLogsWeeklyIncrements - archives Audit Logs from the start date specified to the last Sunday,
// in weekly increments (taking [Monday - Sunday] period each time)
func (a *AKPUtilAuditLogsArchiver) ArchiveAuditLogsWeeklyIncrements(orgId string, startDate time.Time) error {
	startTime := time.Now()
	currentDate := truncateTime(startTime.UTC())
	// Number of days from the startDate to the following Sunday
	daysToFollowingSunday := (7 - startDate.Weekday()) % 7
	// Sunday corresponding to the startDate's week
	endDate := startDate.Add(time.Duration(daysToFollowingSunday) * consts.Day)

	if !endDate.Before(currentDate) {
		return fmt.Errorf("start date %q is too close to the current date for a weekly archival, should be %q at least",
			formatDate(startDate),
			// Last week's Monday = this week's Sunday - 13 days
			formatDate(endDate.Add(-13*consts.Day)))
	}

	weeksProcessed := 0
	for endDate.Before(currentDate) {
		if endDate.Weekday() != 0 {
			return fmt.Errorf("end date %q should have fallen on Sunday", formatDate(endDate))
		}
		if err := a.ArchiveAuditLogs(orgId, startDate, endDate); err != nil {
			return err
		}
		weeksProcessed++
		startDate = endDate.Add(1 * consts.Day) // Monday
		endDate = endDate.Add(consts.Week)      // Sunday
		if startDate.Weekday() != 1 {
			return fmt.Errorf("start date %q should have fallen on Monday", formatDate(startDate))
		}
	}

	a.Log.Info(fmt.Sprintf("Processed %d weeks", weeksProcessed),
		"timeSeconds", fmt.Sprintf("%.1f", time.Since(startTime).Seconds()))
	return nil
}

// ArchiveAuditLogs - archives Audit Logs for the period specified
func (a *AKPUtilAuditLogsArchiver) ArchiveAuditLogs(orgId string, startDate, endDate time.Time) error {
	if err := validateStartEndDate(startDate, endDate); err != nil {
		return err
	}

	startDate = truncateTime(startDate)
	endDate = truncateTime(endDate)

	if orgId == "" {
		return a.archiveAuditLogsAllOrganizations(startDate, endDate)
	}

	return a.archiveAuditLogsSingleOrganization(orgId, startDate, endDate)
}

// archiveAuditLogsSingleOrganization - creates a Zip archive with Organization's Audit Logs for a time period specified
func (a *AKPUtilAuditLogsArchiver) archiveAuditLogsSingleOrganization(orgId string, startDate, endDate time.Time) error {
	if orgId == "" {
		return fmt.Errorf("missing orgId")
	}

	// Querying Organization's Audit Logs for the [startDate, endDate + 1d) range

	startDateString := formatDate(startDate)
	queryEndDateString := formatDate(endDate.Add(1 * consts.Day))
	auditLogRecords, err := a.readAuditLogsRecords(organizationAuditLogs, orgId, startDateString, queryEndDateString)
	if err != nil {
		return err
	}

	return a.archiveAndUploadAuditLogsRecords(auditLogRecords, orgId, startDate, endDate)
}

// archiveAuditLogsAllOrganizations - creates Zip archives with all Organizations Audit Logs for a time period specified
func (a *AKPUtilAuditLogsArchiver) archiveAuditLogsAllOrganizations(startDate, endDate time.Time) error {
	// Querying all Organizations Audit Logs for the [startDate, endDate + 1d) range

	startDateString := formatDate(startDate)
	queryEndDateString := formatDate(endDate.Add(1 * consts.Day))
	auditLogRecords, err := a.readAuditLogsRecords(allOrganizationsAuditLogs, startDateString, queryEndDateString)
	if err != nil {
		return err
	}

	// Grouping Audit Log records by Org: orgId => []auditLogDBRecord

	auditLogRecordsByOrg := map[string][]auditLogDBRecord{}
	for _, auditLogRecord := range *auditLogRecords {
		orgId := auditLogRecord.OrgId
		if _, ok := auditLogRecordsByOrg[orgId]; !ok {
			auditLogRecordsByOrg[orgId] = []auditLogDBRecord{}
		}
		auditLogRecordsByOrg[orgId] = append(auditLogRecordsByOrg[orgId], auditLogRecord)
	}

	// Archiving each Organization's Audit Logs in its own Zip file

	for orgId, auditLogRecords := range auditLogRecordsByOrg {
		if err := a.archiveAndUploadAuditLogsRecords(&auditLogRecords, orgId, startDate, endDate); err != nil {
			return err
		}
	}

	return nil
}

// readAuditLogsRecords - executes the query with the arguments provided to read the Audit Log records
func (a *AKPUtilAuditLogsArchiver) readAuditLogsRecords(query string, queryArgs ...interface{}) (*[]auditLogDBRecord, error) {
	auditLogsQueryStart := time.Now()
	var auditLogRecords []auditLogDBRecord
	if err := models.NewQuery(qm.SQL(query, queryArgs...)).Bind(a.Ctx, a.PortalDBPool.DB, &auditLogRecords); err != nil {
		return nil, fmt.Errorf("failed to execute query %q with arguments %v: %w", query, queryArgs, err)
	}

	a.Log.Info(fmt.Sprintf("Fetched %d Audit Log records", len(auditLogRecords)),
		"queryArguments", queryArgs,
		"timeMillis", time.Since(auditLogsQueryStart).Milliseconds())

	return &auditLogRecords, nil
}

// archiveAndUploadAuditLogsRecords - archives Organization's Audit Log records and uploads them to an S3 bucket
func (a *AKPUtilAuditLogsArchiver) archiveAndUploadAuditLogsRecords(auditLogRecords *[]auditLogDBRecord, orgId string, startDate, endDate time.Time) error {
	if auditLogRecords == nil {
		return fmt.Errorf("nil auditLogRecords")
	}

	if orgId == "" {
		return fmt.Errorf("missing orgId")
	}

	auditLogRecordsNumber := len(*auditLogRecords)
	if auditLogRecordsNumber < 1 {
		return nil
	}

	startTime := time.Now()
	startDateString := formatDate(startDate)
	endDateString := formatDate(endDate)
	startRecordId := (*auditLogRecords)[0].Id
	endRecordId := (*auditLogRecords)[auditLogRecordsNumber-1].Id
	archivalPeriodInDays := int(endDate.Sub(startDate)/consts.Day) + 1

	// "casy3qevhykuuwkc_2023-10-09_2023-10-15.zip"
	zipFileName := fmt.Sprintf("%s_%s_%s.zip", orgId, startDateString, endDateString)

	// "audit-log/casy3qevhykuuwkc/2023/10/casy3qevhykuuwkc_2023-10-09_2023-10-15.zip"
	s3Key := fmt.Sprintf("audit-log/%s/%d/%02d/%s", orgId, startDate.Year(), startDate.Month(), zipFileName)

	// "s3://akuity.cloud.akp-001-dev-usw2.audit-log-archive/audit-log/casy3qevhykuuwkc/2023/10/casy3qevhykuuwkc_2023-10-09_2023-10-15.zip"
	s3Path := fmt.Sprintf("%s%s/%s", s3PathPrefix, a.s3Bucket, s3Key)

	// Checking if this archive has already been uploaded and journaled in the past

	auditLogsJournalRepoSet := client.NewRepoSet(a.PortalDBPool.DB).AuditLogsArchives()
	journalRecords, err := auditLogsJournalRepoSet.Filter(
		models.AuditLogArchiveWhere.OrganizationID.EQ(orgId),
		models.AuditLogArchiveWhere.StartDate.EQ(startDate),
		models.AuditLogArchiveWhere.EndDate.EQ(endDate),
		// If archive's S3 path changes - we end up re-uploading the archive to the new path
		models.AuditLogArchiveWhere.Path.EQ(s3Path),
	).ListAll(a.Ctx)
	if err != nil {
		return fmt.Errorf("failed to check the Audit Log journal for Organization %q and [%s, %s] period: %w",
			orgId, startDateString, endDateString, err)
	}

	// Checking archive's S3 checksum

	s3Checksum, sizeInBytes, err := aws.S3ObjectAttributes(a.Ctx, a.s3Bucket, s3Key)
	if err != nil {
		return err
	}

	if len(journalRecords) > 0 {
		// The archive has already been uploaded and journaled in the past

		journalRecordID := journalRecords[0].ID
		journalRecordChecksum := journalRecords[0].Checksum

		if s3Checksum == "" {
			return fmt.Errorf("audit Log journal record %q for %q - S3 object at %q is missing",
				journalRecordID, s3Path, s3Key)
		}

		if journalRecordChecksum != s3Checksum {
			return fmt.Errorf("audit Log journal record %q for %q - checksum %q != S3 checksum %q",
				journalRecordID, s3Path, journalRecordChecksum, s3Checksum)
		}

		a.Log.Info(fmt.Sprintf("%d Audit Log records are already uploaded to S3", auditLogRecordsNumber),
			"orgId", orgId,
			"journalRecordID", journalRecordID,
			"startDate", startDateString,
			"endDate", endDateString,
			"days", archivalPeriodInDays,
			"s3Path", s3Path,
			"checksum", journalRecordChecksum,
			"sizeInKBytes", fmt.Sprintf("%.2f", float64(sizeInBytes)/1024),
			"timeMillis", time.Since(startTime).Milliseconds())
		return nil
	}

	if s3Checksum == "" {
		// The archive hasn't been uploaded to S3 yes - zip-ing Audit Log records and uploading the archive to S3

		var zipContentBytes []byte
		zipContentBytes, s3Checksum, err = zipAuditLogRecords(zipFileName, auditLogRecords, archivalPeriodInDays)
		if len(zipContentBytes) < 1 || s3Checksum == "" || err != nil {
			return err
		}

		if err := aws.S3PutObject(a.Ctx, &zipContentBytes, a.s3Bucket, s3Key, s3Checksum, map[string]string{
			"organization": orgId,
			"startDate":    startDateString,
			"endDate":      endDateString,
			"records":      fmt.Sprintf("%d", auditLogRecordsNumber),
			"days":         fmt.Sprintf("%d", archivalPeriodInDays),
			"startRecord":  startRecordId,
			"endRecord":    endRecordId,
		}); err != nil {
			return err
		}

		a.Log.Info(fmt.Sprintf("Uploaded %d Audit Log records to S3", auditLogRecordsNumber),
			"organization_id", orgId,
			"startDate", startDateString,
			"endDate", endDateString,
			"days", archivalPeriodInDays,
			"s3Path", s3Path,
			"checksum", s3Checksum,
			"timeMillis", time.Since(startTime).Milliseconds())
	} else {
		// The journal has no record for this archive (otherwise, we'd leave earlier) but it has already been uploaded to S3.
		// This could happen if after S3 upload we failed to create a journal record below.
		a.Log.Info(fmt.Sprintf("%d Audit Log records are already uploaded to S3", auditLogRecordsNumber),
			"orgId", orgId,
			"startDate", startDateString,
			"endDate", endDateString,
			"days", archivalPeriodInDays,
			"s3Path", s3Path,
			"checksum", s3Checksum,
			"sizeInKBytes", fmt.Sprintf("%.2f", float64(sizeInBytes)/1024),
			"timeMillis", time.Since(startTime).Milliseconds())
	}

	if err := auditLogsJournalRepoSet.Create(a.Ctx, &models.AuditLogArchive{
		OrganizationID: orgId,
		StartDate:      startDate,
		EndDate:        endDate,
		Records:        auditLogRecordsNumber,
		Days:           archivalPeriodInDays,
		StartRecord:    startRecordId,
		EndRecord:      endRecordId,
		Path:           s3Path,
		Checksum:       s3Checksum,
	}); err != nil {
		return fmt.Errorf("failed to create Audit Log journal record for Organization %q and [%s, %s] period: %w",
			orgId, startDateString, endDateString, err)
	}

	return nil
}
