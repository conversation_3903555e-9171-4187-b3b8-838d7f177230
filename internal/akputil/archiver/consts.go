package archiver

import (
	"time"
)

const (
	// archivalPeriodStartYear - a year Audit Log records have started
	archivalPeriodStartYear = 2022

	// maxArchivalPeriodInDays - max archival period allowed (in days) to avoid heavy DB pulls
	maxArchivalPeriodInDays = 31

	// backInTimeDays - number of days to go back when no start date is specified
	backInTimeDays = 90

	// auditLogsDateFormat - date format to use when formatting archival dates: "2023-09-01"
	auditLogsDateFormat = time.DateOnly

	// organizationAuditLogs - a query to fetch Audit Logs for an Organization and period specified
	organizationAuditLogs = `
select
	id, timestamp, action, object, details, actor, count, last_occurred_timestamp
from
	audit_log
where
	organization_id = $1 and
  	timestamp >= $2 and
  	timestamp <  $3
order by timestamp asc
`
	// allOrganizationsAuditLogs - a query to fetch Audit Logs for all Organizations and period specified
	allOrganizationsAuditLogs = `
select
	id, organization_id, timestamp, action, object, details, actor, count, last_occurred_timestamp
from
	audit_log
where
  	timestamp >= $1 and
  	timestamp <  $2
order by timestamp asc
`

	updateAuditLogsIsDeletedSingleOrg = `
	update audit_log
		set is_deleted = $1
	where 
		(extract(epoch from now() - timestamp) / 86400)::integer > $2 and
		not is_deleted = $1 and
		organization_id = $3;
`
	updateAuditLogsIsDeletedAllOrgs = `
	update audit_log
		set is_deleted = $1
	where 
		(extract(epoch from now() - timestamp) / 86400)::integer > $2 and
		not is_deleted = $1;
`
	deleteAuditLogsSingleOrg = `
	delete from audit_log
	where 
		(extract(epoch from now() - timestamp) / 86400)::integer > $1 and 
		organization_id = $2;
`
	deleteAuditLogsAllOrgs = `
	delete from audit_log
	where 
		(extract(epoch from now() - timestamp) / 86400)::integer > $1
`

	s3PathPrefix = "s3://"
)
