package archiver

import (
	"fmt"
	"time"

	"github.com/spf13/cobra"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/akuityio/akuity-platform/internal/akputil/shared"
	"github.com/akuityio/akuity-platform/internal/cli"
)

func NewArchiverCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use: "archiver",
		Run: func(cmd *cobra.Command, args []string) {
			cmd.HelpFunc()(cmd, args)
		},
	}
	cmd.AddCommand(NewArchiveAuditLogRecordsCommand())
	cmd.AddCommand(NewVerifyAuditLogRecordsJournalCommand())
	cmd.AddCommand(NewSoftDeleteAuditLogRecordsCommand())
	cmd.AddCommand(NewUndeleteAuditLogRecordsCommand())
	cmd.AddCommand(NewHardDeleteAuditLogRecordsCommand())
	return cmd
}

// NewArchiveAuditLogRecordsCommand - archives Audit Logs for a single or all Organizations:
// akputil logs archive-audit-logs - archives recent Audit Logs for all Organizations
// akputil logs archive-audit-logs --org-id <orgId> - archives Organization's recent Audit Logs
// akputil logs archive-audit-logs --start-date <startDate> - archives all Organizations Audit Logs from the start date in weekly increments
// akputil logs archive-audit-logs --org-id <orgId> --start-date <startDate> - archives Organization's Audit Logs from the start date in weekly increments
// akputil logs archive-audit-logs --start-date <startDate> --end-date <endDate> - archives all Organization's Audit Logs in the period specified
// akputil logs archive-audit-logs --org-id <orgId> --start-date <startDate> --end-date <endDate> - archives Organization's Audit Logs in the period specified
func NewArchiveAuditLogRecordsCommand() *cobra.Command {
	var (
		clientConfig                                    clientcmd.ClientConfig
		startDate, endDate                              time.Time
		orgId, startDateString, endDateString, s3Bucket string
	)
	// akuity-platform-deploy/base/akuity-platform-shared/archiver/archiver-cronjobs.yaml
	// http://prometheus-prod.monitoring.svc/graph?g0.expr=sum%20by%20(job_name)%20(kube_job_status_completion_time%7Bjob_name%3D~%22audit-log-records-archiver.%2B%22%7D%20-%20kube_job_status_start_time%7Bjob_name%3D~%22audit-log-records-archiver.%2B%22%7D)&g0.tab=0&g0.display_mode=lines&g0.show_exemplars=0&g0.range_input=1w
	cmd := &cobra.Command{
		Use:   "archive-audit-log-records",
		Short: "Archives Audit Log DB records to S3",
		Run: func(cmd *cobra.Command, args []string) {
			akp := shared.NewAKPUtil(clientConfig, cmd.Context(), 5*time.Minute)
			defer func() { _ = akp.Close() }()

			archiver := NewAuditLogsArchiver(akp, s3Bucket)

			if startDateString == "" {
				if endDateString != "" {
					cli.CheckErr(fmt.Errorf("end date is specified without the start date"))
					return
				}
				cli.CheckErr(archiver.ArchiveAuditLogsRecentPeriod(orgId))
				return
			}

			var err error
			startDate, err = time.Parse(auditLogsDateFormat, startDateString)
			cli.CheckErr(err)

			if endDateString == "" {
				cli.CheckErr(archiver.ArchiveAuditLogsWeeklyIncrements(orgId, startDate))
				return
			}

			endDate, err = time.Parse(auditLogsDateFormat, endDateString)
			cli.CheckErr(err)

			cli.CheckErr(archiver.ArchiveAuditLogs(orgId, startDate, endDate))
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().StringVar(&orgId, "org-id", "", "Organization ID to archive its Audit logs")
	cmd.Flags().StringVar(&startDateString, "start-date", "", fmt.Sprintf("Audit Log date to start the archival process from (%q format, optional)", auditLogsDateFormat))
	cmd.Flags().StringVar(&endDateString, "end-date", "", fmt.Sprintf("Audit Log date to stop the archival process at, inclusive (%q format, optional)", auditLogsDateFormat))
	cmd.Flags().StringVar(&s3Bucket, "s3-bucket", "", "S3 bucket name to upload the archives (required)")
	cli.CheckErr(cmd.MarkFlagRequired("s3-bucket"))
	return cmd
}

func NewVerifyAuditLogRecordsJournalCommand() *cobra.Command {
	var (
		clientConfig            clientcmd.ClientConfig
		stalenessBackInTimeDays uint16
	)
	// akuity-platform-deploy/base/akuity-platform-shared/archiver/archiver-cronjobs.yaml
	// http://prometheus-prod.monitoring.svc/graph?g0.expr=sum%20by%20(job_name)%20(kube_job_status_completion_time%7Bjob_name%3D~%22audit-log-records-verifier.%2B%22%7D%20-%20kube_job_status_start_time%7Bjob_name%3D~%22audit-log-records-verifier.%2B%22%7D)&g0.tab=0&g0.display_mode=lines&g0.show_exemplars=0&g0.range_input=1w
	cmd := &cobra.Command{
		Use:   "verify-audit-log-records-journal",
		Short: "Verifies Audit Log journal records",
		Run: func(cmd *cobra.Command, args []string) {
			akp := shared.NewAKPUtil(clientConfig, cmd.Context(), 10*time.Minute)
			defer func() { _ = akp.Close() }()

			cli.CheckErr(NewAuditLogsVerifier(akp).VerifyAuditLogRecordsJournal(stalenessBackInTimeDays))
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().Uint16Var(&stalenessBackInTimeDays, "staleness-days", backInTimeDays, "Number of days to go back to validate for stale records")
	return cmd
}

func NewSoftDeleteAuditLogRecordsCommand() *cobra.Command {
	var (
		clientConfig    clientcmd.ClientConfig
		orgId           string
		retentionPeriod uint16
	)
	// akuity-platform-deploy/base/akuity-platform-shared/archiver/archiver-cronjobs.yaml
	// http://prometheus-prod.monitoring.svc/graph?g0.expr=sum%20by%20(job_name)%20(kube_job_status_completion_time%7Bjob_name%3D~%22audit-log-records-soft-delete.%2B%22%7D%20-%20kube_job_status_start_time%7Bjob_name%3D~%22audit-log-records-soft-delete.%2B%22%7D)&g0.tab=0&g0.display_mode=lines&g0.show_exemplars=0&g0.range_input=1w
	cmd := &cobra.Command{
		Use:   "soft-delete-audit-log-records",
		Short: "Marks old Audit Log records as deleted but keeps them in the DB",
		Run: func(cmd *cobra.Command, args []string) {
			akp := shared.NewAKPUtil(clientConfig, cmd.Context(), time.Minute)
			defer func() { _ = akp.Close() }()

			cli.CheckErr(NewAuditLogsDeleter(akp).SoftDeleteAuditLogs(orgId, retentionPeriod))
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().StringVar(&orgId, "org-id", "", "Organization ID to mark its Audit Log records as deleted")
	cmd.Flags().Uint16Var(&retentionPeriod, "retention-period", 90, "Retention period (in days) - mark as deleted Audit Logs records older than retention period")
	return cmd
}

func NewUndeleteAuditLogRecordsCommand() *cobra.Command {
	var (
		clientConfig    clientcmd.ClientConfig
		orgId           string
		retentionPeriod uint16
	)
	cmd := &cobra.Command{
		Use:   "undelete-audit-log-records",
		Short: "Marks previously soft-deleted Audit Log records as not deleted",
		Run: func(cmd *cobra.Command, args []string) {
			akp := shared.NewAKPUtil(clientConfig, cmd.Context(), time.Minute)
			defer func() { _ = akp.Close() }()

			cli.CheckErr(NewAuditLogsDeleter(akp).UndeleteAuditLogs(orgId, retentionPeriod))
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().StringVar(&orgId, "org-id", "", "Organization ID to mark its Audit Log records as not deleted")
	cmd.Flags().Uint16Var(&retentionPeriod, "retention-period", 90, "Retention period (in days) - mark as not deleted Audit Logs records older than retention period")
	return cmd
}

func NewHardDeleteAuditLogRecordsCommand() *cobra.Command {
	var (
		clientConfig    clientcmd.ClientConfig
		orgId           string
		retentionPeriod uint16
	)
	cmd := &cobra.Command{
		Use:   "hard-delete-audit-log-records",
		Short: "Physically deletes the Audit Log records from the DB",
		Run: func(cmd *cobra.Command, args []string) {
			akp := shared.NewAKPUtil(clientConfig, cmd.Context(), 5*time.Minute)
			defer func() { _ = akp.Close() }()

			cli.CheckErr(NewAuditLogsDeleter(akp).HardDeleteAuditLogs(orgId, retentionPeriod))
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().StringVar(&orgId, "org-id", "", "Organization ID to have its Audit Log records deleted")
	cmd.Flags().Uint16Var(&retentionPeriod, "retention-period", 180, "Retention period (in days) - delete Audit Logs records older than retention period")
	return cmd
}
