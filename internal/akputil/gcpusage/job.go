package gcpusage

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"time"

	"google.golang.org/api/option"
	"google.golang.org/api/servicecontrol/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/uuid"
	corev1apply "k8s.io/client-go/applyconfigurations/core/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/utils/ptr"

	"github.com/akuityio/akuity-platform/internal/utils/consts"
)

const (
	akuityNS             = consts.AkuityPlatformNamespace
	reportingBaseURL     = "https://servicecontrol.googleapis.com/"
	gcpCmName            = "gcp-usage-cm"
	gcpAkuityServiceName = "akuity-platform.endpoints.akuity-public.cloud.goog"
)

func ReportUsage(ctx context.Context, usageID, pathToGcpCreds string, reportInterval time.Duration, config *rest.Config) error {
	client, err := servicecontrol.NewService(context.Background(), option.WithCredentialsFile(pathToGcpCreds), option.WithEndpoint(reportingBaseURL))
	if err != nil {
		return fmt.Errorf("could not create gcp reporting client: %w", err)
	}
	client.BasePath = reportingBaseURL
	lastReportTime := time.Now().Add(-1 * reportInterval)
	hostKubeClient, err := kubernetes.NewForConfig(config)
	if err != nil {
		return err
	}

	firstRun := true
	cm, err := hostKubeClient.CoreV1().ConfigMaps(akuityNS).Get(ctx, gcpCmName, v1.GetOptions{})
	if k8serrors.IsNotFound(err) || cm.Data["start"] == "" {
		cmApply := corev1apply.ConfigMap(gcpCmName, akuityNS).WithData(map[string]string{
			"start":            fmt.Sprintf("%v", time.Now().Unix()),
			"lastReportedTime": fmt.Sprintf("%v", lastReportTime.Unix()),
		})
		cm, err = hostKubeClient.CoreV1().ConfigMaps(akuityNS).Apply(ctx, cmApply, v1.ApplyOptions{FieldManager: "gcp-reporter"})
		if err != nil {
			return fmt.Errorf("failed to create gcp cm %w", err)
		}
		log.Printf("created gcp cm with last report time %v", lastReportTime.UTC().Format(time.RFC3339))
	} else if err == nil {
		temp, err := strconv.ParseInt(cm.Data["lastReportedTime"], 10, 64)
		if err != nil {
			return err
		}
		lastReportTime = time.Unix(temp, 0)
		firstRun = false
		log.Printf("using existing gcp cm with last report time %v", lastReportTime.UTC().Format(time.RFC3339))
	} else {
		return err
	}

	for {
		currentReportingTime := time.Now()
		if lastReportTime.After(currentReportingTime.Add(-1 * reportInterval)) {
			log.Printf("interval not complete sleeping for %v", time.Until(lastReportTime.Add(reportInterval)))
			time.Sleep(time.Until(lastReportTime.Add(reportInterval)))
			continue
		} else {
			currentReportingTime = lastReportTime.Add(reportInterval)
			log.Printf("running report submission")
		}

		operation := &servicecontrol.Operation{
			ConsumerId: usageID,
			EndTime:    currentReportingTime.UTC().Format(time.RFC3339),
			MetricValueSets: []*servicecontrol.MetricValueSet{
				{
					MetricName: "akuity-platform.endpoints.akuity-public.cloud.goog/base_plan_applications",
					MetricValues: []*servicecontrol.MetricValue{
						{
							Int64Value: ptr.To(int64(1)),
						},
					},
				},
			},
			OperationId:   string(uuid.NewUUID()),
			OperationName: "Akuity Platform Usage Report",
			StartTime:     lastReportTime.UTC().Format(time.RFC3339),
		}

		// if this is the first run then report 1 control plane usage, skip this from next reports
		if firstRun {
			operation.MetricValueSets = append(operation.MetricValueSets, &servicecontrol.MetricValueSet{
				MetricName: "akuity-platform.endpoints.akuity-public.cloud.goog/base_plan_argo_cd_instances",
				MetricValues: []*servicecontrol.MetricValue{
					{
						Int64Value: ptr.To(int64(1)),
					},
				},
			})
		}

		checkResp, err := client.Services.Check(gcpAkuityServiceName, &servicecontrol.CheckRequest{
			Operation: operation,
		}).Do()
		if err != nil {
			return fmt.Errorf("failed check call %w", err)
		}
		if len(checkResp.CheckErrors) > 0 {
			data, _ := json.Marshal(checkResp.CheckErrors)
			log.Printf("check errors : %v", string(data))
			return fmt.Errorf("check errors : %v", string(data))
		}
		// can set labels in report only
		operation.UserLabels = map[string]string{
			"akuity.io/orgid": "12eg44",
		}
		reportResp, err := client.Services.Report(gcpAkuityServiceName, &servicecontrol.ReportRequest{
			Operations: []*servicecontrol.Operation{
				operation,
			},
		}).Do()
		if err != nil {
			return fmt.Errorf("failed report call %w", err)
		}
		if len(reportResp.ReportErrors) > 0 {
			data, _ := json.Marshal(reportResp.ReportErrors)
			log.Printf("report errors : %v", string(data))
			return fmt.Errorf("report errors : %v", string(data))
		}
		lastReportTime = currentReportingTime
		cmApply := corev1apply.ConfigMap(gcpCmName, akuityNS).WithData(cm.Data)
		cmApply.Data["lastReportedTime"] = fmt.Sprintf("%v", lastReportTime.Unix())

		ts := time.Now().UTC().Format(time.RFC3339)
		log.Printf("%v %v %v %v %v %v %v", ts, operation.OperationId, operation.StartTime, operation.EndTime, operation.ConsumerId, operation.MetricValueSets[0].MetricName, 1)
		// for first try we have 2 items in metric report
		if len(operation.MetricValueSets) > 1 {
			log.Printf("%v %v %v %v %v %v %v", ts, operation.OperationId, operation.StartTime, operation.EndTime, operation.ConsumerId, operation.MetricValueSets[1].MetricName, 1)
		}
		if _, err := hostKubeClient.CoreV1().ConfigMaps(akuityNS).Apply(ctx, cmApply, v1.ApplyOptions{FieldManager: "gcp-reporter"}); err != nil {
			return fmt.Errorf("failed to update gcp cm %w", err)
		}
	}
}
