package gcpusage

import (
	"fmt"
	"time"

	"github.com/spf13/cobra"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/akuityio/akuity-platform/internal/cli"
)

var gcpCMD = &cobra.Command{
	Use:   "gcpusage",
	Short: "GCP Usage reporter cronjob command",
	Run: func(cmd *cobra.Command, args []string) {
		cmd.HelpFunc()(cmd, args)
	},
}

func NewGCPUsageCmd() *cobra.Command {
	gcpCMD.AddCommand(NewGCPUsageReporterJob())
	return gcpCMD
}

const (
	reportInterval = time.Hour
)

func NewGCPUsageReporterJob() *cobra.Command {
	var (
		clientConfig   clientcmd.ClientConfig
		usageID        string
		pathToGcpCreds string
		interval       time.Duration
	)

	cmd := &cobra.Command{
		Use:   "start",
		Short: "Starts a cron job to report usage fee every hour",
		Run: func(cmd *cobra.Command, args []string) {
			restConfig, err := clientConfig.ClientConfig()
			cli.CheckErr(err)
			if usageID == "" {
				cli.CheckErr(fmt.Errorf("empty usage id"))
			}
			cli.CheckErr(ReportUsage(cmd.Context(), usageID, pathToGcpCreds, interval, restConfig))
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().StringVar(&usageID, "usage-id", "", "Usage id to use for reporting usage")
	cli.CheckErr(cmd.MarkFlagRequired("usage-id"))
	cmd.Flags().StringVar(&pathToGcpCreds, "gcp-cred-path", "", "Path to the gp service account creds")
	cli.CheckErr(cmd.MarkFlagRequired("gcp-cred-path"))
	cmd.Flags().DurationVar(&interval, "report-interval", reportInterval, "Interval period to make usage reports(default 1h)")
	return cmd
}
