package shared

import (
	"context"
	"fmt"
	"os"
	"time"

	"github.com/go-logr/logr"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/akuityio/akuity-platform/internal/cli"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/utils/consts"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/utils/logging"
	"github.com/akuityio/akuity-platform/internal/utils/metrics"
	"github.com/akuityio/akuity-platform/models/models"
)

const (
	dbDataSecretKey = "DB_DATA_KEY"

	// isArgoInstanceQuery is a query to determine if an instance ID specified is of Argo CD Instance
	isArgoInstanceQuery = `
    SELECT 
		count(id) 
	FROM 
		argo_cd_instance 
	WHERE 
		id = $1;
  `
	// isKargoInstanceQuery is a query to determine if an instance ID specified is of Kargo Instance
	isKargoInstanceQuery = `
    SELECT 
		count(id)
	FROM 
		kargo_instance 
	WHERE 
		id = $1;
  `
)

var akputilDbConnectionConfig = &config.DBConnectionConfig{
	MaxOpen:     5,
	MaxIdle:     5,
	MaxLifetime: time.Minute * 5,
	MaxIdleTime: time.Minute * 5,
}

type AKPUtil struct {
	Log          *logr.Logger
	RestConfig   *rest.Config
	K8sClient    *kubernetes.Clientset
	PortalDBPool *database.DBPool
	Auth0Config  config.PortalAuth0Config
	Ctx          context.Context
	Cancel       context.CancelFunc
}

func NewAKPUtil(clientConfig clientcmd.ClientConfig, ctx context.Context, contextTimeout time.Duration) *AKPUtil {
	log, err := logging.NewLogger()
	cli.CheckErr(err)

	restConfig, err := clientConfig.ClientConfig()
	cli.CheckErr(err)

	k8sClient, err := kubernetes.NewForConfig(restConfig)
	cli.CheckErr(err)

	ctx, cancel := context.WithTimeout(ctx, contextTimeout)

	a := &AKPUtil{
		Log:        &log,
		RestConfig: restConfig,
		K8sClient:  k8sClient,
		Ctx:        ctx,
		Cancel:     cancel,
	}

	cfg, err := config.NewPortalServerConfig()
	cli.CheckErr(err)

	a.Auth0Config = cfg.Auth0

	if cfg.PortalDBConnection == "" {
		cfg.PortalDBConnection, err = a.readDbConnectionStringFromSecret(consts.SecretKeyPortalDBConnection)
		cli.CheckErr(err)
	}

	a.PortalDBPool, err = database.GetDBPool(cfg.PortalDBConnection, akputilDbConnectionConfig)
	cli.CheckErr(err)

	if _, ok := os.LookupEnv("AKPUTIL_METRICS_SERVER"); ok {
		go func() { _ = metrics.NewMetricsServer(&log, "akputil", config.AkputilMetricsPort)() }()
	}

	return a
}

// Close - closes the AkpUtil's Portal DB connection pool
func (a *AKPUtil) Close() error {
	a.Cancel()
	return database.CloseDBPool(a.PortalDBPool)
}

func (a *AKPUtil) IsArgoInstance(instanceID string) (bool, error) {
	return a.isInstance(isArgoInstanceQuery, instanceID)
}

func (a *AKPUtil) IsKargoInstance(instanceID string) (bool, error) {
	return a.isInstance(isKargoInstanceQuery, instanceID)
}

func (a *AKPUtil) isInstance(query, instanceID string) (bool, error) {
	var count int
	if err := models.NewQuery(qm.SQL(query, instanceID)).QueryRowContext(a.Ctx, a.PortalDBPool.DB).Scan(&count); err != nil {
		return false, err
	}
	return count == 1, nil
}

func (a *AKPUtil) GetAllClusterNames(instanceID string) ([]string, error) {
	ok, err := a.IsArgoInstance(instanceID)
	if err != nil {
		return nil, err
	}

	var tableName string
	if ok {
		tableName = "argo_cd_cluster"
	} else {
		tableName = "kargo_agent"
	}

	query := fmt.Sprintf("SELECT name FROM %s WHERE instance_id = $1", tableName)

	rows, err := models.NewQuery(qm.SQL(query, instanceID)).QueryContext(a.Ctx, a.PortalDBPool.DB)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	clusterNames := make([]string, 0)
	for rows.Next() {
		var name string
		if err := rows.Scan(&name); err != nil {
			return nil, err
		}
		clusterNames = append(clusterNames, name)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return clusterNames, nil
}

func (a *AKPUtil) readDbConnectionStringFromSecret(dbConnectionSecretKey string) (string, error) {
	dbSecrets, err := a.K8sClient.CoreV1().Secrets(consts.AkuityPlatformNamespace).Get(a.Ctx, consts.AkuityPlatformSecret, metav1.GetOptions{})
	if err != nil {
		return "", err
	}

	secretDescription := fmt.Sprintf("secret '%s:%s'", consts.AkuityPlatformNamespace, consts.AkuityPlatformSecret)

	// https://pkg.go.dev/github.com/lib/pq#hdr-Connection_String_Parameters
	// user=.. dbname=.. host=.. port=5432 sslmode=require password=..
	dbConnectionString, err := readSecretKey(dbSecrets, secretDescription, dbConnectionSecretKey)
	if err != nil {
		return "", err
	}

	dbDataKey, err := readSecretKey(dbSecrets, secretDescription, dbDataSecretKey)
	if err != nil {
		return "", err
	}

	if err := database.InitializeDataKey(config.DBDataKeyConfig{
		DataKey: dbDataKey,
	}); err != nil {
		return "", err
	}

	return dbConnectionString, nil
}

func readSecretKey(secret *v1.Secret, secretDescription, secretKey string) (string, error) {
	value, ok := secret.Data[secretKey]
	if !ok {
		return "", fmt.Errorf("%s contains no key %q", secretDescription, secretKey)
	}
	return string(value), nil
}
