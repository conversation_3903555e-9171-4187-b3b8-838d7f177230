package tokens

import (
	"time"

	"github.com/spf13/cobra"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/akuityio/akuity-platform/internal/akputil/shared"
	"github.com/akuityio/akuity-platform/internal/cli"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

func NewTokenCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "tokens",
		Short: "Manage blacklisted_tokens",
		Run: func(cmd *cobra.Command, args []string) {
			cmd.HelpFunc()(cmd, args)
		},
	}
	cmd.AddCommand(NewDeleteExpiredTokensCommand())
	return cmd
}

func NewDeleteExpiredTokensCommand() *cobra.Command {
	var clientConfig clientcmd.ClientConfig
	cmd := &cobra.Command{
		Use:   "delete-expired-tokens",
		Short: "Delete expired tokens",
		Run: func(cmd *cobra.Command, args []string) {
			akpUtil := shared.NewAKPUtil(clientConfig, cmd.Context(), time.Minute)
			defer func() { _ = akpUtil.Close() }()

			repoSet := client.NewRepoSet(akpUtil.PortalDBPool.DB)
			if err := repoSet.BlacklistedTokens().Filter(models.BlacklistedTokenWhere.ExpirationTimestamp.LT(time.Now())).DeleteAll(cmd.Context()); err != nil {
				akpUtil.Log.Error(err, "Failed to delete expired tokens")
			}

			akpUtil.Log.Info("Deleted expired tokens")
		},
	}
	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	return cmd
}
