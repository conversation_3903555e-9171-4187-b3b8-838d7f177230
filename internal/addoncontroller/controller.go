package addoncontroller

import (
	"context"
	"fmt"
	"os"

	"github.com/spf13/cobra"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/akuityio/akuity-platform/controllers/addon/integration"
	"github.com/akuityio/akuity-platform/internal/cli"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/utils/devcheck"
	"github.com/akuityio/akuity-platform/internal/utils/logging"
	"github.com/akuityio/akuity-platform/internal/utils/metrics"
	"github.com/akuityio/akuity-platform/models/client"
)

func NewAddonControllerCommand() *cobra.Command {
	var (
		numWorkers  int
		insecure    bool
		metricsPort int
		shard       string
		debug       bool
	)
	cmd := &cobra.Command{
		Use:   "addon-controller",
		Short: "Akuity Addon Controller",
		Run: func(cmd *cobra.Command, args []string) {
			controllerConfig, err := config.NewAddonControllerConfig()
			cli.CheckErr(err)

			var logOpts []logging.Option
			if debug {
				logOpts = append(logOpts, logging.WithDebug())
			}
			log, err := logging.NewLogger(logOpts...)
			cli.CheckErr(err)

			// dev: license init needs to happen before feature flag init
			cli.CheckErr(config.InitializeLicense(log))

			err = database.InitializeDataKey(controllerConfig.DBDataKey)
			cli.CheckErr(err)

			ctx, cancel := context.WithCancel(context.Background())
			defer cancel()

			portalDBPool, err := database.GetDBPool(controllerConfig.PortalDBConnection, &controllerConfig.DBConnection)
			if err != nil {
				cli.CheckErr(fmt.Errorf("failed to connect to portal RW db: %w", err))
			}

			featureSvc := features.NewService(client.NewRepoSet(portalDBPool.DB), portalDBPool.DB, config.IsSelfHosted, config.IsSelfHostedReleaseBuild, controllerConfig.FeatureGatesSource, config.GetLicense(), features.WithLogger(&log))

			k3sDBPool, err := database.GetDBPool(controllerConfig.TenantDBConnection, &controllerConfig.DBConnection)
			if err != nil {
				cli.CheckErr(fmt.Errorf("failed to connect to k3s db: %w", err))
			}

			loadingRules := clientcmd.NewDefaultClientConfigLoadingRules()
			loadingRules.DefaultClientConfig = &clientcmd.DefaultClientConfig
			overrides := clientcmd.ConfigOverrides{}
			clientConfig := clientcmd.NewInteractiveDeferredLoadingClientConfig(loadingRules, &overrides, os.Stdin)
			restConfig, err := clientConfig.ClientConfig()
			cli.CheckErr(err)
			if insecure {
				restConfig.Insecure = insecure
				restConfig.CAData = nil
				restConfig.CAFile = ""
			}

			k8sClient, err := kubernetes.NewForConfig(restConfig)
			cli.CheckErr(err)

			cli.CheckErr(devcheck.Safeguard(ctx, config.ConfigWithConnection(&controllerConfig), k8sClient))

			settings := integration.ControllerSettings{
				K8SRestConfig:      restConfig,
				K8SClientSet:       k8sClient,
				PortalDBRawClient:  portalDBPool.DB,
				K3sDBRawClient:     k3sDBPool.DB,
				K3sDBConnection:    controllerConfig.TenantDBConnection,
				PortalDBConnection: controllerConfig.PortalDBConnection,
				RepoSet:            client.NewRepoSet(portalDBPool.DB),
				Log:                &log,
				Shard:              shard,
			}
			addonController, err := integration.NewAddonController(&log, settings, controllerConfig, featureSvc)
			cli.CheckErr(err)

			go func() { cli.CheckErr(metrics.NewMetricsServer(&log, "addon-controller", metricsPort)()) }()

			cli.CheckErr(addonController.Init(context.Background()))
			cli.CheckErr(addonController.Run(ctx, numWorkers))
		},
	}
	cmd.Flags().IntVar(&numWorkers, "num-workers", 1, "The number of workers")
	cmd.Flags().BoolVar(&insecure, "insecure", false, "Whether to enable insecure")
	cmd.Flags().StringVar(&shard, "shard", "", "Shard name")
	cmd.Flags().IntVar(&metricsPort, "metrics-port", config.DefaultAddonControllerMetricsPort, "The metrics server port")
	cmd.Flags().BoolVar(&debug, "debug", false, "Enable debug mode")
	return cmd
}
